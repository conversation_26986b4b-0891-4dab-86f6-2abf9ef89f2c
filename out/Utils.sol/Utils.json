{"abi": [], "bytecode": {"object": "0x6080604052348015600e575f5ffd5b50603e80601a5f395ff3fe60806040525f5ffdfea2646970667358221220c0a7940fd5123d76e6d94bd018dae9c637af063b6e3a281a18f6c6576d77121264736f6c634300081d0033", "sourceMap": "97:4992:31:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x60806040525f5ffdfea2646970667358221220c0a7940fd5123d76e6d94bd018dae9c637af063b6e3a281a18f6c6576d77121264736f6c634300081d0033", "sourceMap": "97:4992:31:-:0;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/setup-helpers/src/Utils.sol\":\"Utils\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/setup-helpers/src/Panic.sol\":{\"keccak256\":\"0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325\",\"dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd\"]},\"lib/setup-helpers/src/Utils.sol\":{\"keccak256\":\"0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523\",\"dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/setup-helpers/src/Utils.sol": "Utils"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/setup-helpers/src/Panic.sol": {"keccak256": "0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92", "urls": ["bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325", "dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd"], "license": "GPL-2.0"}, "lib/setup-helpers/src/Utils.sol": {"keccak256": "0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27", "urls": ["bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523", "dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau"], "license": "GPL-2.0"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/setup-helpers/src/Utils.sol", "id": 42407, "exportedSymbols": {"Panic": [42052], "Utils": [42406]}, "nodeType": "SourceUnit", "src": "36:5054:31", "nodes": [{"id": 42054, "nodeType": "PragmaDirective", "src": "36:23:31", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 42056, "nodeType": "ImportDirective", "src": "61:34:31", "nodes": [], "absolutePath": "lib/setup-helpers/src/Panic.sol", "file": "./Panic.sol", "nameLocation": "-1:-1:-1", "scope": 42407, "sourceUnit": 42053, "symbolAliases": [{"foreign": {"id": 42055, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "69:5:31", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42406, "nodeType": "ContractDefinition", "src": "97:4992:31", "nodes": [{"id": 42133, "nodeType": "FunctionDefinition", "src": "383:765:31", "nodes": [], "body": {"id": 42132, "nodeType": "Block", "src": "474:674:31", "nodes": [], "statements": [{"assignments": [42067, 42069], "declarations": [{"constant": false, "id": 42067, "mutability": "mutable", "name": "revertMsg", "nameLocation": "499:9:31", "nodeType": "VariableDeclaration", "scope": 42132, "src": "485:23:31", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42066, "name": "string", "nodeType": "ElementaryTypeName", "src": "485:6:31", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 42069, "mutability": "mutable", "name": "customError", "nameLocation": "515:11:31", "nodeType": "VariableDeclaration", "scope": 42132, "src": "510:16:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 42068, "name": "bool", "nodeType": "ElementaryTypeName", "src": "510:4:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "id": 42073, "initialValue": {"arguments": [{"id": 42071, "name": "err", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42059, "src": "544:3:31", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 42070, "name": "_getRevertMsg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42215, "src": "530:13:31", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$_t_bool_$", "typeString": "function (bytes memory) pure returns (string memory,bool)"}}, "id": 42072, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "530:18:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "nodeType": "VariableDeclarationStatement", "src": "484:64:31"}, {"assignments": [42075], "declarations": [{"constant": false, "id": 42075, "mutability": "mutable", "name": "errorBytes", "nameLocation": "567:10:31", "nodeType": "VariableDeclaration", "scope": 42132, "src": "559:18:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 42074, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "559:7:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 42076, "nodeType": "VariableDeclarationStatement", "src": "559:18:31"}, {"assignments": [42078], "declarations": [{"constant": false, "id": 42078, "mutability": "mutable", "name": "expectedBytes", "nameLocation": "595:13:31", "nodeType": "VariableDeclaration", "scope": 42132, "src": "587:21:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 42077, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "587:7:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 42079, "nodeType": "VariableDeclarationStatement", "src": "587:21:31"}, {"condition": {"id": 42080, "name": "customError", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42069, "src": "623:11:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 42126, "nodeType": "Block", "src": "903:143:31", "statements": [{"expression": {"id": 42115, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 42108, "name": "errorBytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42075, "src": "917:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"arguments": [{"id": 42112, "name": "revertMsg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42067, "src": "957:9:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 42110, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "940:3:31", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 42111, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "944:12:31", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "940:16:31", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 42113, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "940:27:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 42109, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "930:9:31", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 42114, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "930:38:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "917:51:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 42116, "nodeType": "ExpressionStatement", "src": "917:51:31"}, {"expression": {"id": 42124, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 42117, "name": "expectedBytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42078, "src": "982:13:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"arguments": [{"id": 42121, "name": "expected", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42061, "src": "1025:8:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 42119, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "1008:3:31", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 42120, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1012:12:31", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "1008:16:31", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 42122, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1008:26:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 42118, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "998:9:31", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 42123, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "998:37:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "982:53:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 42125, "nodeType": "ExpressionStatement", "src": "982:53:31"}]}, "id": 42127, "nodeType": "IfStatement", "src": "619:427:31", "trueBody": {"id": 42107, "nodeType": "Block", "src": "636:261:31", "statements": [{"expression": {"id": 42093, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 42081, "name": "errorBytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42075, "src": "750:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"arguments": [{"id": 42086, "name": "revertMsg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42067, "src": "788:9:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"arguments": [{"hexValue": "30", "id": 42089, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "807:1:31", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 42088, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "799:7:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes28_$", "typeString": "type(bytes28)"}, "typeName": {"id": 42087, "name": "bytes28", "nodeType": "ElementaryTypeName", "src": "799:7:31", "typeDescriptions": {}}}, "id": 42090, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "799:10:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes28", "typeString": "bytes28"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_bytes28", "typeString": "bytes28"}], "expression": {"id": 42084, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "771:3:31", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 42085, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "775:12:31", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "771:16:31", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 42091, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "771:39:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 42083, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "763:7:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes32_$", "typeString": "type(bytes32)"}, "typeName": {"id": 42082, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "763:7:31", "typeDescriptions": {}}}, "id": 42092, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "763:48:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "750:61:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 42094, "nodeType": "ExpressionStatement", "src": "750:61:31"}, {"expression": {"id": 42105, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 42095, "name": "expectedBytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42078, "src": "825:13:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"arguments": [{"arguments": [{"id": 42101, "name": "expected", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42061, "src": "875:8:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 42099, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "858:3:31", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 42100, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "862:12:31", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "858:16:31", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 42102, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "858:26:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 42098, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "848:9:31", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 42103, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "848:37:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 42097, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "841:6:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes4_$", "typeString": "type(bytes4)"}, "typeName": {"id": 42096, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "841:6:31", "typeDescriptions": {}}}, "id": 42104, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "841:45:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "src": "825:61:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 42106, "nodeType": "ExpressionStatement", "src": "825:61:31"}]}}, {"expression": {"commonType": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "id": 42130, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42128, "name": "errorBytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42075, "src": "1114:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 42129, "name": "expectedBytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42078, "src": "1128:13:31", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "1114:27:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 42065, "id": 42131, "nodeType": "Return", "src": "1107:34:31"}]}, "documentation": {"id": 42057, "nodeType": "StructuredDocumentation", "src": "118:260:31", "text": "@dev check if the error returned from a call is the same as the expected error\n @param err the error returned from a call\n @param expected the expected error\n @return true if the error is the same as the expected error, false otherwise"}, "implemented": true, "kind": "function", "modifiers": [], "name": "checkError", "nameLocation": "392:10:31", "parameters": {"id": 42062, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42059, "mutability": "mutable", "name": "err", "nameLocation": "416:3:31", "nodeType": "VariableDeclaration", "scope": 42133, "src": "403:16:31", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 42058, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "403:5:31", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}, {"constant": false, "id": 42061, "mutability": "mutable", "name": "expected", "nameLocation": "435:8:31", "nodeType": "VariableDeclaration", "scope": 42133, "src": "421:22:31", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42060, "name": "string", "nodeType": "ElementaryTypeName", "src": "421:6:31", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "402:42:31"}, "returnParameters": {"id": 42065, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42064, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 42133, "src": "468:4:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 42063, "name": "bool", "nodeType": "ElementaryTypeName", "src": "468:4:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "467:6:31"}, "scope": 42406, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 42215, "nodeType": "FunctionDefinition", "src": "1407:1376:31", "nodes": [], "body": {"id": 42214, "nodeType": "Block", "src": "1499:1284:31", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42146, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 42143, "name": "returnData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42136, "src": "1619:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 42144, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1630:6:31", "memberName": "length", "nodeType": "MemberAccess", "src": "1619:17:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 42145, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1640:1:31", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "1619:22:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 42151, "nodeType": "IfStatement", "src": "1615:46:31", "trueBody": {"expression": {"components": [{"hexValue": "", "id": 42147, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1651:2:31", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}, "value": ""}, {"hexValue": "66616c7365", "id": 42148, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1655:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42149, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1650:11:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470_$_t_bool_$", "typeString": "tuple(literal_string \"\",bool)"}}, "functionReturnParameters": 42142, "id": 42150, "nodeType": "Return", "src": "1643:18:31"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42157, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 42152, "name": "returnData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42136, "src": "1804:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 42153, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1815:6:31", "memberName": "length", "nodeType": "MemberAccess", "src": "1804:17:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"commonType": {"typeIdentifier": "t_rational_36_by_1", "typeString": "int_const 36"}, "id": 42156, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"hexValue": "34", "id": 42154, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1825:1:31", "typeDescriptions": {"typeIdentifier": "t_rational_4_by_1", "typeString": "int_const 4"}, "value": "4"}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"hexValue": "3332", "id": 42155, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1829:2:31", "typeDescriptions": {"typeIdentifier": "t_rational_32_by_1", "typeString": "int_const 32"}, "value": "32"}, "src": "1825:6:31", "typeDescriptions": {"typeIdentifier": "t_rational_36_by_1", "typeString": "int_const 36"}}, "src": "1804:27:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 42172, "nodeType": "IfStatement", "src": "1800:253:31", "trueBody": {"id": 42171, "nodeType": "Block", "src": "1833:220:31", "statements": [{"assignments": [42159], "declarations": [{"constant": false, "id": 42159, "mutability": "mutable", "name": "panic", "nameLocation": "1919:5:31", "nodeType": "VariableDeclaration", "scope": 42171, "src": "1914:10:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 42158, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1914:4:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "id": 42163, "initialValue": {"arguments": [{"id": 42161, "name": "returnData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42136, "src": "1941:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 42160, "name": "_checkIfPanic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42260, "src": "1927:13:31", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$_t_bool_$", "typeString": "function (bytes memory) pure returns (bool)"}}, "id": 42162, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1927:25:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "VariableDeclarationStatement", "src": "1914:38:31"}, {"condition": {"id": 42164, "name": "panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42159, "src": "1971:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 42170, "nodeType": "IfStatement", "src": "1967:76:31", "trueBody": {"id": 42169, "nodeType": "Block", "src": "1978:65:31", "statements": [{"expression": {"arguments": [{"id": 42166, "name": "returnData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42136, "src": "2017:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 42165, "name": "_getPanicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42394, "src": "2003:13:31", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$_t_bool_$", "typeString": "function (bytes memory) pure returns (string memory,bool)"}}, "id": 42167, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2003:25:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42142, "id": 42168, "nodeType": "Return", "src": "1996:32:31"}]}}]}}, {"assignments": [42174], "declarations": [{"constant": false, "id": 42174, "mutability": "mutable", "name": "errorSelector", "nameLocation": "2120:13:31", "nodeType": "VariableDeclaration", "scope": 42214, "src": "2113:20:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}, "typeName": {"id": 42173, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "2113:6:31", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "visibility": "internal"}], "id": 42178, "initialValue": {"arguments": [{"id": 42176, "name": "returnData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42136, "src": "2154:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 42175, "name": "_getErrorSelector", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42405, "src": "2136:17:31", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$_t_bytes4_$", "typeString": "function (bytes memory) pure returns (bytes4)"}}, "id": 42177, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2136:29:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "nodeType": "VariableDeclarationStatement", "src": "2113:52:31"}, {"assignments": [42180], "declarations": [{"constant": false, "id": 42180, "mutability": "mutable", "name": "errorStringSelector", "nameLocation": "2246:19:31", "nodeType": "VariableDeclaration", "scope": 42214, "src": "2239:26:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}, "typeName": {"id": 42179, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "2239:6:31", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "visibility": "internal"}], "id": 42187, "initialValue": {"arguments": [{"arguments": [{"hexValue": "4572726f7228737472696e6729", "id": 42184, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2285:15:31", "typeDescriptions": {"typeIdentifier": "t_stringliteral_08c379a0afcc32b1a39302f7cb8073359698411ab5fd6e3edb2c02c0b5fba8aa", "typeString": "literal_string \"Error(string)\""}, "value": "Error(string)"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_08c379a0afcc32b1a39302f7cb8073359698411ab5fd6e3edb2c02c0b5fba8aa", "typeString": "literal_string \"Error(string)\""}], "id": 42183, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "2275:9:31", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 42185, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2275:26:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 42182, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2268:6:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes4_$", "typeString": "type(bytes4)"}, "typeName": {"id": 42181, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "2268:6:31", "typeDescriptions": {}}}, "id": 42186, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2268:34:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "nodeType": "VariableDeclarationStatement", "src": "2239:63:31"}, {"condition": {"commonType": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}, "id": 42190, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42188, "name": "errorSelector", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42174, "src": "2360:13:31", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 42189, "name": "errorStringSelector", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42180, "src": "2377:19:31", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "src": "2360:36:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 42203, "nodeType": "IfStatement", "src": "2356:282:31", "trueBody": {"id": 42202, "nodeType": "Block", "src": "2398:240:31", "statements": [{"AST": {"nativeSrc": "2421:145:31", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2421:145:31", "statements": [{"nativeSrc": "2517:35:31", "nodeType": "YulAssignment", "src": "2517:35:31", "value": {"arguments": [{"name": "returnData", "nativeSrc": "2535:10:31", "nodeType": "YulIdentifier", "src": "2535:10:31"}, {"kind": "number", "nativeSrc": "2547:4:31", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2547:4:31", "type": "", "value": "0x04"}], "functionName": {"name": "add", "nativeSrc": "2531:3:31", "nodeType": "YulIdentifier", "src": "2531:3:31"}, "nativeSrc": "2531:21:31", "nodeType": "YulFunctionCall", "src": "2531:21:31"}, "variableNames": [{"name": "returnData", "nativeSrc": "2517:10:31", "nodeType": "YulIdentifier", "src": "2517:10:31"}]}]}, "evmVersion": "prague", "externalReferences": [{"declaration": 42136, "isOffset": false, "isSlot": false, "src": "2517:10:31", "valueSize": 1}, {"declaration": 42136, "isOffset": false, "isSlot": false, "src": "2535:10:31", "valueSize": 1}], "id": 42191, "nodeType": "InlineAssembly", "src": "2412:154:31"}, {"expression": {"components": [{"arguments": [{"id": 42194, "name": "returnData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42136, "src": "2598:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 42196, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2611:6:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 42195, "name": "string", "nodeType": "ElementaryTypeName", "src": "2611:6:31", "typeDescriptions": {}}}], "id": 42197, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "2610:8:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}], "expression": {"id": 42192, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "2587:3:31", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 42193, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "2591:6:31", "memberName": "decode", "nodeType": "MemberAccess", "src": "2587:10:31", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 42198, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2587:32:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "66616c7365", "id": 42199, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2621:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42200, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "2586:41:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42142, "id": 42201, "nodeType": "Return", "src": "2579:48:31"}]}}, {"expression": {"components": [{"arguments": [{"arguments": [{"id": 42208, "name": "errorSelector", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42174, "src": "2754:13:31", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes4", "typeString": "bytes4"}], "expression": {"id": 42206, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "2737:3:31", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 42207, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "2741:12:31", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "2737:16:31", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 42209, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2737:31:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 42205, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2730:6:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 42204, "name": "string", "nodeType": "ElementaryTypeName", "src": "2730:6:31", "typeDescriptions": {}}}, "id": 42210, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2730:39:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "74727565", "id": 42211, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2771:4:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}], "id": 42212, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "2729:47:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42142, "id": 42213, "nodeType": "Return", "src": "2722:54:31"}]}, "documentation": {"id": 42134, "nodeType": "StructuredDocumentation", "src": "1154:248:31", "text": "@dev get the revert message from a call\n @notice based on https://ethereum.stackexchange.com/a/83577\n @param returnData the return data from a call\n @return the revert message and a boolean indicating if it's a custom error"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_getRevertMsg", "nameLocation": "1416:13:31", "parameters": {"id": 42137, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42136, "mutability": "mutable", "name": "returnData", "nameLocation": "1443:10:31", "nodeType": "VariableDeclaration", "scope": 42215, "src": "1430:23:31", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 42135, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1430:5:31", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "1429:25:31"}, "returnParameters": {"id": 42142, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42139, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 42215, "src": "1478:13:31", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42138, "name": "string", "nodeType": "ElementaryTypeName", "src": "1478:6:31", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 42141, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 42215, "src": "1493:4:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 42140, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1493:4:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1477:21:31"}, "scope": 42406, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 42260, "nodeType": "FunctionDefinition", "src": "2789:333:31", "nodes": [], "body": {"id": 42259, "nodeType": "Block", "src": "2866:256:31", "nodes": [], "statements": [{"assignments": [42223], "declarations": [{"constant": false, "id": 42223, "mutability": "mutable", "name": "panicSignature", "nameLocation": "2883:14:31", "nodeType": "VariableDeclaration", "scope": 42259, "src": "2876:21:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}, "typeName": {"id": 42222, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "2876:6:31", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "visibility": "internal"}], "id": 42233, "initialValue": {"arguments": [{"arguments": [{"arguments": [{"hexValue": "50616e69632875696e7432353629", "id": 42229, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2923:16:31", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, "value": "Panic(uint256)"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}], "id": 42228, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2917:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 42227, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "2917:5:31", "typeDescriptions": {}}}, "id": 42230, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2917:23:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 42226, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "2907:9:31", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 42231, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2907:34:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 42225, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2900:6:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes4_$", "typeString": "type(bytes4)"}, "typeName": {"id": 42224, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "2900:6:31", "typeDescriptions": {}}}, "id": 42232, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2900:42:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "nodeType": "VariableDeclarationStatement", "src": "2876:66:31"}, {"body": {"id": 42255, "nodeType": "Block", "src": "2985:109:31", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}, "id": 42250, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"baseExpression": {"id": 42244, "name": "returnData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42217, "src": "3003:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 42246, "indexExpression": {"id": 42245, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42235, "src": "3014:1:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3003:13:31", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"baseExpression": {"id": 42247, "name": "panicSignature", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42223, "src": "3020:14:31", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "id": 42249, "indexExpression": {"id": 42248, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42235, "src": "3035:1:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3020:17:31", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "src": "3003:34:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 42254, "nodeType": "IfStatement", "src": "2999:85:31", "trueBody": {"id": 42253, "nodeType": "Block", "src": "3039:45:31", "statements": [{"expression": {"hexValue": "66616c7365", "id": 42251, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3064:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "functionReturnParameters": 42221, "id": 42252, "nodeType": "Return", "src": "3057:12:31"}]}}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42240, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42238, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42235, "src": "2973:1:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "34", "id": 42239, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2977:1:31", "typeDescriptions": {"typeIdentifier": "t_rational_4_by_1", "typeString": "int_const 4"}, "value": "4"}, "src": "2973:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 42256, "initializationExpression": {"assignments": [42235], "declarations": [{"constant": false, "id": 42235, "mutability": "mutable", "name": "i", "nameLocation": "2966:1:31", "nodeType": "VariableDeclaration", "scope": 42256, "src": "2958:9:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42234, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2958:7:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 42237, "initialValue": {"hexValue": "30", "id": 42236, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2970:1:31", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "2958:13:31"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 42242, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "2980:3:31", "subExpression": {"id": 42241, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42235, "src": "2980:1:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 42243, "nodeType": "ExpressionStatement", "src": "2980:3:31"}, "nodeType": "ForStatement", "src": "2953:141:31"}, {"expression": {"hexValue": "74727565", "id": 42257, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3111:4:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 42221, "id": 42258, "nodeType": "Return", "src": "3104:11:31"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_checkIfPanic", "nameLocation": "2798:13:31", "parameters": {"id": 42218, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42217, "mutability": "mutable", "name": "returnData", "nameLocation": "2825:10:31", "nodeType": "VariableDeclaration", "scope": 42260, "src": "2812:23:31", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 42216, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "2812:5:31", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "2811:25:31"}, "returnParameters": {"id": 42221, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42220, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 42260, "src": "2860:4:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 42219, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2860:4:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2859:6:31"}, "scope": 42406, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 42394, "nodeType": "FunctionDefinition", "src": "3128:1732:31", "nodes": [], "body": {"id": 42393, "nodeType": "Block", "src": "3220:1640:31", "nodes": [], "statements": [{"assignments": [42270], "declarations": [{"constant": false, "id": 42270, "mutability": "mutable", "name": "panicCode", "nameLocation": "3238:9:31", "nodeType": "VariableDeclaration", "scope": 42393, "src": "3230:17:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42269, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3230:7:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 42271, "nodeType": "VariableDeclarationStatement", "src": "3230:17:31"}, {"body": {"id": 42297, "nodeType": "Block", "src": "3290:98:31", "statements": [{"expression": {"id": 42286, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 42282, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "3304:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42285, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42283, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "3316:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<<", "rightExpression": {"hexValue": "38", "id": 42284, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3329:1:31", "typeDescriptions": {"typeIdentifier": "t_rational_8_by_1", "typeString": "int_const 8"}, "value": "8"}, "src": "3316:14:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3304:26:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 42287, "nodeType": "ExpressionStatement", "src": "3304:26:31"}, {"expression": {"id": 42295, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 42288, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "3344:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "|=", "rightHandSide": {"arguments": [{"baseExpression": {"id": 42291, "name": "returnData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42262, "src": "3363:10:31", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 42293, "indexExpression": {"id": 42292, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42273, "src": "3374:1:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3363:13:31", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes1", "typeString": "bytes1"}], "id": 42290, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3357:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint8_$", "typeString": "type(uint8)"}, "typeName": {"id": 42289, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "3357:5:31", "typeDescriptions": {}}}, "id": 42294, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3357:20:31", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "src": "3344:33:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 42296, "nodeType": "ExpressionStatement", "src": "3344:33:31"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42278, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42276, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42273, "src": "3277:1:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "3336", "id": 42277, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3281:2:31", "typeDescriptions": {"typeIdentifier": "t_rational_36_by_1", "typeString": "int_const 36"}, "value": "36"}, "src": "3277:6:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 42298, "initializationExpression": {"assignments": [42273], "declarations": [{"constant": false, "id": 42273, "mutability": "mutable", "name": "i", "nameLocation": "3270:1:31", "nodeType": "VariableDeclaration", "scope": 42298, "src": "3262:9:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42272, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3262:7:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 42275, "initialValue": {"hexValue": "34", "id": 42274, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3274:1:31", "typeDescriptions": {"typeIdentifier": "t_rational_4_by_1", "typeString": "int_const 4"}, "value": "4"}, "nodeType": "VariableDeclarationStatement", "src": "3262:13:31"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 42280, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "3285:3:31", "subExpression": {"id": 42279, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42273, "src": "3285:1:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 42281, "nodeType": "ExpressionStatement", "src": "3285:3:31"}, "nodeType": "ForStatement", "src": "3257:131:31"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42301, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42299, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "3467:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "31", "id": 42300, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3480:1:31", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "3467:14:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42310, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42308, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "3622:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3137", "id": 42309, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3635:2:31", "typeDescriptions": {"typeIdentifier": "t_rational_17_by_1", "typeString": "int_const 17"}, "value": "17"}, "src": "3622:15:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42319, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42317, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "3780:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3138", "id": 42318, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3793:2:31", "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "src": "3780:15:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42328, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42326, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "3909:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3333", "id": 42327, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3922:2:31", "typeDescriptions": {"typeIdentifier": "t_rational_33_by_1", "typeString": "int_const 33"}, "value": "33"}, "src": "3909:15:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42337, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42335, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "4071:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3334", "id": 42336, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4084:2:31", "typeDescriptions": {"typeIdentifier": "t_rational_34_by_1", "typeString": "int_const 34"}, "value": "34"}, "src": "4071:15:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42346, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42344, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "4226:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3439", "id": 42345, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4239:2:31", "typeDescriptions": {"typeIdentifier": "t_rational_49_by_1", "typeString": "int_const 49"}, "value": "49"}, "src": "4226:15:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42355, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42353, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "4360:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3530", "id": 42354, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4373:2:31", "typeDescriptions": {"typeIdentifier": "t_rational_50_by_1", "typeString": "int_const 50"}, "value": "50"}, "src": "4360:15:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42364, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42362, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "4492:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3635", "id": 42363, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4505:2:31", "typeDescriptions": {"typeIdentifier": "t_rational_65_by_1", "typeString": "int_const 65"}, "value": "65"}, "src": "4492:15:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 42373, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 42371, "name": "panicCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42270, "src": "4654:9:31", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3831", "id": 42372, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4667:2:31", "typeDescriptions": {"typeIdentifier": "t_rational_81_by_1", "typeString": "int_const 81"}, "value": "81"}, "src": "4654:15:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 42380, "nodeType": "IfStatement", "src": "4650:155:31", "trueBody": {"id": 42379, "nodeType": "Block", "src": "4671:134:31", "statements": [{"expression": {"components": [{"expression": {"id": 42374, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "4767:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Panic_$42052_$", "typeString": "type(library Panic)"}}, "id": 42375, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4773:13:31", "memberName": "functionPanic", "nodeType": "MemberAccess", "referencedDeclaration": 42051, "src": "4767:19:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "66616c7365", "id": 42376, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4788:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42377, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4766:28:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42268, "id": 42378, "nodeType": "Return", "src": "4759:35:31"}]}}, "id": 42381, "nodeType": "IfStatement", "src": "4488:317:31", "trueBody": {"id": 42370, "nodeType": "Block", "src": "4509:135:31", "statements": [{"expression": {"components": [{"expression": {"id": 42365, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "4608:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Panic_$42052_$", "typeString": "type(library Panic)"}}, "id": 42366, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4614:11:31", "memberName": "memoryPanic", "nodeType": "MemberAccess", "referencedDeclaration": 42048, "src": "4608:17:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "66616c7365", "id": 42367, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4627:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42368, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4607:26:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42268, "id": 42369, "nodeType": "Return", "src": "4600:33:31"}]}}, "id": 42382, "nodeType": "IfStatement", "src": "4356:449:31", "trueBody": {"id": 42361, "nodeType": "Block", "src": "4377:105:31", "statements": [{"expression": {"components": [{"expression": {"id": 42356, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "4441:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Panic_$42052_$", "typeString": "type(library Panic)"}}, "id": 42357, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4447:16:31", "memberName": "outOfBoundsPanic", "nodeType": "MemberAccess", "referencedDeclaration": 42045, "src": "4441:22:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "66616c7365", "id": 42358, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4465:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42359, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4440:31:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42268, "id": 42360, "nodeType": "Return", "src": "4433:38:31"}]}}, "id": 42383, "nodeType": "IfStatement", "src": "4222:583:31", "trueBody": {"id": 42352, "nodeType": "Block", "src": "4243:107:31", "statements": [{"expression": {"components": [{"expression": {"id": 42347, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "4310:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Panic_$42052_$", "typeString": "type(library Panic)"}}, "id": 42348, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4316:15:31", "memberName": "emptyArrayPanic", "nodeType": "MemberAccess", "referencedDeclaration": 42042, "src": "4310:21:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "66616c7365", "id": 42349, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4333:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42350, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4309:30:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42268, "id": 42351, "nodeType": "Return", "src": "4302:37:31"}]}}, "id": 42384, "nodeType": "IfStatement", "src": "4067:738:31", "trueBody": {"id": 42343, "nodeType": "Block", "src": "4088:128:31", "statements": [{"expression": {"components": [{"expression": {"id": 42338, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "4181:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Panic_$42052_$", "typeString": "type(library Panic)"}}, "id": 42339, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4187:10:31", "memberName": "arrayPanic", "nodeType": "MemberAccess", "referencedDeclaration": 42039, "src": "4181:16:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "66616c7365", "id": 42340, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4199:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42341, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4180:25:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42268, "id": 42342, "nodeType": "Return", "src": "4173:32:31"}]}}, "id": 42385, "nodeType": "IfStatement", "src": "3905:900:31", "trueBody": {"id": 42334, "nodeType": "Block", "src": "3926:135:31", "statements": [{"expression": {"components": [{"expression": {"id": 42329, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "4027:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Panic_$42052_$", "typeString": "type(library Panic)"}}, "id": 42330, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4033:9:31", "memberName": "enumPanic", "nodeType": "MemberAccess", "referencedDeclaration": 42036, "src": "4027:15:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "66616c7365", "id": 42331, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4044:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42332, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4026:24:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42268, "id": 42333, "nodeType": "Return", "src": "4019:31:31"}]}}, "id": 42386, "nodeType": "IfStatement", "src": "3776:1029:31", "trueBody": {"id": 42325, "nodeType": "Block", "src": "3797:102:31", "statements": [{"expression": {"components": [{"expression": {"id": 42320, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "3861:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Panic_$42052_$", "typeString": "type(library Panic)"}}, "id": 42321, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3867:13:31", "memberName": "divisionPanic", "nodeType": "MemberAccess", "referencedDeclaration": 42033, "src": "3861:19:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "66616c7365", "id": 42322, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3882:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42323, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3860:28:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42268, "id": 42324, "nodeType": "Return", "src": "3853:35:31"}]}}, "id": 42387, "nodeType": "IfStatement", "src": "3618:1187:31", "trueBody": {"id": 42316, "nodeType": "Block", "src": "3639:131:31", "statements": [{"expression": {"components": [{"expression": {"id": 42311, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "3730:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Panic_$42052_$", "typeString": "type(library Panic)"}}, "id": 42312, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3736:15:31", "memberName": "arithmeticPanic", "nodeType": "MemberAccess", "referencedDeclaration": 42030, "src": "3730:21:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "66616c7365", "id": 42313, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3753:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42314, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3729:30:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42268, "id": 42315, "nodeType": "Return", "src": "3722:37:31"}]}}, "id": 42388, "nodeType": "IfStatement", "src": "3463:1342:31", "trueBody": {"id": 42307, "nodeType": "Block", "src": "3483:129:31", "statements": [{"expression": {"components": [{"expression": {"id": 42302, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "3573:5:31", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Panic_$42052_$", "typeString": "type(library Panic)"}}, "id": 42303, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3579:14:31", "memberName": "assertionPanic", "nodeType": "MemberAccess", "referencedDeclaration": 42027, "src": "3573:20:31", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "66616c7365", "id": 42304, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3595:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42305, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3572:29:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_bool_$", "typeString": "tuple(string memory,bool)"}}, "functionReturnParameters": 42268, "id": 42306, "nodeType": "Return", "src": "3565:36:31"}]}}, {"expression": {"components": [{"hexValue": "556e646566696e65642070616e696320636f6465", "id": 42389, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4823:22:31", "typeDescriptions": {"typeIdentifier": "t_stringliteral_3f96279afdb4eb65aaefc80692281a5c0eb1733101cf379ad17b872d1349857f", "typeString": "literal_string \"Undefined panic code\""}, "value": "Undefined panic code"}, {"hexValue": "66616c7365", "id": 42390, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4847:5:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "id": 42391, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4822:31:31", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_stringliteral_3f96279afdb4eb65aaefc80692281a5c0eb1733101cf379ad17b872d1349857f_$_t_bool_$", "typeString": "tuple(literal_string \"Undefined panic code\",bool)"}}, "functionReturnParameters": 42268, "id": 42392, "nodeType": "Return", "src": "4815:38:31"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_getPanicCode", "nameLocation": "3137:13:31", "parameters": {"id": 42263, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42262, "mutability": "mutable", "name": "returnData", "nameLocation": "3164:10:31", "nodeType": "VariableDeclaration", "scope": 42394, "src": "3151:23:31", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 42261, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "3151:5:31", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "3150:25:31"}, "returnParameters": {"id": 42268, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42265, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 42394, "src": "3199:13:31", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42264, "name": "string", "nodeType": "ElementaryTypeName", "src": "3199:6:31", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 42267, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 42394, "src": "3214:4:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 42266, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3214:4:31", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3198:21:31"}, "scope": 42406, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 42405, "nodeType": "FunctionDefinition", "src": "4866:221:31", "nodes": [], "body": {"id": 42404, "nodeType": "Block", "src": "4963:124:31", "nodes": [], "statements": [{"AST": {"nativeSrc": "4982:69:31", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4982:69:31", "statements": [{"nativeSrc": "4996:45:31", "nodeType": "YulAssignment", "src": "4996:45:31", "value": {"arguments": [{"arguments": [{"name": "returnData", "nativeSrc": "5023:10:31", "nodeType": "YulIdentifier", "src": "5023:10:31"}, {"kind": "number", "nativeSrc": "5035:4:31", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5035:4:31", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "5019:3:31", "nodeType": "YulIdentifier", "src": "5019:3:31"}, "nativeSrc": "5019:21:31", "nodeType": "YulFunctionCall", "src": "5019:21:31"}], "functionName": {"name": "mload", "nativeSrc": "5013:5:31", "nodeType": "YulIdentifier", "src": "5013:5:31"}, "nativeSrc": "5013:28:31", "nodeType": "YulFunctionCall", "src": "5013:28:31"}, "variableNames": [{"name": "errorSelector", "nativeSrc": "4996:13:31", "nodeType": "YulIdentifier", "src": "4996:13:31"}]}]}, "evmVersion": "prague", "externalReferences": [{"declaration": 42399, "isOffset": false, "isSlot": false, "src": "4996:13:31", "valueSize": 1}, {"declaration": 42396, "isOffset": false, "isSlot": false, "src": "5023:10:31", "valueSize": 1}], "id": 42401, "nodeType": "InlineAssembly", "src": "4973:78:31"}, {"expression": {"id": 42402, "name": "errorSelector", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42399, "src": "5067:13:31", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "functionReturnParameters": 42400, "id": 42403, "nodeType": "Return", "src": "5060:20:31"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_getErrorSelector", "nameLocation": "4875:17:31", "parameters": {"id": 42397, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42396, "mutability": "mutable", "name": "returnData", "nameLocation": "4906:10:31", "nodeType": "VariableDeclaration", "scope": 42405, "src": "4893:23:31", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 42395, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4893:5:31", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "4892:25:31"}, "returnParameters": {"id": 42400, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42399, "mutability": "mutable", "name": "errorSelector", "nameLocation": "4948:13:31", "nodeType": "VariableDeclaration", "scope": 42405, "src": "4941:20:31", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}, "typeName": {"id": 42398, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "4941:6:31", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "visibility": "internal"}], "src": "4940:22:31"}, "scope": 42406, "stateMutability": "pure", "virtual": false, "visibility": "internal"}], "abstract": false, "baseContracts": [], "canonicalName": "Utils", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [42406], "name": "Utils", "nameLocation": "106:5:31", "scope": 42407, "usedErrors": [], "usedEvents": []}], "license": "GPL-2.0"}, "id": 31}