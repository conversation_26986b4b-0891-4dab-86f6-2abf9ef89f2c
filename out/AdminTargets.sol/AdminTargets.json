{"abi": [{"type": "error", "name": "ActorExists", "inputs": []}, {"type": "error", "name": "ActorNotAdded", "inputs": []}, {"type": "error", "name": "ActorNotSetup", "inputs": []}, {"type": "error", "name": "DefaultActor", "inputs": []}, {"type": "error", "name": "Exists", "inputs": []}, {"type": "error", "name": "NotAdded", "inputs": []}, {"type": "error", "name": "NotSetup", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ActorExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotSetup\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DefaultActor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Exists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotSetup\",\"type\":\"error\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/recon/targets/AdminTargets.sol\":\"AdminTargets\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/Asserts.sol\":{\"keccak256\":\"0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626\",\"dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK\"]},\"lib/chimera/src/BaseProperties.sol\":{\"keccak256\":\"0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848\",\"dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w\"]},\"lib/chimera/src/BaseSetup.sol\":{\"keccak256\":\"0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe\",\"dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1\"]},\"lib/chimera/src/BaseTargetFunctions.sol\":{\"keccak256\":\"0xe3b3de6200ab7039a14bb0a2a7e090402a36bd2c0c31c6d677d766b0f335bd60\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5326abd254a25c5bf8c2219e36641bc1288114271678dac8611d8365fc432851\",\"dweb:/ipfs/QmR4BF3JrTU3XhPVY4vPxGCQFXbUv94Bi48FjvgphroPxJ\"]},\"lib/chimera/src/Hevm.sol\":{\"keccak256\":\"0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b\",\"dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr\"]},\"lib/setup-helpers/src/ActorManager.sol\":{\"keccak256\":\"0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c\",\"dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB\"]},\"lib/setup-helpers/src/AssetManager.sol\":{\"keccak256\":\"0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa\",\"dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq\"]},\"lib/setup-helpers/src/EnumerableSet.sol\":{\"keccak256\":\"0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df\",\"dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW\"]},\"lib/setup-helpers/src/MockERC20.sol\":{\"keccak256\":\"0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578\",\"dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE\"]},\"lib/setup-helpers/src/Panic.sol\":{\"keccak256\":\"0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325\",\"dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd\"]},\"lib/setup-helpers/src/Utils.sol\":{\"keccak256\":\"0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523\",\"dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau\"]},\"src/Counter.sol\":{\"keccak256\":\"0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e\",\"dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2\"]},\"test/recon/BeforeAfter.sol\":{\"keccak256\":\"0x749640c8e4e366e58f610fd34fc71bfb6d0abdc4bb482366ca6a1fff07fcc36e\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://de1f26ec166caba53e9e04b74a5fcfe64cc39a1687a68d357d17b79d65a6bc75\",\"dweb:/ipfs/QmfFjMrfBQ6hGUyEyasvEBknA4SNdokrNCrrWHbPVKGz9k\"]},\"test/recon/Properties.sol\":{\"keccak256\":\"0x5dbc6eba0dc68541aee76cfb2c774eb53912665d44ba9b00e2b4c67ae4b600d9\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://f786cfc9b24abdc05071da9aad64929c9f7b5fd5c6ee317962ad587dff6a1777\",\"dweb:/ipfs/QmWw9B7jLeFgHQeJFcaSgb9SY7oNCLHG12f3owHV4dNDSM\"]},\"test/recon/Setup.sol\":{\"keccak256\":\"0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024\",\"dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk\"]},\"test/recon/targets/AdminTargets.sol\":{\"keccak256\":\"0x609af5d88accf96850234bd2366044b2ee1d16fa3cdc2a732eb5875ec42f1e91\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://2e5a3611cf25245e3eaeb7447811c276cd9015f4fe635f90d33a0819e737bac7\",\"dweb:/ipfs/QmXbuUSKiWwj2DqyQQ1wUtfnpKnvUV9eGH6Gimhn6vjoX3\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ActorExists"}, {"inputs": [], "type": "error", "name": "ActorNotAdded"}, {"inputs": [], "type": "error", "name": "ActorNotSetup"}, {"inputs": [], "type": "error", "name": "DefaultActor"}, {"inputs": [], "type": "error", "name": "Exists"}, {"inputs": [], "type": "error", "name": "NotAdded"}, {"inputs": [], "type": "error", "name": "NotSetup"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/recon/targets/AdminTargets.sol": "AdminTargets"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/Asserts.sol": {"keccak256": "0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9", "urls": ["bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626", "dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK"], "license": "MIT"}, "lib/chimera/src/BaseProperties.sol": {"keccak256": "0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a", "urls": ["bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848", "dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w"], "license": "MIT"}, "lib/chimera/src/BaseSetup.sol": {"keccak256": "0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107", "urls": ["bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe", "dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1"], "license": "MIT"}, "lib/chimera/src/BaseTargetFunctions.sol": {"keccak256": "0xe3b3de6200ab7039a14bb0a2a7e090402a36bd2c0c31c6d677d766b0f335bd60", "urls": ["bzz-raw://5326abd254a25c5bf8c2219e36641bc1288114271678dac8611d8365fc432851", "dweb:/ipfs/QmR4BF3JrTU3XhPVY4vPxGCQFXbUv94Bi48FjvgphroPxJ"], "license": "MIT"}, "lib/chimera/src/Hevm.sol": {"keccak256": "0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe", "urls": ["bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b", "dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr"], "license": "MIT"}, "lib/setup-helpers/src/ActorManager.sol": {"keccak256": "0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06", "urls": ["bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c", "dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB"], "license": "GPL-2.0"}, "lib/setup-helpers/src/AssetManager.sol": {"keccak256": "0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc", "urls": ["bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa", "dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq"], "license": "GPL-2.0"}, "lib/setup-helpers/src/EnumerableSet.sol": {"keccak256": "0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514", "urls": ["bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df", "dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW"], "license": "MIT"}, "lib/setup-helpers/src/MockERC20.sol": {"keccak256": "0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f", "urls": ["bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578", "dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE"], "license": "AGPL-3.0-only"}, "lib/setup-helpers/src/Panic.sol": {"keccak256": "0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92", "urls": ["bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325", "dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd"], "license": "GPL-2.0"}, "lib/setup-helpers/src/Utils.sol": {"keccak256": "0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27", "urls": ["bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523", "dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau"], "license": "GPL-2.0"}, "src/Counter.sol": {"keccak256": "0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c", "urls": ["bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e", "dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2"], "license": "UNLICENSED"}, "test/recon/BeforeAfter.sol": {"keccak256": "0x749640c8e4e366e58f610fd34fc71bfb6d0abdc4bb482366ca6a1fff07fcc36e", "urls": ["bzz-raw://de1f26ec166caba53e9e04b74a5fcfe64cc39a1687a68d357d17b79d65a6bc75", "dweb:/ipfs/QmfFjMrfBQ6hGUyEyasvEBknA4SNdokrNCrrWHbPVKGz9k"], "license": "GPL-2.0"}, "test/recon/Properties.sol": {"keccak256": "0x5dbc6eba0dc68541aee76cfb2c774eb53912665d44ba9b00e2b4c67ae4b600d9", "urls": ["bzz-raw://f786cfc9b24abdc05071da9aad64929c9f7b5fd5c6ee317962ad587dff6a1777", "dweb:/ipfs/QmWw9B7jLeFgHQeJFcaSgb9SY7oNCLHG12f3owHV4dNDSM"], "license": "GPL-2.0"}, "test/recon/Setup.sol": {"keccak256": "0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5", "urls": ["bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024", "dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk"], "license": "GPL-2.0"}, "test/recon/targets/AdminTargets.sol": {"keccak256": "0x609af5d88accf96850234bd2366044b2ee1d16fa3cdc2a732eb5875ec42f1e91", "urls": ["bzz-raw://2e5a3611cf25245e3eaeb7447811c276cd9015f4fe635f90d33a0819e737bac7", "dweb:/ipfs/QmXbuUSKiWwj2DqyQQ1wUtfnpKnvUV9eGH6Gimhn6vjoX3"], "license": "GPL-2.0"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 40405, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "_actor", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 40409, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "_actors", "offset": 0, "slot": "1", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 40565, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "__asset", "offset": 0, "slot": "3", "type": "t_address"}, {"astId": 40569, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "_assets", "offset": 0, "slot": "4", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 42682, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "counter", "offset": 0, "slot": "6", "type": "t_contract(Counter)42568"}, {"astId": 42580, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "_before", "offset": 0, "slot": "7", "type": "t_struct(Vars)42577_storage"}, {"astId": 42583, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "_after", "offset": 0, "slot": "8", "type": "t_struct(Vars)42577_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_contract(Counter)42568": {"encoding": "inplace", "label": "contract Counter", "numberOfBytes": "20"}, "t_mapping(t_bytes32,t_uint256)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(AddressSet)41192_storage": {"encoding": "inplace", "label": "struct EnumerableSet.AddressSet", "numberOfBytes": "64", "members": [{"astId": 41191, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "_inner", "offset": 0, "slot": "0", "type": "t_struct(Set)40877_storage"}]}, "t_struct(Set)40877_storage": {"encoding": "inplace", "label": "struct EnumerableSet.Set", "numberOfBytes": "64", "members": [{"astId": 40872, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "_values", "offset": 0, "slot": "0", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 40876, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "_indexes", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_uint256)"}]}, "t_struct(Vars)42577_storage": {"encoding": "inplace", "label": "struct BeforeAfter.Vars", "numberOfBytes": "32", "members": [{"astId": 42576, "contract": "test/recon/targets/AdminTargets.sol:AdminTargets", "label": "__ignore__", "offset": 0, "slot": "0", "type": "t_uint256"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "test/recon/targets/AdminTargets.sol", "id": 42765, "exportedSymbols": {"AdminTargets": [42764], "BaseTargetFunctions": [105], "BeforeAfter": [42602], "Panic": [42052], "Properties": [42658], "vm": [719]}, "nodeType": "SourceUnit", "src": "36:537:39", "nodes": [{"id": 42749, "nodeType": "PragmaDirective", "src": "36:23:39", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 42751, "nodeType": "ImportDirective", "src": "61:69:39", "nodes": [], "absolutePath": "lib/chimera/src/BaseTargetFunctions.sol", "file": "@chimera/BaseTargetFunctions.sol", "nameLocation": "-1:-1:-1", "scope": 42765, "sourceUnit": 106, "symbolAliases": [{"foreign": {"id": 42750, "name": "BaseTargetFunctions", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "69:19:39", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42753, "nodeType": "ImportDirective", "src": "131:47:39", "nodes": [], "absolutePath": "test/recon/BeforeAfter.sol", "file": "../BeforeAfter.sol", "nameLocation": "-1:-1:-1", "scope": 42765, "sourceUnit": 42603, "symbolAliases": [{"foreign": {"id": 42752, "name": "BeforeAfter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42602, "src": "139:11:39", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42755, "nodeType": "ImportDirective", "src": "179:45:39", "nodes": [], "absolutePath": "test/recon/Properties.sol", "file": "../Properties.sol", "nameLocation": "-1:-1:-1", "scope": 42765, "sourceUnit": 42659, "symbolAliases": [{"foreign": {"id": 42754, "name": "Properties", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42658, "src": "187:10:39", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42757, "nodeType": "ImportDirective", "src": "241:37:39", "nodes": [], "absolutePath": "lib/chimera/src/Hevm.sol", "file": "@chimera/Hevm.sol", "nameLocation": "-1:-1:-1", "scope": 42765, "sourceUnit": 720, "symbolAliases": [{"foreign": {"id": 42756, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "249:2:39", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42759, "nodeType": "ImportDirective", "src": "291:39:39", "nodes": [], "absolutePath": "lib/setup-helpers/src/Panic.sol", "file": "@recon/Panic.sol", "nameLocation": "-1:-1:-1", "scope": 42765, "sourceUnit": 42053, "symbolAliases": [{"foreign": {"id": 42758, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "299:5:39", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42764, "nodeType": "ContractDefinition", "src": "332:241:39", "nodes": [], "abstract": true, "baseContracts": [{"baseName": {"id": 42760, "name": "BaseTargetFunctions", "nameLocations": ["370:19:39"], "nodeType": "IdentifierPath", "referencedDeclaration": 105, "src": "370:19:39"}, "id": 42761, "nodeType": "InheritanceSpecifier", "src": "370:19:39"}, {"baseName": {"id": 42762, "name": "Properties", "nameLocations": ["395:10:39"], "nodeType": "IdentifierPath", "referencedDeclaration": 42658, "src": "395:10:39"}, "id": 42763, "nodeType": "InheritanceSpecifier", "src": "395:10:39"}], "canonicalName": "AdminTargets", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": false, "linearizedBaseContracts": [42764, 42658, 105, 81, 42602, 42724, 42406, 40866, 40547, 88, 94], "name": "AdminTargets", "nameLocation": "350:12:39", "scope": 42765, "usedErrors": [40411, 40413, 40415, 40417, 40571, 40573, 40575], "usedEvents": []}], "license": "GPL-2.0"}, "id": 39}