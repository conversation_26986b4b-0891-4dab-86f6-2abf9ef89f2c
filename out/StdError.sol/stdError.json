{"abi": [{"type": "function", "name": "arithmeticError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "assertionError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "divisionError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "encodeStorageError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "enumConversionError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "indexOOBError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "memOverflowError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "popError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "zeroVarError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}], "bytecode": {"object": "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", "sourceMap": "162:850:6:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "162:850:6:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;740:85;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;185:86;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;461:91;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;277:87;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;831:88;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;654:80;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;925:84;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;558:90;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;370:85;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;740;820:4;778:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;740:85;:::o;185:86::-;266:4;224:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;185:86;:::o;461:91::-;547:4;505:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;461:91;:::o;277:87::-;359:4;317:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;277:87;:::o;831:88::-;914:4;872:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;831:88;:::o;654:80::-;729:4;687:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;654:80;:::o;925:84::-;1004:4;962:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;925:84;:::o;558:90::-;643:4;601:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;558:90;:::o;370:85::-;450:4;408:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;370:85;:::o;7:98:24:-;58:6;92:5;86:12;76:22;;7:98;;;:::o;111:176::-;202:11;236:6;231:3;224:19;276:4;271:3;267:14;252:29;;111:176;;;;:::o;293:139::-;382:6;377:3;372;366:23;423:1;414:6;409:3;405:16;398:27;293:139;;;:::o;438:102::-;479:6;530:2;526:7;521:2;514:5;510:14;506:28;496:38;;438:102;;;:::o;546:389::-;640:3;668:38;700:5;668:38;:::i;:::-;722:78;793:6;788:3;722:78;:::i;:::-;715:85;;809:65;867:6;862:3;855:4;848:5;844:16;809:65;:::i;:::-;899:29;921:6;899:29;:::i;:::-;894:3;890:39;883:46;;644:291;546:389;;;;:::o;941:325::-;1060:4;1098:2;1087:9;1083:18;1075:26;;1147:9;1141:4;1137:20;1133:1;1122:9;1118:17;1111:47;1175:84;1254:4;1245:6;1175:84;:::i;:::-;1167:92;;941:325;;;;:::o;1272:86::-;1318:7;1347:5;1336:16;;1272:86;;;:::o;1364:::-;1399:7;1439:4;1432:5;1428:16;1417:27;;1364:86;;;:::o;1456:60::-;1484:3;1505:5;1498:12;;1456:60;;;:::o;1522:156::-;1579:9;1612:60;1628:43;1637:33;1664:5;1637:33;:::i;:::-;1628:43;:::i;:::-;1612:60;:::i;:::-;1599:73;;1522:156;;;:::o;1684:145::-;1778:44;1816:5;1778:44;:::i;:::-;1773:3;1766:57;1684:145;;:::o;1835:236::-;1935:4;1973:2;1962:9;1958:18;1950:26;;1986:78;2061:1;2050:9;2046:17;2037:6;1986:78;:::i;:::-;1835:236;;;;:::o;2077:85::-;2122:7;2151:5;2140:16;;2077:85;;;:::o;2168:154::-;2224:9;2257:59;2273:42;2282:32;2308:5;2282:32;:::i;:::-;2273:42;:::i;:::-;2257:59;:::i;:::-;2244:72;;2168:154;;;:::o;2328:143::-;2421:43;2458:5;2421:43;:::i;:::-;2416:3;2409:56;2328:143;;:::o;2477:234::-;2576:4;2614:2;2603:9;2599:18;2591:26;;2627:77;2701:1;2690:9;2686:17;2677:6;2627:77;:::i;:::-;2477:234;;;;:::o;2717:86::-;2763:7;2792:5;2781:16;;2717:86;;;:::o;2809:156::-;2866:9;2899:60;2915:43;2924:33;2951:5;2924:33;:::i;:::-;2915:43;:::i;:::-;2899:60;:::i;:::-;2886:73;;2809:156;;;:::o;2971:145::-;3065:44;3103:5;3065:44;:::i;:::-;3060:3;3053:57;2971:145;;:::o;3122:236::-;3222:4;3260:2;3249:9;3245:18;3237:26;;3273:78;3348:1;3337:9;3333:17;3324:6;3273:78;:::i;:::-;3122:236;;;;:::o;3364:86::-;3410:7;3439:5;3428:16;;3364:86;;;:::o;3456:156::-;3513:9;3546:60;3562:43;3571:33;3598:5;3571:33;:::i;:::-;3562:43;:::i;:::-;3546:60;:::i;:::-;3533:73;;3456:156;;;:::o;3618:145::-;3712:44;3750:5;3712:44;:::i;:::-;3707:3;3700:57;3618:145;;:::o;3769:236::-;3869:4;3907:2;3896:9;3892:18;3884:26;;3920:78;3995:1;3984:9;3980:17;3971:6;3920:78;:::i;:::-;3769:236;;;;:::o;4011:86::-;4057:7;4086:5;4075:16;;4011:86;;;:::o;4103:156::-;4160:9;4193:60;4209:43;4218:33;4245:5;4218:33;:::i;:::-;4209:43;:::i;:::-;4193:60;:::i;:::-;4180:73;;4103:156;;;:::o;4265:145::-;4359:44;4397:5;4359:44;:::i;:::-;4354:3;4347:57;4265:145;;:::o;4416:236::-;4516:4;4554:2;4543:9;4539:18;4531:26;;4567:78;4642:1;4631:9;4627:17;4618:6;4567:78;:::i;:::-;4416:236;;;;:::o;4658:86::-;4704:7;4733:5;4722:16;;4658:86;;;:::o;4750:156::-;4807:9;4840:60;4856:43;4865:33;4892:5;4865:33;:::i;:::-;4856:43;:::i;:::-;4840:60;:::i;:::-;4827:73;;4750:156;;;:::o;4912:145::-;5006:44;5044:5;5006:44;:::i;:::-;5001:3;4994:57;4912:145;;:::o;5063:236::-;5163:4;5201:2;5190:9;5186:18;5178:26;;5214:78;5289:1;5278:9;5274:17;5265:6;5214:78;:::i;:::-;5063:236;;;;:::o;5305:86::-;5351:7;5380:5;5369:16;;5305:86;;;:::o;5397:156::-;5454:9;5487:60;5503:43;5512:33;5539:5;5512:33;:::i;:::-;5503:43;:::i;:::-;5487:60;:::i;:::-;5474:73;;5397:156;;;:::o;5559:145::-;5653:44;5691:5;5653:44;:::i;:::-;5648:3;5641:57;5559:145;;:::o;5710:236::-;5810:4;5848:2;5837:9;5833:18;5825:26;;5861:78;5936:1;5925:9;5921:17;5912:6;5861:78;:::i;:::-;5710:236;;;;:::o;5952:86::-;5998:7;6027:5;6016:16;;5952:86;;;:::o;6044:156::-;6101:9;6134:60;6150:43;6159:33;6186:5;6159:33;:::i;:::-;6150:43;:::i;:::-;6134:60;:::i;:::-;6121:73;;6044:156;;;:::o;6206:145::-;6300:44;6338:5;6300:44;:::i;:::-;6295:3;6288:57;6206:145;;:::o;6357:236::-;6457:4;6495:2;6484:9;6480:18;6472:26;;6508:78;6583:1;6572:9;6568:17;6559:6;6508:78;:::i;:::-;6357:236;;;;:::o;6599:86::-;6645:7;6674:5;6663:16;;6599:86;;;:::o;6691:156::-;6748:9;6781:60;6797:43;6806:33;6833:5;6806:33;:::i;:::-;6797:43;:::i;:::-;6781:60;:::i;:::-;6768:73;;6691:156;;;:::o;6853:145::-;6947:44;6985:5;6947:44;:::i;:::-;6942:3;6935:57;6853:145;;:::o;7004:236::-;7104:4;7142:2;7131:9;7127:18;7119:26;;7155:78;7230:1;7219:9;7215:17;7206:6;7155:78;:::i;:::-;7004:236;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"arithmeticError()": "8995290f", "assertionError()": "10332977", "divisionError()": "fa784a44", "encodeStorageError()": "d160e4de", "enumConversionError()": "1de45560", "indexOOBError()": "05ee8612", "memOverflowError()": "986c5f68", "popError()": "b22dc54d", "zeroVarError()": "b67689da"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"arithmeticError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"assertionError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"divisionError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"encodeStorageError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"enumConversionError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"indexOOBError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"memOverflowError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"popError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"zeroVarError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdError.sol\":\"stdError\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "arithmeticError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "assertionError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "divisionError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "encodeStorageError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "enumConversionError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "indexOOBError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "memOverflowError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "popError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "zeroVarError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdError.sol": "stdError"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/forge-std/src/StdError.sol", "id": 6914, "exportedSymbols": {"stdError": [6913]}, "nodeType": "SourceUnit", "src": "129:884:6", "nodes": [{"id": 6849, "nodeType": "PragmaDirective", "src": "129:31:6", "nodes": [], "literals": ["solidity", ">=", "0.6", ".2", "<", "0.9", ".0"]}, {"id": 6913, "nodeType": "ContractDefinition", "src": "162:850:6", "nodes": [{"id": 6856, "nodeType": "VariableDeclaration", "src": "185:86:6", "nodes": [], "constant": true, "functionSelector": "10332977", "mutability": "constant", "name": "assertionError", "nameLocation": "207:14:6", "scope": 6913, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 6850, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "185:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "value": {"arguments": [{"hexValue": "50616e69632875696e7432353629", "id": 6853, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "248:16:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, "value": "Panic(uint256)"}, {"hexValue": "30783031", "id": 6854, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "266:4:6", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "0x01"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}], "expression": {"id": 6851, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "224:3:6", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 6852, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "228:19:6", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "224:23:6", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 6855, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "224:47:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "visibility": "public"}, {"id": 6863, "nodeType": "VariableDeclaration", "src": "277:87:6", "nodes": [], "constant": true, "functionSelector": "8995290f", "mutability": "constant", "name": "arithmeticError", "nameLocation": "299:15:6", "scope": 6913, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 6857, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "277:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "value": {"arguments": [{"hexValue": "50616e69632875696e7432353629", "id": 6860, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "341:16:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, "value": "Panic(uint256)"}, {"hexValue": "30783131", "id": 6861, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "359:4:6", "typeDescriptions": {"typeIdentifier": "t_rational_17_by_1", "typeString": "int_const 17"}, "value": "0x11"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, {"typeIdentifier": "t_rational_17_by_1", "typeString": "int_const 17"}], "expression": {"id": 6858, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "317:3:6", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 6859, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "321:19:6", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "317:23:6", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 6862, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "317:47:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "visibility": "public"}, {"id": 6870, "nodeType": "VariableDeclaration", "src": "370:85:6", "nodes": [], "constant": true, "functionSelector": "fa784a44", "mutability": "constant", "name": "divisionError", "nameLocation": "392:13:6", "scope": 6913, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 6864, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "370:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "value": {"arguments": [{"hexValue": "50616e69632875696e7432353629", "id": 6867, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "432:16:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, "value": "Panic(uint256)"}, {"hexValue": "30783132", "id": 6868, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "450:4:6", "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "0x12"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}], "expression": {"id": 6865, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "408:3:6", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 6866, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "412:19:6", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "408:23:6", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 6869, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "408:47:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "visibility": "public"}, {"id": 6877, "nodeType": "VariableDeclaration", "src": "461:91:6", "nodes": [], "constant": true, "functionSelector": "1de45560", "mutability": "constant", "name": "enumConversionError", "nameLocation": "483:19:6", "scope": 6913, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 6871, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "461:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "value": {"arguments": [{"hexValue": "50616e69632875696e7432353629", "id": 6874, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "529:16:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, "value": "Panic(uint256)"}, {"hexValue": "30783231", "id": 6875, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "547:4:6", "typeDescriptions": {"typeIdentifier": "t_rational_33_by_1", "typeString": "int_const 33"}, "value": "0x21"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, {"typeIdentifier": "t_rational_33_by_1", "typeString": "int_const 33"}], "expression": {"id": 6872, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "505:3:6", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 6873, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "509:19:6", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "505:23:6", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 6876, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "505:47:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "visibility": "public"}, {"id": 6884, "nodeType": "VariableDeclaration", "src": "558:90:6", "nodes": [], "constant": true, "functionSelector": "d160e4de", "mutability": "constant", "name": "encodeStorageError", "nameLocation": "580:18:6", "scope": 6913, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 6878, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "558:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "value": {"arguments": [{"hexValue": "50616e69632875696e7432353629", "id": 6881, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "625:16:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, "value": "Panic(uint256)"}, {"hexValue": "30783232", "id": 6882, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "643:4:6", "typeDescriptions": {"typeIdentifier": "t_rational_34_by_1", "typeString": "int_const 34"}, "value": "0x22"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, {"typeIdentifier": "t_rational_34_by_1", "typeString": "int_const 34"}], "expression": {"id": 6879, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "601:3:6", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 6880, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "605:19:6", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "601:23:6", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 6883, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "601:47:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "visibility": "public"}, {"id": 6891, "nodeType": "VariableDeclaration", "src": "654:80:6", "nodes": [], "constant": true, "functionSelector": "b22dc54d", "mutability": "constant", "name": "popError", "nameLocation": "676:8:6", "scope": 6913, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 6885, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "654:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "value": {"arguments": [{"hexValue": "50616e69632875696e7432353629", "id": 6888, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "711:16:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, "value": "Panic(uint256)"}, {"hexValue": "30783331", "id": 6889, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "729:4:6", "typeDescriptions": {"typeIdentifier": "t_rational_49_by_1", "typeString": "int_const 49"}, "value": "0x31"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, {"typeIdentifier": "t_rational_49_by_1", "typeString": "int_const 49"}], "expression": {"id": 6886, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "687:3:6", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 6887, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "691:19:6", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "687:23:6", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 6890, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "687:47:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "visibility": "public"}, {"id": 6898, "nodeType": "VariableDeclaration", "src": "740:85:6", "nodes": [], "constant": true, "functionSelector": "05ee8612", "mutability": "constant", "name": "indexOOBError", "nameLocation": "762:13:6", "scope": 6913, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 6892, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "740:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "value": {"arguments": [{"hexValue": "50616e69632875696e7432353629", "id": 6895, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "802:16:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, "value": "Panic(uint256)"}, {"hexValue": "30783332", "id": 6896, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "820:4:6", "typeDescriptions": {"typeIdentifier": "t_rational_50_by_1", "typeString": "int_const 50"}, "value": "0x32"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, {"typeIdentifier": "t_rational_50_by_1", "typeString": "int_const 50"}], "expression": {"id": 6893, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "778:3:6", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 6894, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "782:19:6", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "778:23:6", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 6897, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "778:47:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "visibility": "public"}, {"id": 6905, "nodeType": "VariableDeclaration", "src": "831:88:6", "nodes": [], "constant": true, "functionSelector": "986c5f68", "mutability": "constant", "name": "memOverflowError", "nameLocation": "853:16:6", "scope": 6913, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 6899, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "831:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "value": {"arguments": [{"hexValue": "50616e69632875696e7432353629", "id": 6902, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "896:16:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, "value": "Panic(uint256)"}, {"hexValue": "30783431", "id": 6903, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "914:4:6", "typeDescriptions": {"typeIdentifier": "t_rational_65_by_1", "typeString": "int_const 65"}, "value": "0x41"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, {"typeIdentifier": "t_rational_65_by_1", "typeString": "int_const 65"}], "expression": {"id": 6900, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "872:3:6", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 6901, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "876:19:6", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "872:23:6", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 6904, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "872:47:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "visibility": "public"}, {"id": 6912, "nodeType": "VariableDeclaration", "src": "925:84:6", "nodes": [], "constant": true, "functionSelector": "b67689da", "mutability": "constant", "name": "zeroVarError", "nameLocation": "947:12:6", "scope": 6913, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 6906, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "925:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "value": {"arguments": [{"hexValue": "50616e69632875696e7432353629", "id": 6909, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "986:16:6", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, "value": "Panic(uint256)"}, {"hexValue": "30783531", "id": 6910, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1004:4:6", "typeDescriptions": {"typeIdentifier": "t_rational_81_by_1", "typeString": "int_const 81"}, "value": "0x51"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4e487b71539e0164c9d29506cc725e49342bcac15e0927282bf30fedfe1c7268", "typeString": "literal_string \"Panic(uint256)\""}, {"typeIdentifier": "t_rational_81_by_1", "typeString": "int_const 81"}], "expression": {"id": 6907, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "962:3:6", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 6908, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "966:19:6", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "962:23:6", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 6911, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "962:47:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "visibility": "public"}], "abstract": false, "baseContracts": [], "canonicalName": "stdError", "contractDependencies": [], "contractKind": "library", "fullyImplemented": true, "linearizedBaseContracts": [6913], "name": "stdError", "nameLocation": "170:8:6", "scope": 6914, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 6}