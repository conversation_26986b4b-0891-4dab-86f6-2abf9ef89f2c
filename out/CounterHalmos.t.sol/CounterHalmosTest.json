{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "check_add_operation_valid_cases", "inputs": [{"name": "initial", "type": "uint256", "internalType": "uint256"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "check_decrement_valid_cases", "inputs": [{"name": "initial", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "check_increment_valid_cases", "inputs": [{"name": "initial", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "check_invariant_never_exceeds_max", "inputs": [{"name": "initial", "type": "uint256", "internalType": "uint256"}, {"name": "operation", "type": "uint8", "internalType": "uint8"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "check_multiple_increments", "inputs": [{"name": "initial", "type": "uint256", "internalType": "uint256"}, {"name": "times", "type": "uint8", "internalType": "uint8"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "check_reset_always_works", "inputs": [{"name": "initial", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "check_sequence_operations_safety", "inputs": [{"name": "initial", "type": "uint256", "internalType": "uint256"}, {"name": "addValue", "type": "uint256", "internalType": "uint256"}, {"name": "subValue", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "check_set_number_valid_cases", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "check_subtract_operation_valid_cases", "inputs": [{"name": "initial", "type": "uint256", "internalType": "uint256"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract Counter"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "151:5492:20:-:0;;;3166:4:2;3126:44;;;;;;;;;;;;;;;;;;;;1087:4:13;1065:26;;;;;;;;;;;;;;;;;;;;151:5492:20;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "151:5492:20:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2312:238;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;4217:886;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;221:64;;;:::i;:::-;;2615:217;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2907:134:6;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3823:151;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3684:133;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3385:141;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;192:22:20;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3193:186:6;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3047:140;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3532:146;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;5169:472:20;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2754:147:6;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2459:141;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1306:195:1;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1864:379:20;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;960:310;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2906:1238;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2606:142:6;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;558:334:20;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;1342:445;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;1065:26:13;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2312:238:20;336:42:0;2426:9:20;;;2445:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2436:5;:28;;2426:39;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2476:7;;;;;;;;;;;:17;;;2494:5;2476:24;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2537:5;2517:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:25;2510:33;;;;:::i;:::-;;2312:238;:::o;4217:886::-;336:42:0;4329:9:20;;;4350:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4339:7;:30;;4329:41;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;4380:9:20;;;4401:1;4390:8;:12;:31;;;;;4418:3;4406:8;:15;;4390:31;4380:42;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336::0;4432:9:20;;;4453:1;4442:8;:12;:31;;;;;4470:3;4458:8;:15;;4442:31;4432:42;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336::0;4484:9:20;;;4516:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4504:8;4494:7;:18;;;;:::i;:::-;:41;;4484:52;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;4546:9:20;;;4578:8;4566;4556:7;:18;;;;:::i;:::-;:30;;4546:41;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4598:7;;;;;;;;;;;:17;;;4616:7;4598:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4682:7;;;;;;;;;;;:11;;;4694:8;4682:21;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4713:16;4732:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4713:35;;4787:8;4777:7;:18;;;;:::i;:::-;4765:8;:30;4758:38;;;;:::i;:::-;;4807:7;;;;;;;;;;;:16;;;4824:8;4807:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4843:21;4867:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4843:40;;4938:8;4927;4917:7;:18;;;;:::i;:::-;:29;;;;:::i;:::-;4900:13;:46;4893:54;;;;:::i;:::-;;5022:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5010:8;:31;;5003:39;;;;:::i;:::-;;5076:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5059:13;:36;;5052:44;;;;:::i;:::-;;4319:784;;4217:886;;;:::o;221:64::-;265:13;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;255:7;;:23;;;;;;;;;;;;;;;;;;221:64::o;2615:217::-;336:42:0;2683:9:20;;;2704:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2693:7;:30;;2683:41;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2735:7;;;;;;;;;;;:17;;;2753:7;2735:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2771:7;;;;;;;;;;;:13;;;:15;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2823:1;2803:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:21;2796:29;;;;:::i;:::-;;2615:217;:::o;2907:134:6:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;3823:151::-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;3684:133::-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;192:22:20:-;;;;;;;;;;;;;:::o;3193:186:6:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3193:186;:::o;3047:140::-;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3047:140;:::o;3532:146::-;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3532:146;:::o;5169:472:20:-;336:42:0;5251:9:20;;;5272:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5261:7;:30;;5251:41;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;5302:9:20;;;5320:1;5312:5;:9;;;:24;;;;;5334:2;5325:5;:11;;;;5312:24;5302:35;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;5347:9:20;;;5376:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5367:5;5357:15;;:7;:15;;;;:::i;:::-;:38;;5347:49;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5407:7;;;;;;;;;;;:17;;;5425:7;5407:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5449:9;5461:1;5449:13;;5444:80;5468:5;5464:9;;:1;:9;5444:80;;;5494:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5475:3;;;;;;;5444:80;;;;5571:5;5561:15;;:7;:15;;;;:::i;:::-;5541:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:35;5534:43;;;;:::i;:::-;;5614:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5594:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:39;;5587:47;;;;:::i;:::-;;5169:472;;:::o;2754:147:6:-;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2754:147;:::o;2459:141::-;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2459:141;:::o;1306:195:1:-;1345:4;1365:7;;;;;;;;;;;1361:134;;;1395:4;1388:11;;;;1361:134;1482:1;1474:10;;219:28;211:37;;1437:7;;;219:28;211:37;;1255:17;1437:33;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:47;;1430:54;;1306:195;;:::o;1864:379:20:-;336:42:0;2004:9:20;;;2025:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2014:7;:30;;2004:41;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;2055:9:20;;;2073:1;2065:5;:9;2055:20;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;2085:9:20;;;2106:5;2095:7;:16;;2085:27;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2123:7;;;;;;;;;;;:17;;;2141:7;2123:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2159:7;;;;;;;;;;;:16;;;2176:5;2159:23;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2230:5;2220:7;:15;;;;:::i;:::-;2200:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:35;2193:43;;;;:::i;:::-;;1864:379;;:::o;960:310::-;336:42:0;1074:9:20;;;1094:1;1084:7;:11;1074:22;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;1106:9:20;;;1127:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1116:7;:30;;1106:41;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1158:7;;;;;;;;;;;:17;;;1176:7;1158:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1194:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1261:1;1251:7;:11;;;;:::i;:::-;1231:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:31;1224:39;;;;:::i;:::-;;960:310;:::o;2906:1238::-;336:42:0;3015:9:20;;;3036:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3025:7;:30;;3015:41;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;3066:9:20;;;3085:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3076:5;:28;;3066:39;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;3115:9:20;;;3133:1;3125:5;:9;3115:20;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3146:7;;;;;;;;;;;:17;;;3164:7;3146:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3248:1;3235:9;:14;;;:47;;;;;3263:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3253:7;:29;3235:47;3231:789;;;3338:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3231:789;;;3391:1;3378:9;:14;;;:29;;;;;3406:1;3396:7;:11;3378:29;3374:646;;;3463:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3374:646;;;3516:1;3503:9;:14;;;:56;;;;;3540:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3531:5;3521:7;:15;;;;:::i;:::-;:38;;3503:56;3499:521;;;3609:7;;;;;;;;;;;:11;;;3621:5;3609:18;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3499:521;;;3661:1;3648:9;:14;;;:34;;;;;3677:5;3666:7;:16;;3648:34;3644:376;;;3737:7;;;;;;;;;;;:16;;;3754:5;3737:23;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3644:376;;;3794:1;3781:9;:14;;;3777:243;;3846:7;;;;;;;;;;;:13;;;:15;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3777:243;;;3895:1;3882:9;:14;;;:46;;;;;3909:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3900:5;:28;;3882:46;3878:142;;;3985:7;;;;;;;;;;;:17;;;4003:5;3985:24;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3878:142;3777:243;3644:376;3499:521;3374:646;3231:789;4117:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4097:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:39;;4090:47;;;;:::i;:::-;;2906:1238;;;:::o;2606:142:6:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;558:334:20:-;336:42:0;672:9:20;;;692:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;682:7;:29;672:40;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;723:7;;;;;;;;;;;:17;;;741:7;723:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;759:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;826:1;816:7;:11;;;;:::i;:::-;796:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:31;789:39;;;;:::i;:::-;;865:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;845:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:39;;838:47;;;;:::i;:::-;;558:334;:::o;1342:445::-;336:42:0;1474:9:20;;;1495:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1484:7;:30;;1474:41;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;1525:9:20;;;1543:1;1535:5;:9;1525:20;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;1555:9:20;;;1584:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1575:5;1565:7;:15;;;;:::i;:::-;:38;;1555:49;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1615:7;;;;;;;;;;;:17;;;1633:7;1615:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1651:7;;;;;;;;;;;:11;;;1663:5;1651:18;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1717:5;1707:7;:15;;;;:::i;:::-;1687:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:35;1680:43;;;;:::i;:::-;;1760:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1740:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:39;;1733:47;;;;:::i;:::-;;1342:445;;:::o;1065:26:13:-;;;;;;;;;;;;;:::o;-1:-1:-1:-;;;;;;;;:::o;88:117:21:-;197:1;194;187:12;334:77;371:7;400:5;389:16;;334:77;;;:::o;417:122::-;490:24;508:5;490:24;:::i;:::-;483:5;480:35;470:63;;529:1;526;519:12;470:63;417:122;:::o;545:139::-;591:5;629:6;616:20;607:29;;645:33;672:5;645:33;:::i;:::-;545:139;;;;:::o;690:329::-;749:6;798:2;786:9;777:7;773:23;769:32;766:119;;;804:79;;:::i;:::-;766:119;924:1;949:53;994:7;985:6;974:9;970:22;949:53;:::i;:::-;939:63;;895:117;690:329;;;;:::o;1025:619::-;1102:6;1110;1118;1167:2;1155:9;1146:7;1142:23;1138:32;1135:119;;;1173:79;;:::i;:::-;1135:119;1293:1;1318:53;1363:7;1354:6;1343:9;1339:22;1318:53;:::i;:::-;1308:63;;1264:117;1420:2;1446:53;1491:7;1482:6;1471:9;1467:22;1446:53;:::i;:::-;1436:63;;1391:118;1548:2;1574:53;1619:7;1610:6;1599:9;1595:22;1574:53;:::i;:::-;1564:63;;1519:118;1025:619;;;;;:::o;1650:114::-;1717:6;1751:5;1745:12;1735:22;;1650:114;;;:::o;1770:184::-;1869:11;1903:6;1898:3;1891:19;1943:4;1938:3;1934:14;1919:29;;1770:184;;;;:::o;1960:132::-;2027:4;2050:3;2042:11;;2080:4;2075:3;2071:14;2063:22;;1960:132;;;:::o;2098:126::-;2135:7;2175:42;2168:5;2164:54;2153:65;;2098:126;;;:::o;2230:96::-;2267:7;2296:24;2314:5;2296:24;:::i;:::-;2285:35;;2230:96;;;:::o;2332:108::-;2409:24;2427:5;2409:24;:::i;:::-;2404:3;2397:37;2332:108;;:::o;2446:179::-;2515:10;2536:46;2578:3;2570:6;2536:46;:::i;:::-;2614:4;2609:3;2605:14;2591:28;;2446:179;;;;:::o;2631:113::-;2701:4;2733;2728:3;2724:14;2716:22;;2631:113;;;:::o;2780:732::-;2899:3;2928:54;2976:5;2928:54;:::i;:::-;2998:86;3077:6;3072:3;2998:86;:::i;:::-;2991:93;;3108:56;3158:5;3108:56;:::i;:::-;3187:7;3218:1;3203:284;3228:6;3225:1;3222:13;3203:284;;;3304:6;3298:13;3331:63;3390:3;3375:13;3331:63;:::i;:::-;3324:70;;3417:60;3470:6;3417:60;:::i;:::-;3407:70;;3263:224;3250:1;3247;3243:9;3238:14;;3203:284;;;3207:14;3503:3;3496:10;;2904:608;;;2780:732;;;;:::o;3518:373::-;3661:4;3699:2;3688:9;3684:18;3676:26;;3748:9;3742:4;3738:20;3734:1;3723:9;3719:17;3712:47;3776:108;3879:4;3870:6;3776:108;:::i;:::-;3768:116;;3518:373;;;;:::o;3897:145::-;3995:6;4029:5;4023:12;4013:22;;3897:145;;;:::o;4048:215::-;4178:11;4212:6;4207:3;4200:19;4252:4;4247:3;4243:14;4228:29;;4048:215;;;;:::o;4269:163::-;4367:4;4390:3;4382:11;;4420:4;4415:3;4411:14;4403:22;;4269:163;;;:::o;4438:124::-;4515:6;4549:5;4543:12;4533:22;;4438:124;;;:::o;4568:184::-;4667:11;4701:6;4696:3;4689:19;4741:4;4736:3;4732:14;4717:29;;4568:184;;;;:::o;4758:142::-;4835:4;4858:3;4850:11;;4888:4;4883:3;4879:14;4871:22;;4758:142;;;:::o;4906:99::-;4958:6;4992:5;4986:12;4976:22;;4906:99;;;:::o;5011:159::-;5085:11;5119:6;5114:3;5107:19;5159:4;5154:3;5150:14;5135:29;;5011:159;;;;:::o;5176:139::-;5265:6;5260:3;5255;5249:23;5306:1;5297:6;5292:3;5288:16;5281:27;5176:139;;;:::o;5321:102::-;5362:6;5413:2;5409:7;5404:2;5397:5;5393:14;5389:28;5379:38;;5321:102;;;:::o;5429:357::-;5507:3;5535:39;5568:5;5535:39;:::i;:::-;5590:61;5644:6;5639:3;5590:61;:::i;:::-;5583:68;;5660:65;5718:6;5713:3;5706:4;5699:5;5695:16;5660:65;:::i;:::-;5750:29;5772:6;5750:29;:::i;:::-;5745:3;5741:39;5734:46;;5511:275;5429:357;;;;:::o;5792:196::-;5881:10;5916:66;5978:3;5970:6;5916:66;:::i;:::-;5902:80;;5792:196;;;;:::o;5994:123::-;6074:4;6106;6101:3;6097:14;6089:22;;5994:123;;;:::o;6151:971::-;6280:3;6309:64;6367:5;6309:64;:::i;:::-;6389:86;6468:6;6463:3;6389:86;:::i;:::-;6382:93;;6501:3;6546:4;6538:6;6534:17;6529:3;6525:27;6576:66;6636:5;6576:66;:::i;:::-;6665:7;6696:1;6681:396;6706:6;6703:1;6700:13;6681:396;;;6777:9;6771:4;6767:20;6762:3;6755:33;6828:6;6822:13;6856:84;6935:4;6920:13;6856:84;:::i;:::-;6848:92;;6963:70;7026:6;6963:70;:::i;:::-;6953:80;;7062:4;7057:3;7053:14;7046:21;;6741:336;6728:1;6725;6721:9;6716:14;;6681:396;;;6685:14;7093:4;7086:11;;7113:3;7106:10;;6285:837;;;;;6151:971;;;;:::o;7206:663::-;7327:3;7363:4;7358:3;7354:14;7450:4;7443:5;7439:16;7433:23;7469:63;7526:4;7521:3;7517:14;7503:12;7469:63;:::i;:::-;7378:164;7629:4;7622:5;7618:16;7612:23;7682:3;7676:4;7672:14;7665:4;7660:3;7656:14;7649:38;7708:123;7826:4;7812:12;7708:123;:::i;:::-;7700:131;;7552:290;7859:4;7852:11;;7332:537;7206:663;;;;:::o;7875:280::-;8006:10;8041:108;8145:3;8137:6;8041:108;:::i;:::-;8027:122;;7875:280;;;;:::o;8161:144::-;8262:4;8294;8289:3;8285:14;8277:22;;8161:144;;;:::o;8393:1159::-;8574:3;8603:85;8682:5;8603:85;:::i;:::-;8704:117;8814:6;8809:3;8704:117;:::i;:::-;8697:124;;8847:3;8892:4;8884:6;8880:17;8875:3;8871:27;8922:87;9003:5;8922:87;:::i;:::-;9032:7;9063:1;9048:459;9073:6;9070:1;9067:13;9048:459;;;9144:9;9138:4;9134:20;9129:3;9122:33;9195:6;9189:13;9223:126;9344:4;9329:13;9223:126;:::i;:::-;9215:134;;9372:91;9456:6;9372:91;:::i;:::-;9362:101;;9492:4;9487:3;9483:14;9476:21;;9108:399;9095:1;9092;9088:9;9083:14;;9048:459;;;9052:14;9523:4;9516:11;;9543:3;9536:10;;8579:973;;;;;8393:1159;;;;:::o;9558:497::-;9763:4;9801:2;9790:9;9786:18;9778:26;;9850:9;9844:4;9840:20;9836:1;9825:9;9821:17;9814:47;9878:170;10043:4;10034:6;9878:170;:::i;:::-;9870:178;;9558:497;;;;:::o;10061:60::-;10089:3;10110:5;10103:12;;10061:60;;;:::o;10127:142::-;10177:9;10210:53;10228:34;10237:24;10255:5;10237:24;:::i;:::-;10228:34;:::i;:::-;10210:53;:::i;:::-;10197:66;;10127:142;;;:::o;10275:126::-;10325:9;10358:37;10389:5;10358:37;:::i;:::-;10345:50;;10275:126;;;:::o;10407:143::-;10474:9;10507:37;10538:5;10507:37;:::i;:::-;10494:50;;10407:143;;;:::o;10556:165::-;10660:54;10708:5;10660:54;:::i;:::-;10655:3;10648:67;10556:165;;:::o;10727:256::-;10837:4;10875:2;10864:9;10860:18;10852:26;;10888:88;10973:1;10962:9;10958:17;10949:6;10888:88;:::i;:::-;10727:256;;;;:::o;10989:152::-;11094:6;11128:5;11122:12;11112:22;;10989:152;;;:::o;11147:222::-;11284:11;11318:6;11313:3;11306:19;11358:4;11353:3;11349:14;11334:29;;11147:222;;;;:::o;11375:170::-;11480:4;11503:3;11495:11;;11533:4;11528:3;11524:14;11516:22;;11375:170;;;:::o;11551:113::-;11617:6;11651:5;11645:12;11635:22;;11551:113;;;:::o;11670:173::-;11758:11;11792:6;11787:3;11780:19;11832:4;11827:3;11823:14;11808:29;;11670:173;;;;:::o;11849:131::-;11915:4;11938:3;11930:11;;11968:4;11963:3;11959:14;11951:22;;11849:131;;;:::o;11986:149::-;12022:7;12062:66;12055:5;12051:78;12040:89;;11986:149;;;:::o;12141:105::-;12216:23;12233:5;12216:23;:::i;:::-;12211:3;12204:36;12141:105;;:::o;12252:175::-;12319:10;12340:44;12380:3;12372:6;12340:44;:::i;:::-;12416:4;12411:3;12407:14;12393:28;;12252:175;;;;:::o;12433:112::-;12502:4;12534;12529:3;12525:14;12517:22;;12433:112;;;:::o;12579:704::-;12686:3;12715:53;12762:5;12715:53;:::i;:::-;12784:75;12852:6;12847:3;12784:75;:::i;:::-;12777:82;;12883:55;12932:5;12883:55;:::i;:::-;12961:7;12992:1;12977:281;13002:6;12999:1;12996:13;12977:281;;;13078:6;13072:13;13105:61;13162:3;13147:13;13105:61;:::i;:::-;13098:68;;13189:59;13241:6;13189:59;:::i;:::-;13179:69;;13037:221;13024:1;13021;13017:9;13012:14;;12977:281;;;12981:14;13274:3;13267:10;;12691:592;;;12579:704;;;;:::o;13381:730::-;13516:3;13552:4;13547:3;13543:14;13643:4;13636:5;13632:16;13626:23;13696:3;13690:4;13686:14;13679:4;13674:3;13670:14;13663:38;13722:73;13790:4;13776:12;13722:73;:::i;:::-;13714:81;;13567:239;13893:4;13886:5;13882:16;13876:23;13946:3;13940:4;13936:14;13929:4;13924:3;13920:14;13913:38;13972:101;14068:4;14054:12;13972:101;:::i;:::-;13964:109;;13816:268;14101:4;14094:11;;13521:590;13381:730;;;;:::o;14117:308::-;14262:10;14297:122;14415:3;14407:6;14297:122;:::i;:::-;14283:136;;14117:308;;;;:::o;14431:151::-;14539:4;14571;14566:3;14562:14;14554:22;;14431:151;;;:::o;14684:1215::-;14879:3;14908:92;14994:5;14908:92;:::i;:::-;15016:124;15133:6;15128:3;15016:124;:::i;:::-;15009:131;;15166:3;15211:4;15203:6;15199:17;15194:3;15190:27;15241:94;15329:5;15241:94;:::i;:::-;15358:7;15389:1;15374:480;15399:6;15396:1;15393:13;15374:480;;;15470:9;15464:4;15460:20;15455:3;15448:33;15521:6;15515:13;15549:140;15684:4;15669:13;15549:140;:::i;:::-;15541:148;;15712:98;15803:6;15712:98;:::i;:::-;15702:108;;15839:4;15834:3;15830:14;15823:21;;15434:420;15421:1;15418;15414:9;15409:14;;15374:480;;;15378:14;15870:4;15863:11;;15890:3;15883:10;;14884:1015;;;;;14684:1215;;;;:::o;15905:525::-;16124:4;16162:2;16151:9;16147:18;16139:26;;16211:9;16205:4;16201:20;16197:1;16186:9;16182:17;16175:47;16239:184;16418:4;16409:6;16239:184;:::i;:::-;16231:192;;15905:525;;;;:::o;16436:194::-;16545:11;16579:6;16574:3;16567:19;16619:4;16614:3;16610:14;16595:29;;16436:194;;;;:::o;16664:991::-;16803:3;16832:64;16890:5;16832:64;:::i;:::-;16912:96;17001:6;16996:3;16912:96;:::i;:::-;16905:103;;17034:3;17079:4;17071:6;17067:17;17062:3;17058:27;17109:66;17169:5;17109:66;:::i;:::-;17198:7;17229:1;17214:396;17239:6;17236:1;17233:13;17214:396;;;17310:9;17304:4;17300:20;17295:3;17288:33;17361:6;17355:13;17389:84;17468:4;17453:13;17389:84;:::i;:::-;17381:92;;17496:70;17559:6;17496:70;:::i;:::-;17486:80;;17595:4;17590:3;17586:14;17579:21;;17274:336;17261:1;17258;17254:9;17249:14;;17214:396;;;17218:14;17626:4;17619:11;;17646:3;17639:10;;16808:847;;;;;16664:991;;;;:::o;17661:413::-;17824:4;17862:2;17851:9;17847:18;17839:26;;17911:9;17905:4;17901:20;17897:1;17886:9;17882:17;17875:47;17939:128;18062:4;18053:6;17939:128;:::i;:::-;17931:136;;17661:413;;;;:::o;18080:144::-;18177:6;18211:5;18205:12;18195:22;;18080:144;;;:::o;18230:214::-;18359:11;18393:6;18388:3;18381:19;18433:4;18428:3;18424:14;18409:29;;18230:214;;;;:::o;18450:162::-;18547:4;18570:3;18562:11;;18600:4;18595:3;18591:14;18583:22;;18450:162;;;:::o;18694:639::-;18813:3;18849:4;18844:3;18840:14;18936:4;18929:5;18925:16;18919:23;18955:63;19012:4;19007:3;19003:14;18989:12;18955:63;:::i;:::-;18864:164;19115:4;19108:5;19104:16;19098:23;19168:3;19162:4;19158:14;19151:4;19146:3;19142:14;19135:38;19194:101;19290:4;19276:12;19194:101;:::i;:::-;19186:109;;19038:268;19323:4;19316:11;;18818:515;18694:639;;;;:::o;19339:276::-;19468:10;19503:106;19605:3;19597:6;19503:106;:::i;:::-;19489:120;;19339:276;;;;:::o;19621:143::-;19721:4;19753;19748:3;19744:14;19736:22;;19621:143;;;:::o;19850:1151::-;20029:3;20058:84;20136:5;20058:84;:::i;:::-;20158:116;20267:6;20262:3;20158:116;:::i;:::-;20151:123;;20300:3;20345:4;20337:6;20333:17;20328:3;20324:27;20375:86;20455:5;20375:86;:::i;:::-;20484:7;20515:1;20500:456;20525:6;20522:1;20519:13;20500:456;;;20596:9;20590:4;20586:20;20581:3;20574:33;20647:6;20641:13;20675:124;20794:4;20779:13;20675:124;:::i;:::-;20667:132;;20822:90;20905:6;20822:90;:::i;:::-;20812:100;;20941:4;20936:3;20932:14;20925:21;;20560:396;20547:1;20544;20540:9;20535:14;;20500:456;;;20504:14;20972:4;20965:11;;20992:3;20985:10;;20034:967;;;;;19850:1151;;;;:::o;21007:493::-;21210:4;21248:2;21237:9;21233:18;21225:26;;21297:9;21291:4;21287:20;21283:1;21272:9;21268:17;21261:47;21325:168;21488:4;21479:6;21325:168;:::i;:::-;21317:176;;21007:493;;;;:::o;21506:86::-;21541:7;21581:4;21574:5;21570:16;21559:27;;21506:86;;;:::o;21598:118::-;21669:22;21685:5;21669:22;:::i;:::-;21662:5;21659:33;21649:61;;21706:1;21703;21696:12;21649:61;21598:118;:::o;21722:135::-;21766:5;21804:6;21791:20;21782:29;;21820:31;21845:5;21820:31;:::i;:::-;21722:135;;;;:::o;21863:470::-;21929:6;21937;21986:2;21974:9;21965:7;21961:23;21957:32;21954:119;;;21992:79;;:::i;:::-;21954:119;22112:1;22137:53;22182:7;22173:6;22162:9;22158:22;22137:53;:::i;:::-;22127:63;;22083:117;22239:2;22265:51;22308:7;22299:6;22288:9;22284:22;22265:51;:::i;:::-;22255:61;;22210:116;21863:470;;;;;:::o;22339:90::-;22373:7;22416:5;22409:13;22402:21;22391:32;;22339:90;;;:::o;22435:109::-;22516:21;22531:5;22516:21;:::i;:::-;22511:3;22504:34;22435:109;;:::o;22550:210::-;22637:4;22675:2;22664:9;22660:18;22652:26;;22688:65;22750:1;22739:9;22735:17;22726:6;22688:65;:::i;:::-;22550:210;;;;:::o;22766:474::-;22834:6;22842;22891:2;22879:9;22870:7;22866:23;22862:32;22859:119;;;22897:79;;:::i;:::-;22859:119;23017:1;23042:53;23087:7;23078:6;23067:9;23063:22;23042:53;:::i;:::-;23032:63;;22988:117;23144:2;23170:53;23215:7;23206:6;23195:9;23191:22;23170:53;:::i;:::-;23160:63;;23115:118;22766:474;;;;;:::o;23246:615::-;23321:6;23329;23337;23386:2;23374:9;23365:7;23361:23;23357:32;23354:119;;;23392:79;;:::i;:::-;23354:119;23512:1;23537:53;23582:7;23573:6;23562:9;23558:22;23537:53;:::i;:::-;23527:63;;23483:117;23639:2;23665:51;23708:7;23699:6;23688:9;23684:22;23665:51;:::i;:::-;23655:61;;23610:116;23765:2;23791:53;23836:7;23827:6;23816:9;23812:22;23791:53;:::i;:::-;23781:63;;23736:118;23246:615;;;;;:::o;23867:143::-;23924:5;23955:6;23949:13;23940:22;;23971:33;23998:5;23971:33;:::i;:::-;23867:143;;;;:::o;24016:351::-;24086:6;24135:2;24123:9;24114:7;24110:23;24106:32;24103:119;;;24141:79;;:::i;:::-;24103:119;24261:1;24286:64;24342:7;24333:6;24322:9;24318:22;24286:64;:::i;:::-;24276:74;;24232:128;24016:351;;;;:::o;24373:118::-;24460:24;24478:5;24460:24;:::i;:::-;24455:3;24448:37;24373:118;;:::o;24497:222::-;24590:4;24628:2;24617:9;24613:18;24605:26;;24641:71;24709:1;24698:9;24694:17;24685:6;24641:71;:::i;:::-;24497:222;;;;:::o;24725:180::-;24773:77;24770:1;24763:88;24870:4;24867:1;24860:15;24894:4;24891:1;24884:15;24911:180;24959:77;24956:1;24949:88;25056:4;25053:1;25046:15;25080:4;25077:1;25070:15;25097:191;25137:3;25156:20;25174:1;25156:20;:::i;:::-;25151:25;;25190:20;25208:1;25190:20;:::i;:::-;25185:25;;25233:1;25230;25226:9;25219:16;;25254:3;25251:1;25248:10;25245:36;;;25261:18;;:::i;:::-;25245:36;25097:191;;;;:::o;25294:194::-;25334:4;25354:20;25372:1;25354:20;:::i;:::-;25349:25;;25388:20;25406:1;25388:20;:::i;:::-;25383:25;;25432:1;25429;25425:9;25417:17;;25456:1;25450:4;25447:11;25444:37;;;25461:18;;:::i;:::-;25444:37;25294:194;;;;:::o;25494:180::-;25542:77;25539:1;25532:88;25639:4;25636:1;25629:15;25663:4;25660:1;25653:15;25680:320;25724:6;25761:1;25755:4;25751:12;25741:22;;25808:1;25802:4;25798:12;25829:18;25819:81;;25885:4;25877:6;25873:17;25863:27;;25819:81;25947:2;25939:6;25936:14;25916:18;25913:38;25910:84;;25966:18;;:::i;:::-;25910:84;25731:269;25680:320;;;:::o;26006:118::-;26093:24;26111:5;26093:24;:::i;:::-;26088:3;26081:37;26006:118;;:::o;26130:77::-;26167:7;26196:5;26185:16;;26130:77;;;:::o;26213:118::-;26300:24;26318:5;26300:24;:::i;:::-;26295:3;26288:37;26213:118;;:::o;26337:332::-;26458:4;26496:2;26485:9;26481:18;26473:26;;26509:71;26577:1;26566:9;26562:17;26553:6;26509:71;:::i;:::-;26590:72;26658:2;26647:9;26643:18;26634:6;26590:72;:::i;:::-;26337:332;;;;;:::o;26675:122::-;26748:24;26766:5;26748:24;:::i;:::-;26741:5;26738:35;26728:63;;26787:1;26784;26777:12;26728:63;26675:122;:::o;26803:143::-;26860:5;26891:6;26885:13;26876:22;;26907:33;26934:5;26907:33;:::i;:::-;26803:143;;;;:::o;26952:351::-;27022:6;27071:2;27059:9;27050:7;27046:23;27042:32;27039:119;;;27077:79;;:::i;:::-;27039:119;27197:1;27222:64;27278:7;27269:6;27258:9;27254:22;27222:64;:::i;:::-;27212:74;;27168:128;26952:351;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "check_add_operation_valid_cases(uint256,uint256)": "f69c36cd", "check_decrement_valid_cases(uint256)": "d1c2242e", "check_increment_valid_cases(uint256)": "f37fc63c", "check_invariant_never_exceeds_max(uint256,uint8,uint256)": "d2274844", "check_multiple_increments(uint256,uint8)": "95534832", "check_reset_always_works(uint256)": "1b065b35", "check_sequence_operations_safety(uint256,uint256,uint256)": "075dacdd", "check_set_number_valid_cases(uint256)": "06d56596", "check_subtract_operation_valid_cases(uint256,uint256)": "c783074f", "counter()": "61bc221a", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "setUp()": "0a9254e4", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"initial\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"check_add_operation_valid_cases\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"initial\",\"type\":\"uint256\"}],\"name\":\"check_decrement_valid_cases\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"initial\",\"type\":\"uint256\"}],\"name\":\"check_increment_valid_cases\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"initial\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"operation\",\"type\":\"uint8\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"check_invariant_never_exceeds_max\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"initial\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"times\",\"type\":\"uint8\"}],\"name\":\"check_multiple_increments\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"initial\",\"type\":\"uint256\"}],\"name\":\"check_reset_always_works\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"initial\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"addValue\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"subValue\",\"type\":\"uint256\"}],\"name\":\"check_sequence_operations_safety\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"check_set_number_valid_cases\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"initial\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"check_subtract_operation_valid_cases\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"counter\",\"outputs\":[{\"internalType\":\"contract Counter\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"check_add_operation_valid_cases(uint256,uint256)\":{\"custom:halmos\":\"--function check_add_operation_valid_cases\"},\"check_decrement_valid_cases(uint256)\":{\"custom:halmos\":\"--function check_decrement_valid_cases\"},\"check_increment_valid_cases(uint256)\":{\"custom:halmos\":\"--function check_increment_valid_cases\"},\"check_invariant_never_exceeds_max(uint256,uint8,uint256)\":{\"custom:halmos\":\"--function check_invariant_never_exceeds_max\"},\"check_multiple_increments(uint256,uint8)\":{\"custom:halmos\":\"--function check_multiple_increments\"},\"check_reset_always_works(uint256)\":{\"custom:halmos\":\"--function check_reset_always_works\"},\"check_sequence_operations_safety(uint256,uint256,uint256)\":{\"custom:halmos\":\"--function check_sequence_operations_safety\"},\"check_set_number_valid_cases(uint256)\":{\"custom:halmos\":\"--function check_set_number_valid_cases\"},\"check_subtract_operation_valid_cases(uint256,uint256)\":{\"custom:halmos\":\"--function check_subtract_operation_valid_cases\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/CounterHalmos.t.sol\":\"CounterHalmosTest\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3\",\"dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"src/Counter.sol\":{\"keccak256\":\"0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e\",\"dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2\"]},\"test/CounterHalmos.t.sol\":{\"keccak256\":\"0xd2a52e27f9807d40c41bc710bfe8a6c7f6e5b5db172d341a4253b09bd08846f8\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://778bc30875b5d8e628dc95997c902c7183ca42ee14c875c1f52bac7ff8f3dbd7\",\"dweb:/ipfs/QmUZVvTtBnWnJT2jdsCFMsmvmm3EMMrcyDUdcnrVfBy3hk\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "initial", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "check_add_operation_valid_cases"}, {"inputs": [{"internalType": "uint256", "name": "initial", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "check_decrement_valid_cases"}, {"inputs": [{"internalType": "uint256", "name": "initial", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "check_increment_valid_cases"}, {"inputs": [{"internalType": "uint256", "name": "initial", "type": "uint256"}, {"internalType": "uint8", "name": "operation", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "check_invariant_never_exceeds_max"}, {"inputs": [{"internalType": "uint256", "name": "initial", "type": "uint256"}, {"internalType": "uint8", "name": "times", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "check_multiple_increments"}, {"inputs": [{"internalType": "uint256", "name": "initial", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "check_reset_always_works"}, {"inputs": [{"internalType": "uint256", "name": "initial", "type": "uint256"}, {"internalType": "uint256", "name": "addValue", "type": "uint256"}, {"internalType": "uint256", "name": "subValue", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "check_sequence_operations_safety"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "check_set_number_valid_cases"}, {"inputs": [{"internalType": "uint256", "name": "initial", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "check_subtract_operation_valid_cases"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "counter", "outputs": [{"internalType": "contract Counter", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setUp"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}], "devdoc": {"kind": "dev", "methods": {"check_add_operation_valid_cases(uint256,uint256)": {"custom:halmos": "--function check_add_operation_valid_cases"}, "check_decrement_valid_cases(uint256)": {"custom:halmos": "--function check_decrement_valid_cases"}, "check_increment_valid_cases(uint256)": {"custom:halmos": "--function check_increment_valid_cases"}, "check_invariant_never_exceeds_max(uint256,uint8,uint256)": {"custom:halmos": "--function check_invariant_never_exceeds_max"}, "check_multiple_increments(uint256,uint8)": {"custom:halmos": "--function check_multiple_increments"}, "check_reset_always_works(uint256)": {"custom:halmos": "--function check_reset_always_works"}, "check_sequence_operations_safety(uint256,uint256,uint256)": {"custom:halmos": "--function check_sequence_operations_safety"}, "check_set_number_valid_cases(uint256)": {"custom:halmos": "--function check_set_number_valid_cases"}, "check_subtract_operation_valid_cases(uint256,uint256)": {"custom:halmos": "--function check_subtract_operation_valid_cases"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/CounterHalmos.t.sol": "CounterHalmosTest"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43", "urls": ["bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3", "dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "src/Counter.sol": {"keccak256": "0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c", "urls": ["bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e", "dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2"], "license": "UNLICENSED"}, "test/CounterHalmos.t.sol": {"keccak256": "0xd2a52e27f9807d40c41bc710bfe8a6c7f6e5b5db172d341a4253b09bd08846f8", "urls": ["bzz-raw://778bc30875b5d8e628dc95997c902c7183ca42ee14c875c1f52bac7ff8f3dbd7", "dweb:/ipfs/QmUZVvTtBnWnJT2jdsCFMsmvmm3EMMrcyDUdcnrVfBy3hk"], "license": "UNLICENSED"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 46, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "stdstore", "offset": 0, "slot": "0", "type": "t_struct(StdStorage)8290_storage"}, {"astId": 209, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_failed", "offset": 0, "slot": "8", "type": "t_bool"}, {"astId": 2942, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "stdChainsInitialized", "offset": 1, "slot": "8", "type": "t_bool"}, {"astId": 2963, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "chains", "offset": 0, "slot": "9", "type": "t_mapping(t_string_memory_ptr,t_struct(Chain)2958_storage)"}, {"astId": 2967, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "defaultRpcUrls", "offset": 0, "slot": "10", "type": "t_mapping(t_string_memory_ptr,t_string_storage)"}, {"astId": 2971, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "idToAlias", "offset": 0, "slot": "11", "type": "t_mapping(t_uint256,t_string_storage)"}, {"astId": 2974, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "fallbackToDefaultRpcUrls", "offset": 0, "slot": "12", "type": "t_bool"}, {"astId": 3912, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "gasMeteringOff", "offset": 1, "slot": "12", "type": "t_bool"}, {"astId": 5979, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "stdstore", "offset": 0, "slot": "13", "type": "t_struct(StdStorage)8290_storage"}, {"astId": 6896, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_excludedContracts", "offset": 0, "slot": "21", "type": "t_array(t_address)dyn_storage"}, {"astId": 6899, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_excludedSenders", "offset": 0, "slot": "22", "type": "t_array(t_address)dyn_storage"}, {"astId": 6902, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_targetedContracts", "offset": 0, "slot": "23", "type": "t_array(t_address)dyn_storage"}, {"astId": 6905, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_targetedSenders", "offset": 0, "slot": "24", "type": "t_array(t_address)dyn_storage"}, {"astId": 6908, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_excludedArtifacts", "offset": 0, "slot": "25", "type": "t_array(t_string_storage)dyn_storage"}, {"astId": 6911, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_targetedArtifacts", "offset": 0, "slot": "26", "type": "t_array(t_string_storage)dyn_storage"}, {"astId": 6915, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_targetedArtifactSelectors", "offset": 0, "slot": "27", "type": "t_array(t_struct(FuzzArtifactSelector)6887_storage)dyn_storage"}, {"astId": 6919, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_excludedSelectors", "offset": 0, "slot": "28", "type": "t_array(t_struct(FuzzSelector)6881_storage)dyn_storage"}, {"astId": 6923, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_targetedSelectors", "offset": 0, "slot": "29", "type": "t_array(t_struct(FuzzSelector)6881_storage)dyn_storage"}, {"astId": 6927, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_targetedInterfaces", "offset": 0, "slot": "30", "type": "t_array(t_struct(FuzzInterface)6893_storage)dyn_storage"}, {"astId": 13182, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "IS_TEST", "offset": 0, "slot": "31", "type": "t_bool"}, {"astId": 39842, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "counter", "offset": 1, "slot": "31", "type": "t_contract(Counter)39831"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"encoding": "dynamic_array", "label": "address[]", "numberOfBytes": "32", "base": "t_address"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_array(t_bytes4)dyn_storage": {"encoding": "dynamic_array", "label": "bytes4[]", "numberOfBytes": "32", "base": "t_bytes4"}, "t_array(t_string_storage)dyn_storage": {"encoding": "dynamic_array", "label": "string[]", "numberOfBytes": "32", "base": "t_string_storage"}, "t_array(t_struct(FuzzArtifactSelector)6887_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzArtifactSelector[]", "numberOfBytes": "32", "base": "t_struct(FuzzArtifactSelector)6887_storage"}, "t_array(t_struct(FuzzInterface)6893_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzInterface[]", "numberOfBytes": "32", "base": "t_struct(FuzzInterface)6893_storage"}, "t_array(t_struct(FuzzSelector)6881_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzSelector[]", "numberOfBytes": "32", "base": "t_struct(FuzzSelector)6881_storage"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_bytes4": {"encoding": "inplace", "label": "bytes4", "numberOfBytes": "4"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_contract(Counter)39831": {"encoding": "inplace", "label": "contract Counter", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8265_storage)))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(bytes4 => mapping(bytes32 => struct FindData)))", "numberOfBytes": "32", "value": "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8265_storage))"}, "t_mapping(t_bytes32,t_struct(FindData)8265_storage)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => struct FindData)", "numberOfBytes": "32", "value": "t_struct(FindData)8265_storage"}, "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8265_storage))": {"encoding": "mapping", "key": "t_bytes4", "label": "mapping(bytes4 => mapping(bytes32 => struct FindData))", "numberOfBytes": "32", "value": "t_mapping(t_bytes32,t_struct(FindData)8265_storage)"}, "t_mapping(t_string_memory_ptr,t_string_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_mapping(t_string_memory_ptr,t_struct(Chain)2958_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => struct StdChains.Chain)", "numberOfBytes": "32", "value": "t_struct(Chain)2958_storage"}, "t_mapping(t_uint256,t_string_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_string_memory_ptr": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Chain)2958_storage": {"encoding": "inplace", "label": "struct StdChains.Chain", "numberOfBytes": "128", "members": [{"astId": 2951, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 2953, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "chainId", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 2955, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "chainAlias", "offset": 0, "slot": "2", "type": "t_string_storage"}, {"astId": 2957, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "rpcUrl", "offset": 0, "slot": "3", "type": "t_string_storage"}]}, "t_struct(FindData)8265_storage": {"encoding": "inplace", "label": "struct FindData", "numberOfBytes": "128", "members": [{"astId": 8258, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "slot", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 8260, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "offsetLeft", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 8262, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "offsetRight", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 8264, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "found", "offset": 0, "slot": "3", "type": "t_bool"}]}, "t_struct(FuzzArtifactSelector)6887_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzArtifactSelector", "numberOfBytes": "64", "members": [{"astId": 6883, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "artifact", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 6886, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "selectors", "offset": 0, "slot": "1", "type": "t_array(t_bytes4)dyn_storage"}]}, "t_struct(FuzzInterface)6893_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzInterface", "numberOfBytes": "64", "members": [{"astId": 6889, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "addr", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 6892, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "artifacts", "offset": 0, "slot": "1", "type": "t_array(t_string_storage)dyn_storage"}]}, "t_struct(FuzzSelector)6881_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzSelector", "numberOfBytes": "64", "members": [{"astId": 6877, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "addr", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 6880, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "selectors", "offset": 0, "slot": "1", "type": "t_array(t_bytes4)dyn_storage"}]}, "t_struct(StdStorage)8290_storage": {"encoding": "inplace", "label": "struct StdStorage", "numberOfBytes": "256", "members": [{"astId": 8274, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "finds", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8265_storage)))"}, {"astId": 8277, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_keys", "offset": 0, "slot": "1", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 8279, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_sig", "offset": 0, "slot": "2", "type": "t_bytes4"}, {"astId": 8281, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_depth", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 8283, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_target", "offset": 0, "slot": "4", "type": "t_address"}, {"astId": 8285, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_set", "offset": 0, "slot": "5", "type": "t_bytes32"}, {"astId": 8287, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_enable_packed_slots", "offset": 0, "slot": "6", "type": "t_bool"}, {"astId": 8289, "contract": "test/CounterHalmos.t.sol:CounterHalmosTest", "label": "_calldata", "offset": 0, "slot": "7", "type": "t_bytes_storage"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "test/CounterHalmos.t.sol", "id": 40507, "exportedSymbols": {"Counter": [39831], "CounterHalmosTest": [40506], "Test": [13183]}, "nodeType": "SourceUnit", "src": "39:5605:20", "nodes": [{"id": 39833, "nodeType": "PragmaDirective", "src": "39:24:20", "nodes": [], "literals": ["solidity", "^", "0.8", ".13"]}, {"id": 39835, "nodeType": "ImportDirective", "src": "65:40:20", "nodes": [], "absolutePath": "lib/forge-std/src/Test.sol", "file": "forge-std/Test.sol", "nameLocation": "-1:-1:-1", "scope": 40507, "sourceUnit": 13184, "symbolAliases": [{"foreign": {"id": 39834, "name": "Test", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13183, "src": "73:4:20", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 39837, "nodeType": "ImportDirective", "src": "106:43:20", "nodes": [], "absolutePath": "src/Counter.sol", "file": "../src/Counter.sol", "nameLocation": "-1:-1:-1", "scope": 40507, "sourceUnit": 39832, "symbolAliases": [{"foreign": {"id": 39836, "name": "Counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39831, "src": "114:7:20", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 40506, "nodeType": "ContractDefinition", "src": "151:5492:20", "nodes": [{"id": 39842, "nodeType": "VariableDeclaration", "src": "192:22:20", "nodes": [], "constant": false, "functionSelector": "61bc221a", "mutability": "mutable", "name": "counter", "nameLocation": "207:7:20", "scope": 40506, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}, "typeName": {"id": 39841, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 39840, "name": "Counter", "nameLocations": ["192:7:20"], "nodeType": "IdentifierPath", "referencedDeclaration": 39831, "src": "192:7:20"}, "referencedDeclaration": 39831, "src": "192:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "visibility": "public"}, {"id": 39853, "nodeType": "FunctionDefinition", "src": "221:64:20", "nodes": [], "body": {"id": 39852, "nodeType": "Block", "src": "245:40:20", "nodes": [], "statements": [{"expression": {"id": 39850, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 39845, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "255:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [], "expression": {"argumentTypes": [], "id": 39848, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "NewExpression", "src": "265:11:20", "typeDescriptions": {"typeIdentifier": "t_function_creation_nonpayable$__$returns$_t_contract$_Counter_$39831_$", "typeString": "function () returns (contract Counter)"}, "typeName": {"id": 39847, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 39846, "name": "Counter", "nameLocations": ["269:7:20"], "nodeType": "IdentifierPath", "referencedDeclaration": 39831, "src": "269:7:20"}, "referencedDeclaration": 39831, "src": "269:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}}, "id": 39849, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "265:13:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "src": "255:23:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39851, "nodeType": "ExpressionStatement", "src": "255:23:20"}]}, "functionSelector": "0a9254e4", "implemented": true, "kind": "function", "modifiers": [], "name": "setUp", "nameLocation": "230:5:20", "parameters": {"id": 39843, "nodeType": "ParameterList", "parameters": [], "src": "235:2:20"}, "returnParameters": {"id": 39844, "nodeType": "ParameterList", "parameters": [], "src": "245:0:20"}, "scope": 40506, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 39901, "nodeType": "FunctionDefinition", "src": "558:334:20", "nodes": [], "body": {"id": 39900, "nodeType": "Block", "src": "619:273:20", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39866, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39862, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39856, "src": "682:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39863, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "692:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39864, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "700:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "692:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39865, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "692:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "682:29:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 39859, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "672:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 39861, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "675:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "672:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 39867, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "672:40:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39868, "nodeType": "ExpressionStatement", "src": "672:40:20"}, {"expression": {"arguments": [{"id": 39872, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39856, "src": "741:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 39869, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "723:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39871, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "731:9:20", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39710, "src": "723:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 39873, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "723:26:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39874, "nodeType": "ExpressionStatement", "src": "723:26:20"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39875, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "759:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39877, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "767:9:20", "memberName": "increment", "nodeType": "MemberAccess", "referencedDeclaration": 39733, "src": "759:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 39878, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "759:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39879, "nodeType": "ExpressionStatement", "src": "759:19:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39887, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39881, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "796:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39882, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "804:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "796:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39883, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "796:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39886, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39884, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39856, "src": "816:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"hexValue": "31", "id": 39885, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "826:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "816:11:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "796:31:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 39880, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "789:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 39888, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "789:39:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39889, "nodeType": "ExpressionStatement", "src": "789:39:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39897, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39891, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "845:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39892, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "853:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "845:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39893, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "845:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39894, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "865:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39895, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "873:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "865:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39896, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "865:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "845:39:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 39890, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "838:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 39898, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "838:47:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39899, "nodeType": "ExpressionStatement", "src": "838:47:20"}]}, "documentation": {"id": 39854, "nodeType": "StructuredDocumentation", "src": "496:57:20", "text": "@custom:halmos --function check_increment_valid_cases"}, "functionSelector": "f37fc63c", "implemented": true, "kind": "function", "modifiers": [], "name": "check_increment_valid_cases", "nameLocation": "567:27:20", "parameters": {"id": 39857, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 39856, "mutability": "mutable", "name": "initial", "nameLocation": "603:7:20", "nodeType": "VariableDeclaration", "scope": 39901, "src": "595:15:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39855, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "595:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "594:17:20"}, "returnParameters": {"id": 39858, "nodeType": "ParameterList", "parameters": [], "src": "619:0:20"}, "scope": 40506, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 39947, "nodeType": "FunctionDefinition", "src": "960:310:20", "nodes": [], "body": {"id": 39946, "nodeType": "Block", "src": "1021:249:20", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39912, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39910, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39904, "src": "1084:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 39911, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1094:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "1084:11:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 39907, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "1074:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 39909, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1077:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "1074:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 39913, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1074:22:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39914, "nodeType": "ExpressionStatement", "src": "1074:22:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39922, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39918, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39904, "src": "1116:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39919, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1127:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39920, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1135:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "1127:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39921, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1127:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1116:30:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 39915, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "1106:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 39917, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1109:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "1106:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 39923, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1106:41:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39924, "nodeType": "ExpressionStatement", "src": "1106:41:20"}, {"expression": {"arguments": [{"id": 39928, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39904, "src": "1176:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 39925, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1158:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39927, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1166:9:20", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39710, "src": "1158:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 39929, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1158:26:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39930, "nodeType": "ExpressionStatement", "src": "1158:26:20"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39931, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1194:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39933, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1202:9:20", "memberName": "decrement", "nodeType": "MemberAccess", "referencedDeclaration": 39756, "src": "1194:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 39934, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1194:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39935, "nodeType": "ExpressionStatement", "src": "1194:19:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39943, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39937, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1231:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39938, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1239:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "1231:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39939, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1231:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39942, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39940, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39904, "src": "1251:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "31", "id": 39941, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1261:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "1251:11:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1231:31:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 39936, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "1224:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 39944, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1224:39:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39945, "nodeType": "ExpressionStatement", "src": "1224:39:20"}]}, "documentation": {"id": 39902, "nodeType": "StructuredDocumentation", "src": "898:57:20", "text": "@custom:halmos --function check_decrement_valid_cases"}, "functionSelector": "d1c2242e", "implemented": true, "kind": "function", "modifiers": [], "name": "check_decrement_valid_cases", "nameLocation": "969:27:20", "parameters": {"id": 39905, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 39904, "mutability": "mutable", "name": "initial", "nameLocation": "1005:7:20", "nodeType": "VariableDeclaration", "scope": 39947, "src": "997:15:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39903, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "997:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "996:17:20"}, "returnParameters": {"id": 39906, "nodeType": "ParameterList", "parameters": [], "src": "1021:0:20"}, "scope": 40506, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40018, "nodeType": "FunctionDefinition", "src": "1342:445:20", "nodes": [], "body": {"id": 40017, "nodeType": "Block", "src": "1422:365:20", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39962, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39958, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39950, "src": "1484:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39959, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1495:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39960, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1503:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "1495:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39961, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1495:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1484:30:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 39955, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "1474:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 39957, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1477:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "1474:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 39963, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1474:41:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39964, "nodeType": "ExpressionStatement", "src": "1474:41:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39970, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39968, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39952, "src": "1535:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 39969, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1543:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "1535:9:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 39965, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "1525:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 39967, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1528:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "1525:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 39971, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1525:20:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39972, "nodeType": "ExpressionStatement", "src": "1525:20:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39982, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39978, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39976, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39950, "src": "1565:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 39977, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39952, "src": "1575:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1565:15:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39979, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1584:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39980, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1592:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "1584:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39981, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1584:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1565:38:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 39973, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "1555:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 39975, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1558:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "1555:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 39983, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1555:49:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39984, "nodeType": "ExpressionStatement", "src": "1555:49:20"}, {"expression": {"arguments": [{"id": 39988, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39950, "src": "1633:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 39985, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1615:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39987, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1623:9:20", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39710, "src": "1615:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 39989, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1615:26:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39990, "nodeType": "ExpressionStatement", "src": "1615:26:20"}, {"expression": {"arguments": [{"id": 39994, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39952, "src": "1663:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 39991, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1651:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39993, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1659:3:20", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 39804, "src": "1651:11:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 39995, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1651:18:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39996, "nodeType": "ExpressionStatement", "src": "1651:18:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40004, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39998, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1687:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 39999, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1695:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "1687:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40000, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1687:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40003, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40001, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39950, "src": "1707:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 40002, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39952, "src": "1717:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1707:15:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1687:35:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 39997, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "1680:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40005, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1680:43:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40006, "nodeType": "ExpressionStatement", "src": "1680:43:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40014, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40008, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1740:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40009, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1748:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "1740:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40010, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1740:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40011, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "1760:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40012, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1768:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "1760:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40013, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1760:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1740:39:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40007, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "1733:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40015, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1733:47:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40016, "nodeType": "ExpressionStatement", "src": "1733:47:20"}]}, "documentation": {"id": 39948, "nodeType": "StructuredDocumentation", "src": "1276:61:20", "text": "@custom:halmos --function check_add_operation_valid_cases"}, "functionSelector": "f69c36cd", "implemented": true, "kind": "function", "modifiers": [], "name": "check_add_operation_valid_cases", "nameLocation": "1351:31:20", "parameters": {"id": 39953, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 39950, "mutability": "mutable", "name": "initial", "nameLocation": "1391:7:20", "nodeType": "VariableDeclaration", "scope": 40018, "src": "1383:15:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39949, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1383:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 39952, "mutability": "mutable", "name": "value", "nameLocation": "1408:5:20", "nodeType": "VariableDeclaration", "scope": 40018, "src": "1400:13:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39951, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1400:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1382:32:20"}, "returnParameters": {"id": 39954, "nodeType": "ParameterList", "parameters": [], "src": "1422:0:20"}, "scope": 40506, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40075, "nodeType": "FunctionDefinition", "src": "1864:379:20", "nodes": [], "body": {"id": 40074, "nodeType": "Block", "src": "1949:294:20", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40033, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40029, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40021, "src": "2014:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40030, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2025:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40031, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "2033:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "2025:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40032, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2025:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2014:30:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40026, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2004:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40028, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2007:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "2004:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40034, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2004:41:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40035, "nodeType": "ExpressionStatement", "src": "2004:41:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40041, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40039, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40023, "src": "2065:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 40040, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2073:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "2065:9:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40036, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2055:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40038, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2058:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "2055:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40042, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2055:20:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40043, "nodeType": "ExpressionStatement", "src": "2055:20:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40049, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40047, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40021, "src": "2095:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 40048, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40023, "src": "2106:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2095:16:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40044, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2085:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40046, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2088:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "2085:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40050, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2085:27:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40051, "nodeType": "ExpressionStatement", "src": "2085:27:20"}, {"expression": {"arguments": [{"id": 40055, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40021, "src": "2141:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40052, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2123:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40054, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2131:9:20", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39710, "src": "2123:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40056, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2123:26:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40057, "nodeType": "ExpressionStatement", "src": "2123:26:20"}, {"expression": {"arguments": [{"id": 40061, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40023, "src": "2176:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40058, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2159:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40060, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2167:8:20", "memberName": "subtract", "nodeType": "MemberAccess", "referencedDeclaration": 39830, "src": "2159:16:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40062, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2159:23:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40063, "nodeType": "ExpressionStatement", "src": "2159:23:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40071, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40065, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2200:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40066, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2208:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "2200:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40067, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2200:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40070, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40068, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40021, "src": "2220:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 40069, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40023, "src": "2230:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2220:15:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2200:35:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40064, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "2193:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40072, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2193:43:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40073, "nodeType": "ExpressionStatement", "src": "2193:43:20"}]}, "documentation": {"id": 40019, "nodeType": "StructuredDocumentation", "src": "1793:66:20", "text": "@custom:halmos --function check_subtract_operation_valid_cases"}, "functionSelector": "c783074f", "implemented": true, "kind": "function", "modifiers": [], "name": "check_subtract_operation_valid_cases", "nameLocation": "1873:36:20", "parameters": {"id": 40024, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40021, "mutability": "mutable", "name": "initial", "nameLocation": "1918:7:20", "nodeType": "VariableDeclaration", "scope": 40075, "src": "1910:15:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40020, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1910:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 40023, "mutability": "mutable", "name": "value", "nameLocation": "1935:5:20", "nodeType": "VariableDeclaration", "scope": 40075, "src": "1927:13:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40022, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1927:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1909:32:20"}, "returnParameters": {"id": 40025, "nodeType": "ParameterList", "parameters": [], "src": "1949:0:20"}, "scope": 40506, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40106, "nodeType": "FunctionDefinition", "src": "2312:238:20", "nodes": [], "body": {"id": 40105, "nodeType": "Block", "src": "2372:178:20", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40088, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40084, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40078, "src": "2436:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40085, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2445:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40086, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "2453:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "2445:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40087, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2445:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2436:28:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40081, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2426:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40083, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2429:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "2426:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40089, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2426:39:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40090, "nodeType": "ExpressionStatement", "src": "2426:39:20"}, {"expression": {"arguments": [{"id": 40094, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40078, "src": "2494:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40091, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2476:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40093, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2484:9:20", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39710, "src": "2476:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40095, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2476:24:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40096, "nodeType": "ExpressionStatement", "src": "2476:24:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40102, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40098, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2517:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40099, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2525:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "2517:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40100, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2517:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 40101, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40078, "src": "2537:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2517:25:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40097, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "2510:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40103, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2510:33:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40104, "nodeType": "ExpressionStatement", "src": "2510:33:20"}]}, "documentation": {"id": 40076, "nodeType": "StructuredDocumentation", "src": "2249:58:20", "text": "@custom:halmos --function check_set_number_valid_cases"}, "functionSelector": "06d56596", "implemented": true, "kind": "function", "modifiers": [], "name": "check_set_number_valid_cases", "nameLocation": "2321:28:20", "parameters": {"id": 40079, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40078, "mutability": "mutable", "name": "value", "nameLocation": "2358:5:20", "nodeType": "VariableDeclaration", "scope": 40106, "src": "2350:13:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40077, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2350:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2349:15:20"}, "returnParameters": {"id": 40080, "nodeType": "ParameterList", "parameters": [], "src": "2372:0:20"}, "scope": 40506, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40142, "nodeType": "FunctionDefinition", "src": "2615:217:20", "nodes": [], "body": {"id": 40141, "nodeType": "Block", "src": "2673:159:20", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40119, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40115, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40109, "src": "2693:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40116, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2704:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40117, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "2712:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "2704:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40118, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2704:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2693:30:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40112, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2683:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40114, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2686:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "2683:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40120, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2683:41:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40121, "nodeType": "ExpressionStatement", "src": "2683:41:20"}, {"expression": {"arguments": [{"id": 40125, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40109, "src": "2753:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40122, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2735:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40124, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2743:9:20", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39710, "src": "2735:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40126, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2735:26:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40127, "nodeType": "ExpressionStatement", "src": "2735:26:20"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40128, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2771:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40130, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2779:5:20", "memberName": "reset", "nodeType": "MemberAccess", "referencedDeclaration": 39776, "src": "2771:13:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 40131, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2771:15:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40132, "nodeType": "ExpressionStatement", "src": "2771:15:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40138, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40134, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "2803:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40135, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2811:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "2803:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40136, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2803:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 40137, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2823:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "2803:21:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40133, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "2796:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40139, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2796:29:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40140, "nodeType": "ExpressionStatement", "src": "2796:29:20"}]}, "documentation": {"id": 40107, "nodeType": "StructuredDocumentation", "src": "2556:54:20", "text": "@custom:halmos --function check_reset_always_works"}, "functionSelector": "1b065b35", "implemented": true, "kind": "function", "modifiers": [], "name": "check_reset_always_works", "nameLocation": "2624:24:20", "parameters": {"id": 40110, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40109, "mutability": "mutable", "name": "initial", "nameLocation": "2657:7:20", "nodeType": "VariableDeclaration", "scope": 40142, "src": "2649:15:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40108, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2649:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2648:17:20"}, "returnParameters": {"id": 40111, "nodeType": "ParameterList", "parameters": [], "src": "2673:0:20"}, "scope": 40506, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40288, "nodeType": "FunctionDefinition", "src": "2906:1238:20", "nodes": [], "body": {"id": 40287, "nodeType": "Block", "src": "3005:1139:20", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40159, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40155, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40145, "src": "3025:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40156, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3036:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40157, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3044:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "3036:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40158, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3036:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3025:30:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40152, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "3015:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40154, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3018:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "3015:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40160, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3015:41:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40161, "nodeType": "ExpressionStatement", "src": "3015:41:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40169, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40165, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40149, "src": "3076:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40166, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3085:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40167, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3093:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "3085:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40168, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3085:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3076:28:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40162, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "3066:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40164, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3069:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "3066:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40170, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3066:39:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40171, "nodeType": "ExpressionStatement", "src": "3066:39:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40177, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40175, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40149, "src": "3125:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 40176, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3133:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3125:9:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40172, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "3115:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40174, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3118:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "3115:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40178, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3115:20:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40179, "nodeType": "ExpressionStatement", "src": "3115:20:20"}, {"expression": {"arguments": [{"id": 40183, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40145, "src": "3164:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40180, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3146:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40182, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3154:9:20", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39710, "src": "3146:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40184, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3146:26:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40185, "nodeType": "ExpressionStatement", "src": "3146:26:20"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 40194, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 40188, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40186, "name": "operation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40147, "src": "3235:9:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 40187, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3248:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3235:14:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40193, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40189, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40145, "src": "3253:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40190, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3263:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40191, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3271:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "3263:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40192, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3263:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3253:29:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "3235:47:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 40207, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 40203, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40201, "name": "operation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40147, "src": "3378:9:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "31", "id": 40202, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3391:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "3378:14:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40206, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40204, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40145, "src": "3396:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 40205, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3406:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3396:11:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "3378:29:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 40224, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 40216, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40214, "name": "operation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40147, "src": "3503:9:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "32", "id": 40215, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3516:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}, "src": "3503:14:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40223, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40219, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40217, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40145, "src": "3521:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 40218, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40149, "src": "3531:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3521:15:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40220, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3540:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40221, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3548:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "3540:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40222, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3540:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3521:38:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "3503:56:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 40238, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 40234, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40232, "name": "operation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40147, "src": "3648:9:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "33", "id": 40233, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3661:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_3_by_1", "typeString": "int_const 3"}, "value": "3"}, "src": "3648:14:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40237, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40235, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40145, "src": "3666:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 40236, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40149, "src": "3677:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3666:16:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "3648:34:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 40248, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40246, "name": "operation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40147, "src": "3781:9:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "34", "id": 40247, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3794:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_4_by_1", "typeString": "int_const 4"}, "value": "4"}, "src": "3781:14:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 40263, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 40257, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40255, "name": "operation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40147, "src": "3882:9:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "35", "id": 40256, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3895:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}, "value": "5"}, "src": "3882:14:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40262, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40258, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40149, "src": "3900:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40259, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3909:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40260, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3917:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "3909:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40261, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3909:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3900:28:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "3882:46:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40271, "nodeType": "IfStatement", "src": "3878:142:20", "trueBody": {"id": 40270, "nodeType": "Block", "src": "3930:90:20", "statements": [{"expression": {"arguments": [{"id": 40267, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40149, "src": "4003:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40264, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3985:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40266, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3993:9:20", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39710, "src": "3985:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40268, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3985:24:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40269, "nodeType": "ExpressionStatement", "src": "3985:24:20"}]}}, "id": 40272, "nodeType": "IfStatement", "src": "3777:243:20", "trueBody": {"id": 40254, "nodeType": "Block", "src": "3797:75:20", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40249, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3846:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40251, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3854:5:20", "memberName": "reset", "nodeType": "MemberAccess", "referencedDeclaration": 39776, "src": "3846:13:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 40252, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3846:15:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40253, "nodeType": "ExpressionStatement", "src": "3846:15:20"}]}}, "id": 40273, "nodeType": "IfStatement", "src": "3644:376:20", "trueBody": {"id": 40245, "nodeType": "Block", "src": "3684:87:20", "statements": [{"expression": {"arguments": [{"id": 40242, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40149, "src": "3754:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40239, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3737:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40241, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3745:8:20", "memberName": "subtract", "nodeType": "MemberAccess", "referencedDeclaration": 39830, "src": "3737:16:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40243, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3737:23:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40244, "nodeType": "ExpressionStatement", "src": "3737:23:20"}]}}, "id": 40274, "nodeType": "IfStatement", "src": "3499:521:20", "trueBody": {"id": 40231, "nodeType": "Block", "src": "3561:77:20", "statements": [{"expression": {"arguments": [{"id": 40228, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40149, "src": "3621:5:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40225, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3609:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40227, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3617:3:20", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 39804, "src": "3609:11:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40229, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3609:18:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40230, "nodeType": "ExpressionStatement", "src": "3609:18:20"}]}}, "id": 40275, "nodeType": "IfStatement", "src": "3374:646:20", "trueBody": {"id": 40213, "nodeType": "Block", "src": "3409:84:20", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40208, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3463:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40210, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3471:9:20", "memberName": "decrement", "nodeType": "MemberAccess", "referencedDeclaration": 39756, "src": "3463:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 40211, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3463:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40212, "nodeType": "ExpressionStatement", "src": "3463:19:20"}]}}, "id": 40276, "nodeType": "IfStatement", "src": "3231:789:20", "trueBody": {"id": 40200, "nodeType": "Block", "src": "3284:84:20", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40195, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "3338:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40197, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3346:9:20", "memberName": "increment", "nodeType": "MemberAccess", "referencedDeclaration": 39733, "src": "3338:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 40198, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3338:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40199, "nodeType": "ExpressionStatement", "src": "3338:19:20"}]}}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40284, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40278, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "4097:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40279, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4105:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "4097:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40280, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4097:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40281, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "4117:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40282, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4125:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "4117:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40283, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4117:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4097:39:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40277, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "4090:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40285, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4090:47:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40286, "nodeType": "ExpressionStatement", "src": "4090:47:20"}]}, "documentation": {"id": 40143, "nodeType": "StructuredDocumentation", "src": "2838:63:20", "text": "@custom:halmos --function check_invariant_never_exceeds_max"}, "functionSelector": "d2274844", "implemented": true, "kind": "function", "modifiers": [], "name": "check_invariant_never_exceeds_max", "nameLocation": "2915:33:20", "parameters": {"id": 40150, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40145, "mutability": "mutable", "name": "initial", "nameLocation": "2957:7:20", "nodeType": "VariableDeclaration", "scope": 40288, "src": "2949:15:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40144, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2949:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 40147, "mutability": "mutable", "name": "operation", "nameLocation": "2972:9:20", "nodeType": "VariableDeclaration", "scope": 40288, "src": "2966:15:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 40146, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "2966:5:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}, {"constant": false, "id": 40149, "mutability": "mutable", "name": "value", "nameLocation": "2991:5:20", "nodeType": "VariableDeclaration", "scope": 40288, "src": "2983:13:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40148, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2983:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2948:49:20"}, "returnParameters": {"id": 40151, "nodeType": "ParameterList", "parameters": [], "src": "3005:0:20"}, "scope": 40506, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40419, "nodeType": "FunctionDefinition", "src": "4217:886:20", "nodes": [], "body": {"id": 40418, "nodeType": "Block", "src": "4319:784:20", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40305, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40301, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40291, "src": "4339:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40302, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "4350:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40303, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4358:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "4350:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40304, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4350:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4339:30:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40298, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "4329:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40300, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4332:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "4329:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40306, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4329:41:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40307, "nodeType": "ExpressionStatement", "src": "4329:41:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 40317, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40313, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40311, "name": "addValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40293, "src": "4390:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 40312, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4401:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4390:12:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40316, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40314, "name": "addValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40293, "src": "4406:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "313030", "id": 40315, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4418:3:20", "typeDescriptions": {"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}, "value": "100"}, "src": "4406:15:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4390:31:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40308, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "4380:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40310, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4383:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "4380:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40318, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4380:42:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40319, "nodeType": "ExpressionStatement", "src": "4380:42:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 40329, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40325, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40323, "name": "subValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40295, "src": "4442:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 40324, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4453:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4442:12:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40328, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40326, "name": "subValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40295, "src": "4458:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "313030", "id": 40327, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4470:3:20", "typeDescriptions": {"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}, "value": "100"}, "src": "4458:15:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4442:31:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40320, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "4432:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40322, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4435:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "4432:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40330, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4432:42:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40331, "nodeType": "ExpressionStatement", "src": "4432:42:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40341, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40337, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40335, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40291, "src": "4494:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 40336, "name": "addValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40293, "src": "4504:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4494:18:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40338, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "4516:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40339, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4524:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "4516:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40340, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4516:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4494:41:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40332, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "4484:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40334, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4487:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "4484:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40342, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4484:52:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40343, "nodeType": "ExpressionStatement", "src": "4484:52:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40351, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40349, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40347, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40291, "src": "4556:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 40348, "name": "addValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40293, "src": "4566:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4556:18:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 40350, "name": "subValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40295, "src": "4578:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4556:30:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40344, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "4546:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40346, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4549:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "4546:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40352, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4546:41:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40353, "nodeType": "ExpressionStatement", "src": "4546:41:20"}, {"expression": {"arguments": [{"id": 40357, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40291, "src": "4616:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40354, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "4598:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40356, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4606:9:20", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39710, "src": "4598:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40358, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4598:26:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40359, "nodeType": "ExpressionStatement", "src": "4598:26:20"}, {"expression": {"arguments": [{"id": 40363, "name": "addValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40293, "src": "4694:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40360, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "4682:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40362, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4690:3:20", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 39804, "src": "4682:11:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40364, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4682:21:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40365, "nodeType": "ExpressionStatement", "src": "4682:21:20"}, {"assignments": [40367], "declarations": [{"constant": false, "id": 40367, "mutability": "mutable", "name": "afterAdd", "nameLocation": "4721:8:20", "nodeType": "VariableDeclaration", "scope": 40418, "src": "4713:16:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40366, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4713:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40371, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40368, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "4732:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40369, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4740:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "4732:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40370, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4732:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "4713:35:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40377, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40373, "name": "afterAdd", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40367, "src": "4765:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40376, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40374, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40291, "src": "4777:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 40375, "name": "addValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40293, "src": "4787:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4777:18:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4765:30:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40372, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "4758:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40378, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4758:38:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40379, "nodeType": "ExpressionStatement", "src": "4758:38:20"}, {"expression": {"arguments": [{"id": 40383, "name": "subValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40295, "src": "4824:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40380, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "4807:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40382, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4815:8:20", "memberName": "subtract", "nodeType": "MemberAccess", "referencedDeclaration": 39830, "src": "4807:16:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40384, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4807:26:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40385, "nodeType": "ExpressionStatement", "src": "4807:26:20"}, {"assignments": [40387], "declarations": [{"constant": false, "id": 40387, "mutability": "mutable", "name": "afterSubtract", "nameLocation": "4851:13:20", "nodeType": "VariableDeclaration", "scope": 40418, "src": "4843:21:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40386, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4843:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40391, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40388, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "4867:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40389, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4875:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "4867:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40390, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4867:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "4843:40:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40399, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40393, "name": "afterSubtract", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40387, "src": "4900:13:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40398, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40396, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40394, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40291, "src": "4917:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 40395, "name": "addValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40293, "src": "4927:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4917:18:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 40397, "name": "subValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40295, "src": "4938:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4917:29:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4900:46:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40392, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "4893:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40400, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4893:54:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40401, "nodeType": "ExpressionStatement", "src": "4893:54:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40407, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40403, "name": "afterAdd", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40367, "src": "5010:8:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40404, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "5022:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40405, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5030:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "5022:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40406, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5022:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5010:31:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40402, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "5003:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40408, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5003:39:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40409, "nodeType": "ExpressionStatement", "src": "5003:39:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40415, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40411, "name": "afterSubtract", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40387, "src": "5059:13:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40412, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "5076:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40413, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5084:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "5076:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40414, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5076:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5059:36:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40410, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "5052:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40416, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5052:44:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40417, "nodeType": "ExpressionStatement", "src": "5052:44:20"}]}, "documentation": {"id": 40289, "nodeType": "StructuredDocumentation", "src": "4150:62:20", "text": "@custom:halmos --function check_sequence_operations_safety"}, "functionSelector": "075dacdd", "implemented": true, "kind": "function", "modifiers": [], "name": "check_sequence_operations_safety", "nameLocation": "4226:32:20", "parameters": {"id": 40296, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40291, "mutability": "mutable", "name": "initial", "nameLocation": "4267:7:20", "nodeType": "VariableDeclaration", "scope": 40419, "src": "4259:15:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40290, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4259:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 40293, "mutability": "mutable", "name": "addValue", "nameLocation": "4284:8:20", "nodeType": "VariableDeclaration", "scope": 40419, "src": "4276:16:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40292, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4276:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 40295, "mutability": "mutable", "name": "subValue", "nameLocation": "4302:8:20", "nodeType": "VariableDeclaration", "scope": 40419, "src": "4294:16:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40294, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4294:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4258:53:20"}, "returnParameters": {"id": 40297, "nodeType": "ParameterList", "parameters": [], "src": "4319:0:20"}, "scope": 40506, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40505, "nodeType": "FunctionDefinition", "src": "5169:472:20", "nodes": [], "body": {"id": 40504, "nodeType": "Block", "src": "5241:400:20", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40434, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40430, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40422, "src": "5261:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40431, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "5272:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40432, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5280:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "5272:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40433, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5272:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5261:30:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40427, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "5251:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40429, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5254:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "5251:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40435, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5251:41:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40436, "nodeType": "ExpressionStatement", "src": "5251:41:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 40446, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 40442, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40440, "name": "times", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40424, "src": "5312:5:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 40441, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5320:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "5312:9:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 40445, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40443, "name": "times", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40424, "src": "5325:5:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "3130", "id": 40444, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5334:2:20", "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}, "src": "5325:11:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "5312:24:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40437, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "5302:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40439, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5305:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "5302:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40447, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5302:35:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40448, "nodeType": "ExpressionStatement", "src": "5302:35:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40458, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40454, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40452, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40422, "src": "5357:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 40453, "name": "times", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40424, "src": "5367:5:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "src": "5357:15:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40455, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "5376:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40456, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5384:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "5376:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40457, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5376:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5357:38:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40449, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "5347:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18311", "typeString": "contract Vm"}}, "id": 40451, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5350:6:20", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16635, "src": "5347:9:20", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40459, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5347:49:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40460, "nodeType": "ExpressionStatement", "src": "5347:49:20"}, {"expression": {"arguments": [{"id": 40464, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40422, "src": "5425:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40461, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "5407:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40463, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5415:9:20", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39710, "src": "5407:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40465, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5407:26:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40466, "nodeType": "ExpressionStatement", "src": "5407:26:20"}, {"body": {"id": 40482, "nodeType": "Block", "src": "5480:44:20", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40477, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "5494:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40479, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5502:9:20", "memberName": "increment", "nodeType": "MemberAccess", "referencedDeclaration": 39733, "src": "5494:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 40480, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5494:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40481, "nodeType": "ExpressionStatement", "src": "5494:19:20"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40473, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40471, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40468, "src": "5464:1:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 40472, "name": "times", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40424, "src": "5468:5:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "src": "5464:9:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40483, "initializationExpression": {"assignments": [40468], "declarations": [{"constant": false, "id": 40468, "mutability": "mutable", "name": "i", "nameLocation": "5457:1:20", "nodeType": "VariableDeclaration", "scope": 40483, "src": "5449:9:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40467, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5449:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40470, "initialValue": {"hexValue": "30", "id": 40469, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5461:1:20", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "5449:13:20"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 40475, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "5475:3:20", "subExpression": {"id": 40474, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40468, "src": "5475:1:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 40476, "nodeType": "ExpressionStatement", "src": "5475:3:20"}, "nodeType": "ForStatement", "src": "5444:80:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40491, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40485, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "5541:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40486, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5549:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "5541:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40487, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5541:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40490, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40488, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40422, "src": "5561:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 40489, "name": "times", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40424, "src": "5571:5:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "src": "5561:15:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5541:35:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40484, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "5534:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40492, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5534:43:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40493, "nodeType": "ExpressionStatement", "src": "5534:43:20"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40501, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40495, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "5594:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40496, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5602:6:20", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39673, "src": "5594:14:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40497, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5594:16:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40498, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39842, "src": "5614:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39831", "typeString": "contract Counter"}}, "id": 40499, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5622:9:20", "memberName": "MAX_VALUE", "nodeType": "MemberAccess", "referencedDeclaration": 39676, "src": "5614:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40500, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5614:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5594:39:20", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 40494, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "5587:6:20", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 40502, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5587:47:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40503, "nodeType": "ExpressionStatement", "src": "5587:47:20"}]}, "documentation": {"id": 40420, "nodeType": "StructuredDocumentation", "src": "5109:55:20", "text": "@custom:halmos --function check_multiple_increments"}, "functionSelector": "95534832", "implemented": true, "kind": "function", "modifiers": [], "name": "check_multiple_increments", "nameLocation": "5178:25:20", "parameters": {"id": 40425, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40422, "mutability": "mutable", "name": "initial", "nameLocation": "5212:7:20", "nodeType": "VariableDeclaration", "scope": 40505, "src": "5204:15:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40421, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5204:7:20", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 40424, "mutability": "mutable", "name": "times", "nameLocation": "5227:5:20", "nodeType": "VariableDeclaration", "scope": 40505, "src": "5221:11:20", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 40423, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "5221:5:20", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "5203:30:20"}, "returnParameters": {"id": 40426, "nodeType": "ParameterList", "parameters": [], "src": "5241:0:20"}, "scope": 40506, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "abstract": false, "baseContracts": [{"baseName": {"id": 39838, "name": "Test", "nameLocations": ["181:4:20"], "nodeType": "IdentifierPath", "referencedDeclaration": 13183, "src": "181:4:20"}, "id": 39839, "nodeType": "InheritanceSpecifier", "src": "181:4:20"}], "canonicalName": "CounterHalmosTest", "contractDependencies": [39831], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [40506, 13183, 13129, 7166, 6765, 5970, 3880, 2918, 50, 47], "name": "CounterHalmosTest", "nameLocation": "160:17:20", "scope": 40507, "usedErrors": [], "usedEvents": [85, 89, 93, 97, 101, 105, 109, 113, 119, 125, 133, 141, 147, 153, 159, 165, 170, 175, 180, 187, 194, 201]}], "license": "UNLICENSED"}, "id": 20}