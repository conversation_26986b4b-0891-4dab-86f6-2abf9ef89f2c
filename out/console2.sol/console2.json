{"abi": [], "bytecode": {"object": "0x", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "linkReferences": {}}, "ast": {"absolutePath": "lib/forge-std/src/console2.sol", "id": 26473, "exportedSymbols": {"console2": [26468]}, "nodeType": "SourceUnit", "src": "32:85:17", "nodes": [{"id": 26470, "nodeType": "PragmaDirective", "src": "32:32:17", "nodes": [], "literals": ["solidity", ">=", "0.4", ".22", "<", "0.9", ".0"]}, {"id": 26472, "nodeType": "ImportDirective", "src": "66:50:17", "nodes": [], "absolutePath": "lib/forge-std/src/console.sol", "file": "./console.sol", "nameLocation": "-1:-1:-1", "scope": 26473, "sourceUnit": 26469, "symbolAliases": [{"foreign": {"id": 26471, "name": "console", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26468, "src": "74:7:17", "typeDescriptions": {}}, "local": "console2", "nameLocation": "-1:-1:-1"}], "unitAlias": ""}], "license": "MIT"}, "id": 17}