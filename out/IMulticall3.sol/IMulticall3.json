{"abi": [{"type": "function", "name": "aggregate", "inputs": [{"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "returnData", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "payable"}, {"type": "function", "name": "aggregate3", "inputs": [{"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call3[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "allowFailure", "type": "bool", "internalType": "bool"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "returnData", "type": "tuple[]", "internalType": "struct IMulticall3.Result[]", "components": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}, {"type": "function", "name": "aggregate3Value", "inputs": [{"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call3Value[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "allowFailure", "type": "bool", "internalType": "bool"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "returnData", "type": "tuple[]", "internalType": "struct IMulticall3.Result[]", "components": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}, {"type": "function", "name": "blockAndAggregate", "inputs": [{"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "blockHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "returnData", "type": "tuple[]", "internalType": "struct IMulticall3.Result[]", "components": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}, {"type": "function", "name": "getBasefee", "inputs": [], "outputs": [{"name": "basefee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getBlockHash", "inputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "blockHash", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getBlockNumber", "inputs": [], "outputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "get<PERSON>hainId", "inputs": [], "outputs": [{"name": "chainid", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentBlockCoinbase", "inputs": [], "outputs": [{"name": "coinbase", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentBlockDifficulty", "inputs": [], "outputs": [{"name": "difficulty", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentBlockGasLimit", "inputs": [], "outputs": [{"name": "gaslimit", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentBlockTimestamp", "inputs": [], "outputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getEthBalance", "inputs": [{"name": "addr", "type": "address", "internalType": "address"}], "outputs": [{"name": "balance", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getLastBlockHash", "inputs": [], "outputs": [{"name": "blockHash", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "tryAggregate", "inputs": [{"name": "requireSuccess", "type": "bool", "internalType": "bool"}, {"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "returnData", "type": "tuple[]", "internalType": "struct IMulticall3.Result[]", "components": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}, {"type": "function", "name": "tryBlockAndAggregate", "inputs": [{"name": "requireSuccess", "type": "bool", "internalType": "bool"}, {"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "blockHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "returnData", "type": "tuple[]", "internalType": "struct IMulticall3.Result[]", "components": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"aggregate((address,bytes)[])": "252dba42", "aggregate3((address,bool,bytes)[])": "82ad56cb", "aggregate3Value((address,bool,uint256,bytes)[])": "174dea71", "blockAndAggregate((address,bytes)[])": "c3077fa9", "getBasefee()": "3e64a696", "getBlockHash(uint256)": "ee82ac5e", "getBlockNumber()": "42cbb15c", "getChainId()": "3408e470", "getCurrentBlockCoinbase()": "a8b0574e", "getCurrentBlockDifficulty()": "72425d9d", "getCurrentBlockGasLimit()": "86d516e8", "getCurrentBlockTimestamp()": "0f28c97d", "getEthBalance(address)": "4d2301cc", "getLastBlockHash()": "27e86d6e", "tryAggregate(bool,(address,bytes)[])": "bce38bd7", "tryBlockAndAggregate(bool,(address,bytes)[])": "399542e9"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"aggregate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"},{\"internalType\":\"bytes[]\",\"name\":\"returnData\",\"type\":\"bytes[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"allowFailure\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call3[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"aggregate3\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"returnData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Result[]\",\"name\":\"returnData\",\"type\":\"tuple[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"allowFailure\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call3Value[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"aggregate3Value\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"returnData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Result[]\",\"name\":\"returnData\",\"type\":\"tuple[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"blockAndAggregate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"blockHash\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"returnData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Result[]\",\"name\":\"returnData\",\"type\":\"tuple[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBasefee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"basefee\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"}],\"name\":\"getBlockHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"blockHash\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBlockNumber\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getChainId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"chainid\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentBlockCoinbase\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"coinbase\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentBlockDifficulty\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"difficulty\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentBlockGasLimit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"gaslimit\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentBlockTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"}],\"name\":\"getEthBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getLastBlockHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"blockHash\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"requireSuccess\",\"type\":\"bool\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"tryAggregate\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"returnData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Result[]\",\"name\":\"returnData\",\"type\":\"tuple[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"requireSuccess\",\"type\":\"bool\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"tryBlockAndAggregate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"blockHash\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"returnData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Result[]\",\"name\":\"returnData\",\"type\":\"tuple[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/interfaces/IMulticall3.sol\":\"IMulticall3\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct IMulticall3.Call[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "aggregate", "outputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "bytes[]", "name": "returnData", "type": "bytes[]"}]}, {"inputs": [{"internalType": "struct IMulticall3.Call3[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bool", "name": "allowFailure", "type": "bool"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "aggregate3", "outputs": [{"internalType": "struct IMulticall3.Result[]", "name": "returnData", "type": "tuple[]", "components": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}]}]}, {"inputs": [{"internalType": "struct IMulticall3.Call3Value[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bool", "name": "allowFailure", "type": "bool"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "aggregate3Value", "outputs": [{"internalType": "struct IMulticall3.Result[]", "name": "returnData", "type": "tuple[]", "components": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}]}]}, {"inputs": [{"internalType": "struct IMulticall3.Call[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "blockAndAggregate", "outputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "bytes32", "name": "blockHash", "type": "bytes32"}, {"internalType": "struct IMulticall3.Result[]", "name": "returnData", "type": "tuple[]", "components": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBasefee", "outputs": [{"internalType": "uint256", "name": "basefee", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getBlockHash", "outputs": [{"internalType": "bytes32", "name": "blockHash", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBlockNumber", "outputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "get<PERSON>hainId", "outputs": [{"internalType": "uint256", "name": "chainid", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentBlockCoinbase", "outputs": [{"internalType": "address", "name": "coinbase", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentBlockDifficulty", "outputs": [{"internalType": "uint256", "name": "difficulty", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentBlockGasLimit", "outputs": [{"internalType": "uint256", "name": "gaslimit", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentBlockTimestamp", "outputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getEthBalance", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getLastBlockHash", "outputs": [{"internalType": "bytes32", "name": "blockHash", "type": "bytes32"}]}, {"inputs": [{"internalType": "bool", "name": "requireSuccess", "type": "bool"}, {"internalType": "struct IMulticall3.Call[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "tryAggregate", "outputs": [{"internalType": "struct IMulticall3.Result[]", "name": "returnData", "type": "tuple[]", "components": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}]}]}, {"inputs": [{"internalType": "bool", "name": "requireSuccess", "type": "bool"}, {"internalType": "struct IMulticall3.Call[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "tryBlockAndAggregate", "outputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "bytes32", "name": "blockHash", "type": "bytes32"}, {"internalType": "struct IMulticall3.Result[]", "name": "returnData", "type": "tuple[]", "components": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}]}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/interfaces/IMulticall3.sol": "IMulticall3"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/forge-std/src/interfaces/IMulticall3.sol", "id": 26636, "exportedSymbols": {"IMulticall3": [26635]}, "nodeType": "SourceUnit", "src": "32:2153:18", "nodes": [{"id": 26474, "nodeType": "PragmaDirective", "src": "32:31:18", "nodes": [], "literals": ["solidity", ">=", "0.6", ".2", "<", "0.9", ".0"]}, {"id": 26475, "nodeType": "PragmaDirective", "src": "65:33:18", "nodes": [], "literals": ["experimental", "ABIEncoderV2"]}, {"id": 26635, "nodeType": "ContractDefinition", "src": "100:2084:18", "nodes": [{"id": 26480, "nodeType": "StructDefinition", "src": "128:67:18", "nodes": [], "canonicalName": "IMulticall3.Call", "members": [{"constant": false, "id": 26477, "mutability": "mutable", "name": "target", "nameLocation": "158:6:18", "nodeType": "VariableDeclaration", "scope": 26480, "src": "150:14:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26476, "name": "address", "nodeType": "ElementaryTypeName", "src": "150:7:18", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 26479, "mutability": "mutable", "name": "callData", "nameLocation": "180:8:18", "nodeType": "VariableDeclaration", "scope": 26480, "src": "174:14:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}, "typeName": {"id": 26478, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "174:5:18", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "name": "Call", "nameLocation": "135:4:18", "scope": 26635, "visibility": "public"}, {"id": 26487, "nodeType": "StructDefinition", "src": "201:95:18", "nodes": [], "canonicalName": "IMulticall3.Call3", "members": [{"constant": false, "id": 26482, "mutability": "mutable", "name": "target", "nameLocation": "232:6:18", "nodeType": "VariableDeclaration", "scope": 26487, "src": "224:14:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26481, "name": "address", "nodeType": "ElementaryTypeName", "src": "224:7:18", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 26484, "mutability": "mutable", "name": "allowFailure", "nameLocation": "253:12:18", "nodeType": "VariableDeclaration", "scope": 26487, "src": "248:17:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 26483, "name": "bool", "nodeType": "ElementaryTypeName", "src": "248:4:18", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 26486, "mutability": "mutable", "name": "callData", "nameLocation": "281:8:18", "nodeType": "VariableDeclaration", "scope": 26487, "src": "275:14:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}, "typeName": {"id": 26485, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "275:5:18", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "name": "Call3", "nameLocation": "208:5:18", "scope": 26635, "visibility": "public"}, {"id": 26496, "nodeType": "StructDefinition", "src": "302:123:18", "nodes": [], "canonicalName": "IMulticall3.Call3Value", "members": [{"constant": false, "id": 26489, "mutability": "mutable", "name": "target", "nameLocation": "338:6:18", "nodeType": "VariableDeclaration", "scope": 26496, "src": "330:14:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26488, "name": "address", "nodeType": "ElementaryTypeName", "src": "330:7:18", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 26491, "mutability": "mutable", "name": "allowFailure", "nameLocation": "359:12:18", "nodeType": "VariableDeclaration", "scope": 26496, "src": "354:17:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 26490, "name": "bool", "nodeType": "ElementaryTypeName", "src": "354:4:18", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 26493, "mutability": "mutable", "name": "value", "nameLocation": "389:5:18", "nodeType": "VariableDeclaration", "scope": 26496, "src": "381:13:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26492, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "381:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 26495, "mutability": "mutable", "name": "callData", "nameLocation": "410:8:18", "nodeType": "VariableDeclaration", "scope": 26496, "src": "404:14:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}, "typeName": {"id": 26494, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "404:5:18", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "name": "Call3Value", "nameLocation": "309:10:18", "scope": 26635, "visibility": "public"}, {"id": 26501, "nodeType": "StructDefinition", "src": "431:69:18", "nodes": [], "canonicalName": "IMulticall3.Result", "members": [{"constant": false, "id": 26498, "mutability": "mutable", "name": "success", "nameLocation": "460:7:18", "nodeType": "VariableDeclaration", "scope": 26501, "src": "455:12:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 26497, "name": "bool", "nodeType": "ElementaryTypeName", "src": "455:4:18", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 26500, "mutability": "mutable", "name": "returnData", "nameLocation": "483:10:18", "nodeType": "VariableDeclaration", "scope": 26501, "src": "477:16:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}, "typeName": {"id": 26499, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "477:5:18", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "name": "Result", "nameLocation": "438:6:18", "scope": 26635, "visibility": "public"}, {"id": 26513, "nodeType": "FunctionDefinition", "src": "506:140:18", "nodes": [], "functionSelector": "252dba42", "implemented": false, "kind": "function", "modifiers": [], "name": "aggregate", "nameLocation": "515:9:18", "parameters": {"id": 26506, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26505, "mutability": "mutable", "name": "calls", "nameLocation": "541:5:18", "nodeType": "VariableDeclaration", "scope": 26513, "src": "525:21:18", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_calldata_ptr_$dyn_calldata_ptr", "typeString": "struct IMulticall3.Call[]"}, "typeName": {"baseType": {"id": 26503, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26502, "name": "Call", "nameLocations": ["525:4:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26480, "src": "525:4:18"}, "referencedDeclaration": 26480, "src": "525:4:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Call_$26480_storage_ptr", "typeString": "struct IMulticall3.Call"}}, "id": 26504, "nodeType": "ArrayTypeName", "src": "525:6:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Call[]"}}, "visibility": "internal"}], "src": "524:23:18"}, "returnParameters": {"id": 26512, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26508, "mutability": "mutable", "name": "blockNumber", "nameLocation": "606:11:18", "nodeType": "VariableDeclaration", "scope": 26513, "src": "598:19:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26507, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "598:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 26511, "mutability": "mutable", "name": "returnData", "nameLocation": "634:10:18", "nodeType": "VariableDeclaration", "scope": 26513, "src": "619:25:18", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 26509, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "619:5:18", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 26510, "nodeType": "ArrayTypeName", "src": "619:7:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "src": "597:48:18"}, "scope": 26635, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 26524, "nodeType": "FunctionDefinition", "src": "652:98:18", "nodes": [], "functionSelector": "82ad56cb", "implemented": false, "kind": "function", "modifiers": [], "name": "aggregate3", "nameLocation": "661:10:18", "parameters": {"id": 26518, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26517, "mutability": "mutable", "name": "calls", "nameLocation": "689:5:18", "nodeType": "VariableDeclaration", "scope": 26524, "src": "672:22:18", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call3_$26487_calldata_ptr_$dyn_calldata_ptr", "typeString": "struct IMulticall3.Call3[]"}, "typeName": {"baseType": {"id": 26515, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26514, "name": "Call3", "nameLocations": ["672:5:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26487, "src": "672:5:18"}, "referencedDeclaration": 26487, "src": "672:5:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Call3_$26487_storage_ptr", "typeString": "struct IMulticall3.Call3"}}, "id": 26516, "nodeType": "ArrayTypeName", "src": "672:7:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call3_$26487_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Call3[]"}}, "visibility": "internal"}], "src": "671:24:18"}, "returnParameters": {"id": 26523, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26522, "mutability": "mutable", "name": "returnData", "nameLocation": "738:10:18", "nodeType": "VariableDeclaration", "scope": 26524, "src": "722:26:18", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Result_$26501_memory_ptr_$dyn_memory_ptr", "typeString": "struct IMulticall3.Result[]"}, "typeName": {"baseType": {"id": 26520, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26519, "name": "Result", "nameLocations": ["722:6:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26501, "src": "722:6:18"}, "referencedDeclaration": 26501, "src": "722:6:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Result_$26501_storage_ptr", "typeString": "struct IMulticall3.Result"}}, "id": 26521, "nodeType": "ArrayTypeName", "src": "722:8:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Result_$26501_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Result[]"}}, "visibility": "internal"}], "src": "721:28:18"}, "scope": 26635, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 26535, "nodeType": "FunctionDefinition", "src": "756:108:18", "nodes": [], "functionSelector": "174dea71", "implemented": false, "kind": "function", "modifiers": [], "name": "aggregate3Value", "nameLocation": "765:15:18", "parameters": {"id": 26529, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26528, "mutability": "mutable", "name": "calls", "nameLocation": "803:5:18", "nodeType": "VariableDeclaration", "scope": 26535, "src": "781:27:18", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call3Value_$26496_calldata_ptr_$dyn_calldata_ptr", "typeString": "struct IMulticall3.Call3Value[]"}, "typeName": {"baseType": {"id": 26526, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26525, "name": "Call3Value", "nameLocations": ["781:10:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26496, "src": "781:10:18"}, "referencedDeclaration": 26496, "src": "781:10:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Call3Value_$26496_storage_ptr", "typeString": "struct IMulticall3.Call3Value"}}, "id": 26527, "nodeType": "ArrayTypeName", "src": "781:12:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call3Value_$26496_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Call3Value[]"}}, "visibility": "internal"}], "src": "780:29:18"}, "returnParameters": {"id": 26534, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26533, "mutability": "mutable", "name": "returnData", "nameLocation": "852:10:18", "nodeType": "VariableDeclaration", "scope": 26535, "src": "836:26:18", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Result_$26501_memory_ptr_$dyn_memory_ptr", "typeString": "struct IMulticall3.Result[]"}, "typeName": {"baseType": {"id": 26531, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26530, "name": "Result", "nameLocations": ["836:6:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26501, "src": "836:6:18"}, "referencedDeclaration": 26501, "src": "836:6:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Result_$26501_storage_ptr", "typeString": "struct IMulticall3.Result"}}, "id": 26532, "nodeType": "ArrayTypeName", "src": "836:8:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Result_$26501_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Result[]"}}, "visibility": "internal"}], "src": "835:28:18"}, "scope": 26635, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 26550, "nodeType": "FunctionDefinition", "src": "870:168:18", "nodes": [], "functionSelector": "c3077fa9", "implemented": false, "kind": "function", "modifiers": [], "name": "blockAndAggregate", "nameLocation": "879:17:18", "parameters": {"id": 26540, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26539, "mutability": "mutable", "name": "calls", "nameLocation": "913:5:18", "nodeType": "VariableDeclaration", "scope": 26550, "src": "897:21:18", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_calldata_ptr_$dyn_calldata_ptr", "typeString": "struct IMulticall3.Call[]"}, "typeName": {"baseType": {"id": 26537, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26536, "name": "Call", "nameLocations": ["897:4:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26480, "src": "897:4:18"}, "referencedDeclaration": 26480, "src": "897:4:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Call_$26480_storage_ptr", "typeString": "struct IMulticall3.Call"}}, "id": 26538, "nodeType": "ArrayTypeName", "src": "897:6:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Call[]"}}, "visibility": "internal"}], "src": "896:23:18"}, "returnParameters": {"id": 26549, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26542, "mutability": "mutable", "name": "blockNumber", "nameLocation": "978:11:18", "nodeType": "VariableDeclaration", "scope": 26550, "src": "970:19:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26541, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "970:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 26544, "mutability": "mutable", "name": "blockHash", "nameLocation": "999:9:18", "nodeType": "VariableDeclaration", "scope": 26550, "src": "991:17:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 26543, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "991:7:18", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 26548, "mutability": "mutable", "name": "returnData", "nameLocation": "1026:10:18", "nodeType": "VariableDeclaration", "scope": 26550, "src": "1010:26:18", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Result_$26501_memory_ptr_$dyn_memory_ptr", "typeString": "struct IMulticall3.Result[]"}, "typeName": {"baseType": {"id": 26546, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26545, "name": "Result", "nameLocations": ["1010:6:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26501, "src": "1010:6:18"}, "referencedDeclaration": 26501, "src": "1010:6:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Result_$26501_storage_ptr", "typeString": "struct IMulticall3.Result"}}, "id": 26547, "nodeType": "ArrayTypeName", "src": "1010:8:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Result_$26501_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Result[]"}}, "visibility": "internal"}], "src": "969:68:18"}, "scope": 26635, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 26555, "nodeType": "FunctionDefinition", "src": "1044:62:18", "nodes": [], "functionSelector": "3e64a696", "implemented": false, "kind": "function", "modifiers": [], "name": "getBasefee", "nameLocation": "1053:10:18", "parameters": {"id": 26551, "nodeType": "ParameterList", "parameters": [], "src": "1063:2:18"}, "returnParameters": {"id": 26554, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26553, "mutability": "mutable", "name": "basefee", "nameLocation": "1097:7:18", "nodeType": "VariableDeclaration", "scope": 26555, "src": "1089:15:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26552, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1089:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1088:17:18"}, "scope": 26635, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 26562, "nodeType": "FunctionDefinition", "src": "1112:85:18", "nodes": [], "functionSelector": "ee82ac5e", "implemented": false, "kind": "function", "modifiers": [], "name": "getBlockHash", "nameLocation": "1121:12:18", "parameters": {"id": 26558, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26557, "mutability": "mutable", "name": "blockNumber", "nameLocation": "1142:11:18", "nodeType": "VariableDeclaration", "scope": 26562, "src": "1134:19:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26556, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1134:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1133:21:18"}, "returnParameters": {"id": 26561, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26560, "mutability": "mutable", "name": "blockHash", "nameLocation": "1186:9:18", "nodeType": "VariableDeclaration", "scope": 26562, "src": "1178:17:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 26559, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1178:7:18", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "1177:19:18"}, "scope": 26635, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 26567, "nodeType": "FunctionDefinition", "src": "1203:70:18", "nodes": [], "functionSelector": "42cbb15c", "implemented": false, "kind": "function", "modifiers": [], "name": "getBlockNumber", "nameLocation": "1212:14:18", "parameters": {"id": 26563, "nodeType": "ParameterList", "parameters": [], "src": "1226:2:18"}, "returnParameters": {"id": 26566, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26565, "mutability": "mutable", "name": "blockNumber", "nameLocation": "1260:11:18", "nodeType": "VariableDeclaration", "scope": 26567, "src": "1252:19:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26564, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1252:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1251:21:18"}, "scope": 26635, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 26572, "nodeType": "FunctionDefinition", "src": "1279:62:18", "nodes": [], "functionSelector": "3408e470", "implemented": false, "kind": "function", "modifiers": [], "name": "get<PERSON>hainId", "nameLocation": "1288:10:18", "parameters": {"id": 26568, "nodeType": "ParameterList", "parameters": [], "src": "1298:2:18"}, "returnParameters": {"id": 26571, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26570, "mutability": "mutable", "name": "chainid", "nameLocation": "1332:7:18", "nodeType": "VariableDeclaration", "scope": 26572, "src": "1324:15:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26569, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1324:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1323:17:18"}, "scope": 26635, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 26577, "nodeType": "FunctionDefinition", "src": "1347:76:18", "nodes": [], "functionSelector": "a8b0574e", "implemented": false, "kind": "function", "modifiers": [], "name": "getCurrentBlockCoinbase", "nameLocation": "1356:23:18", "parameters": {"id": 26573, "nodeType": "ParameterList", "parameters": [], "src": "1379:2:18"}, "returnParameters": {"id": 26576, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26575, "mutability": "mutable", "name": "coinbase", "nameLocation": "1413:8:18", "nodeType": "VariableDeclaration", "scope": 26577, "src": "1405:16:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26574, "name": "address", "nodeType": "ElementaryTypeName", "src": "1405:7:18", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1404:18:18"}, "scope": 26635, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 26582, "nodeType": "FunctionDefinition", "src": "1429:80:18", "nodes": [], "functionSelector": "72425d9d", "implemented": false, "kind": "function", "modifiers": [], "name": "getCurrentBlockDifficulty", "nameLocation": "1438:25:18", "parameters": {"id": 26578, "nodeType": "ParameterList", "parameters": [], "src": "1463:2:18"}, "returnParameters": {"id": 26581, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26580, "mutability": "mutable", "name": "difficulty", "nameLocation": "1497:10:18", "nodeType": "VariableDeclaration", "scope": 26582, "src": "1489:18:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26579, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1489:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1488:20:18"}, "scope": 26635, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 26587, "nodeType": "FunctionDefinition", "src": "1515:76:18", "nodes": [], "functionSelector": "86d516e8", "implemented": false, "kind": "function", "modifiers": [], "name": "getCurrentBlockGasLimit", "nameLocation": "1524:23:18", "parameters": {"id": 26583, "nodeType": "ParameterList", "parameters": [], "src": "1547:2:18"}, "returnParameters": {"id": 26586, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26585, "mutability": "mutable", "name": "gaslimit", "nameLocation": "1581:8:18", "nodeType": "VariableDeclaration", "scope": 26587, "src": "1573:16:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26584, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1573:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1572:18:18"}, "scope": 26635, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 26592, "nodeType": "FunctionDefinition", "src": "1597:78:18", "nodes": [], "functionSelector": "0f28c97d", "implemented": false, "kind": "function", "modifiers": [], "name": "getCurrentBlockTimestamp", "nameLocation": "1606:24:18", "parameters": {"id": 26588, "nodeType": "ParameterList", "parameters": [], "src": "1630:2:18"}, "returnParameters": {"id": 26591, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26590, "mutability": "mutable", "name": "timestamp", "nameLocation": "1664:9:18", "nodeType": "VariableDeclaration", "scope": 26592, "src": "1656:17:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26589, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1656:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1655:19:18"}, "scope": 26635, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 26599, "nodeType": "FunctionDefinition", "src": "1681:77:18", "nodes": [], "functionSelector": "4d2301cc", "implemented": false, "kind": "function", "modifiers": [], "name": "getEthBalance", "nameLocation": "1690:13:18", "parameters": {"id": 26595, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26594, "mutability": "mutable", "name": "addr", "nameLocation": "1712:4:18", "nodeType": "VariableDeclaration", "scope": 26599, "src": "1704:12:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26593, "name": "address", "nodeType": "ElementaryTypeName", "src": "1704:7:18", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1703:14:18"}, "returnParameters": {"id": 26598, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26597, "mutability": "mutable", "name": "balance", "nameLocation": "1749:7:18", "nodeType": "VariableDeclaration", "scope": 26599, "src": "1741:15:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26596, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1741:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1740:17:18"}, "scope": 26635, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 26604, "nodeType": "FunctionDefinition", "src": "1764:70:18", "nodes": [], "functionSelector": "27e86d6e", "implemented": false, "kind": "function", "modifiers": [], "name": "getLastBlockHash", "nameLocation": "1773:16:18", "parameters": {"id": 26600, "nodeType": "ParameterList", "parameters": [], "src": "1789:2:18"}, "returnParameters": {"id": 26603, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26602, "mutability": "mutable", "name": "blockHash", "nameLocation": "1823:9:18", "nodeType": "VariableDeclaration", "scope": 26604, "src": "1815:17:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 26601, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1815:7:18", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "1814:19:18"}, "scope": 26635, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 26617, "nodeType": "FunctionDefinition", "src": "1840:144:18", "nodes": [], "functionSelector": "bce38bd7", "implemented": false, "kind": "function", "modifiers": [], "name": "tryAggregate", "nameLocation": "1849:12:18", "parameters": {"id": 26611, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26606, "mutability": "mutable", "name": "requireSuccess", "nameLocation": "1867:14:18", "nodeType": "VariableDeclaration", "scope": 26617, "src": "1862:19:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 26605, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1862:4:18", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 26610, "mutability": "mutable", "name": "calls", "nameLocation": "1899:5:18", "nodeType": "VariableDeclaration", "scope": 26617, "src": "1883:21:18", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_calldata_ptr_$dyn_calldata_ptr", "typeString": "struct IMulticall3.Call[]"}, "typeName": {"baseType": {"id": 26608, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26607, "name": "Call", "nameLocations": ["1883:4:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26480, "src": "1883:4:18"}, "referencedDeclaration": 26480, "src": "1883:4:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Call_$26480_storage_ptr", "typeString": "struct IMulticall3.Call"}}, "id": 26609, "nodeType": "ArrayTypeName", "src": "1883:6:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Call[]"}}, "visibility": "internal"}], "src": "1861:44:18"}, "returnParameters": {"id": 26616, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26615, "mutability": "mutable", "name": "returnData", "nameLocation": "1972:10:18", "nodeType": "VariableDeclaration", "scope": 26617, "src": "1956:26:18", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Result_$26501_memory_ptr_$dyn_memory_ptr", "typeString": "struct IMulticall3.Result[]"}, "typeName": {"baseType": {"id": 26613, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26612, "name": "Result", "nameLocations": ["1956:6:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26501, "src": "1956:6:18"}, "referencedDeclaration": 26501, "src": "1956:6:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Result_$26501_storage_ptr", "typeString": "struct IMulticall3.Result"}}, "id": 26614, "nodeType": "ArrayTypeName", "src": "1956:8:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Result_$26501_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Result[]"}}, "visibility": "internal"}], "src": "1955:28:18"}, "scope": 26635, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 26634, "nodeType": "FunctionDefinition", "src": "1990:192:18", "nodes": [], "functionSelector": "399542e9", "implemented": false, "kind": "function", "modifiers": [], "name": "tryBlockAndAggregate", "nameLocation": "1999:20:18", "parameters": {"id": 26624, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26619, "mutability": "mutable", "name": "requireSuccess", "nameLocation": "2025:14:18", "nodeType": "VariableDeclaration", "scope": 26634, "src": "2020:19:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 26618, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2020:4:18", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 26623, "mutability": "mutable", "name": "calls", "nameLocation": "2057:5:18", "nodeType": "VariableDeclaration", "scope": 26634, "src": "2041:21:18", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_calldata_ptr_$dyn_calldata_ptr", "typeString": "struct IMulticall3.Call[]"}, "typeName": {"baseType": {"id": 26621, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26620, "name": "Call", "nameLocations": ["2041:4:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26480, "src": "2041:4:18"}, "referencedDeclaration": 26480, "src": "2041:4:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Call_$26480_storage_ptr", "typeString": "struct IMulticall3.Call"}}, "id": 26622, "nodeType": "ArrayTypeName", "src": "2041:6:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Call[]"}}, "visibility": "internal"}], "src": "2019:44:18"}, "returnParameters": {"id": 26633, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26626, "mutability": "mutable", "name": "blockNumber", "nameLocation": "2122:11:18", "nodeType": "VariableDeclaration", "scope": 26634, "src": "2114:19:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26625, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2114:7:18", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 26628, "mutability": "mutable", "name": "blockHash", "nameLocation": "2143:9:18", "nodeType": "VariableDeclaration", "scope": 26634, "src": "2135:17:18", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 26627, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2135:7:18", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 26632, "mutability": "mutable", "name": "returnData", "nameLocation": "2170:10:18", "nodeType": "VariableDeclaration", "scope": 26634, "src": "2154:26:18", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Result_$26501_memory_ptr_$dyn_memory_ptr", "typeString": "struct IMulticall3.Result[]"}, "typeName": {"baseType": {"id": 26630, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 26629, "name": "Result", "nameLocations": ["2154:6:18"], "nodeType": "IdentifierPath", "referencedDeclaration": 26501, "src": "2154:6:18"}, "referencedDeclaration": 26501, "src": "2154:6:18", "typeDescriptions": {"typeIdentifier": "t_struct$_Result_$26501_storage_ptr", "typeString": "struct IMulticall3.Result"}}, "id": 26631, "nodeType": "ArrayTypeName", "src": "2154:8:18", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Result_$26501_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Result[]"}}, "visibility": "internal"}], "src": "2113:68:18"}, "scope": 26635, "stateMutability": "payable", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IMulticall3", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "linearizedBaseContracts": [26635], "name": "IMulticall3", "nameLocation": "110:11:18", "scope": 26636, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 18}