{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "counter", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract Counter"}], "stateMutability": "view"}, {"type": "function", "name": "run", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "155:223:20:-:0;;;3166:4:3;3126:44;;;;;;;;;;;;;;;;;;;;873:4:1;849:28;;;;;;;;;;;;;;;;;;;;155:223:20;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "155:223:20:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;223:26;;;:::i;:::-;;194:22;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;255:121;;;:::i;:::-;;849:28:1;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;223:26:20;:::o;194:22::-;;;;;;;;;;;;;:::o;255:121::-;336:42:0;287:17:20;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;327:13;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;317:7;;:23;;;;;;;;;;;;;;;;;;336:42:0;351:16:20;;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;255:121::o;849:28:1:-;;;;;;;;;;;;;:::o;-1:-1:-1:-;;;;;;;;:::o;7:126:24:-;44:7;84:42;77:5;73:54;62:65;;7:126;;;:::o;139:60::-;167:3;188:5;181:12;;139:60;;;:::o;205:142::-;255:9;288:53;306:34;315:24;333:5;315:24;:::i;:::-;306:34;:::i;:::-;288:53;:::i;:::-;275:66;;205:142;;;:::o;353:126::-;403:9;436:37;467:5;436:37;:::i;:::-;423:50;;353:126;;;:::o;485:143::-;552:9;585:37;616:5;585:37;:::i;:::-;572:50;;485:143;;;:::o;634:165::-;738:54;786:5;738:54;:::i;:::-;733:3;726:67;634:165;;:::o;805:256::-;915:4;953:2;942:9;938:18;930:26;;966:88;1051:1;1040:9;1036:17;1027:6;966:88;:::i;:::-;805:256;;;;:::o;1067:90::-;1101:7;1144:5;1137:13;1130:21;1119:32;;1067:90;;;:::o;1163:109::-;1244:21;1259:5;1244:21;:::i;:::-;1239:3;1232:34;1163:109;;:::o;1278:210::-;1365:4;1403:2;1392:9;1388:18;1380:26;;1416:65;1478:1;1467:9;1463:17;1454:6;1416:65;:::i;:::-;1278:210;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47", "counter()": "61bc221a", "run()": "c0406226", "setUp()": "0a9254e4"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"counter\",\"outputs\":[{\"internalType\":\"contract Counter\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"run\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"script/Counter.s.sol\":\"CounterScript\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"script/Counter.s.sol\":{\"keccak256\":\"0x1e29ed50b3ea68f3b88913c4f075db1a3b345483acc5d0bb18493c815a1d3392\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://1f1482b3e6566f14e11ad41a2d5d3efed819add8f2d6ebebcb7d379336df1cbf\",\"dweb:/ipfs/QmVPSavJipPhB1ZncPdfev998RNJ115XzLnZaXp7evB6WZ\"]},\"src/Counter.sol\":{\"keccak256\":\"0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e\",\"dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "counter", "outputs": [{"internalType": "contract Counter", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "run"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setUp"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"script/Counter.s.sol": "CounterScript"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "script/Counter.s.sol": {"keccak256": "0x1e29ed50b3ea68f3b88913c4f075db1a3b345483acc5d0bb18493c815a1d3392", "urls": ["bzz-raw://1f1482b3e6566f14e11ad41a2d5d3efed819add8f2d6ebebcb7d379336df1cbf", "dweb:/ipfs/QmVPSavJipPhB1ZncPdfev998RNJ115XzLnZaXp7evB6WZ"], "license": "UNLICENSED"}, "src/Counter.sol": {"keccak256": "0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c", "urls": ["bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e", "dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2"], "license": "UNLICENSED"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 46, "contract": "script/Counter.s.sol:CounterScript", "label": "stdstore", "offset": 0, "slot": "0", "type": "t_struct(StdStorage)8331_storage"}, {"astId": 2983, "contract": "script/Counter.s.sol:CounterScript", "label": "stdChainsInitialized", "offset": 0, "slot": "8", "type": "t_bool"}, {"astId": 3004, "contract": "script/Counter.s.sol:CounterScript", "label": "chains", "offset": 0, "slot": "9", "type": "t_mapping(t_string_memory_ptr,t_struct(Chain)2999_storage)"}, {"astId": 3008, "contract": "script/Counter.s.sol:CounterScript", "label": "defaultRpcUrls", "offset": 0, "slot": "10", "type": "t_mapping(t_string_memory_ptr,t_string_storage)"}, {"astId": 3012, "contract": "script/Counter.s.sol:CounterScript", "label": "idToAlias", "offset": 0, "slot": "11", "type": "t_mapping(t_uint256,t_string_storage)"}, {"astId": 3015, "contract": "script/Counter.s.sol:CounterScript", "label": "fallbackToDefaultRpcUrls", "offset": 0, "slot": "12", "type": "t_bool"}, {"astId": 3953, "contract": "script/Counter.s.sol:CounterScript", "label": "gasMeteringOff", "offset": 1, "slot": "12", "type": "t_bool"}, {"astId": 99, "contract": "script/Counter.s.sol:CounterScript", "label": "IS_SCRIPT", "offset": 2, "slot": "12", "type": "t_bool"}, {"astId": 39721, "contract": "script/Counter.s.sol:CounterScript", "label": "counter", "offset": 3, "slot": "12", "type": "t_contract(Counter)39909"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_bytes4": {"encoding": "inplace", "label": "bytes4", "numberOfBytes": "4"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_contract(Counter)39909": {"encoding": "inplace", "label": "contract Counter", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage)))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(bytes4 => mapping(bytes32 => struct FindData)))", "numberOfBytes": "32", "value": "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage))"}, "t_mapping(t_bytes32,t_struct(FindData)8306_storage)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => struct FindData)", "numberOfBytes": "32", "value": "t_struct(FindData)8306_storage"}, "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage))": {"encoding": "mapping", "key": "t_bytes4", "label": "mapping(bytes4 => mapping(bytes32 => struct FindData))", "numberOfBytes": "32", "value": "t_mapping(t_bytes32,t_struct(FindData)8306_storage)"}, "t_mapping(t_string_memory_ptr,t_string_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_mapping(t_string_memory_ptr,t_struct(Chain)2999_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => struct StdChains.Chain)", "numberOfBytes": "32", "value": "t_struct(Chain)2999_storage"}, "t_mapping(t_uint256,t_string_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_string_memory_ptr": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Chain)2999_storage": {"encoding": "inplace", "label": "struct StdChains.Chain", "numberOfBytes": "128", "members": [{"astId": 2992, "contract": "script/Counter.s.sol:CounterScript", "label": "name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 2994, "contract": "script/Counter.s.sol:CounterScript", "label": "chainId", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 2996, "contract": "script/Counter.s.sol:CounterScript", "label": "chainAlias", "offset": 0, "slot": "2", "type": "t_string_storage"}, {"astId": 2998, "contract": "script/Counter.s.sol:CounterScript", "label": "rpcUrl", "offset": 0, "slot": "3", "type": "t_string_storage"}]}, "t_struct(FindData)8306_storage": {"encoding": "inplace", "label": "struct FindData", "numberOfBytes": "128", "members": [{"astId": 8299, "contract": "script/Counter.s.sol:CounterScript", "label": "slot", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 8301, "contract": "script/Counter.s.sol:CounterScript", "label": "offsetLeft", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 8303, "contract": "script/Counter.s.sol:CounterScript", "label": "offsetRight", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 8305, "contract": "script/Counter.s.sol:CounterScript", "label": "found", "offset": 0, "slot": "3", "type": "t_bool"}]}, "t_struct(StdStorage)8331_storage": {"encoding": "inplace", "label": "struct StdStorage", "numberOfBytes": "256", "members": [{"astId": 8315, "contract": "script/Counter.s.sol:CounterScript", "label": "finds", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage)))"}, {"astId": 8318, "contract": "script/Counter.s.sol:CounterScript", "label": "_keys", "offset": 0, "slot": "1", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 8320, "contract": "script/Counter.s.sol:CounterScript", "label": "_sig", "offset": 0, "slot": "2", "type": "t_bytes4"}, {"astId": 8322, "contract": "script/Counter.s.sol:CounterScript", "label": "_depth", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 8324, "contract": "script/Counter.s.sol:CounterScript", "label": "_target", "offset": 0, "slot": "4", "type": "t_address"}, {"astId": 8326, "contract": "script/Counter.s.sol:CounterScript", "label": "_set", "offset": 0, "slot": "5", "type": "t_bytes32"}, {"astId": 8328, "contract": "script/Counter.s.sol:CounterScript", "label": "_enable_packed_slots", "offset": 0, "slot": "6", "type": "t_bool"}, {"astId": 8330, "contract": "script/Counter.s.sol:CounterScript", "label": "_calldata", "offset": 0, "slot": "7", "type": "t_bytes_storage"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "script/Counter.s.sol", "id": 39748, "exportedSymbols": {"Counter": [39909], "CounterScript": [39747], "Script": [100]}, "nodeType": "SourceUnit", "src": "39:340:20", "nodes": [{"id": 39712, "nodeType": "PragmaDirective", "src": "39:24:20", "nodes": [], "literals": ["solidity", "^", "0.8", ".13"]}, {"id": 39714, "nodeType": "ImportDirective", "src": "65:44:20", "nodes": [], "absolutePath": "lib/forge-std/src/Script.sol", "file": "forge-std/Script.sol", "nameLocation": "-1:-1:-1", "scope": 39748, "sourceUnit": 101, "symbolAliases": [{"foreign": {"id": 39713, "name": "<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 100, "src": "73:6:20", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 39716, "nodeType": "ImportDirective", "src": "110:43:20", "nodes": [], "absolutePath": "src/Counter.sol", "file": "../src/Counter.sol", "nameLocation": "-1:-1:-1", "scope": 39748, "sourceUnit": 39910, "symbolAliases": [{"foreign": {"id": 39715, "name": "Counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39909, "src": "118:7:20", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 39747, "nodeType": "ContractDefinition", "src": "155:223:20", "nodes": [{"id": 39721, "nodeType": "VariableDeclaration", "src": "194:22:20", "nodes": [], "constant": false, "functionSelector": "61bc221a", "mutability": "mutable", "name": "counter", "nameLocation": "209:7:20", "scope": 39747, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}, "typeName": {"id": 39720, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 39719, "name": "Counter", "nameLocations": ["194:7:20"], "nodeType": "IdentifierPath", "referencedDeclaration": 39909, "src": "194:7:20"}, "referencedDeclaration": 39909, "src": "194:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "visibility": "public"}, {"id": 39725, "nodeType": "FunctionDefinition", "src": "223:26:20", "nodes": [], "body": {"id": 39724, "nodeType": "Block", "src": "247:2:20", "nodes": [], "statements": []}, "functionSelector": "0a9254e4", "implemented": true, "kind": "function", "modifiers": [], "name": "setUp", "nameLocation": "232:5:20", "parameters": {"id": 39722, "nodeType": "ParameterList", "parameters": [], "src": "237:2:20"}, "returnParameters": {"id": 39723, "nodeType": "ParameterList", "parameters": [], "src": "247:0:20"}, "scope": 39747, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 39746, "nodeType": "FunctionDefinition", "src": "255:121:20", "nodes": [], "body": {"id": 39745, "nodeType": "Block", "src": "277:99:20", "nodes": [], "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39728, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "287:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 39730, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "290:14:20", "memberName": "startBroadcast", "nodeType": "MemberAccess", "referencedDeclaration": 15303, "src": "287:17:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 39731, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "287:19:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39732, "nodeType": "ExpressionStatement", "src": "287:19:20"}, {"expression": {"id": 39738, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 39733, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39721, "src": "317:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [], "expression": {"argumentTypes": [], "id": 39736, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "NewExpression", "src": "327:11:20", "typeDescriptions": {"typeIdentifier": "t_function_creation_nonpayable$__$returns$_t_contract$_Counter_$39909_$", "typeString": "function () returns (contract Counter)"}, "typeName": {"id": 39735, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 39734, "name": "Counter", "nameLocations": ["331:7:20"], "nodeType": "IdentifierPath", "referencedDeclaration": 39909, "src": "331:7:20"}, "referencedDeclaration": 39909, "src": "331:7:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}}, "id": 39737, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "327:13:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "src": "317:23:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39739, "nodeType": "ExpressionStatement", "src": "317:23:20"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39740, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "351:2:20", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 39742, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "354:13:20", "memberName": "stopBroadcast", "nodeType": "MemberAccess", "referencedDeclaration": 15319, "src": "351:16:20", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 39743, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "351:18:20", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39744, "nodeType": "ExpressionStatement", "src": "351:18:20"}]}, "functionSelector": "c0406226", "implemented": true, "kind": "function", "modifiers": [], "name": "run", "nameLocation": "264:3:20", "parameters": {"id": 39726, "nodeType": "ParameterList", "parameters": [], "src": "267:2:20"}, "returnParameters": {"id": 39727, "nodeType": "ParameterList", "parameters": [], "src": "277:0:20"}, "scope": 39747, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "abstract": false, "baseContracts": [{"baseName": {"id": 39717, "name": "<PERSON><PERSON><PERSON>", "nameLocations": ["181:6:20"], "nodeType": "IdentifierPath", "referencedDeclaration": 100, "src": "181:6:20"}, "id": 39718, "nodeType": "InheritanceSpecifier", "src": "181:6:20"}], "canonicalName": "CounterScript", "contractDependencies": [39909], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [39747, 100, 13170, 6011, 3921, 59, 47], "name": "CounterScript", "nameLocation": "164:13:20", "scope": 39748, "usedErrors": [], "usedEvents": []}], "license": "UNLICENSED"}, "id": 20}