{"abi": [{"type": "event", "name": "Log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}], "bytecode": {"object": "0x6080604052348015600e575f5ffd5b50603e80601a5f395ff3fe60806040525f5ffdfea2646970667358221220d767f50c4209fa8630402dd3a719332900b8f1fb145fe139ec68419875263e6c64736f6c634300081d0033", "sourceMap": "97:1919:4:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x60806040525f5ffdfea2646970667358221220d767f50c4209fa8630402dd3a719332900b8f1fb145fe139ec68419875263e6c64736f6c634300081d0033", "sourceMap": "97:1919:4:-:0;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"Log\",\"type\":\"event\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/chimera/src/CryticAsserts.sol\":\"CryticAsserts\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/Asserts.sol\":{\"keccak256\":\"0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626\",\"dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK\"]},\"lib/chimera/src/CryticAsserts.sol\":{\"keccak256\":\"0x10c62d527da30a8786eac73ec0dea4e461ac0db67df80878b8c6272d4fc9f368\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2b8985424d687a722d4adf3cc7e2134d2c8eb610eae62bce785a50d7f2dcf469\",\"dweb:/ipfs/QmVRZVXYppEiCTHJjRGP1BqgM7XAjXU5fDKBBXVip1BJUK\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "Log", "anonymous": false}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/chimera/src/CryticAsserts.sol": "CryticAsserts"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/Asserts.sol": {"keccak256": "0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9", "urls": ["bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626", "dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK"], "license": "MIT"}, "lib/chimera/src/CryticAsserts.sol": {"keccak256": "0x10c62d527da30a8786eac73ec0dea4e461ac0db67df80878b8c6272d4fc9f368", "urls": ["bzz-raw://2b8985424d687a722d4adf3cc7e2134d2c8eb610eae62bce785a50d7f2dcf469", "dweb:/ipfs/QmVRZVXYppEiCTHJjRGP1BqgM7XAjXU5fDKBBXVip1BJUK"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/chimera/src/CryticAsserts.sol", "id": 377, "exportedSymbols": {"Asserts": [81], "CryticAsserts": [376]}, "nodeType": "SourceUnit", "src": "32:1985:4", "nodes": [{"id": 107, "nodeType": "PragmaDirective", "src": "32:23:4", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 109, "nodeType": "ImportDirective", "src": "57:38:4", "nodes": [], "absolutePath": "lib/chimera/src/Asserts.sol", "file": "./Asserts.sol", "nameLocation": "-1:-1:-1", "scope": 377, "sourceUnit": 82, "symbolAliases": [{"foreign": {"id": 108, "name": "Asserts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 81, "src": "65:7:4", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 376, "nodeType": "ContractDefinition", "src": "97:1919:4", "nodes": [{"id": 115, "nodeType": "EventDefinition", "src": "137:18:4", "nodes": [], "anonymous": false, "eventSelector": "cf34ef537ac33ee1ac626ca1587a0a7e8e51561e5514f8cb36afa1c5102b3bab", "name": "Log", "nameLocation": "143:3:4", "parameters": {"id": 114, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 113, "indexed": false, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 115, "src": "147:6:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 112, "name": "string", "nodeType": "ElementaryTypeName", "src": "147:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "146:8:4"}}, {"id": 141, "nodeType": "FunctionDefinition", "src": "161:180:4", "nodes": [], "body": {"id": 140, "nodeType": "Block", "src": "243:98:4", "nodes": [], "statements": [{"condition": {"id": 129, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "257:8:4", "subExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 127, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 125, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 117, "src": "259:1:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 126, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 119, "src": "263:1:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "259:5:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "id": 128, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "258:7:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 139, "nodeType": "IfStatement", "src": "253:82:4", "trueBody": {"id": 138, "nodeType": "Block", "src": "267:68:4", "statements": [{"eventCall": {"arguments": [{"id": 131, "name": "reason", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 121, "src": "290:6:4", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 130, "name": "Log", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 115, "src": "286:3:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory)"}}, "id": 132, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "286:11:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 133, "nodeType": "EmitStatement", "src": "281:16:4"}, {"expression": {"arguments": [{"hexValue": "66616c7365", "id": 135, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "318:5:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 134, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "311:6:4", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 136, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "311:13:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 137, "nodeType": "ExpressionStatement", "src": "311:13:4"}]}}]}, "baseFunctions": [10], "implemented": true, "kind": "function", "modifiers": [], "name": "gt", "nameLocation": "170:2:4", "overrides": {"id": 123, "nodeType": "OverrideSpecifier", "overrides": [], "src": "234:8:4"}, "parameters": {"id": 122, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 117, "mutability": "mutable", "name": "a", "nameLocation": "181:1:4", "nodeType": "VariableDeclaration", "scope": 141, "src": "173:9:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 116, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "173:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 119, "mutability": "mutable", "name": "b", "nameLocation": "192:1:4", "nodeType": "VariableDeclaration", "scope": 141, "src": "184:9:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 118, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "184:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 121, "mutability": "mutable", "name": "reason", "nameLocation": "209:6:4", "nodeType": "VariableDeclaration", "scope": 141, "src": "195:20:4", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 120, "name": "string", "nodeType": "ElementaryTypeName", "src": "195:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "172:44:4"}, "returnParameters": {"id": 124, "nodeType": "ParameterList", "parameters": [], "src": "243:0:4"}, "scope": 376, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 167, "nodeType": "FunctionDefinition", "src": "347:182:4", "nodes": [], "body": {"id": 166, "nodeType": "Block", "src": "430:99:4", "nodes": [], "statements": [{"condition": {"id": 155, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "444:9:4", "subExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 153, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 151, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 143, "src": "446:1:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 152, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 145, "src": "451:1:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "446:6:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "id": 154, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "445:8:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 165, "nodeType": "IfStatement", "src": "440:83:4", "trueBody": {"id": 164, "nodeType": "Block", "src": "455:68:4", "statements": [{"eventCall": {"arguments": [{"id": 157, "name": "reason", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 147, "src": "478:6:4", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 156, "name": "Log", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 115, "src": "474:3:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory)"}}, "id": 158, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "474:11:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 159, "nodeType": "EmitStatement", "src": "469:16:4"}, {"expression": {"arguments": [{"hexValue": "66616c7365", "id": 161, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "506:5:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 160, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "499:6:4", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 162, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "499:13:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 163, "nodeType": "ExpressionStatement", "src": "499:13:4"}]}}]}, "baseFunctions": [19], "implemented": true, "kind": "function", "modifiers": [], "name": "gte", "nameLocation": "356:3:4", "overrides": {"id": 149, "nodeType": "OverrideSpecifier", "overrides": [], "src": "421:8:4"}, "parameters": {"id": 148, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 143, "mutability": "mutable", "name": "a", "nameLocation": "368:1:4", "nodeType": "VariableDeclaration", "scope": 167, "src": "360:9:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 142, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "360:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 145, "mutability": "mutable", "name": "b", "nameLocation": "379:1:4", "nodeType": "VariableDeclaration", "scope": 167, "src": "371:9:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 144, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "371:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 147, "mutability": "mutable", "name": "reason", "nameLocation": "396:6:4", "nodeType": "VariableDeclaration", "scope": 167, "src": "382:20:4", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 146, "name": "string", "nodeType": "ElementaryTypeName", "src": "382:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "359:44:4"}, "returnParameters": {"id": 150, "nodeType": "ParameterList", "parameters": [], "src": "430:0:4"}, "scope": 376, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 193, "nodeType": "FunctionDefinition", "src": "535:180:4", "nodes": [], "body": {"id": 192, "nodeType": "Block", "src": "617:98:4", "nodes": [], "statements": [{"condition": {"id": 181, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "631:8:4", "subExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 179, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 177, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 169, "src": "633:1:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 178, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 171, "src": "637:1:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "633:5:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "id": 180, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "632:7:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 191, "nodeType": "IfStatement", "src": "627:82:4", "trueBody": {"id": 190, "nodeType": "Block", "src": "641:68:4", "statements": [{"eventCall": {"arguments": [{"id": 183, "name": "reason", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 173, "src": "664:6:4", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 182, "name": "Log", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 115, "src": "660:3:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory)"}}, "id": 184, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "660:11:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 185, "nodeType": "EmitStatement", "src": "655:16:4"}, {"expression": {"arguments": [{"hexValue": "66616c7365", "id": 187, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "692:5:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 186, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "685:6:4", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 188, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "685:13:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 189, "nodeType": "ExpressionStatement", "src": "685:13:4"}]}}]}, "baseFunctions": [28], "implemented": true, "kind": "function", "modifiers": [], "name": "lt", "nameLocation": "544:2:4", "overrides": {"id": 175, "nodeType": "OverrideSpecifier", "overrides": [], "src": "608:8:4"}, "parameters": {"id": 174, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 169, "mutability": "mutable", "name": "a", "nameLocation": "555:1:4", "nodeType": "VariableDeclaration", "scope": 193, "src": "547:9:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 168, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "547:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 171, "mutability": "mutable", "name": "b", "nameLocation": "566:1:4", "nodeType": "VariableDeclaration", "scope": 193, "src": "558:9:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 170, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "558:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 173, "mutability": "mutable", "name": "reason", "nameLocation": "583:6:4", "nodeType": "VariableDeclaration", "scope": 193, "src": "569:20:4", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 172, "name": "string", "nodeType": "ElementaryTypeName", "src": "569:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "546:44:4"}, "returnParameters": {"id": 176, "nodeType": "ParameterList", "parameters": [], "src": "617:0:4"}, "scope": 376, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 219, "nodeType": "FunctionDefinition", "src": "721:182:4", "nodes": [], "body": {"id": 218, "nodeType": "Block", "src": "804:99:4", "nodes": [], "statements": [{"condition": {"id": 207, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "818:9:4", "subExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 205, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 203, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 195, "src": "820:1:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 204, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 197, "src": "825:1:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "820:6:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "id": 206, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "819:8:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 217, "nodeType": "IfStatement", "src": "814:83:4", "trueBody": {"id": 216, "nodeType": "Block", "src": "829:68:4", "statements": [{"eventCall": {"arguments": [{"id": 209, "name": "reason", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 199, "src": "852:6:4", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 208, "name": "Log", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 115, "src": "848:3:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory)"}}, "id": 210, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "848:11:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 211, "nodeType": "EmitStatement", "src": "843:16:4"}, {"expression": {"arguments": [{"hexValue": "66616c7365", "id": 213, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "880:5:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 212, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "873:6:4", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 214, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "873:13:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 215, "nodeType": "ExpressionStatement", "src": "873:13:4"}]}}]}, "baseFunctions": [37], "implemented": true, "kind": "function", "modifiers": [], "name": "lte", "nameLocation": "730:3:4", "overrides": {"id": 201, "nodeType": "OverrideSpecifier", "overrides": [], "src": "795:8:4"}, "parameters": {"id": 200, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 195, "mutability": "mutable", "name": "a", "nameLocation": "742:1:4", "nodeType": "VariableDeclaration", "scope": 219, "src": "734:9:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 194, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "734:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 197, "mutability": "mutable", "name": "b", "nameLocation": "753:1:4", "nodeType": "VariableDeclaration", "scope": 219, "src": "745:9:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 196, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "745:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 199, "mutability": "mutable", "name": "reason", "nameLocation": "770:6:4", "nodeType": "VariableDeclaration", "scope": 219, "src": "756:20:4", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 198, "name": "string", "nodeType": "ElementaryTypeName", "src": "756:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "733:44:4"}, "returnParameters": {"id": 202, "nodeType": "ParameterList", "parameters": [], "src": "804:0:4"}, "scope": 376, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 245, "nodeType": "FunctionDefinition", "src": "909:181:4", "nodes": [], "body": {"id": 244, "nodeType": "Block", "src": "991:99:4", "nodes": [], "statements": [{"condition": {"id": 233, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "1005:9:4", "subExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 231, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 229, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 221, "src": "1007:1:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 230, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 223, "src": "1012:1:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1007:6:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "id": 232, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1006:8:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 243, "nodeType": "IfStatement", "src": "1001:83:4", "trueBody": {"id": 242, "nodeType": "Block", "src": "1016:68:4", "statements": [{"eventCall": {"arguments": [{"id": 235, "name": "reason", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 225, "src": "1039:6:4", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 234, "name": "Log", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 115, "src": "1035:3:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory)"}}, "id": 236, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1035:11:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 237, "nodeType": "EmitStatement", "src": "1030:16:4"}, {"expression": {"arguments": [{"hexValue": "66616c7365", "id": 239, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1067:5:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 238, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "1060:6:4", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 240, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1060:13:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 241, "nodeType": "ExpressionStatement", "src": "1060:13:4"}]}}]}, "baseFunctions": [46], "implemented": true, "kind": "function", "modifiers": [], "name": "eq", "nameLocation": "918:2:4", "overrides": {"id": 227, "nodeType": "OverrideSpecifier", "overrides": [], "src": "982:8:4"}, "parameters": {"id": 226, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 221, "mutability": "mutable", "name": "a", "nameLocation": "929:1:4", "nodeType": "VariableDeclaration", "scope": 245, "src": "921:9:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 220, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "921:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 223, "mutability": "mutable", "name": "b", "nameLocation": "940:1:4", "nodeType": "VariableDeclaration", "scope": 245, "src": "932:9:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 222, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "932:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 225, "mutability": "mutable", "name": "reason", "nameLocation": "957:6:4", "nodeType": "VariableDeclaration", "scope": 245, "src": "943:20:4", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 224, "name": "string", "nodeType": "ElementaryTypeName", "src": "943:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "920:44:4"}, "returnParameters": {"id": 228, "nodeType": "ParameterList", "parameters": [], "src": "991:0:4"}, "scope": 376, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 266, "nodeType": "FunctionDefinition", "src": "1096:159:4", "nodes": [], "body": {"id": 265, "nodeType": "Block", "src": "1163:92:4", "nodes": [], "statements": [{"condition": {"id": 254, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "1177:2:4", "subExpression": {"id": 253, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 247, "src": "1178:1:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 264, "nodeType": "IfStatement", "src": "1173:76:4", "trueBody": {"id": 263, "nodeType": "Block", "src": "1181:68:4", "statements": [{"eventCall": {"arguments": [{"id": 256, "name": "reason", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 249, "src": "1204:6:4", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 255, "name": "Log", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 115, "src": "1200:3:4", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory)"}}, "id": 257, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1200:11:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 258, "nodeType": "EmitStatement", "src": "1195:16:4"}, {"expression": {"arguments": [{"hexValue": "66616c7365", "id": 260, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1232:5:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 259, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "1225:6:4", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 261, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1225:13:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 262, "nodeType": "ExpressionStatement", "src": "1225:13:4"}]}}]}, "baseFunctions": [53], "implemented": true, "kind": "function", "modifiers": [], "name": "t", "nameLocation": "1105:1:4", "overrides": {"id": 251, "nodeType": "OverrideSpecifier", "overrides": [], "src": "1154:8:4"}, "parameters": {"id": 250, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 247, "mutability": "mutable", "name": "b", "nameLocation": "1112:1:4", "nodeType": "VariableDeclaration", "scope": 266, "src": "1107:6:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 246, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1107:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 249, "mutability": "mutable", "name": "reason", "nameLocation": "1129:6:4", "nodeType": "VariableDeclaration", "scope": 266, "src": "1115:20:4", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 248, "name": "string", "nodeType": "ElementaryTypeName", "src": "1115:6:4", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1106:30:4"}, "returnParameters": {"id": 252, "nodeType": "ParameterList", "parameters": [], "src": "1163:0:4"}, "scope": 376, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 306, "nodeType": "FunctionDefinition", "src": "1261:269:4", "nodes": [], "body": {"id": 305, "nodeType": "Block", "src": "1364:166:4", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 284, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 280, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 278, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 268, "src": "1378:5:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 279, "name": "low", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 270, "src": "1386:3:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1378:11:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "||", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 283, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 281, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 268, "src": "1393:5:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 282, "name": "high", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 272, "src": "1401:4:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1393:12:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "1378:27:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 302, "nodeType": "IfStatement", "src": "1374:128:4", "trueBody": {"id": 301, "nodeType": "Block", "src": "1407:95:4", "statements": [{"assignments": [286], "declarations": [{"constant": false, "id": 286, "mutability": "mutable", "name": "ans", "nameLocation": "1429:3:4", "nodeType": "VariableDeclaration", "scope": 301, "src": "1421:11:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 285, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1421:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 298, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 297, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 287, "name": "low", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 270, "src": "1435:3:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 295, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 288, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 268, "src": "1442:5:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "%", "rightExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 293, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 291, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 289, "name": "high", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 272, "src": "1451:4:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 290, "name": "low", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 270, "src": "1458:3:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1451:10:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"hexValue": "31", "id": 292, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1464:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "1451:14:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 294, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1450:16:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1442:24:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 296, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1441:26:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1435:32:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "1421:46:4"}, {"expression": {"id": 299, "name": "ans", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 286, "src": "1488:3:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 277, "id": 300, "nodeType": "Return", "src": "1481:10:4"}]}}, {"expression": {"id": 303, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 268, "src": "1518:5:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 277, "id": 304, "nodeType": "Return", "src": "1511:12:4"}]}, "baseFunctions": [64], "implemented": true, "kind": "function", "modifiers": [], "name": "between", "nameLocation": "1270:7:4", "overrides": {"id": 274, "nodeType": "OverrideSpecifier", "overrides": [], "src": "1337:8:4"}, "parameters": {"id": 273, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 268, "mutability": "mutable", "name": "value", "nameLocation": "1286:5:4", "nodeType": "VariableDeclaration", "scope": 306, "src": "1278:13:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 267, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1278:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 270, "mutability": "mutable", "name": "low", "nameLocation": "1301:3:4", "nodeType": "VariableDeclaration", "scope": 306, "src": "1293:11:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 269, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1293:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 272, "mutability": "mutable", "name": "high", "nameLocation": "1314:4:4", "nodeType": "VariableDeclaration", "scope": 306, "src": "1306:12:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 271, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1306:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1277:42:4"}, "returnParameters": {"id": 277, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 276, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 306, "src": "1355:7:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 275, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1355:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1354:9:4"}, "scope": 376, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 364, "nodeType": "FunctionDefinition", "src": "1536:389:4", "nodes": [], "body": {"id": 363, "nodeType": "Block", "src": "1635:290:4", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 324, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 320, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 318, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 308, "src": "1649:5:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 319, "name": "low", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 310, "src": "1657:3:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "1649:11:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "||", "rightExpression": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 323, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 321, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 308, "src": "1664:5:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 322, "name": "high", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 312, "src": "1672:4:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "1664:12:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "1649:27:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 360, "nodeType": "IfStatement", "src": "1645:252:4", "trueBody": {"id": 359, "nodeType": "Block", "src": "1678:219:4", "statements": [{"assignments": [326], "declarations": [{"constant": false, "id": 326, "mutability": "mutable", "name": "range", "nameLocation": "1699:5:4", "nodeType": "VariableDeclaration", "scope": 359, "src": "1692:12:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 325, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1692:6:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "id": 332, "initialValue": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 331, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 329, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 327, "name": "high", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 312, "src": "1707:4:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 328, "name": "low", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 310, "src": "1714:3:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "1707:10:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"hexValue": "31", "id": 330, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1720:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "1707:14:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "VariableDeclarationStatement", "src": "1692:29:4"}, {"assignments": [334], "declarations": [{"constant": false, "id": 334, "mutability": "mutable", "name": "clamped", "nameLocation": "1742:7:4", "nodeType": "VariableDeclaration", "scope": 359, "src": "1735:14:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 333, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1735:6:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "id": 342, "initialValue": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 341, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"components": [{"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 337, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 335, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 308, "src": "1753:5:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 336, "name": "low", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 310, "src": "1761:3:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "1753:11:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "id": 338, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1752:13:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "%", "rightExpression": {"components": [{"id": 339, "name": "range", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 326, "src": "1769:5:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "id": 340, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1768:7:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "1752:23:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "VariableDeclarationStatement", "src": "1735:40:4"}, {"condition": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 345, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 343, "name": "clamped", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 334, "src": "1793:7:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "30", "id": 344, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1803:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "1793:11:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 350, "nodeType": "IfStatement", "src": "1789:33:4", "trueBody": {"expression": {"id": 348, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 346, "name": "clamped", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 334, "src": "1806:7:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 347, "name": "range", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 326, "src": "1817:5:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "1806:16:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 349, "nodeType": "ExpressionStatement", "src": "1806:16:4"}}, {"assignments": [352], "declarations": [{"constant": false, "id": 352, "mutability": "mutable", "name": "ans", "nameLocation": "1843:3:4", "nodeType": "VariableDeclaration", "scope": 359, "src": "1836:10:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 351, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1836:6:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "id": 356, "initialValue": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 355, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 353, "name": "low", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 310, "src": "1849:3:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 354, "name": "clamped", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 334, "src": "1855:7:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "1849:13:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "VariableDeclarationStatement", "src": "1836:26:4"}, {"expression": {"id": 357, "name": "ans", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 352, "src": "1883:3:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "functionReturnParameters": 317, "id": 358, "nodeType": "Return", "src": "1876:10:4"}]}}, {"expression": {"id": 361, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 308, "src": "1913:5:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "functionReturnParameters": 317, "id": 362, "nodeType": "Return", "src": "1906:12:4"}]}, "baseFunctions": [75], "implemented": true, "kind": "function", "modifiers": [], "name": "between", "nameLocation": "1545:7:4", "overrides": {"id": 314, "nodeType": "OverrideSpecifier", "overrides": [], "src": "1609:8:4"}, "parameters": {"id": 313, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 308, "mutability": "mutable", "name": "value", "nameLocation": "1560:5:4", "nodeType": "VariableDeclaration", "scope": 364, "src": "1553:12:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 307, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1553:6:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}, {"constant": false, "id": 310, "mutability": "mutable", "name": "low", "nameLocation": "1574:3:4", "nodeType": "VariableDeclaration", "scope": 364, "src": "1567:10:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 309, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1567:6:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}, {"constant": false, "id": 312, "mutability": "mutable", "name": "high", "nameLocation": "1586:4:4", "nodeType": "VariableDeclaration", "scope": 364, "src": "1579:11:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 311, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1579:6:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "1552:39:4"}, "returnParameters": {"id": 317, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 316, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 364, "src": "1627:6:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 315, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1627:6:4", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "1626:8:4"}, "scope": 376, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 375, "nodeType": "FunctionDefinition", "src": "1931:83:4", "nodes": [], "body": {"id": 374, "nodeType": "Block", "src": "1987:27:4", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 371, "name": "p", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 366, "src": "2005:1:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 370, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "1997:7:4", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 372, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1997:10:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 373, "nodeType": "ExpressionStatement", "src": "1997:10:4"}]}, "baseFunctions": [80], "implemented": true, "kind": "function", "modifiers": [], "name": "precondition", "nameLocation": "1940:12:4", "overrides": {"id": 368, "nodeType": "OverrideSpecifier", "overrides": [], "src": "1978:8:4"}, "parameters": {"id": 367, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 366, "mutability": "mutable", "name": "p", "nameLocation": "1958:1:4", "nodeType": "VariableDeclaration", "scope": 375, "src": "1953:6:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 365, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1953:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1952:8:4"}, "returnParameters": {"id": 369, "nodeType": "ParameterList", "parameters": [], "src": "1987:0:4"}, "scope": 376, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "abstract": false, "baseContracts": [{"baseName": {"id": 110, "name": "Asserts", "nameLocations": ["123:7:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 81, "src": "123:7:4"}, "id": 111, "nodeType": "InheritanceSpecifier", "src": "123:7:4"}], "canonicalName": "CryticAsserts", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [376, 81], "name": "CryticAsserts", "nameLocation": "106:13:4", "scope": 377, "usedErrors": [], "usedEvents": [115]}], "license": "MIT"}, "id": 4}