{"abi": [{"type": "function", "name": "counter_add", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_decrement", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_increment", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_reset", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_setNumber", "inputs": [{"name": "newNumber", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_subtract", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "error", "name": "ActorExists", "inputs": []}, {"type": "error", "name": "ActorNotAdded", "inputs": []}, {"type": "error", "name": "ActorNotSetup", "inputs": []}, {"type": "error", "name": "DefaultActor", "inputs": []}, {"type": "error", "name": "Exists", "inputs": []}, {"type": "error", "name": "NotAdded", "inputs": []}, {"type": "error", "name": "NotSetup", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"counter_add(uint256)": "c8441bf6", "counter_decrement()": "fdfba6cd", "counter_increment()": "a1e3ca41", "counter_reset()": "ab031669", "counter_setNumber(uint256)": "4cc7ae6c", "counter_subtract(uint256)": "6e76f7cf"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ActorExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotSetup\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DefaultActor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Exists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotSetup\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"counter_add\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"counter_decrement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"counter_increment\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"counter_reset\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newNumber\",\"type\":\"uint256\"}],\"name\":\"counter_setNumber\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"counter_subtract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"counter_add(uint256)\":{\"notice\":\"AUTO GENERATED TARGET FUNCTIONS - WARNING: DO NOT DELETE OR MODIFY THIS LINE ///\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/recon/targets/CounterTargets.sol\":\"CounterTargets\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/Asserts.sol\":{\"keccak256\":\"0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626\",\"dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK\"]},\"lib/chimera/src/BaseProperties.sol\":{\"keccak256\":\"0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848\",\"dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w\"]},\"lib/chimera/src/BaseSetup.sol\":{\"keccak256\":\"0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe\",\"dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1\"]},\"lib/chimera/src/BaseTargetFunctions.sol\":{\"keccak256\":\"0xe3b3de6200ab7039a14bb0a2a7e090402a36bd2c0c31c6d677d766b0f335bd60\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5326abd254a25c5bf8c2219e36641bc1288114271678dac8611d8365fc432851\",\"dweb:/ipfs/QmR4BF3JrTU3XhPVY4vPxGCQFXbUv94Bi48FjvgphroPxJ\"]},\"lib/chimera/src/Hevm.sol\":{\"keccak256\":\"0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b\",\"dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr\"]},\"lib/setup-helpers/src/ActorManager.sol\":{\"keccak256\":\"0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c\",\"dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB\"]},\"lib/setup-helpers/src/AssetManager.sol\":{\"keccak256\":\"0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa\",\"dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq\"]},\"lib/setup-helpers/src/EnumerableSet.sol\":{\"keccak256\":\"0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df\",\"dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW\"]},\"lib/setup-helpers/src/MockERC20.sol\":{\"keccak256\":\"0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578\",\"dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE\"]},\"lib/setup-helpers/src/Panic.sol\":{\"keccak256\":\"0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325\",\"dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd\"]},\"lib/setup-helpers/src/Utils.sol\":{\"keccak256\":\"0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523\",\"dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau\"]},\"src/Counter.sol\":{\"keccak256\":\"0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e\",\"dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2\"]},\"test/recon/BeforeAfter.sol\":{\"keccak256\":\"0x749640c8e4e366e58f610fd34fc71bfb6d0abdc4bb482366ca6a1fff07fcc36e\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://de1f26ec166caba53e9e04b74a5fcfe64cc39a1687a68d357d17b79d65a6bc75\",\"dweb:/ipfs/QmfFjMrfBQ6hGUyEyasvEBknA4SNdokrNCrrWHbPVKGz9k\"]},\"test/recon/Properties.sol\":{\"keccak256\":\"0x5dbc6eba0dc68541aee76cfb2c774eb53912665d44ba9b00e2b4c67ae4b600d9\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://f786cfc9b24abdc05071da9aad64929c9f7b5fd5c6ee317962ad587dff6a1777\",\"dweb:/ipfs/QmWw9B7jLeFgHQeJFcaSgb9SY7oNCLHG12f3owHV4dNDSM\"]},\"test/recon/Setup.sol\":{\"keccak256\":\"0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024\",\"dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk\"]},\"test/recon/targets/CounterTargets.sol\":{\"keccak256\":\"0x8161ebacdff8537dce038d235860e7f482db54c065c21c2e5dea6fa918f70062\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://e63dc03d78d1219484537042da69532dbbf939582892d6342a75b94a03c312a3\",\"dweb:/ipfs/QmXVsDiVa68bbXToqi8CpgS9vXJiv2dpp2J2C6J6fhtw7S\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ActorExists"}, {"inputs": [], "type": "error", "name": "ActorNotAdded"}, {"inputs": [], "type": "error", "name": "ActorNotSetup"}, {"inputs": [], "type": "error", "name": "DefaultActor"}, {"inputs": [], "type": "error", "name": "Exists"}, {"inputs": [], "type": "error", "name": "NotAdded"}, {"inputs": [], "type": "error", "name": "NotSetup"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "counter_add"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "counter_decrement"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "counter_increment"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "counter_reset"}, {"inputs": [{"internalType": "uint256", "name": "newNumber", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "counter_setNumber"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "counter_subtract"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {"counter_add(uint256)": {"notice": "AUTO GENERATED TARGET FUNCTIONS - WARNING: DO NOT DELETE OR MODIFY THIS LINE ///"}}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/recon/targets/CounterTargets.sol": "CounterTargets"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/Asserts.sol": {"keccak256": "0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9", "urls": ["bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626", "dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK"], "license": "MIT"}, "lib/chimera/src/BaseProperties.sol": {"keccak256": "0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a", "urls": ["bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848", "dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w"], "license": "MIT"}, "lib/chimera/src/BaseSetup.sol": {"keccak256": "0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107", "urls": ["bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe", "dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1"], "license": "MIT"}, "lib/chimera/src/BaseTargetFunctions.sol": {"keccak256": "0xe3b3de6200ab7039a14bb0a2a7e090402a36bd2c0c31c6d677d766b0f335bd60", "urls": ["bzz-raw://5326abd254a25c5bf8c2219e36641bc1288114271678dac8611d8365fc432851", "dweb:/ipfs/QmR4BF3JrTU3XhPVY4vPxGCQFXbUv94Bi48FjvgphroPxJ"], "license": "MIT"}, "lib/chimera/src/Hevm.sol": {"keccak256": "0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe", "urls": ["bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b", "dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr"], "license": "MIT"}, "lib/setup-helpers/src/ActorManager.sol": {"keccak256": "0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06", "urls": ["bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c", "dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB"], "license": "GPL-2.0"}, "lib/setup-helpers/src/AssetManager.sol": {"keccak256": "0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc", "urls": ["bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa", "dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq"], "license": "GPL-2.0"}, "lib/setup-helpers/src/EnumerableSet.sol": {"keccak256": "0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514", "urls": ["bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df", "dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW"], "license": "MIT"}, "lib/setup-helpers/src/MockERC20.sol": {"keccak256": "0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f", "urls": ["bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578", "dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE"], "license": "AGPL-3.0-only"}, "lib/setup-helpers/src/Panic.sol": {"keccak256": "0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92", "urls": ["bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325", "dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd"], "license": "GPL-2.0"}, "lib/setup-helpers/src/Utils.sol": {"keccak256": "0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27", "urls": ["bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523", "dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau"], "license": "GPL-2.0"}, "src/Counter.sol": {"keccak256": "0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c", "urls": ["bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e", "dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2"], "license": "UNLICENSED"}, "test/recon/BeforeAfter.sol": {"keccak256": "0x749640c8e4e366e58f610fd34fc71bfb6d0abdc4bb482366ca6a1fff07fcc36e", "urls": ["bzz-raw://de1f26ec166caba53e9e04b74a5fcfe64cc39a1687a68d357d17b79d65a6bc75", "dweb:/ipfs/QmfFjMrfBQ6hGUyEyasvEBknA4SNdokrNCrrWHbPVKGz9k"], "license": "GPL-2.0"}, "test/recon/Properties.sol": {"keccak256": "0x5dbc6eba0dc68541aee76cfb2c774eb53912665d44ba9b00e2b4c67ae4b600d9", "urls": ["bzz-raw://f786cfc9b24abdc05071da9aad64929c9f7b5fd5c6ee317962ad587dff6a1777", "dweb:/ipfs/QmWw9B7jLeFgHQeJFcaSgb9SY7oNCLHG12f3owHV4dNDSM"], "license": "GPL-2.0"}, "test/recon/Setup.sol": {"keccak256": "0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5", "urls": ["bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024", "dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk"], "license": "GPL-2.0"}, "test/recon/targets/CounterTargets.sol": {"keccak256": "0x8161ebacdff8537dce038d235860e7f482db54c065c21c2e5dea6fa918f70062", "urls": ["bzz-raw://e63dc03d78d1219484537042da69532dbbf939582892d6342a75b94a03c312a3", "dweb:/ipfs/QmXVsDiVa68bbXToqi8CpgS9vXJiv2dpp2J2C6J6fhtw7S"], "license": "GPL-2.0"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 40405, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "_actor", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 40409, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "_actors", "offset": 0, "slot": "1", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 40565, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "__asset", "offset": 0, "slot": "3", "type": "t_address"}, {"astId": 40569, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "_assets", "offset": 0, "slot": "4", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 42682, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "counter", "offset": 0, "slot": "6", "type": "t_contract(Counter)42568"}, {"astId": 42580, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "_before", "offset": 0, "slot": "7", "type": "t_struct(Vars)42577_storage"}, {"astId": 42583, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "_after", "offset": 0, "slot": "8", "type": "t_struct(Vars)42577_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_contract(Counter)42568": {"encoding": "inplace", "label": "contract Counter", "numberOfBytes": "20"}, "t_mapping(t_bytes32,t_uint256)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(AddressSet)41192_storage": {"encoding": "inplace", "label": "struct EnumerableSet.AddressSet", "numberOfBytes": "64", "members": [{"astId": 41191, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "_inner", "offset": 0, "slot": "0", "type": "t_struct(Set)40877_storage"}]}, "t_struct(Set)40877_storage": {"encoding": "inplace", "label": "struct EnumerableSet.Set", "numberOfBytes": "64", "members": [{"astId": 40872, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "_values", "offset": 0, "slot": "0", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 40876, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "_indexes", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_uint256)"}]}, "t_struct(Vars)42577_storage": {"encoding": "inplace", "label": "struct BeforeAfter.Vars", "numberOfBytes": "32", "members": [{"astId": 42576, "contract": "test/recon/targets/CounterTargets.sol:CounterTargets", "label": "__ignore__", "offset": 0, "slot": "0", "type": "t_uint256"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "test/recon/targets/CounterTargets.sol", "id": 42859, "exportedSymbols": {"BaseTargetFunctions": [105], "BeforeAfter": [42602], "Counter": [42568], "CounterTargets": [42858], "Panic": [42052], "Properties": [42658], "vm": [719]}, "nodeType": "SourceUnit", "src": "36:1122:40", "nodes": [{"id": 42766, "nodeType": "PragmaDirective", "src": "36:23:40", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 42768, "nodeType": "ImportDirective", "src": "61:69:40", "nodes": [], "absolutePath": "lib/chimera/src/BaseTargetFunctions.sol", "file": "@chimera/BaseTargetFunctions.sol", "nameLocation": "-1:-1:-1", "scope": 42859, "sourceUnit": 106, "symbolAliases": [{"foreign": {"id": 42767, "name": "BaseTargetFunctions", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "69:19:40", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42770, "nodeType": "ImportDirective", "src": "131:47:40", "nodes": [], "absolutePath": "test/recon/BeforeAfter.sol", "file": "../BeforeAfter.sol", "nameLocation": "-1:-1:-1", "scope": 42859, "sourceUnit": 42603, "symbolAliases": [{"foreign": {"id": 42769, "name": "BeforeAfter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42602, "src": "139:11:40", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42772, "nodeType": "ImportDirective", "src": "179:45:40", "nodes": [], "absolutePath": "test/recon/Properties.sol", "file": "../Properties.sol", "nameLocation": "-1:-1:-1", "scope": 42859, "sourceUnit": 42659, "symbolAliases": [{"foreign": {"id": 42771, "name": "Properties", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42658, "src": "187:10:40", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42774, "nodeType": "ImportDirective", "src": "241:37:40", "nodes": [], "absolutePath": "lib/chimera/src/Hevm.sol", "file": "@chimera/Hevm.sol", "nameLocation": "-1:-1:-1", "scope": 42859, "sourceUnit": 720, "symbolAliases": [{"foreign": {"id": 42773, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "249:2:40", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42776, "nodeType": "ImportDirective", "src": "291:39:40", "nodes": [], "absolutePath": "lib/setup-helpers/src/Panic.sol", "file": "@recon/Panic.sol", "nameLocation": "-1:-1:-1", "scope": 42859, "sourceUnit": 42053, "symbolAliases": [{"foreign": {"id": 42775, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "299:5:40", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42777, "nodeType": "ImportDirective", "src": "332:25:40", "nodes": [], "absolutePath": "src/Counter.sol", "file": "src/Counter.sol", "nameLocation": "-1:-1:-1", "scope": 42859, "sourceUnit": 42569, "symbolAliases": [], "unitAlias": ""}, {"id": 42858, "nodeType": "ContractDefinition", "src": "359:799:40", "nodes": [{"id": 42796, "nodeType": "FunctionDefinition", "src": "606:86:40", "nodes": [], "body": {"id": 42795, "nodeType": "Block", "src": "657:35:40", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42792, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42784, "src": "679:5:40", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 42789, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42682, "src": "667:7:40", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}, "id": 42791, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "675:3:40", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 42541, "src": "667:11:40", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 42793, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "667:18:40", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42794, "nodeType": "ExpressionStatement", "src": "667:18:40"}]}, "documentation": {"id": 42782, "nodeType": "StructuredDocumentation", "src": "516:85:40", "text": "AUTO GENERATED TARGET FUNCTIONS - WARNING: DO NOT DELETE OR MODIFY THIS LINE ///"}, "functionSelector": "c8441bf6", "implemented": true, "kind": "function", "modifiers": [{"id": 42787, "kind": "modifierInvocation", "modifierName": {"id": 42786, "name": "as<PERSON><PERSON>", "nameLocations": ["649:7:40"], "nodeType": "IdentifierPath", "referencedDeclaration": 42723, "src": "649:7:40"}, "nodeType": "ModifierInvocation", "src": "649:7:40"}], "name": "counter_add", "nameLocation": "615:11:40", "parameters": {"id": 42785, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42784, "mutability": "mutable", "name": "value", "nameLocation": "635:5:40", "nodeType": "VariableDeclaration", "scope": 42796, "src": "627:13:40", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42783, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "627:7:40", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "626:15:40"}, "returnParameters": {"id": 42788, "nodeType": "ParameterList", "parameters": [], "src": "657:0:40"}, "scope": 42858, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42807, "nodeType": "FunctionDefinition", "src": "698:80:40", "nodes": [], "body": {"id": 42806, "nodeType": "Block", "src": "742:36:40", "nodes": [], "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 42801, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42682, "src": "752:7:40", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}, "id": 42803, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "760:9:40", "memberName": "decrement", "nodeType": "MemberAccess", "referencedDeclaration": 42493, "src": "752:17:40", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 42804, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "752:19:40", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42805, "nodeType": "ExpressionStatement", "src": "752:19:40"}]}, "functionSelector": "fdfba6cd", "implemented": true, "kind": "function", "modifiers": [{"id": 42799, "kind": "modifierInvocation", "modifierName": {"id": 42798, "name": "as<PERSON><PERSON>", "nameLocations": ["734:7:40"], "nodeType": "IdentifierPath", "referencedDeclaration": 42723, "src": "734:7:40"}, "nodeType": "ModifierInvocation", "src": "734:7:40"}], "name": "counter_decrement", "nameLocation": "707:17:40", "parameters": {"id": 42797, "nodeType": "ParameterList", "parameters": [], "src": "724:2:40"}, "returnParameters": {"id": 42800, "nodeType": "ParameterList", "parameters": [], "src": "742:0:40"}, "scope": 42858, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42818, "nodeType": "FunctionDefinition", "src": "784:80:40", "nodes": [], "body": {"id": 42817, "nodeType": "Block", "src": "828:36:40", "nodes": [], "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 42812, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42682, "src": "838:7:40", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}, "id": 42814, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "846:9:40", "memberName": "increment", "nodeType": "MemberAccess", "referencedDeclaration": 42470, "src": "838:17:40", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 42815, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "838:19:40", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42816, "nodeType": "ExpressionStatement", "src": "838:19:40"}]}, "functionSelector": "a1e3ca41", "implemented": true, "kind": "function", "modifiers": [{"id": 42810, "kind": "modifierInvocation", "modifierName": {"id": 42809, "name": "as<PERSON><PERSON>", "nameLocations": ["820:7:40"], "nodeType": "IdentifierPath", "referencedDeclaration": 42723, "src": "820:7:40"}, "nodeType": "ModifierInvocation", "src": "820:7:40"}], "name": "counter_increment", "nameLocation": "793:17:40", "parameters": {"id": 42808, "nodeType": "ParameterList", "parameters": [], "src": "810:2:40"}, "returnParameters": {"id": 42811, "nodeType": "ParameterList", "parameters": [], "src": "828:0:40"}, "scope": 42858, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42829, "nodeType": "FunctionDefinition", "src": "870:72:40", "nodes": [], "body": {"id": 42828, "nodeType": "Block", "src": "910:32:40", "nodes": [], "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 42823, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42682, "src": "920:7:40", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}, "id": 42825, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "928:5:40", "memberName": "reset", "nodeType": "MemberAccess", "referencedDeclaration": 42513, "src": "920:13:40", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 42826, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "920:15:40", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42827, "nodeType": "ExpressionStatement", "src": "920:15:40"}]}, "functionSelector": "ab031669", "implemented": true, "kind": "function", "modifiers": [{"id": 42821, "kind": "modifierInvocation", "modifierName": {"id": 42820, "name": "as<PERSON><PERSON>", "nameLocations": ["902:7:40"], "nodeType": "IdentifierPath", "referencedDeclaration": 42723, "src": "902:7:40"}, "nodeType": "ModifierInvocation", "src": "902:7:40"}], "name": "counter_reset", "nameLocation": "879:13:40", "parameters": {"id": 42819, "nodeType": "ParameterList", "parameters": [], "src": "892:2:40"}, "returnParameters": {"id": 42822, "nodeType": "ParameterList", "parameters": [], "src": "910:0:40"}, "scope": 42858, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42843, "nodeType": "FunctionDefinition", "src": "948:106:40", "nodes": [], "body": {"id": 42842, "nodeType": "Block", "src": "1009:45:40", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42839, "name": "newNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42831, "src": "1037:9:40", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 42836, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42682, "src": "1019:7:40", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}, "id": 42838, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1027:9:40", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 42447, "src": "1019:17:40", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 42840, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1019:28:40", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42841, "nodeType": "ExpressionStatement", "src": "1019:28:40"}]}, "functionSelector": "4cc7ae6c", "implemented": true, "kind": "function", "modifiers": [{"id": 42834, "kind": "modifierInvocation", "modifierName": {"id": 42833, "name": "as<PERSON><PERSON>", "nameLocations": ["1001:7:40"], "nodeType": "IdentifierPath", "referencedDeclaration": 42723, "src": "1001:7:40"}, "nodeType": "ModifierInvocation", "src": "1001:7:40"}], "name": "counter_setNumber", "nameLocation": "957:17:40", "parameters": {"id": 42832, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42831, "mutability": "mutable", "name": "newNumber", "nameLocation": "983:9:40", "nodeType": "VariableDeclaration", "scope": 42843, "src": "975:17:40", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42830, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "975:7:40", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "974:19:40"}, "returnParameters": {"id": 42835, "nodeType": "ParameterList", "parameters": [], "src": "1009:0:40"}, "scope": 42858, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42857, "nodeType": "FunctionDefinition", "src": "1060:96:40", "nodes": [], "body": {"id": 42856, "nodeType": "Block", "src": "1116:40:40", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42853, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42845, "src": "1143:5:40", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 42850, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42682, "src": "1126:7:40", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}, "id": 42852, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1134:8:40", "memberName": "subtract", "nodeType": "MemberAccess", "referencedDeclaration": 42567, "src": "1126:16:40", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 42854, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1126:23:40", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42855, "nodeType": "ExpressionStatement", "src": "1126:23:40"}]}, "functionSelector": "6e76f7cf", "implemented": true, "kind": "function", "modifiers": [{"id": 42848, "kind": "modifierInvocation", "modifierName": {"id": 42847, "name": "as<PERSON><PERSON>", "nameLocations": ["1108:7:40"], "nodeType": "IdentifierPath", "referencedDeclaration": 42723, "src": "1108:7:40"}, "nodeType": "ModifierInvocation", "src": "1108:7:40"}], "name": "counter_subtract", "nameLocation": "1069:16:40", "parameters": {"id": 42846, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42845, "mutability": "mutable", "name": "value", "nameLocation": "1094:5:40", "nodeType": "VariableDeclaration", "scope": 42857, "src": "1086:13:40", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42844, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1086:7:40", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1085:15:40"}, "returnParameters": {"id": 42849, "nodeType": "ParameterList", "parameters": [], "src": "1116:0:40"}, "scope": 42858, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "abstract": true, "baseContracts": [{"baseName": {"id": 42778, "name": "BaseTargetFunctions", "nameLocations": ["399:19:40"], "nodeType": "IdentifierPath", "referencedDeclaration": 105, "src": "399:19:40"}, "id": 42779, "nodeType": "InheritanceSpecifier", "src": "399:19:40"}, {"baseName": {"id": 42780, "name": "Properties", "nameLocations": ["424:10:40"], "nodeType": "IdentifierPath", "referencedDeclaration": 42658, "src": "424:10:40"}, "id": 42781, "nodeType": "InheritanceSpecifier", "src": "424:10:40"}], "canonicalName": "CounterTargets", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": false, "linearizedBaseContracts": [42858, 42658, 105, 81, 42602, 42724, 42406, 40866, 40547, 88, 94], "name": "CounterTargets", "nameLocation": "377:14:40", "scope": 42859, "usedErrors": [40411, 40413, 40415, 40417, 40571, 40573, 40575], "usedEvents": []}], "license": "GPL-2.0"}, "id": 40}