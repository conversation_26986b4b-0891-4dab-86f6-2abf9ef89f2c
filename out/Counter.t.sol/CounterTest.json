{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "counter", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract Counter"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "testFuzz_Add", "inputs": [{"name": "initial", "type": "uint256", "internalType": "uint256"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testFuzz_IncrementSequence", "inputs": [{"name": "times", "type": "uint8", "internalType": "uint8"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testFuzz_SetNumber", "inputs": [{"name": "x", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testFuzz_Subtract", "inputs": [{"name": "initial", "type": "uint256", "internalType": "uint256"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_Add", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_AddOverflow", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_Decrement", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_DecrementAtZero", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_Increment", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_IncrementAtMaxValue", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_InitialValue", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_Reset", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_SetNumber", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_SetNumberAboveMax", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_Subtract", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_SubtractUnderflow", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "151:2974:22:-:0;;;3166:4:3;3126:44;;;;;;;;;;;;;;;;;;;;1087:4:14;1065:26;;;;;;;;;;;;;;;;;;;;151:2974:22;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "151:2974:22:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;769:132;;;:::i;:::-;;215:64;;;:::i;:::-;;2907:134:7;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3823:151;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;285:82:22;;;:::i;:::-;;2313:300;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;3684:133:7;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3385:141;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1335:178:22;;;:::i;:::-;;373:177;;;:::i;:::-;;1968:163;;;:::i;:::-;;2155:152;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;1656:138;;;:::i;:::-;;186:22;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3193:186:7;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1519:131:22;;;:::i;:::-;;1042:138;;;:::i;:::-;;2619:269;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2894:229;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;3047:140:7;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;556:207:22;;;:::i;:::-;;1186:111;;;:::i;:::-;;3532:146:7;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;907:129:22;;;:::i;:::-;;2754:147:7;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2459:141;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1306:195:2;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2606:142:7;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1800:162:22;;;:::i;:::-;;1065:26:14;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;769:132:22;808:7;;;;;;;;;;;:17;;;826:3;808:22;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;840:7;;;;;;;;;;;:13;;;:15;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;865:29;874:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;892:1;865:8;:29::i;:::-;769:132::o;215:64::-;259:13;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;249:7;;:23;;;;;;;;;;;;;;;;;;215:64::o;2907:134:7:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;3823:151::-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;285:82:22:-;331:29;340:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;358:1;331:8;:29::i;:::-;285:82::o;2313:300::-;336:42:0;2384:9:22;;;2405:4;2394:7;:15;;2384:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;2420:9:22;;;2439:4;2430:5;:13;;2420:24;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;2454:9:22;;;2483:4;2474:5;2464:7;:15;;;;:::i;:::-;:23;;2454:34;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2499:7;;;;;;;;;;;:17;;;2517:7;2499:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2535:7;;;;;;;;;;;:11;;;2547:5;2535:18;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2563:43;2572:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2600:5;2590:7;:15;;;;:::i;:::-;2563:8;:43::i;:::-;2313:300;;:::o;3684:133:7:-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;1335:178:22:-;1388:7;;;;;;;;;;;:17;;;1406:4;1388:23;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;1421:15:22;;;:56;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1487:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1335:178::o;373:177::-;416:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;445:29;454:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;472:1;445:8;:29::i;:::-;485:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;514:29;523:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;541:1;514:8;:29::i;:::-;373:177::o;1968:163::-;2019:7;;;;;;;;;;;:17;;;2037:1;2019:20;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;2049:15:22;;;:46;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2105:7;;;;;;;;;;;:16;;;2122:1;2105:19;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1968:163::o;2155:152::-;336:42:0;2211:9:22;;;2226:4;2221:1;:9;;2211:20;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2241:7;;;;;;;;;;;:17;;;2259:1;2241:20;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2271:29;2280:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2298:1;2271:8;:29::i;:::-;2155:152;:::o;1656:138::-;336:42:0;1707:15:22;;;:47;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1764:7;;;;;;;;;;;:17;;;1782:4;1764:23;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1656:138::o;186:22::-;;;;;;;;;;;;;:::o;3193:186:7:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3193:186;:::o;1519:131:22:-;336:42:0;1568:15:22;;;:46;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1624:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1519:131::o;1042:138::-;1084:7;;;;;;;;;;;:17;;;1102:2;1084:21;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1115:7;;;;;;;;;;;:16;;;1132:1;1115:19;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1144:29;1153:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1171:1;1144:8;:29::i;:::-;1042:138::o;2619:269::-;336:42:0;2695:9:22;;;2716:4;2705:7;:15;;2695:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;2731:9:22;;;2750:7;2741:5;:16;;2731:27;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2769:7;;;;;;;;;;;:17;;;2787:7;2769:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2805:7;;;;;;;;;;;:16;;;2822:5;2805:23;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2838:43;2847:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2875:5;2865:7;:15;;;;:::i;:::-;2838:8;:43::i;:::-;2619:269;;:::o;2894:229::-;336:42:0;2960:9:22;;;2979:3;2970:5;:12;;;;2960:23;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2999:9;3011:1;2999:13;;2994:80;3018:5;3014:9;;:1;:9;2994:80;;;3044:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3025:3;;;;;;;2994:80;;;;3083:33;3092:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3110:5;3083:33;;:8;:33::i;:::-;2894:229;:::o;3047:140:7:-;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3047:140;:::o;556:207:22:-;599:7;;;;;;;;;;;:17;;;617:1;599:20;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;629:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;658:29;667:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;685:1;658:8;:29::i;:::-;698:7;;;;;;;;;;;:17;;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;727:29;736:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;754:1;727:8;:29::i;:::-;556:207::o;1186:111::-;1229:7;;;;;;;;;;;:17;;;1247:2;1229:21;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1260:30;1269:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1287:2;1260:8;:30::i;:::-;1186:111::o;3532:146:7:-;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3532:146;:::o;907:129:22:-;944:7;;;;;;;;;;;:17;;;962:2;944:21;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;975:7;;;;;;;;;;;:11;;;987:1;975:14;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;999:30;1008:7;;;;;;;;;;;:14;;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1026:2;999:8;:30::i;:::-;907:129::o;2754:147:7:-;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2754:147;:::o;2459:141::-;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2459:141;:::o;1306:195:2:-;1345:4;1365:7;;;;;;;;;;;1361:134;;;1395:4;1388:11;;;;1361:134;1482:1;1474:10;;219:28;211:37;;1437:7;;;219:28;211:37;;1255:17;1437:33;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:47;;1430:54;;1306:195;;:::o;2606:142:7:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;1800:162:22:-;1845:7;;;;;;;;;;;:17;;;1863:3;1845:22;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;336:42:0;1877:15:22;;;:54;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1941:7;;;;;;;;;;;:11;;;1953:1;1941:14;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1800:162::o;1065:26:14:-;;;;;;;;;;;;;:::o;2664:153:2:-;2755:5;2747:4;:13;2743:68;;219:28;211:37;;2776:11;;;2788:4;2794:5;2776:24;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2743:68;2664:153;;:::o;-1:-1:-1:-;;;;;;;;:::o;7:114:24:-;74:6;108:5;102:12;92:22;;7:114;;;:::o;127:184::-;226:11;260:6;255:3;248:19;300:4;295:3;291:14;276:29;;127:184;;;;:::o;317:132::-;384:4;407:3;399:11;;437:4;432:3;428:14;420:22;;317:132;;;:::o;455:126::-;492:7;532:42;525:5;521:54;510:65;;455:126;;;:::o;587:96::-;624:7;653:24;671:5;653:24;:::i;:::-;642:35;;587:96;;;:::o;689:108::-;766:24;784:5;766:24;:::i;:::-;761:3;754:37;689:108;;:::o;803:179::-;872:10;893:46;935:3;927:6;893:46;:::i;:::-;971:4;966:3;962:14;948:28;;803:179;;;;:::o;988:113::-;1058:4;1090;1085:3;1081:14;1073:22;;988:113;;;:::o;1137:732::-;1256:3;1285:54;1333:5;1285:54;:::i;:::-;1355:86;1434:6;1429:3;1355:86;:::i;:::-;1348:93;;1465:56;1515:5;1465:56;:::i;:::-;1544:7;1575:1;1560:284;1585:6;1582:1;1579:13;1560:284;;;1661:6;1655:13;1688:63;1747:3;1732:13;1688:63;:::i;:::-;1681:70;;1774:60;1827:6;1774:60;:::i;:::-;1764:70;;1620:224;1607:1;1604;1600:9;1595:14;;1560:284;;;1564:14;1860:3;1853:10;;1261:608;;;1137:732;;;;:::o;1875:373::-;2018:4;2056:2;2045:9;2041:18;2033:26;;2105:9;2099:4;2095:20;2091:1;2080:9;2076:17;2069:47;2133:108;2236:4;2227:6;2133:108;:::i;:::-;2125:116;;1875:373;;;;:::o;2254:145::-;2352:6;2386:5;2380:12;2370:22;;2254:145;;;:::o;2405:215::-;2535:11;2569:6;2564:3;2557:19;2609:4;2604:3;2600:14;2585:29;;2405:215;;;;:::o;2626:163::-;2724:4;2747:3;2739:11;;2777:4;2772:3;2768:14;2760:22;;2626:163;;;:::o;2795:124::-;2872:6;2906:5;2900:12;2890:22;;2795:124;;;:::o;2925:184::-;3024:11;3058:6;3053:3;3046:19;3098:4;3093:3;3089:14;3074:29;;2925:184;;;;:::o;3115:142::-;3192:4;3215:3;3207:11;;3245:4;3240:3;3236:14;3228:22;;3115:142;;;:::o;3263:99::-;3315:6;3349:5;3343:12;3333:22;;3263:99;;;:::o;3368:159::-;3442:11;3476:6;3471:3;3464:19;3516:4;3511:3;3507:14;3492:29;;3368:159;;;;:::o;3533:139::-;3622:6;3617:3;3612;3606:23;3663:1;3654:6;3649:3;3645:16;3638:27;3533:139;;;:::o;3678:102::-;3719:6;3770:2;3766:7;3761:2;3754:5;3750:14;3746:28;3736:38;;3678:102;;;:::o;3786:357::-;3864:3;3892:39;3925:5;3892:39;:::i;:::-;3947:61;4001:6;3996:3;3947:61;:::i;:::-;3940:68;;4017:65;4075:6;4070:3;4063:4;4056:5;4052:16;4017:65;:::i;:::-;4107:29;4129:6;4107:29;:::i;:::-;4102:3;4098:39;4091:46;;3868:275;3786:357;;;;:::o;4149:196::-;4238:10;4273:66;4335:3;4327:6;4273:66;:::i;:::-;4259:80;;4149:196;;;;:::o;4351:123::-;4431:4;4463;4458:3;4454:14;4446:22;;4351:123;;;:::o;4508:971::-;4637:3;4666:64;4724:5;4666:64;:::i;:::-;4746:86;4825:6;4820:3;4746:86;:::i;:::-;4739:93;;4858:3;4903:4;4895:6;4891:17;4886:3;4882:27;4933:66;4993:5;4933:66;:::i;:::-;5022:7;5053:1;5038:396;5063:6;5060:1;5057:13;5038:396;;;5134:9;5128:4;5124:20;5119:3;5112:33;5185:6;5179:13;5213:84;5292:4;5277:13;5213:84;:::i;:::-;5205:92;;5320:70;5383:6;5320:70;:::i;:::-;5310:80;;5419:4;5414:3;5410:14;5403:21;;5098:336;5085:1;5082;5078:9;5073:14;;5038:396;;;5042:14;5450:4;5443:11;;5470:3;5463:10;;4642:837;;;;;4508:971;;;;:::o;5563:663::-;5684:3;5720:4;5715:3;5711:14;5807:4;5800:5;5796:16;5790:23;5826:63;5883:4;5878:3;5874:14;5860:12;5826:63;:::i;:::-;5735:164;5986:4;5979:5;5975:16;5969:23;6039:3;6033:4;6029:14;6022:4;6017:3;6013:14;6006:38;6065:123;6183:4;6169:12;6065:123;:::i;:::-;6057:131;;5909:290;6216:4;6209:11;;5689:537;5563:663;;;;:::o;6232:280::-;6363:10;6398:108;6502:3;6494:6;6398:108;:::i;:::-;6384:122;;6232:280;;;;:::o;6518:144::-;6619:4;6651;6646:3;6642:14;6634:22;;6518:144;;;:::o;6750:1159::-;6931:3;6960:85;7039:5;6960:85;:::i;:::-;7061:117;7171:6;7166:3;7061:117;:::i;:::-;7054:124;;7204:3;7249:4;7241:6;7237:17;7232:3;7228:27;7279:87;7360:5;7279:87;:::i;:::-;7389:7;7420:1;7405:459;7430:6;7427:1;7424:13;7405:459;;;7501:9;7495:4;7491:20;7486:3;7479:33;7552:6;7546:13;7580:126;7701:4;7686:13;7580:126;:::i;:::-;7572:134;;7729:91;7813:6;7729:91;:::i;:::-;7719:101;;7849:4;7844:3;7840:14;7833:21;;7465:399;7452:1;7449;7445:9;7440:14;;7405:459;;;7409:14;7880:4;7873:11;;7900:3;7893:10;;6936:973;;;;;6750:1159;;;;:::o;7915:497::-;8120:4;8158:2;8147:9;8143:18;8135:26;;8207:9;8201:4;8197:20;8193:1;8182:9;8178:17;8171:47;8235:170;8400:4;8391:6;8235:170;:::i;:::-;8227:178;;7915:497;;;;:::o;8499:117::-;8608:1;8605;8598:12;8745:77;8782:7;8811:5;8800:16;;8745:77;;;:::o;8828:122::-;8901:24;8919:5;8901:24;:::i;:::-;8894:5;8891:35;8881:63;;8940:1;8937;8930:12;8881:63;8828:122;:::o;8956:139::-;9002:5;9040:6;9027:20;9018:29;;9056:33;9083:5;9056:33;:::i;:::-;8956:139;;;;:::o;9101:474::-;9169:6;9177;9226:2;9214:9;9205:7;9201:23;9197:32;9194:119;;;9232:79;;:::i;:::-;9194:119;9352:1;9377:53;9422:7;9413:6;9402:9;9398:22;9377:53;:::i;:::-;9367:63;;9323:117;9479:2;9505:53;9550:7;9541:6;9530:9;9526:22;9505:53;:::i;:::-;9495:63;;9450:118;9101:474;;;;;:::o;9581:329::-;9640:6;9689:2;9677:9;9668:7;9664:23;9660:32;9657:119;;;9695:79;;:::i;:::-;9657:119;9815:1;9840:53;9885:7;9876:6;9865:9;9861:22;9840:53;:::i;:::-;9830:63;;9786:117;9581:329;;;;:::o;9916:60::-;9944:3;9965:5;9958:12;;9916:60;;;:::o;9982:142::-;10032:9;10065:53;10083:34;10092:24;10110:5;10092:24;:::i;:::-;10083:34;:::i;:::-;10065:53;:::i;:::-;10052:66;;9982:142;;;:::o;10130:126::-;10180:9;10213:37;10244:5;10213:37;:::i;:::-;10200:50;;10130:126;;;:::o;10262:143::-;10329:9;10362:37;10393:5;10362:37;:::i;:::-;10349:50;;10262:143;;;:::o;10411:165::-;10515:54;10563:5;10515:54;:::i;:::-;10510:3;10503:67;10411:165;;:::o;10582:256::-;10692:4;10730:2;10719:9;10715:18;10707:26;;10743:88;10828:1;10817:9;10813:17;10804:6;10743:88;:::i;:::-;10582:256;;;;:::o;10844:152::-;10949:6;10983:5;10977:12;10967:22;;10844:152;;;:::o;11002:222::-;11139:11;11173:6;11168:3;11161:19;11213:4;11208:3;11204:14;11189:29;;11002:222;;;;:::o;11230:170::-;11335:4;11358:3;11350:11;;11388:4;11383:3;11379:14;11371:22;;11230:170;;;:::o;11406:113::-;11472:6;11506:5;11500:12;11490:22;;11406:113;;;:::o;11525:173::-;11613:11;11647:6;11642:3;11635:19;11687:4;11682:3;11678:14;11663:29;;11525:173;;;;:::o;11704:131::-;11770:4;11793:3;11785:11;;11823:4;11818:3;11814:14;11806:22;;11704:131;;;:::o;11841:149::-;11877:7;11917:66;11910:5;11906:78;11895:89;;11841:149;;;:::o;11996:105::-;12071:23;12088:5;12071:23;:::i;:::-;12066:3;12059:36;11996:105;;:::o;12107:175::-;12174:10;12195:44;12235:3;12227:6;12195:44;:::i;:::-;12271:4;12266:3;12262:14;12248:28;;12107:175;;;;:::o;12288:112::-;12357:4;12389;12384:3;12380:14;12372:22;;12288:112;;;:::o;12434:704::-;12541:3;12570:53;12617:5;12570:53;:::i;:::-;12639:75;12707:6;12702:3;12639:75;:::i;:::-;12632:82;;12738:55;12787:5;12738:55;:::i;:::-;12816:7;12847:1;12832:281;12857:6;12854:1;12851:13;12832:281;;;12933:6;12927:13;12960:61;13017:3;13002:13;12960:61;:::i;:::-;12953:68;;13044:59;13096:6;13044:59;:::i;:::-;13034:69;;12892:221;12879:1;12876;12872:9;12867:14;;12832:281;;;12836:14;13129:3;13122:10;;12546:592;;;12434:704;;;;:::o;13236:730::-;13371:3;13407:4;13402:3;13398:14;13498:4;13491:5;13487:16;13481:23;13551:3;13545:4;13541:14;13534:4;13529:3;13525:14;13518:38;13577:73;13645:4;13631:12;13577:73;:::i;:::-;13569:81;;13422:239;13748:4;13741:5;13737:16;13731:23;13801:3;13795:4;13791:14;13784:4;13779:3;13775:14;13768:38;13827:101;13923:4;13909:12;13827:101;:::i;:::-;13819:109;;13671:268;13956:4;13949:11;;13376:590;13236:730;;;;:::o;13972:308::-;14117:10;14152:122;14270:3;14262:6;14152:122;:::i;:::-;14138:136;;13972:308;;;;:::o;14286:151::-;14394:4;14426;14421:3;14417:14;14409:22;;14286:151;;;:::o;14539:1215::-;14734:3;14763:92;14849:5;14763:92;:::i;:::-;14871:124;14988:6;14983:3;14871:124;:::i;:::-;14864:131;;15021:3;15066:4;15058:6;15054:17;15049:3;15045:27;15096:94;15184:5;15096:94;:::i;:::-;15213:7;15244:1;15229:480;15254:6;15251:1;15248:13;15229:480;;;15325:9;15319:4;15315:20;15310:3;15303:33;15376:6;15370:13;15404:140;15539:4;15524:13;15404:140;:::i;:::-;15396:148;;15567:98;15658:6;15567:98;:::i;:::-;15557:108;;15694:4;15689:3;15685:14;15678:21;;15289:420;15276:1;15273;15269:9;15264:14;;15229:480;;;15233:14;15725:4;15718:11;;15745:3;15738:10;;14739:1015;;;;;14539:1215;;;;:::o;15760:525::-;15979:4;16017:2;16006:9;16002:18;15994:26;;16066:9;16060:4;16056:20;16052:1;16041:9;16037:17;16030:47;16094:184;16273:4;16264:6;16094:184;:::i;:::-;16086:192;;15760:525;;;;:::o;16291:86::-;16326:7;16366:4;16359:5;16355:16;16344:27;;16291:86;;;:::o;16383:118::-;16454:22;16470:5;16454:22;:::i;:::-;16447:5;16444:33;16434:61;;16491:1;16488;16481:12;16434:61;16383:118;:::o;16507:135::-;16551:5;16589:6;16576:20;16567:29;;16605:31;16630:5;16605:31;:::i;:::-;16507:135;;;;:::o;16648:325::-;16705:6;16754:2;16742:9;16733:7;16729:23;16725:32;16722:119;;;16760:79;;:::i;:::-;16722:119;16880:1;16905:51;16948:7;16939:6;16928:9;16924:22;16905:51;:::i;:::-;16895:61;;16851:115;16648:325;;;;:::o;16979:194::-;17088:11;17122:6;17117:3;17110:19;17162:4;17157:3;17153:14;17138:29;;16979:194;;;;:::o;17207:991::-;17346:3;17375:64;17433:5;17375:64;:::i;:::-;17455:96;17544:6;17539:3;17455:96;:::i;:::-;17448:103;;17577:3;17622:4;17614:6;17610:17;17605:3;17601:27;17652:66;17712:5;17652:66;:::i;:::-;17741:7;17772:1;17757:396;17782:6;17779:1;17776:13;17757:396;;;17853:9;17847:4;17843:20;17838:3;17831:33;17904:6;17898:13;17932:84;18011:4;17996:13;17932:84;:::i;:::-;17924:92;;18039:70;18102:6;18039:70;:::i;:::-;18029:80;;18138:4;18133:3;18129:14;18122:21;;17817:336;17804:1;17801;17797:9;17792:14;;17757:396;;;17761:14;18169:4;18162:11;;18189:3;18182:10;;17351:847;;;;;17207:991;;;;:::o;18204:413::-;18367:4;18405:2;18394:9;18390:18;18382:26;;18454:9;18448:4;18444:20;18440:1;18429:9;18425:17;18418:47;18482:128;18605:4;18596:6;18482:128;:::i;:::-;18474:136;;18204:413;;;;:::o;18623:144::-;18720:6;18754:5;18748:12;18738:22;;18623:144;;;:::o;18773:214::-;18902:11;18936:6;18931:3;18924:19;18976:4;18971:3;18967:14;18952:29;;18773:214;;;;:::o;18993:162::-;19090:4;19113:3;19105:11;;19143:4;19138:3;19134:14;19126:22;;18993:162;;;:::o;19237:639::-;19356:3;19392:4;19387:3;19383:14;19479:4;19472:5;19468:16;19462:23;19498:63;19555:4;19550:3;19546:14;19532:12;19498:63;:::i;:::-;19407:164;19658:4;19651:5;19647:16;19641:23;19711:3;19705:4;19701:14;19694:4;19689:3;19685:14;19678:38;19737:101;19833:4;19819:12;19737:101;:::i;:::-;19729:109;;19581:268;19866:4;19859:11;;19361:515;19237:639;;;;:::o;19882:276::-;20011:10;20046:106;20148:3;20140:6;20046:106;:::i;:::-;20032:120;;19882:276;;;;:::o;20164:143::-;20264:4;20296;20291:3;20287:14;20279:22;;20164:143;;;:::o;20393:1151::-;20572:3;20601:84;20679:5;20601:84;:::i;:::-;20701:116;20810:6;20805:3;20701:116;:::i;:::-;20694:123;;20843:3;20888:4;20880:6;20876:17;20871:3;20867:27;20918:86;20998:5;20918:86;:::i;:::-;21027:7;21058:1;21043:456;21068:6;21065:1;21062:13;21043:456;;;21139:9;21133:4;21129:20;21124:3;21117:33;21190:6;21184:13;21218:124;21337:4;21322:13;21218:124;:::i;:::-;21210:132;;21365:90;21448:6;21365:90;:::i;:::-;21355:100;;21484:4;21479:3;21475:14;21468:21;;21103:396;21090:1;21087;21083:9;21078:14;;21043:456;;;21047:14;21515:4;21508:11;;21535:3;21528:10;;20577:967;;;;;20393:1151;;;;:::o;21550:493::-;21753:4;21791:2;21780:9;21776:18;21768:26;;21840:9;21834:4;21830:20;21826:1;21815:9;21811:17;21804:47;21868:168;22031:4;22022:6;21868:168;:::i;:::-;21860:176;;21550:493;;;;:::o;22049:90::-;22083:7;22126:5;22119:13;22112:21;22101:32;;22049:90;;;:::o;22145:109::-;22226:21;22241:5;22226:21;:::i;:::-;22221:3;22214:34;22145:109;;:::o;22260:210::-;22347:4;22385:2;22374:9;22370:18;22362:26;;22398:65;22460:1;22449:9;22445:17;22436:6;22398:65;:::i;:::-;22260:210;;;;:::o;22476:87::-;22523:7;22552:5;22541:16;;22476:87;;;:::o;22569:162::-;22629:9;22662:63;22680:44;22689:34;22717:5;22689:34;:::i;:::-;22680:44;:::i;:::-;22662:63;:::i;:::-;22649:76;;22569:162;;;:::o;22737:151::-;22834:47;22875:5;22834:47;:::i;:::-;22829:3;22822:60;22737:151;;:::o;22894:242::-;22997:4;23035:2;23024:9;23020:18;23012:26;;23048:81;23126:1;23115:9;23111:17;23102:6;23048:81;:::i;:::-;22894:242;;;;:::o;23142:143::-;23199:5;23230:6;23224:13;23215:22;;23246:33;23273:5;23246:33;:::i;:::-;23142:143;;;;:::o;23291:351::-;23361:6;23410:2;23398:9;23389:7;23385:23;23381:32;23378:119;;;23416:79;;:::i;:::-;23378:119;23536:1;23561:64;23617:7;23608:6;23597:9;23593:22;23561:64;:::i;:::-;23551:74;;23507:128;23291:351;;;;:::o;23648:180::-;23696:77;23693:1;23686:88;23793:4;23790:1;23783:15;23817:4;23814:1;23807:15;23834:320;23878:6;23915:1;23909:4;23905:12;23895:22;;23962:1;23956:4;23952:12;23983:18;23973:81;;24039:4;24031:6;24027:17;24017:27;;23973:81;24101:2;24093:6;24090:14;24070:18;24067:38;24064:84;;24120:18;;:::i;:::-;24064:84;23885:269;23834:320;;;:::o;24160:180::-;24208:77;24205:1;24198:88;24305:4;24302:1;24295:15;24329:4;24326:1;24319:15;24346:191;24386:3;24405:20;24423:1;24405:20;:::i;:::-;24400:25;;24439:20;24457:1;24439:20;:::i;:::-;24434:25;;24482:1;24479;24475:9;24468:16;;24503:3;24500:1;24497:10;24494:36;;;24510:18;;:::i;:::-;24494:36;24346:191;;;;:::o;24543:118::-;24630:24;24648:5;24630:24;:::i;:::-;24625:3;24618:37;24543:118;;:::o;24667:222::-;24760:4;24798:2;24787:9;24783:18;24775:26;;24811:71;24879:1;24868:9;24864:17;24855:6;24811:71;:::i;:::-;24667:222;;;;:::o;24895:88::-;24943:7;24972:5;24961:16;;24895:88;;;:::o;24989:164::-;25050:9;25083:64;25101:45;25110:35;25139:5;25110:35;:::i;:::-;25101:45;:::i;:::-;25083:64;:::i;:::-;25070:77;;24989:164;;;:::o;25159:153::-;25257:48;25299:5;25257:48;:::i;:::-;25252:3;25245:61;25159:153;;:::o;25318:244::-;25422:4;25460:2;25449:9;25445:18;25437:26;;25473:82;25552:1;25541:9;25537:17;25528:6;25473:82;:::i;:::-;25318:244;;;;:::o;25568:168::-;25651:11;25685:6;25680:3;25673:19;25725:4;25720:3;25716:14;25701:29;;25568:168;;;;:::o;25742:224::-;25882:34;25878:1;25870:6;25866:14;25859:58;25951:7;25946:2;25938:6;25934:15;25927:32;25742:224;:::o;25972:364::-;26113:3;26134:66;26197:2;26192:3;26134:66;:::i;:::-;26127:73;;26209:93;26298:3;26209:93;:::i;:::-;26327:2;26322:3;26318:12;26311:19;;25972:364;;;:::o;26342:417::-;26507:4;26545:2;26534:9;26530:18;26522:26;;26594:9;26588:4;26584:20;26580:1;26569:9;26565:17;26558:47;26622:130;26747:4;26622:130;:::i;:::-;26614:138;;26342:417;;;:::o;26765:85::-;26810:7;26839:5;26828:16;;26765:85;;;:::o;26856:158::-;26914:9;26947:61;26965:42;26974:32;27000:5;26974:32;:::i;:::-;26965:42;:::i;:::-;26947:61;:::i;:::-;26934:74;;26856:158;;;:::o;27020:147::-;27115:45;27154:5;27115:45;:::i;:::-;27110:3;27103:58;27020:147;;:::o;27173:238::-;27274:4;27312:2;27301:9;27297:18;27289:26;;27325:79;27401:1;27390:9;27386:17;27377:6;27325:79;:::i;:::-;27173:238;;;;:::o;27417:177::-;27557:29;27553:1;27545:6;27541:14;27534:53;27417:177;:::o;27600:364::-;27741:3;27762:66;27825:2;27820:3;27762:66;:::i;:::-;27755:73;;27837:93;27926:3;27837:93;:::i;:::-;27955:2;27950:3;27946:12;27939:19;;27600:364;;;:::o;27970:417::-;28135:4;28173:2;28162:9;28158:18;28150:26;;28222:9;28216:4;28212:20;28208:1;28197:9;28193:17;28186:47;28250:130;28375:4;28250:130;:::i;:::-;28242:138;;27970:417;;;:::o;28393:85::-;28438:7;28467:5;28456:16;;28393:85;;;:::o;28484:158::-;28542:9;28575:61;28593:42;28602:32;28628:5;28602:32;:::i;:::-;28593:42;:::i;:::-;28575:61;:::i;:::-;28562:74;;28484:158;;;:::o;28648:147::-;28743:45;28782:5;28743:45;:::i;:::-;28738:3;28731:58;28648:147;;:::o;28801:238::-;28902:4;28940:2;28929:9;28925:18;28917:26;;28953:79;29029:1;29018:9;29014:17;29005:6;28953:79;:::i;:::-;28801:238;;;;:::o;29045:178::-;29185:30;29181:1;29173:6;29169:14;29162:54;29045:178;:::o;29229:364::-;29370:3;29391:66;29454:2;29449:3;29391:66;:::i;:::-;29384:73;;29466:93;29555:3;29466:93;:::i;:::-;29584:2;29579:3;29575:12;29568:19;;29229:364;;;:::o;29599:417::-;29764:4;29802:2;29791:9;29787:18;29779:26;;29851:9;29845:4;29841:20;29837:1;29826:9;29822:17;29815:47;29879:130;30004:4;29879:130;:::i;:::-;29871:138;;29599:417;;;:::o;30022:88::-;30070:7;30099:5;30088:16;;30022:88;;;:::o;30116:164::-;30177:9;30210:64;30228:45;30237:35;30266:5;30237:35;:::i;:::-;30228:45;:::i;:::-;30210:64;:::i;:::-;30197:77;;30116:164;;;:::o;30286:153::-;30384:48;30426:5;30384:48;:::i;:::-;30379:3;30372:61;30286:153;;:::o;30445:244::-;30549:4;30587:2;30576:9;30572:18;30564:26;;30600:82;30679:1;30668:9;30664:17;30655:6;30600:82;:::i;:::-;30445:244;;;;:::o;30695:177::-;30835:29;30831:1;30823:6;30819:14;30812:53;30695:177;:::o;30878:364::-;31019:3;31040:66;31103:2;31098:3;31040:66;:::i;:::-;31033:73;;31115:93;31204:3;31115:93;:::i;:::-;31233:2;31228:3;31224:12;31217:19;;30878:364;;;:::o;31248:417::-;31413:4;31451:2;31440:9;31436:18;31428:26;;31500:9;31494:4;31490:20;31486:1;31475:9;31471:17;31464:47;31528:130;31653:4;31528:130;:::i;:::-;31520:138;;31248:417;;;:::o;31671:86::-;31717:7;31746:5;31735:16;;31671:86;;;:::o;31763:160::-;31822:9;31855:62;31873:43;31882:33;31909:5;31882:33;:::i;:::-;31873:43;:::i;:::-;31855:62;:::i;:::-;31842:75;;31763:160;;;:::o;31929:149::-;32025:46;32065:5;32025:46;:::i;:::-;32020:3;32013:59;31929:149;;:::o;32084:240::-;32186:4;32224:2;32213:9;32209:18;32201:26;;32237:80;32314:1;32303:9;32299:17;32290:6;32237:80;:::i;:::-;32084:240;;;;:::o;32330:85::-;32375:7;32404:5;32393:16;;32330:85;;;:::o;32421:158::-;32479:9;32512:61;32530:42;32539:32;32565:5;32539:32;:::i;:::-;32530:42;:::i;:::-;32512:61;:::i;:::-;32499:74;;32421:158;;;:::o;32585:147::-;32680:45;32719:5;32680:45;:::i;:::-;32675:3;32668:58;32585:147;;:::o;32738:238::-;32839:4;32877:2;32866:9;32862:18;32854:26;;32890:79;32966:1;32955:9;32951:17;32942:6;32890:79;:::i;:::-;32738:238;;;;:::o;32982:194::-;33022:4;33042:20;33060:1;33042:20;:::i;:::-;33037:25;;33076:20;33094:1;33076:20;:::i;:::-;33071:25;;33120:1;33117;33113:9;33105:17;;33144:1;33138:4;33135:11;33132:37;;;33149:18;;:::i;:::-;33132:37;32982:194;;;;:::o;33182:86::-;33228:7;33257:5;33246:16;;33182:86;;;:::o;33274:160::-;33333:9;33366:62;33384:43;33393:33;33420:5;33393:33;:::i;:::-;33384:43;:::i;:::-;33366:62;:::i;:::-;33353:75;;33274:160;;;:::o;33440:149::-;33536:46;33576:5;33536:46;:::i;:::-;33531:3;33524:59;33440:149;;:::o;33595:240::-;33697:4;33735:2;33724:9;33720:18;33712:26;;33748:80;33825:1;33814:9;33810:17;33801:6;33748:80;:::i;:::-;33595:240;;;;:::o;33841:118::-;33928:24;33946:5;33928:24;:::i;:::-;33923:3;33916:37;33841:118;;:::o;33965:77::-;34002:7;34031:5;34020:16;;33965:77;;;:::o;34048:118::-;34135:24;34153:5;34135:24;:::i;:::-;34130:3;34123:37;34048:118;;:::o;34172:332::-;34293:4;34331:2;34320:9;34316:18;34308:26;;34344:71;34412:1;34401:9;34397:17;34388:6;34344:71;:::i;:::-;34425:72;34493:2;34482:9;34478:18;34469:6;34425:72;:::i;:::-;34172:332;;;;;:::o;34510:122::-;34583:24;34601:5;34583:24;:::i;:::-;34576:5;34573:35;34563:63;;34622:1;34619;34612:12;34563:63;34510:122;:::o;34638:143::-;34695:5;34726:6;34720:13;34711:22;;34742:33;34769:5;34742:33;:::i;:::-;34638:143;;;;:::o;34787:351::-;34857:6;34906:2;34894:9;34885:7;34881:23;34877:32;34874:119;;;34912:79;;:::i;:::-;34874:119;35032:1;35057:64;35113:7;35104:6;35093:9;35089:22;35057:64;:::i;:::-;35047:74;;35003:128;34787:351;;;;:::o;35144:87::-;35191:7;35220:5;35209:16;;35144:87;;;:::o;35237:162::-;35297:9;35330:63;35348:44;35357:34;35385:5;35357:34;:::i;:::-;35348:44;:::i;:::-;35330:63;:::i;:::-;35317:76;;35237:162;;;:::o;35405:151::-;35502:47;35543:5;35502:47;:::i;:::-;35497:3;35490:60;35405:151;;:::o;35562:242::-;35665:4;35703:2;35692:9;35688:18;35680:26;;35716:81;35794:1;35783:9;35779:17;35770:6;35716:81;:::i;:::-;35562:242;;;;:::o;35810:222::-;35950:34;35946:1;35938:6;35934:14;35927:58;36019:5;36014:2;36006:6;36002:15;35995:30;35810:222;:::o;36038:364::-;36179:3;36200:66;36263:2;36258:3;36200:66;:::i;:::-;36193:73;;36275:93;36364:3;36275:93;:::i;:::-;36393:2;36388:3;36384:12;36377:19;;36038:364;;;:::o;36408:417::-;36573:4;36611:2;36600:9;36596:18;36588:26;;36660:9;36654:4;36650:20;36646:1;36635:9;36631:17;36624:47;36688:130;36813:4;36688:130;:::i;:::-;36680:138;;36408:417;;;:::o;36831:85::-;36876:7;36905:5;36894:16;;36831:85;;;:::o;36922:158::-;36980:9;37013:61;37031:42;37040:32;37066:5;37040:32;:::i;:::-;37031:42;:::i;:::-;37013:61;:::i;:::-;37000:74;;36922:158;;;:::o;37086:147::-;37181:45;37220:5;37181:45;:::i;:::-;37176:3;37169:58;37086:147;;:::o;37239:238::-;37340:4;37378:2;37367:9;37363:18;37355:26;;37391:79;37467:1;37456:9;37452:17;37443:6;37391:79;:::i;:::-;37239:238;;;;:::o;37483:332::-;37604:4;37642:2;37631:9;37627:18;37619:26;;37655:71;37723:1;37712:9;37708:17;37699:6;37655:71;:::i;:::-;37736:72;37804:2;37793:9;37789:18;37780:6;37736:72;:::i;:::-;37483:332;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "counter()": "61bc221a", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "setUp()": "0a9254e4", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "testFuzz_Add(uint256,uint256)": "2db0ee45", "testFuzz_IncrementSequence(uint8)": "82c0899e", "testFuzz_SetNumber(uint256)": "5c7f60d7", "testFuzz_Subtract(uint256,uint256)": "8227a7d7", "test_Add()": "98222acb", "test_AddOverflow()": "f091df33", "test_Decrement()": "88fb571b", "test_DecrementAtZero()": "79c99c54", "test_Increment()": "4820a105", "test_IncrementAtMaxValue()": "46f6245d", "test_InitialValue()": "2ba9bc17", "test_Reset()": "0887afb1", "test_SetNumber()": "8c879ab1", "test_SetNumberAboveMax()": "5d9e5868", "test_Subtract()": "81c95b33", "test_SubtractUnderflow()": "4ce6a8e6"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"counter\",\"outputs\":[{\"internalType\":\"contract Counter\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"initial\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"testFuzz_Add\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"times\",\"type\":\"uint8\"}],\"name\":\"testFuzz_IncrementSequence\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"x\",\"type\":\"uint256\"}],\"name\":\"testFuzz_SetNumber\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"initial\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"testFuzz_Subtract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_Add\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_AddOverflow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_Decrement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_DecrementAtZero\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_Increment\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_IncrementAtMaxValue\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_InitialValue\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_Reset\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_SetNumber\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_SetNumberAboveMax\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_Subtract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_SubtractUnderflow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/Counter.t.sol\":\"CounterTest\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3\",\"dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"src/Counter.sol\":{\"keccak256\":\"0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e\",\"dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2\"]},\"test/Counter.t.sol\":{\"keccak256\":\"0x072b40cab6fc7345f51af1eb4c914ffd66c8405d7271f0805bccd36358230915\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://6809d41f227e3823f19d44b6fae7acf523defa5257df6af513e634c1913bc121\",\"dweb:/ipfs/QmU6jTSVjok7Ca1eG6PdzwqNznXQ5oRJQVw734372dX7WZ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "counter", "outputs": [{"internalType": "contract Counter", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setUp"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [{"internalType": "uint256", "name": "initial", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "testFuzz_Add"}, {"inputs": [{"internalType": "uint8", "name": "times", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "testFuzz_IncrementSequence"}, {"inputs": [{"internalType": "uint256", "name": "x", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "testFuzz_SetNumber"}, {"inputs": [{"internalType": "uint256", "name": "initial", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "testFuzz_Subtract"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_Add"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_AddOverflow"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_Decrement"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_DecrementAtZero"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_Increment"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_IncrementAtMaxValue"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_InitialValue"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_Reset"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_SetNumber"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_SetNumberAboveMax"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_Subtract"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_SubtractUnderflow"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/Counter.t.sol": "CounterTest"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43", "urls": ["bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3", "dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "src/Counter.sol": {"keccak256": "0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c", "urls": ["bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e", "dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2"], "license": "UNLICENSED"}, "test/Counter.t.sol": {"keccak256": "0x072b40cab6fc7345f51af1eb4c914ffd66c8405d7271f0805bccd36358230915", "urls": ["bzz-raw://6809d41f227e3823f19d44b6fae7acf523defa5257df6af513e634c1913bc121", "dweb:/ipfs/QmU6jTSVjok7Ca1eG6PdzwqNznXQ5oRJQVw734372dX7WZ"], "license": "UNLICENSED"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 46, "contract": "test/Counter.t.sol:CounterTest", "label": "stdstore", "offset": 0, "slot": "0", "type": "t_struct(StdStorage)8331_storage"}, {"astId": 250, "contract": "test/Counter.t.sol:CounterTest", "label": "_failed", "offset": 0, "slot": "8", "type": "t_bool"}, {"astId": 2983, "contract": "test/Counter.t.sol:CounterTest", "label": "stdChainsInitialized", "offset": 1, "slot": "8", "type": "t_bool"}, {"astId": 3004, "contract": "test/Counter.t.sol:CounterTest", "label": "chains", "offset": 0, "slot": "9", "type": "t_mapping(t_string_memory_ptr,t_struct(Chain)2999_storage)"}, {"astId": 3008, "contract": "test/Counter.t.sol:CounterTest", "label": "defaultRpcUrls", "offset": 0, "slot": "10", "type": "t_mapping(t_string_memory_ptr,t_string_storage)"}, {"astId": 3012, "contract": "test/Counter.t.sol:CounterTest", "label": "idToAlias", "offset": 0, "slot": "11", "type": "t_mapping(t_uint256,t_string_storage)"}, {"astId": 3015, "contract": "test/Counter.t.sol:CounterTest", "label": "fallbackToDefaultRpcUrls", "offset": 0, "slot": "12", "type": "t_bool"}, {"astId": 3953, "contract": "test/Counter.t.sol:CounterTest", "label": "gasMeteringOff", "offset": 1, "slot": "12", "type": "t_bool"}, {"astId": 6020, "contract": "test/Counter.t.sol:CounterTest", "label": "stdstore", "offset": 0, "slot": "13", "type": "t_struct(StdStorage)8331_storage"}, {"astId": 6937, "contract": "test/Counter.t.sol:CounterTest", "label": "_excludedContracts", "offset": 0, "slot": "21", "type": "t_array(t_address)dyn_storage"}, {"astId": 6940, "contract": "test/Counter.t.sol:CounterTest", "label": "_excludedSenders", "offset": 0, "slot": "22", "type": "t_array(t_address)dyn_storage"}, {"astId": 6943, "contract": "test/Counter.t.sol:CounterTest", "label": "_targetedContracts", "offset": 0, "slot": "23", "type": "t_array(t_address)dyn_storage"}, {"astId": 6946, "contract": "test/Counter.t.sol:CounterTest", "label": "_targetedSenders", "offset": 0, "slot": "24", "type": "t_array(t_address)dyn_storage"}, {"astId": 6949, "contract": "test/Counter.t.sol:CounterTest", "label": "_excludedArtifacts", "offset": 0, "slot": "25", "type": "t_array(t_string_storage)dyn_storage"}, {"astId": 6952, "contract": "test/Counter.t.sol:CounterTest", "label": "_targetedArtifacts", "offset": 0, "slot": "26", "type": "t_array(t_string_storage)dyn_storage"}, {"astId": 6956, "contract": "test/Counter.t.sol:CounterTest", "label": "_targetedArtifactSelectors", "offset": 0, "slot": "27", "type": "t_array(t_struct(FuzzArtifactSelector)6928_storage)dyn_storage"}, {"astId": 6960, "contract": "test/Counter.t.sol:CounterTest", "label": "_excludedSelectors", "offset": 0, "slot": "28", "type": "t_array(t_struct(FuzzSelector)6922_storage)dyn_storage"}, {"astId": 6964, "contract": "test/Counter.t.sol:CounterTest", "label": "_targetedSelectors", "offset": 0, "slot": "29", "type": "t_array(t_struct(FuzzSelector)6922_storage)dyn_storage"}, {"astId": 6968, "contract": "test/Counter.t.sol:CounterTest", "label": "_targetedInterfaces", "offset": 0, "slot": "30", "type": "t_array(t_struct(FuzzInterface)6934_storage)dyn_storage"}, {"astId": 13223, "contract": "test/Counter.t.sol:CounterTest", "label": "IS_TEST", "offset": 0, "slot": "31", "type": "t_bool"}, {"astId": 39920, "contract": "test/Counter.t.sol:CounterTest", "label": "counter", "offset": 1, "slot": "31", "type": "t_contract(Counter)39909"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"encoding": "dynamic_array", "label": "address[]", "numberOfBytes": "32", "base": "t_address"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_array(t_bytes4)dyn_storage": {"encoding": "dynamic_array", "label": "bytes4[]", "numberOfBytes": "32", "base": "t_bytes4"}, "t_array(t_string_storage)dyn_storage": {"encoding": "dynamic_array", "label": "string[]", "numberOfBytes": "32", "base": "t_string_storage"}, "t_array(t_struct(FuzzArtifactSelector)6928_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzArtifactSelector[]", "numberOfBytes": "32", "base": "t_struct(FuzzArtifactSelector)6928_storage"}, "t_array(t_struct(FuzzInterface)6934_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzInterface[]", "numberOfBytes": "32", "base": "t_struct(FuzzInterface)6934_storage"}, "t_array(t_struct(FuzzSelector)6922_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzSelector[]", "numberOfBytes": "32", "base": "t_struct(FuzzSelector)6922_storage"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_bytes4": {"encoding": "inplace", "label": "bytes4", "numberOfBytes": "4"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_contract(Counter)39909": {"encoding": "inplace", "label": "contract Counter", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage)))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(bytes4 => mapping(bytes32 => struct FindData)))", "numberOfBytes": "32", "value": "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage))"}, "t_mapping(t_bytes32,t_struct(FindData)8306_storage)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => struct FindData)", "numberOfBytes": "32", "value": "t_struct(FindData)8306_storage"}, "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage))": {"encoding": "mapping", "key": "t_bytes4", "label": "mapping(bytes4 => mapping(bytes32 => struct FindData))", "numberOfBytes": "32", "value": "t_mapping(t_bytes32,t_struct(FindData)8306_storage)"}, "t_mapping(t_string_memory_ptr,t_string_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_mapping(t_string_memory_ptr,t_struct(Chain)2999_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => struct StdChains.Chain)", "numberOfBytes": "32", "value": "t_struct(Chain)2999_storage"}, "t_mapping(t_uint256,t_string_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_string_memory_ptr": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Chain)2999_storage": {"encoding": "inplace", "label": "struct StdChains.Chain", "numberOfBytes": "128", "members": [{"astId": 2992, "contract": "test/Counter.t.sol:CounterTest", "label": "name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 2994, "contract": "test/Counter.t.sol:CounterTest", "label": "chainId", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 2996, "contract": "test/Counter.t.sol:CounterTest", "label": "chainAlias", "offset": 0, "slot": "2", "type": "t_string_storage"}, {"astId": 2998, "contract": "test/Counter.t.sol:CounterTest", "label": "rpcUrl", "offset": 0, "slot": "3", "type": "t_string_storage"}]}, "t_struct(FindData)8306_storage": {"encoding": "inplace", "label": "struct FindData", "numberOfBytes": "128", "members": [{"astId": 8299, "contract": "test/Counter.t.sol:CounterTest", "label": "slot", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 8301, "contract": "test/Counter.t.sol:CounterTest", "label": "offsetLeft", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 8303, "contract": "test/Counter.t.sol:CounterTest", "label": "offsetRight", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 8305, "contract": "test/Counter.t.sol:CounterTest", "label": "found", "offset": 0, "slot": "3", "type": "t_bool"}]}, "t_struct(FuzzArtifactSelector)6928_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzArtifactSelector", "numberOfBytes": "64", "members": [{"astId": 6924, "contract": "test/Counter.t.sol:CounterTest", "label": "artifact", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 6927, "contract": "test/Counter.t.sol:CounterTest", "label": "selectors", "offset": 0, "slot": "1", "type": "t_array(t_bytes4)dyn_storage"}]}, "t_struct(FuzzInterface)6934_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzInterface", "numberOfBytes": "64", "members": [{"astId": 6930, "contract": "test/Counter.t.sol:CounterTest", "label": "addr", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 6933, "contract": "test/Counter.t.sol:CounterTest", "label": "artifacts", "offset": 0, "slot": "1", "type": "t_array(t_string_storage)dyn_storage"}]}, "t_struct(FuzzSelector)6922_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzSelector", "numberOfBytes": "64", "members": [{"astId": 6918, "contract": "test/Counter.t.sol:CounterTest", "label": "addr", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 6921, "contract": "test/Counter.t.sol:CounterTest", "label": "selectors", "offset": 0, "slot": "1", "type": "t_array(t_bytes4)dyn_storage"}]}, "t_struct(StdStorage)8331_storage": {"encoding": "inplace", "label": "struct StdStorage", "numberOfBytes": "256", "members": [{"astId": 8315, "contract": "test/Counter.t.sol:CounterTest", "label": "finds", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage)))"}, {"astId": 8318, "contract": "test/Counter.t.sol:CounterTest", "label": "_keys", "offset": 0, "slot": "1", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 8320, "contract": "test/Counter.t.sol:CounterTest", "label": "_sig", "offset": 0, "slot": "2", "type": "t_bytes4"}, {"astId": 8322, "contract": "test/Counter.t.sol:CounterTest", "label": "_depth", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 8324, "contract": "test/Counter.t.sol:CounterTest", "label": "_target", "offset": 0, "slot": "4", "type": "t_address"}, {"astId": 8326, "contract": "test/Counter.t.sol:CounterTest", "label": "_set", "offset": 0, "slot": "5", "type": "t_bytes32"}, {"astId": 8328, "contract": "test/Counter.t.sol:CounterTest", "label": "_enable_packed_slots", "offset": 0, "slot": "6", "type": "t_bool"}, {"astId": 8330, "contract": "test/Counter.t.sol:CounterTest", "label": "_calldata", "offset": 0, "slot": "7", "type": "t_bytes_storage"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "test/Counter.t.sol", "id": 40352, "exportedSymbols": {"Counter": [39909], "CounterTest": [40351], "Test": [13224]}, "nodeType": "SourceUnit", "src": "39:3087:22", "nodes": [{"id": 39911, "nodeType": "PragmaDirective", "src": "39:24:22", "nodes": [], "literals": ["solidity", "^", "0.8", ".13"]}, {"id": 39913, "nodeType": "ImportDirective", "src": "65:40:22", "nodes": [], "absolutePath": "lib/forge-std/src/Test.sol", "file": "forge-std/Test.sol", "nameLocation": "-1:-1:-1", "scope": 40352, "sourceUnit": 13225, "symbolAliases": [{"foreign": {"id": 39912, "name": "Test", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13224, "src": "73:4:22", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 39915, "nodeType": "ImportDirective", "src": "106:43:22", "nodes": [], "absolutePath": "src/Counter.sol", "file": "../src/Counter.sol", "nameLocation": "-1:-1:-1", "scope": 40352, "sourceUnit": 39910, "symbolAliases": [{"foreign": {"id": 39914, "name": "Counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39909, "src": "114:7:22", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 40351, "nodeType": "ContractDefinition", "src": "151:2974:22", "nodes": [{"id": 39920, "nodeType": "VariableDeclaration", "src": "186:22:22", "nodes": [], "constant": false, "functionSelector": "61bc221a", "mutability": "mutable", "name": "counter", "nameLocation": "201:7:22", "scope": 40351, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}, "typeName": {"id": 39919, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 39918, "name": "Counter", "nameLocations": ["186:7:22"], "nodeType": "IdentifierPath", "referencedDeclaration": 39909, "src": "186:7:22"}, "referencedDeclaration": 39909, "src": "186:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "visibility": "public"}, {"id": 39931, "nodeType": "FunctionDefinition", "src": "215:64:22", "nodes": [], "body": {"id": 39930, "nodeType": "Block", "src": "239:40:22", "nodes": [], "statements": [{"expression": {"id": 39928, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 39923, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "249:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [], "expression": {"argumentTypes": [], "id": 39926, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "NewExpression", "src": "259:11:22", "typeDescriptions": {"typeIdentifier": "t_function_creation_nonpayable$__$returns$_t_contract$_Counter_$39909_$", "typeString": "function () returns (contract Counter)"}, "typeName": {"id": 39925, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 39924, "name": "Counter", "nameLocations": ["263:7:22"], "nodeType": "IdentifierPath", "referencedDeclaration": 39909, "src": "263:7:22"}, "referencedDeclaration": 39909, "src": "263:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}}, "id": 39927, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "259:13:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "src": "249:23:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39929, "nodeType": "ExpressionStatement", "src": "249:23:22"}]}, "functionSelector": "0a9254e4", "implemented": true, "kind": "function", "modifiers": [], "name": "setUp", "nameLocation": "224:5:22", "parameters": {"id": 39921, "nodeType": "ParameterList", "parameters": [], "src": "229:2:22"}, "returnParameters": {"id": 39922, "nodeType": "ParameterList", "parameters": [], "src": "239:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 39942, "nodeType": "FunctionDefinition", "src": "285:82:22", "nodes": [], "body": {"id": 39941, "nodeType": "Block", "src": "321:46:22", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39935, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "340:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39936, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "348:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "340:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39937, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "340:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "30", "id": 39938, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "358:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 39934, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "331:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 39939, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "331:29:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39940, "nodeType": "ExpressionStatement", "src": "331:29:22"}]}, "functionSelector": "2ba9bc17", "implemented": true, "kind": "function", "modifiers": [], "name": "test_InitialValue", "nameLocation": "294:17:22", "parameters": {"id": 39932, "nodeType": "ParameterList", "parameters": [], "src": "311:2:22"}, "returnParameters": {"id": 39933, "nodeType": "ParameterList", "parameters": [], "src": "321:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 39970, "nodeType": "FunctionDefinition", "src": "373:177:22", "nodes": [], "body": {"id": 39969, "nodeType": "Block", "src": "406:144:22", "nodes": [], "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39945, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "416:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39947, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "424:9:22", "memberName": "increment", "nodeType": "MemberAccess", "referencedDeclaration": 39811, "src": "416:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 39948, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "416:19:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39949, "nodeType": "ExpressionStatement", "src": "416:19:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39951, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "454:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39952, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "462:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "454:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39953, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "454:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "31", "id": 39954, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "472:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}], "id": 39950, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "445:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 39955, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "445:29:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39956, "nodeType": "ExpressionStatement", "src": "445:29:22"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39957, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "485:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39959, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "493:9:22", "memberName": "increment", "nodeType": "MemberAccess", "referencedDeclaration": 39811, "src": "485:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 39960, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "485:19:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39961, "nodeType": "ExpressionStatement", "src": "485:19:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39963, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "523:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39964, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "531:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "523:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39965, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "523:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "32", "id": 39966, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "541:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}], "id": 39962, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "514:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 39967, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "514:29:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39968, "nodeType": "ExpressionStatement", "src": "514:29:22"}]}, "functionSelector": "4820a105", "implemented": true, "kind": "function", "modifiers": [], "name": "test_Increment", "nameLocation": "382:14:22", "parameters": {"id": 39943, "nodeType": "ParameterList", "parameters": [], "src": "396:2:22"}, "returnParameters": {"id": 39944, "nodeType": "ParameterList", "parameters": [], "src": "406:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40004, "nodeType": "FunctionDefinition", "src": "556:207:22", "nodes": [], "body": {"id": 40003, "nodeType": "Block", "src": "589:174:22", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "35", "id": 39976, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "617:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}, "value": "5"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}], "expression": {"id": 39973, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "599:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39975, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "607:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "599:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 39977, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "599:20:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39978, "nodeType": "ExpressionStatement", "src": "599:20:22"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39979, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "629:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39981, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "637:9:22", "memberName": "decrement", "nodeType": "MemberAccess", "referencedDeclaration": 39834, "src": "629:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 39982, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "629:19:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39983, "nodeType": "ExpressionStatement", "src": "629:19:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39985, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "667:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39986, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "675:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "667:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39987, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "667:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "34", "id": 39988, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "685:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_4_by_1", "typeString": "int_const 4"}, "value": "4"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_4_by_1", "typeString": "int_const 4"}], "id": 39984, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "658:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 39989, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "658:29:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39990, "nodeType": "ExpressionStatement", "src": "658:29:22"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39991, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "698:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39993, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "706:9:22", "memberName": "decrement", "nodeType": "MemberAccess", "referencedDeclaration": 39834, "src": "698:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 39994, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "698:19:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39995, "nodeType": "ExpressionStatement", "src": "698:19:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 39997, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "736:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 39998, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "744:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "736:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 39999, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "736:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "33", "id": 40000, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "754:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_3_by_1", "typeString": "int_const 3"}, "value": "3"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_3_by_1", "typeString": "int_const 3"}], "id": 39996, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "727:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 40001, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "727:29:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40002, "nodeType": "ExpressionStatement", "src": "727:29:22"}]}, "functionSelector": "88fb571b", "implemented": true, "kind": "function", "modifiers": [], "name": "test_Decrement", "nameLocation": "565:14:22", "parameters": {"id": 39971, "nodeType": "ParameterList", "parameters": [], "src": "579:2:22"}, "returnParameters": {"id": 39972, "nodeType": "ParameterList", "parameters": [], "src": "589:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40026, "nodeType": "FunctionDefinition", "src": "769:132:22", "nodes": [], "body": {"id": 40025, "nodeType": "Block", "src": "798:103:22", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "313030", "id": 40010, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "826:3:22", "typeDescriptions": {"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}, "value": "100"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}], "expression": {"id": 40007, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "808:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40009, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "816:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "808:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40011, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "808:22:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40012, "nodeType": "ExpressionStatement", "src": "808:22:22"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40013, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "840:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40015, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "848:5:22", "memberName": "reset", "nodeType": "MemberAccess", "referencedDeclaration": 39854, "src": "840:13:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 40016, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "840:15:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40017, "nodeType": "ExpressionStatement", "src": "840:15:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40019, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "874:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40020, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "882:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "874:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40021, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "874:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "30", "id": 40022, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "892:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 40018, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "865:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 40023, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "865:29:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40024, "nodeType": "ExpressionStatement", "src": "865:29:22"}]}, "functionSelector": "0887afb1", "implemented": true, "kind": "function", "modifiers": [], "name": "test_Reset", "nameLocation": "778:10:22", "parameters": {"id": 40005, "nodeType": "ParameterList", "parameters": [], "src": "788:2:22"}, "returnParameters": {"id": 40006, "nodeType": "ParameterList", "parameters": [], "src": "798:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40049, "nodeType": "FunctionDefinition", "src": "907:129:22", "nodes": [], "body": {"id": 40048, "nodeType": "Block", "src": "934:102:22", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "3130", "id": 40032, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "962:2:22", "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}], "expression": {"id": 40029, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "944:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40031, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "952:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "944:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40033, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "944:21:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40034, "nodeType": "ExpressionStatement", "src": "944:21:22"}, {"expression": {"arguments": [{"hexValue": "35", "id": 40038, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "987:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}, "value": "5"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}], "expression": {"id": 40035, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "975:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40037, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "983:3:22", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 39882, "src": "975:11:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40039, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "975:14:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40040, "nodeType": "ExpressionStatement", "src": "975:14:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40042, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1008:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40043, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1016:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "1008:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40044, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1008:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "3135", "id": 40045, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1026:2:22", "typeDescriptions": {"typeIdentifier": "t_rational_15_by_1", "typeString": "int_const 15"}, "value": "15"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_15_by_1", "typeString": "int_const 15"}], "id": 40041, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "999:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 40046, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "999:30:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40047, "nodeType": "ExpressionStatement", "src": "999:30:22"}]}, "functionSelector": "98222acb", "implemented": true, "kind": "function", "modifiers": [], "name": "test_Add", "nameLocation": "916:8:22", "parameters": {"id": 40027, "nodeType": "ParameterList", "parameters": [], "src": "924:2:22"}, "returnParameters": {"id": 40028, "nodeType": "ParameterList", "parameters": [], "src": "934:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40072, "nodeType": "FunctionDefinition", "src": "1042:138:22", "nodes": [], "body": {"id": 40071, "nodeType": "Block", "src": "1074:106:22", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "3130", "id": 40055, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1102:2:22", "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}], "expression": {"id": 40052, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1084:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40054, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1092:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "1084:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40056, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1084:21:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40057, "nodeType": "ExpressionStatement", "src": "1084:21:22"}, {"expression": {"arguments": [{"hexValue": "33", "id": 40061, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1132:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_3_by_1", "typeString": "int_const 3"}, "value": "3"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_3_by_1", "typeString": "int_const 3"}], "expression": {"id": 40058, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1115:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40060, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1123:8:22", "memberName": "subtract", "nodeType": "MemberAccess", "referencedDeclaration": 39908, "src": "1115:16:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40062, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1115:19:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40063, "nodeType": "ExpressionStatement", "src": "1115:19:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40065, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1153:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40066, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1161:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "1153:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40067, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1153:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "37", "id": 40068, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1171:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_7_by_1", "typeString": "int_const 7"}, "value": "7"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_7_by_1", "typeString": "int_const 7"}], "id": 40064, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "1144:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 40069, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1144:29:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40070, "nodeType": "ExpressionStatement", "src": "1144:29:22"}]}, "functionSelector": "81c95b33", "implemented": true, "kind": "function", "modifiers": [], "name": "test_Subtract", "nameLocation": "1051:13:22", "parameters": {"id": 40050, "nodeType": "ParameterList", "parameters": [], "src": "1064:2:22"}, "returnParameters": {"id": 40051, "nodeType": "ParameterList", "parameters": [], "src": "1074:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40089, "nodeType": "FunctionDefinition", "src": "1186:111:22", "nodes": [], "body": {"id": 40088, "nodeType": "Block", "src": "1219:78:22", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "3432", "id": 40078, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1247:2:22", "typeDescriptions": {"typeIdentifier": "t_rational_42_by_1", "typeString": "int_const 42"}, "value": "42"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_42_by_1", "typeString": "int_const 42"}], "expression": {"id": 40075, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1229:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40077, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1237:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "1229:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40079, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1229:21:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40080, "nodeType": "ExpressionStatement", "src": "1229:21:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40082, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1269:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40083, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1277:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "1269:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40084, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1269:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "3432", "id": 40085, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1287:2:22", "typeDescriptions": {"typeIdentifier": "t_rational_42_by_1", "typeString": "int_const 42"}, "value": "42"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_42_by_1", "typeString": "int_const 42"}], "id": 40081, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "1260:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 40086, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1260:30:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40087, "nodeType": "ExpressionStatement", "src": "1260:30:22"}]}, "functionSelector": "8c879ab1", "implemented": true, "kind": "function", "modifiers": [], "name": "test_SetNumber", "nameLocation": "1195:14:22", "parameters": {"id": 40073, "nodeType": "ParameterList", "parameters": [], "src": "1209:2:22"}, "returnParameters": {"id": 40074, "nodeType": "ParameterList", "parameters": [], "src": "1219:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40110, "nodeType": "FunctionDefinition", "src": "1335:178:22", "nodes": [], "body": {"id": 40109, "nodeType": "Block", "src": "1378:135:22", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "31303030", "id": 40095, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1406:4:22", "typeDescriptions": {"typeIdentifier": "t_rational_1000_by_1", "typeString": "int_const 1000"}, "value": "1000"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_1000_by_1", "typeString": "int_const 1000"}], "expression": {"id": 40092, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1388:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40094, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1396:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "1388:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40096, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1388:23:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40097, "nodeType": "ExpressionStatement", "src": "1388:23:22"}, {"expression": {"arguments": [{"hexValue": "43616e6e6f7420696e6372656d656e74206265796f6e64206d6178696d756d2076616c7565", "id": 40101, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1437:39:22", "typeDescriptions": {"typeIdentifier": "t_stringliteral_dc4ad0c2847e925214d60d7358aab774a9f3fd9a370fbde50cf6cd0455cea68b", "typeString": "literal_string \"Cannot increment beyond maximum value\""}, "value": "Cannot increment beyond maximum value"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_dc4ad0c2847e925214d60d7358aab774a9f3fd9a370fbde50cf6cd0455cea68b", "typeString": "literal_string \"Cannot increment beyond maximum value\""}], "expression": {"id": 40098, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "1421:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40100, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1424:12:22", "memberName": "expectRevert", "nodeType": "MemberAccess", "referencedDeclaration": 18261, "src": "1421:15:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) external"}}, "id": 40102, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1421:56:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40103, "nodeType": "ExpressionStatement", "src": "1421:56:22"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40104, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1487:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40106, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1495:9:22", "memberName": "increment", "nodeType": "MemberAccess", "referencedDeclaration": 39811, "src": "1487:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 40107, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1487:19:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40108, "nodeType": "ExpressionStatement", "src": "1487:19:22"}]}, "functionSelector": "46f6245d", "implemented": true, "kind": "function", "modifiers": [], "name": "test_IncrementAtMaxValue", "nameLocation": "1344:24:22", "parameters": {"id": 40090, "nodeType": "ParameterList", "parameters": [], "src": "1368:2:22"}, "returnParameters": {"id": 40091, "nodeType": "ParameterList", "parameters": [], "src": "1378:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40125, "nodeType": "FunctionDefinition", "src": "1519:131:22", "nodes": [], "body": {"id": 40124, "nodeType": "Block", "src": "1558:92:22", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "43616e6e6f742064656372656d656e742062656c6f77207a65726f", "id": 40116, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1584:29:22", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d1a36f333738d56db25706f3ee0d170f6f047b262c854995a688cb11b3df3828", "typeString": "literal_string \"Cannot decrement below zero\""}, "value": "Cannot decrement below zero"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_d1a36f333738d56db25706f3ee0d170f6f047b262c854995a688cb11b3df3828", "typeString": "literal_string \"Cannot decrement below zero\""}], "expression": {"id": 40113, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "1568:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40115, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1571:12:22", "memberName": "expectRevert", "nodeType": "MemberAccess", "referencedDeclaration": 18261, "src": "1568:15:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) external"}}, "id": 40117, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1568:46:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40118, "nodeType": "ExpressionStatement", "src": "1568:46:22"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40119, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1624:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40121, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1632:9:22", "memberName": "decrement", "nodeType": "MemberAccess", "referencedDeclaration": 39834, "src": "1624:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 40122, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1624:19:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40123, "nodeType": "ExpressionStatement", "src": "1624:19:22"}]}, "functionSelector": "79c99c54", "implemented": true, "kind": "function", "modifiers": [], "name": "test_DecrementAtZero", "nameLocation": "1528:20:22", "parameters": {"id": 40111, "nodeType": "ParameterList", "parameters": [], "src": "1548:2:22"}, "returnParameters": {"id": 40112, "nodeType": "ParameterList", "parameters": [], "src": "1558:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40141, "nodeType": "FunctionDefinition", "src": "1656:138:22", "nodes": [], "body": {"id": 40140, "nodeType": "Block", "src": "1697:97:22", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "4e756d6265722065786365656473206d6178696d756d2076616c7565", "id": 40131, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1723:30:22", "typeDescriptions": {"typeIdentifier": "t_stringliteral_1f7057b68f544209f2678bbc67b5d3111fa214ef9c8eb8c8097330ba8340bd48", "typeString": "literal_string \"Number exceeds maximum value\""}, "value": "Number exceeds maximum value"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_1f7057b68f544209f2678bbc67b5d3111fa214ef9c8eb8c8097330ba8340bd48", "typeString": "literal_string \"Number exceeds maximum value\""}], "expression": {"id": 40128, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "1707:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40130, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1710:12:22", "memberName": "expectRevert", "nodeType": "MemberAccess", "referencedDeclaration": 18261, "src": "1707:15:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) external"}}, "id": 40132, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1707:47:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40133, "nodeType": "ExpressionStatement", "src": "1707:47:22"}, {"expression": {"arguments": [{"hexValue": "31303031", "id": 40137, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1782:4:22", "typeDescriptions": {"typeIdentifier": "t_rational_1001_by_1", "typeString": "int_const 1001"}, "value": "1001"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_1001_by_1", "typeString": "int_const 1001"}], "expression": {"id": 40134, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1764:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40136, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1772:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "1764:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40138, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1764:23:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40139, "nodeType": "ExpressionStatement", "src": "1764:23:22"}]}, "functionSelector": "5d9e5868", "implemented": true, "kind": "function", "modifiers": [], "name": "test_SetNumberAboveMax", "nameLocation": "1665:22:22", "parameters": {"id": 40126, "nodeType": "ParameterList", "parameters": [], "src": "1687:2:22"}, "returnParameters": {"id": 40127, "nodeType": "ParameterList", "parameters": [], "src": "1697:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40163, "nodeType": "FunctionDefinition", "src": "1800:162:22", "nodes": [], "body": {"id": 40162, "nodeType": "Block", "src": "1835:127:22", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "393939", "id": 40147, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1863:3:22", "typeDescriptions": {"typeIdentifier": "t_rational_999_by_1", "typeString": "int_const 999"}, "value": "999"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_999_by_1", "typeString": "int_const 999"}], "expression": {"id": 40144, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1845:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40146, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1853:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "1845:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40148, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1845:22:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40149, "nodeType": "ExpressionStatement", "src": "1845:22:22"}, {"expression": {"arguments": [{"hexValue": "4164646974696f6e20776f756c6420657863656564206d6178696d756d2076616c7565", "id": 40153, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1893:37:22", "typeDescriptions": {"typeIdentifier": "t_stringliteral_5d6edfb1d28183084ca1af74059bf1cb57af67efcb1c81f7d881b49775afa072", "typeString": "literal_string \"Addition would exceed maximum value\""}, "value": "Addition would exceed maximum value"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_5d6edfb1d28183084ca1af74059bf1cb57af67efcb1c81f7d881b49775afa072", "typeString": "literal_string \"Addition would exceed maximum value\""}], "expression": {"id": 40150, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "1877:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40152, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1880:12:22", "memberName": "expectRevert", "nodeType": "MemberAccess", "referencedDeclaration": 18261, "src": "1877:15:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) external"}}, "id": 40154, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1877:54:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40155, "nodeType": "ExpressionStatement", "src": "1877:54:22"}, {"expression": {"arguments": [{"hexValue": "32", "id": 40159, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1953:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}], "expression": {"id": 40156, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "1941:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40158, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1949:3:22", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 39882, "src": "1941:11:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40160, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1941:14:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40161, "nodeType": "ExpressionStatement", "src": "1941:14:22"}]}, "functionSelector": "f091df33", "implemented": true, "kind": "function", "modifiers": [], "name": "test_AddOverflow", "nameLocation": "1809:16:22", "parameters": {"id": 40142, "nodeType": "ParameterList", "parameters": [], "src": "1825:2:22"}, "returnParameters": {"id": 40143, "nodeType": "ParameterList", "parameters": [], "src": "1835:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40185, "nodeType": "FunctionDefinition", "src": "1968:163:22", "nodes": [], "body": {"id": 40184, "nodeType": "Block", "src": "2009:122:22", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "35", "id": 40169, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2037:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}, "value": "5"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}], "expression": {"id": 40166, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "2019:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40168, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2027:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "2019:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40170, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2019:20:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40171, "nodeType": "ExpressionStatement", "src": "2019:20:22"}, {"expression": {"arguments": [{"hexValue": "5375627472616374696f6e20776f756c6420756e646572666c6f77", "id": 40175, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2065:29:22", "typeDescriptions": {"typeIdentifier": "t_stringliteral_bafc5048e29e1b5de6b86e7151b8bdcfb1fcf05c231391025069de3655c7401f", "typeString": "literal_string \"Subtraction would underflow\""}, "value": "Subtraction would underflow"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_bafc5048e29e1b5de6b86e7151b8bdcfb1fcf05c231391025069de3655c7401f", "typeString": "literal_string \"Subtraction would underflow\""}], "expression": {"id": 40172, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2049:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40174, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2052:12:22", "memberName": "expectRevert", "nodeType": "MemberAccess", "referencedDeclaration": 18261, "src": "2049:15:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) external"}}, "id": 40176, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2049:46:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40177, "nodeType": "ExpressionStatement", "src": "2049:46:22"}, {"expression": {"arguments": [{"hexValue": "36", "id": 40181, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2122:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_6_by_1", "typeString": "int_const 6"}, "value": "6"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_6_by_1", "typeString": "int_const 6"}], "expression": {"id": 40178, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "2105:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40180, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2113:8:22", "memberName": "subtract", "nodeType": "MemberAccess", "referencedDeclaration": 39908, "src": "2105:16:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40182, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2105:19:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40183, "nodeType": "ExpressionStatement", "src": "2105:19:22"}]}, "functionSelector": "4ce6a8e6", "implemented": true, "kind": "function", "modifiers": [], "name": "test_SubtractUnderflow", "nameLocation": "1977:22:22", "parameters": {"id": 40164, "nodeType": "ParameterList", "parameters": [], "src": "1999:2:22"}, "returnParameters": {"id": 40165, "nodeType": "ParameterList", "parameters": [], "src": "2009:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40212, "nodeType": "FunctionDefinition", "src": "2155:152:22", "nodes": [], "body": {"id": 40211, "nodeType": "Block", "src": "2201:106:22", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40195, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40193, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40187, "src": "2221:1:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "31303030", "id": 40194, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2226:4:22", "typeDescriptions": {"typeIdentifier": "t_rational_1000_by_1", "typeString": "int_const 1000"}, "value": "1000"}, "src": "2221:9:22", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40190, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2211:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40192, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2214:6:22", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16676, "src": "2211:9:22", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40196, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2211:20:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40197, "nodeType": "ExpressionStatement", "src": "2211:20:22"}, {"expression": {"arguments": [{"id": 40201, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40187, "src": "2259:1:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40198, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "2241:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40200, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2249:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "2241:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40202, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2241:20:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40203, "nodeType": "ExpressionStatement", "src": "2241:20:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40205, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "2280:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40206, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2288:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "2280:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40207, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2280:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 40208, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40187, "src": "2298:1:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 40204, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "2271:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 40209, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2271:29:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40210, "nodeType": "ExpressionStatement", "src": "2271:29:22"}]}, "functionSelector": "5c7f60d7", "implemented": true, "kind": "function", "modifiers": [], "name": "testFuzz_SetNumber", "nameLocation": "2164:18:22", "parameters": {"id": 40188, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40187, "mutability": "mutable", "name": "x", "nameLocation": "2191:1:22", "nodeType": "VariableDeclaration", "scope": 40212, "src": "2183:9:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40186, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2183:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2182:11:22"}, "returnParameters": {"id": 40189, "nodeType": "ParameterList", "parameters": [], "src": "2201:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40267, "nodeType": "FunctionDefinition", "src": "2313:300:22", "nodes": [], "body": {"id": 40266, "nodeType": "Block", "src": "2374:239:22", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40224, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40222, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40214, "src": "2394:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "31303030", "id": 40223, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2405:4:22", "typeDescriptions": {"typeIdentifier": "t_rational_1000_by_1", "typeString": "int_const 1000"}, "value": "1000"}, "src": "2394:15:22", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40219, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2384:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40221, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2387:6:22", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16676, "src": "2384:9:22", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40225, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2384:26:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40226, "nodeType": "ExpressionStatement", "src": "2384:26:22"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40232, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40230, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40216, "src": "2430:5:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "31303030", "id": 40231, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2439:4:22", "typeDescriptions": {"typeIdentifier": "t_rational_1000_by_1", "typeString": "int_const 1000"}, "value": "1000"}, "src": "2430:13:22", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40227, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2420:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40229, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2423:6:22", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16676, "src": "2420:9:22", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40233, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2420:24:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40234, "nodeType": "ExpressionStatement", "src": "2420:24:22"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40242, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40240, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40238, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40214, "src": "2464:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 40239, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40216, "src": "2474:5:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2464:15:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "31303030", "id": 40241, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2483:4:22", "typeDescriptions": {"typeIdentifier": "t_rational_1000_by_1", "typeString": "int_const 1000"}, "value": "1000"}, "src": "2464:23:22", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40235, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2454:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40237, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2457:6:22", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16676, "src": "2454:9:22", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40243, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2454:34:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40244, "nodeType": "ExpressionStatement", "src": "2454:34:22"}, {"expression": {"arguments": [{"id": 40248, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40214, "src": "2517:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40245, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "2499:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40247, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2507:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "2499:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40249, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2499:26:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40250, "nodeType": "ExpressionStatement", "src": "2499:26:22"}, {"expression": {"arguments": [{"id": 40254, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40216, "src": "2547:5:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40251, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "2535:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40253, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2543:3:22", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 39882, "src": "2535:11:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40255, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2535:18:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40256, "nodeType": "ExpressionStatement", "src": "2535:18:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40258, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "2572:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40259, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2580:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "2572:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40260, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2572:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40263, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40261, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40214, "src": "2590:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 40262, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40216, "src": "2600:5:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2590:15:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 40257, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "2563:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 40264, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2563:43:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40265, "nodeType": "ExpressionStatement", "src": "2563:43:22"}]}, "functionSelector": "2db0ee45", "implemented": true, "kind": "function", "modifiers": [], "name": "testFuzz_Add", "nameLocation": "2322:12:22", "parameters": {"id": 40217, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40214, "mutability": "mutable", "name": "initial", "nameLocation": "2343:7:22", "nodeType": "VariableDeclaration", "scope": 40267, "src": "2335:15:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40213, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2335:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 40216, "mutability": "mutable", "name": "value", "nameLocation": "2360:5:22", "nodeType": "VariableDeclaration", "scope": 40267, "src": "2352:13:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40215, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2352:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2334:32:22"}, "returnParameters": {"id": 40218, "nodeType": "ParameterList", "parameters": [], "src": "2374:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40312, "nodeType": "FunctionDefinition", "src": "2619:269:22", "nodes": [], "body": {"id": 40311, "nodeType": "Block", "src": "2685:203:22", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40279, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40277, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40269, "src": "2705:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "31303030", "id": 40278, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2716:4:22", "typeDescriptions": {"typeIdentifier": "t_rational_1000_by_1", "typeString": "int_const 1000"}, "value": "1000"}, "src": "2705:15:22", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40274, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2695:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40276, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2698:6:22", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16676, "src": "2695:9:22", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40280, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2695:26:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40281, "nodeType": "ExpressionStatement", "src": "2695:26:22"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40287, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40285, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40271, "src": "2741:5:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 40286, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40269, "src": "2750:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2741:16:22", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40282, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2731:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40284, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2734:6:22", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16676, "src": "2731:9:22", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40288, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2731:27:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40289, "nodeType": "ExpressionStatement", "src": "2731:27:22"}, {"expression": {"arguments": [{"id": 40293, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40269, "src": "2787:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40290, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "2769:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40292, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2777:9:22", "memberName": "setNumber", "nodeType": "MemberAccess", "referencedDeclaration": 39788, "src": "2769:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40294, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2769:26:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40295, "nodeType": "ExpressionStatement", "src": "2769:26:22"}, {"expression": {"arguments": [{"id": 40299, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40271, "src": "2822:5:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40296, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "2805:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40298, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2813:8:22", "memberName": "subtract", "nodeType": "MemberAccess", "referencedDeclaration": 39908, "src": "2805:16:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 40300, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2805:23:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40301, "nodeType": "ExpressionStatement", "src": "2805:23:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40303, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "2847:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40304, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2855:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "2847:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40305, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2847:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40308, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40306, "name": "initial", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40269, "src": "2865:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 40307, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40271, "src": "2875:5:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2865:15:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 40302, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "2838:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 40309, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2838:43:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40310, "nodeType": "ExpressionStatement", "src": "2838:43:22"}]}, "functionSelector": "8227a7d7", "implemented": true, "kind": "function", "modifiers": [], "name": "testFuzz_Subtract", "nameLocation": "2628:17:22", "parameters": {"id": 40272, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40269, "mutability": "mutable", "name": "initial", "nameLocation": "2654:7:22", "nodeType": "VariableDeclaration", "scope": 40312, "src": "2646:15:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40268, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2646:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 40271, "mutability": "mutable", "name": "value", "nameLocation": "2671:5:22", "nodeType": "VariableDeclaration", "scope": 40312, "src": "2663:13:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40270, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2663:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2645:32:22"}, "returnParameters": {"id": 40273, "nodeType": "ParameterList", "parameters": [], "src": "2685:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 40350, "nodeType": "FunctionDefinition", "src": "2894:229:22", "nodes": [], "body": {"id": 40349, "nodeType": "Block", "src": "2950:173:22", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 40322, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40320, "name": "times", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40314, "src": "2970:5:22", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "313030", "id": 40321, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2979:3:22", "typeDescriptions": {"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}, "value": "100"}, "src": "2970:12:22", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 40317, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2960:2:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "id": 40319, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2963:6:22", "memberName": "assume", "nodeType": "MemberAccess", "referencedDeclaration": 16676, "src": "2960:9:22", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure external"}}, "id": 40323, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2960:23:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40324, "nodeType": "ExpressionStatement", "src": "2960:23:22"}, {"body": {"id": 40340, "nodeType": "Block", "src": "3030:44:22", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40335, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "3044:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40337, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3052:9:22", "memberName": "increment", "nodeType": "MemberAccess", "referencedDeclaration": 39811, "src": "3044:17:22", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$__$", "typeString": "function () external"}}, "id": 40338, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3044:19:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40339, "nodeType": "ExpressionStatement", "src": "3044:19:22"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40331, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40329, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40326, "src": "3014:1:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 40330, "name": "times", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40314, "src": "3018:5:22", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "src": "3014:9:22", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40341, "initializationExpression": {"assignments": [40326], "declarations": [{"constant": false, "id": 40326, "mutability": "mutable", "name": "i", "nameLocation": "3007:1:22", "nodeType": "VariableDeclaration", "scope": 40341, "src": "2999:9:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40325, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2999:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40328, "initialValue": {"hexValue": "30", "id": 40327, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3011:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "2999:13:22"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 40333, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "3025:3:22", "subExpression": {"id": 40332, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40326, "src": "3025:1:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 40334, "nodeType": "ExpressionStatement", "src": "3025:3:22"}, "nodeType": "ForStatement", "src": "2994:80:22"}, {"expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40343, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39920, "src": "3092:7:22", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$39909", "typeString": "contract Counter"}}, "id": 40344, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3100:6:22", "memberName": "number", "nodeType": "MemberAccess", "referencedDeclaration": 39751, "src": "3092:14:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint256_$", "typeString": "function () view external returns (uint256)"}}, "id": 40345, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3092:16:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 40346, "name": "times", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40314, "src": "3110:5:22", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint8", "typeString": "uint8"}], "id": 40342, "name": "assertEq", "nodeType": "Identifier", "overloadedDeclarations": [405, 428, 448, 471, 530, 553, 612, 635, 655, 678, 736, 754, 769, 787, 804, 824, 841, 861, 878, 898, 915, 935, 952, 972, 989, 1009, 1026, 1046], "referencedDeclaration": 448, "src": "3083:8:22", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256) pure"}}, "id": 40347, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3083:33:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40348, "nodeType": "ExpressionStatement", "src": "3083:33:22"}]}, "functionSelector": "82c0899e", "implemented": true, "kind": "function", "modifiers": [], "name": "testFuzz_IncrementSequence", "nameLocation": "2903:26:22", "parameters": {"id": 40315, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40314, "mutability": "mutable", "name": "times", "nameLocation": "2936:5:22", "nodeType": "VariableDeclaration", "scope": 40350, "src": "2930:11:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 40313, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "2930:5:22", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "2929:13:22"}, "returnParameters": {"id": 40316, "nodeType": "ParameterList", "parameters": [], "src": "2950:0:22"}, "scope": 40351, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "abstract": false, "baseContracts": [{"baseName": {"id": 39916, "name": "Test", "nameLocations": ["175:4:22"], "nodeType": "IdentifierPath", "referencedDeclaration": 13224, "src": "175:4:22"}, "id": 39917, "nodeType": "InheritanceSpecifier", "src": "175:4:22"}], "canonicalName": "CounterTest", "contractDependencies": [39909], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [40351, 13224, 13170, 7207, 6806, 6011, 3921, 2959, 50, 47], "name": "CounterTest", "nameLocation": "160:11:22", "scope": 40352, "usedErrors": [], "usedEvents": [126, 130, 134, 138, 142, 146, 150, 154, 160, 166, 174, 182, 188, 194, 200, 206, 211, 216, 221, 228, 235, 242]}], "license": "UNLICENSED"}, "id": 22}