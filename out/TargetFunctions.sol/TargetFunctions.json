{"abi": [{"type": "function", "name": "add_new_asset", "inputs": [{"name": "decimals", "type": "uint8", "internalType": "uint8"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "asset_approve", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amt", "type": "uint128", "internalType": "uint128"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "asset_mint", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amt", "type": "uint128", "internalType": "uint128"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_add", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_decrement", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_increment", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_reset", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_setNumber", "inputs": [{"name": "newNumber", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "counter_subtract", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "switchActor", "inputs": [{"name": "entropy", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "switch_asset", "inputs": [{"name": "entropy", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "error", "name": "ActorExists", "inputs": []}, {"type": "error", "name": "ActorNotAdded", "inputs": []}, {"type": "error", "name": "ActorNotSetup", "inputs": []}, {"type": "error", "name": "DefaultActor", "inputs": []}, {"type": "error", "name": "Exists", "inputs": []}, {"type": "error", "name": "NotAdded", "inputs": []}, {"type": "error", "name": "NotSetup", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"add_new_asset(uint8)": "7fbba149", "asset_approve(address,uint128)": "2fa9dadc", "asset_mint(address,uint128)": "652e3935", "counter_add(uint256)": "c8441bf6", "counter_decrement()": "fdfba6cd", "counter_increment()": "a1e3ca41", "counter_reset()": "ab031669", "counter_setNumber(uint256)": "4cc7ae6c", "counter_subtract(uint256)": "6e76f7cf", "switchActor(uint256)": "6801b82e", "switch_asset(uint256)": "55ba98ff"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ActorExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotSetup\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DefaultActor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Exists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotSetup\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"decimals\",\"type\":\"uint8\"}],\"name\":\"add_new_asset\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"amt\",\"type\":\"uint128\"}],\"name\":\"asset_approve\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"amt\",\"type\":\"uint128\"}],\"name\":\"asset_mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"counter_add\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"counter_decrement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"counter_increment\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"counter_reset\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newNumber\",\"type\":\"uint256\"}],\"name\":\"counter_setNumber\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"counter_subtract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"entropy\",\"type\":\"uint256\"}],\"name\":\"switchActor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"entropy\",\"type\":\"uint256\"}],\"name\":\"switch_asset\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"add_new_asset(uint8)\":{\"details\":\"Deploy a new token and add it to the list of assets, then set it as the current asset\"},\"asset_approve(address,uint128)\":{\"details\":\"Approve to arbitrary address, uses Actor by default NOTE: You're almost always better off setting approvals in `Setup`\"},\"asset_mint(address,uint128)\":{\"details\":\"Mint to arbitrary address, uses owner by default, even though MockERC20 doesn't check\"},\"switchActor(uint256)\":{\"details\":\"Start acting as another actor\"},\"switch_asset(uint256)\":{\"details\":\"Starts using a new asset\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"counter_add(uint256)\":{\"notice\":\"AUTO GENERATED TARGET FUNCTIONS - WARNING: DO NOT DELETE OR MODIFY THIS LINE ///\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/recon/TargetFunctions.sol\":\"TargetFunctions\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/Asserts.sol\":{\"keccak256\":\"0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626\",\"dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK\"]},\"lib/chimera/src/BaseProperties.sol\":{\"keccak256\":\"0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848\",\"dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w\"]},\"lib/chimera/src/BaseSetup.sol\":{\"keccak256\":\"0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe\",\"dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1\"]},\"lib/chimera/src/BaseTargetFunctions.sol\":{\"keccak256\":\"0xe3b3de6200ab7039a14bb0a2a7e090402a36bd2c0c31c6d677d766b0f335bd60\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5326abd254a25c5bf8c2219e36641bc1288114271678dac8611d8365fc432851\",\"dweb:/ipfs/QmR4BF3JrTU3XhPVY4vPxGCQFXbUv94Bi48FjvgphroPxJ\"]},\"lib/chimera/src/Hevm.sol\":{\"keccak256\":\"0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b\",\"dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr\"]},\"lib/setup-helpers/src/ActorManager.sol\":{\"keccak256\":\"0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c\",\"dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB\"]},\"lib/setup-helpers/src/AssetManager.sol\":{\"keccak256\":\"0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa\",\"dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq\"]},\"lib/setup-helpers/src/EnumerableSet.sol\":{\"keccak256\":\"0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df\",\"dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW\"]},\"lib/setup-helpers/src/MockERC20.sol\":{\"keccak256\":\"0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578\",\"dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE\"]},\"lib/setup-helpers/src/Panic.sol\":{\"keccak256\":\"0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325\",\"dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd\"]},\"lib/setup-helpers/src/Utils.sol\":{\"keccak256\":\"0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523\",\"dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau\"]},\"src/Counter.sol\":{\"keccak256\":\"0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e\",\"dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2\"]},\"test/recon/BeforeAfter.sol\":{\"keccak256\":\"0x749640c8e4e366e58f610fd34fc71bfb6d0abdc4bb482366ca6a1fff07fcc36e\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://de1f26ec166caba53e9e04b74a5fcfe64cc39a1687a68d357d17b79d65a6bc75\",\"dweb:/ipfs/QmfFjMrfBQ6hGUyEyasvEBknA4SNdokrNCrrWHbPVKGz9k\"]},\"test/recon/Properties.sol\":{\"keccak256\":\"0x5dbc6eba0dc68541aee76cfb2c774eb53912665d44ba9b00e2b4c67ae4b600d9\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://f786cfc9b24abdc05071da9aad64929c9f7b5fd5c6ee317962ad587dff6a1777\",\"dweb:/ipfs/QmWw9B7jLeFgHQeJFcaSgb9SY7oNCLHG12f3owHV4dNDSM\"]},\"test/recon/Setup.sol\":{\"keccak256\":\"0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024\",\"dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk\"]},\"test/recon/TargetFunctions.sol\":{\"keccak256\":\"0x0e232245acc530f99719e4f7c91c3a2eab6ec05503a74fc76c61dea2b0cb12ff\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://b3eef06de9419cac79d41d82e3059b4b2622927be9ae8b7de7821af4a1c44581\",\"dweb:/ipfs/QmWKCuZxrek5osBdZ1v9dw2kwidpBb9wiXb2q3dW59Yn3q\"]},\"test/recon/targets/AdminTargets.sol\":{\"keccak256\":\"0x609af5d88accf96850234bd2366044b2ee1d16fa3cdc2a732eb5875ec42f1e91\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://2e5a3611cf25245e3eaeb7447811c276cd9015f4fe635f90d33a0819e737bac7\",\"dweb:/ipfs/QmXbuUSKiWwj2DqyQQ1wUtfnpKnvUV9eGH6Gimhn6vjoX3\"]},\"test/recon/targets/CounterTargets.sol\":{\"keccak256\":\"0x8161ebacdff8537dce038d235860e7f482db54c065c21c2e5dea6fa918f70062\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://e63dc03d78d1219484537042da69532dbbf939582892d6342a75b94a03c312a3\",\"dweb:/ipfs/QmXVsDiVa68bbXToqi8CpgS9vXJiv2dpp2J2C6J6fhtw7S\"]},\"test/recon/targets/DoomsdayTargets.sol\":{\"keccak256\":\"0x729afac232dba23725104bcaf81079cb82337721352d5e66a2869fd035527dd3\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://faad4922157b58fa99e320d84ec324c61edc6e7030a92e02cc024d25820deadb\",\"dweb:/ipfs/QmSuvPbt4iTjeKKiT5iNKJV9mbBA9Sspy8JER8a8wGGZb1\"]},\"test/recon/targets/ManagersTargets.sol\":{\"keccak256\":\"0x2d2c2ac36388f88d7e53b08c661d5057b0ed84e6b3251ab8d0bfd3e10cd5445a\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://7b7b122d9522ab44b79dc478afd41f1274cffe4aca1933bb7af1dd9dec0b3680\",\"dweb:/ipfs/QmVtJpXLyZvJMouhsssc7ULv6kr6KsALJv3Pq8UQYoTKUT\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ActorExists"}, {"inputs": [], "type": "error", "name": "ActorNotAdded"}, {"inputs": [], "type": "error", "name": "ActorNotSetup"}, {"inputs": [], "type": "error", "name": "DefaultActor"}, {"inputs": [], "type": "error", "name": "Exists"}, {"inputs": [], "type": "error", "name": "NotAdded"}, {"inputs": [], "type": "error", "name": "NotSetup"}, {"inputs": [{"internalType": "uint8", "name": "decimals", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "add_new_asset", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint128", "name": "amt", "type": "uint128"}], "stateMutability": "nonpayable", "type": "function", "name": "asset_approve"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint128", "name": "amt", "type": "uint128"}], "stateMutability": "nonpayable", "type": "function", "name": "asset_mint"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "counter_add"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "counter_decrement"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "counter_increment"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "counter_reset"}, {"inputs": [{"internalType": "uint256", "name": "newNumber", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "counter_setNumber"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "counter_subtract"}, {"inputs": [{"internalType": "uint256", "name": "entropy", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "switchActor"}, {"inputs": [{"internalType": "uint256", "name": "entropy", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "switch_asset"}], "devdoc": {"kind": "dev", "methods": {"add_new_asset(uint8)": {"details": "Deploy a new token and add it to the list of assets, then set it as the current asset"}, "asset_approve(address,uint128)": {"details": "Approve to arbitrary address, uses Actor by default NOTE: You're almost always better off setting approvals in `Setup`"}, "asset_mint(address,uint128)": {"details": "Mint to arbitrary address, uses owner by default, even though MockERC20 doesn't check"}, "switchActor(uint256)": {"details": "Start acting as another actor"}, "switch_asset(uint256)": {"details": "Starts using a new asset"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"counter_add(uint256)": {"notice": "AUTO GENERATED TARGET FUNCTIONS - WARNING: DO NOT DELETE OR MODIFY THIS LINE ///"}}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/recon/TargetFunctions.sol": "TargetFunctions"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/Asserts.sol": {"keccak256": "0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9", "urls": ["bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626", "dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK"], "license": "MIT"}, "lib/chimera/src/BaseProperties.sol": {"keccak256": "0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a", "urls": ["bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848", "dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w"], "license": "MIT"}, "lib/chimera/src/BaseSetup.sol": {"keccak256": "0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107", "urls": ["bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe", "dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1"], "license": "MIT"}, "lib/chimera/src/BaseTargetFunctions.sol": {"keccak256": "0xe3b3de6200ab7039a14bb0a2a7e090402a36bd2c0c31c6d677d766b0f335bd60", "urls": ["bzz-raw://5326abd254a25c5bf8c2219e36641bc1288114271678dac8611d8365fc432851", "dweb:/ipfs/QmR4BF3JrTU3XhPVY4vPxGCQFXbUv94Bi48FjvgphroPxJ"], "license": "MIT"}, "lib/chimera/src/Hevm.sol": {"keccak256": "0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe", "urls": ["bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b", "dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr"], "license": "MIT"}, "lib/setup-helpers/src/ActorManager.sol": {"keccak256": "0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06", "urls": ["bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c", "dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB"], "license": "GPL-2.0"}, "lib/setup-helpers/src/AssetManager.sol": {"keccak256": "0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc", "urls": ["bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa", "dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq"], "license": "GPL-2.0"}, "lib/setup-helpers/src/EnumerableSet.sol": {"keccak256": "0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514", "urls": ["bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df", "dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW"], "license": "MIT"}, "lib/setup-helpers/src/MockERC20.sol": {"keccak256": "0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f", "urls": ["bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578", "dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE"], "license": "AGPL-3.0-only"}, "lib/setup-helpers/src/Panic.sol": {"keccak256": "0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92", "urls": ["bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325", "dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd"], "license": "GPL-2.0"}, "lib/setup-helpers/src/Utils.sol": {"keccak256": "0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27", "urls": ["bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523", "dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau"], "license": "GPL-2.0"}, "src/Counter.sol": {"keccak256": "0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c", "urls": ["bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e", "dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2"], "license": "UNLICENSED"}, "test/recon/BeforeAfter.sol": {"keccak256": "0x749640c8e4e366e58f610fd34fc71bfb6d0abdc4bb482366ca6a1fff07fcc36e", "urls": ["bzz-raw://de1f26ec166caba53e9e04b74a5fcfe64cc39a1687a68d357d17b79d65a6bc75", "dweb:/ipfs/QmfFjMrfBQ6hGUyEyasvEBknA4SNdokrNCrrWHbPVKGz9k"], "license": "GPL-2.0"}, "test/recon/Properties.sol": {"keccak256": "0x5dbc6eba0dc68541aee76cfb2c774eb53912665d44ba9b00e2b4c67ae4b600d9", "urls": ["bzz-raw://f786cfc9b24abdc05071da9aad64929c9f7b5fd5c6ee317962ad587dff6a1777", "dweb:/ipfs/QmWw9B7jLeFgHQeJFcaSgb9SY7oNCLHG12f3owHV4dNDSM"], "license": "GPL-2.0"}, "test/recon/Setup.sol": {"keccak256": "0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5", "urls": ["bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024", "dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk"], "license": "GPL-2.0"}, "test/recon/TargetFunctions.sol": {"keccak256": "0x0e232245acc530f99719e4f7c91c3a2eab6ec05503a74fc76c61dea2b0cb12ff", "urls": ["bzz-raw://b3eef06de9419cac79d41d82e3059b4b2622927be9ae8b7de7821af4a1c44581", "dweb:/ipfs/QmWKCuZxrek5osBdZ1v9dw2kwidpBb9wiXb2q3dW59Yn3q"], "license": "GPL-2.0"}, "test/recon/targets/AdminTargets.sol": {"keccak256": "0x609af5d88accf96850234bd2366044b2ee1d16fa3cdc2a732eb5875ec42f1e91", "urls": ["bzz-raw://2e5a3611cf25245e3eaeb7447811c276cd9015f4fe635f90d33a0819e737bac7", "dweb:/ipfs/QmXbuUSKiWwj2DqyQQ1wUtfnpKnvUV9eGH6Gimhn6vjoX3"], "license": "GPL-2.0"}, "test/recon/targets/CounterTargets.sol": {"keccak256": "0x8161ebacdff8537dce038d235860e7f482db54c065c21c2e5dea6fa918f70062", "urls": ["bzz-raw://e63dc03d78d1219484537042da69532dbbf939582892d6342a75b94a03c312a3", "dweb:/ipfs/QmXVsDiVa68bbXToqi8CpgS9vXJiv2dpp2J2C6J6fhtw7S"], "license": "GPL-2.0"}, "test/recon/targets/DoomsdayTargets.sol": {"keccak256": "0x729afac232dba23725104bcaf81079cb82337721352d5e66a2869fd035527dd3", "urls": ["bzz-raw://faad4922157b58fa99e320d84ec324c61edc6e7030a92e02cc024d25820deadb", "dweb:/ipfs/QmSuvPbt4iTjeKKiT5iNKJV9mbBA9Sspy8JER8a8wGGZb1"], "license": "GPL-2.0"}, "test/recon/targets/ManagersTargets.sol": {"keccak256": "0x2d2c2ac36388f88d7e53b08c661d5057b0ed84e6b3251ab8d0bfd3e10cd5445a", "urls": ["bzz-raw://7b7b122d9522ab44b79dc478afd41f1274cffe4aca1933bb7af1dd9dec0b3680", "dweb:/ipfs/QmVtJpXLyZvJMouhsssc7ULv6kr6KsALJv3Pq8UQYoTKUT"], "license": "GPL-2.0"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 40405, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "_actor", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 40409, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "_actors", "offset": 0, "slot": "1", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 40565, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "__asset", "offset": 0, "slot": "3", "type": "t_address"}, {"astId": 40569, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "_assets", "offset": 0, "slot": "4", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 42682, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "counter", "offset": 0, "slot": "6", "type": "t_contract(Counter)42568"}, {"astId": 42580, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "_before", "offset": 0, "slot": "7", "type": "t_struct(Vars)42577_storage"}, {"astId": 42583, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "_after", "offset": 0, "slot": "8", "type": "t_struct(Vars)42577_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_contract(Counter)42568": {"encoding": "inplace", "label": "contract Counter", "numberOfBytes": "20"}, "t_mapping(t_bytes32,t_uint256)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(AddressSet)41192_storage": {"encoding": "inplace", "label": "struct EnumerableSet.AddressSet", "numberOfBytes": "64", "members": [{"astId": 41191, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "_inner", "offset": 0, "slot": "0", "type": "t_struct(Set)40877_storage"}]}, "t_struct(Set)40877_storage": {"encoding": "inplace", "label": "struct EnumerableSet.Set", "numberOfBytes": "64", "members": [{"astId": 40872, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "_values", "offset": 0, "slot": "0", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 40876, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "_indexes", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_uint256)"}]}, "t_struct(Vars)42577_storage": {"encoding": "inplace", "label": "struct BeforeAfter.Vars", "numberOfBytes": "32", "members": [{"astId": 42576, "contract": "test/recon/TargetFunctions.sol:TargetFunctions", "label": "__ignore__", "offset": 0, "slot": "0", "type": "t_uint256"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "test/recon/TargetFunctions.sol", "id": 42748, "exportedSymbols": {"AdminTargets": [42764], "CounterTargets": [42858], "DoomsdayTargets": [42884], "ManagersTargets": [42984], "Panic": [42052], "TargetFunctions": [42747], "vm": [719]}, "nodeType": "SourceUnit", "src": "36:766:38", "nodes": [{"id": 42726, "nodeType": "PragmaDirective", "src": "36:23:38", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 42728, "nodeType": "ImportDirective", "src": "77:37:38", "nodes": [], "absolutePath": "lib/chimera/src/Hevm.sol", "file": "@chimera/Hevm.sol", "nameLocation": "-1:-1:-1", "scope": 42748, "sourceUnit": 720, "symbolAliases": [{"foreign": {"id": 42727, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "85:2:38", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42730, "nodeType": "ImportDirective", "src": "127:39:38", "nodes": [], "absolutePath": "lib/setup-helpers/src/Panic.sol", "file": "@recon/Panic.sol", "nameLocation": "-1:-1:-1", "scope": 42748, "sourceUnit": 42053, "symbolAliases": [{"foreign": {"id": 42729, "name": "Panic", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42052, "src": "135:5:38", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42732, "nodeType": "ImportDirective", "src": "265:58:38", "nodes": [], "absolutePath": "test/recon/targets/AdminTargets.sol", "file": "./targets/AdminTargets.sol", "nameLocation": "-1:-1:-1", "scope": 42748, "sourceUnit": 42765, "symbolAliases": [{"foreign": {"id": 42731, "name": "AdminTargets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42764, "src": "274:12:38", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42734, "nodeType": "ImportDirective", "src": "324:62:38", "nodes": [], "absolutePath": "test/recon/targets/CounterTargets.sol", "file": "./targets/CounterTargets.sol", "nameLocation": "-1:-1:-1", "scope": 42748, "sourceUnit": 42859, "symbolAliases": [{"foreign": {"id": 42733, "name": "CounterTargets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42858, "src": "333:14:38", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42736, "nodeType": "ImportDirective", "src": "387:64:38", "nodes": [], "absolutePath": "test/recon/targets/DoomsdayTargets.sol", "file": "./targets/DoomsdayTargets.sol", "nameLocation": "-1:-1:-1", "scope": 42748, "sourceUnit": 42885, "symbolAliases": [{"foreign": {"id": 42735, "name": "DoomsdayTargets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42884, "src": "396:15:38", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42738, "nodeType": "ImportDirective", "src": "452:64:38", "nodes": [], "absolutePath": "test/recon/targets/ManagersTargets.sol", "file": "./targets/ManagersTargets.sol", "nameLocation": "-1:-1:-1", "scope": 42748, "sourceUnit": 42985, "symbolAliases": [{"foreign": {"id": 42737, "name": "ManagersTargets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42984, "src": "461:15:38", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42747, "nodeType": "ContractDefinition", "src": "518:283:38", "nodes": [], "abstract": true, "baseContracts": [{"baseName": {"id": 42739, "name": "AdminTargets", "nameLocations": ["559:12:38"], "nodeType": "IdentifierPath", "referencedDeclaration": 42764, "src": "559:12:38"}, "id": 42740, "nodeType": "InheritanceSpecifier", "src": "559:12:38"}, {"baseName": {"id": 42741, "name": "CounterTargets", "nameLocations": ["577:14:38"], "nodeType": "IdentifierPath", "referencedDeclaration": 42858, "src": "577:14:38"}, "id": 42742, "nodeType": "InheritanceSpecifier", "src": "577:14:38"}, {"baseName": {"id": 42743, "name": "DoomsdayTargets", "nameLocations": ["597:15:38"], "nodeType": "IdentifierPath", "referencedDeclaration": 42884, "src": "597:15:38"}, "id": 42744, "nodeType": "InheritanceSpecifier", "src": "597:15:38"}, {"baseName": {"id": 42745, "name": "ManagersTargets", "nameLocations": ["618:15:38"], "nodeType": "IdentifierPath", "referencedDeclaration": 42984, "src": "618:15:38"}, "id": 42746, "nodeType": "InheritanceSpecifier", "src": "618:15:38"}], "canonicalName": "TargetFunctions", "contractDependencies": [42022], "contractKind": "contract", "fullyImplemented": false, "linearizedBaseContracts": [42747, 42984, 42884, 42858, 42764, 42658, 105, 81, 42602, 42724, 42406, 40866, 40547, 88, 94], "name": "TargetFunctions", "nameLocation": "536:15:38", "scope": 42748, "usedErrors": [40411, 40413, 40415, 40417, 40571, 40573, 40575], "usedEvents": []}], "license": "GPL-2.0"}, "id": 38}