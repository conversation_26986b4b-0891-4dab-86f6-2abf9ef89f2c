{"abi": [], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/chimera/src/Asserts.sol\":\"Asserts\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/Asserts.sol\":{\"keccak256\":\"0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626\",\"dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/chimera/src/Asserts.sol": "Asserts"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/Asserts.sol": {"keccak256": "0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9", "urls": ["bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626", "dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/chimera/src/Asserts.sol", "id": 82, "exportedSymbols": {"Asserts": [81]}, "nodeType": "SourceUnit", "src": "32:764:0", "nodes": [{"id": 1, "nodeType": "PragmaDirective", "src": "32:23:0", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 81, "nodeType": "ContractDefinition", "src": "57:738:0", "nodes": [{"id": 10, "nodeType": "FunctionDefinition", "src": "89:73:0", "nodes": [], "implemented": false, "kind": "function", "modifiers": [], "name": "gt", "nameLocation": "98:2:0", "parameters": {"id": 8, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3, "mutability": "mutable", "name": "a", "nameLocation": "109:1:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "101:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "101:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 5, "mutability": "mutable", "name": "b", "nameLocation": "120:1:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "112:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "112:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 7, "mutability": "mutable", "name": "reason", "nameLocation": "137:6:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "123:20:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 6, "name": "string", "nodeType": "ElementaryTypeName", "src": "123:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "100:44:0"}, "returnParameters": {"id": 9, "nodeType": "ParameterList", "parameters": [], "src": "161:0:0"}, "scope": 81, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 19, "nodeType": "FunctionDefinition", "src": "168:74:0", "nodes": [], "implemented": false, "kind": "function", "modifiers": [], "name": "gte", "nameLocation": "177:3:0", "parameters": {"id": 17, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12, "mutability": "mutable", "name": "a", "nameLocation": "189:1:0", "nodeType": "VariableDeclaration", "scope": 19, "src": "181:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 11, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "181:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 14, "mutability": "mutable", "name": "b", "nameLocation": "200:1:0", "nodeType": "VariableDeclaration", "scope": 19, "src": "192:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 13, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "192:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 16, "mutability": "mutable", "name": "reason", "nameLocation": "217:6:0", "nodeType": "VariableDeclaration", "scope": 19, "src": "203:20:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 15, "name": "string", "nodeType": "ElementaryTypeName", "src": "203:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "180:44:0"}, "returnParameters": {"id": 18, "nodeType": "ParameterList", "parameters": [], "src": "241:0:0"}, "scope": 81, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 28, "nodeType": "FunctionDefinition", "src": "248:73:0", "nodes": [], "implemented": false, "kind": "function", "modifiers": [], "name": "lt", "nameLocation": "257:2:0", "parameters": {"id": 26, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 21, "mutability": "mutable", "name": "a", "nameLocation": "268:1:0", "nodeType": "VariableDeclaration", "scope": 28, "src": "260:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 20, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "260:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 23, "mutability": "mutable", "name": "b", "nameLocation": "279:1:0", "nodeType": "VariableDeclaration", "scope": 28, "src": "271:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 22, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "271:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 25, "mutability": "mutable", "name": "reason", "nameLocation": "296:6:0", "nodeType": "VariableDeclaration", "scope": 28, "src": "282:20:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 24, "name": "string", "nodeType": "ElementaryTypeName", "src": "282:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "259:44:0"}, "returnParameters": {"id": 27, "nodeType": "ParameterList", "parameters": [], "src": "320:0:0"}, "scope": 81, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 37, "nodeType": "FunctionDefinition", "src": "327:74:0", "nodes": [], "implemented": false, "kind": "function", "modifiers": [], "name": "lte", "nameLocation": "336:3:0", "parameters": {"id": 35, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 30, "mutability": "mutable", "name": "a", "nameLocation": "348:1:0", "nodeType": "VariableDeclaration", "scope": 37, "src": "340:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 29, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "340:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 32, "mutability": "mutable", "name": "b", "nameLocation": "359:1:0", "nodeType": "VariableDeclaration", "scope": 37, "src": "351:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 31, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "351:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 34, "mutability": "mutable", "name": "reason", "nameLocation": "376:6:0", "nodeType": "VariableDeclaration", "scope": 37, "src": "362:20:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 33, "name": "string", "nodeType": "ElementaryTypeName", "src": "362:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "339:44:0"}, "returnParameters": {"id": 36, "nodeType": "ParameterList", "parameters": [], "src": "400:0:0"}, "scope": 81, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 46, "nodeType": "FunctionDefinition", "src": "407:73:0", "nodes": [], "implemented": false, "kind": "function", "modifiers": [], "name": "eq", "nameLocation": "416:2:0", "parameters": {"id": 44, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 39, "mutability": "mutable", "name": "a", "nameLocation": "427:1:0", "nodeType": "VariableDeclaration", "scope": 46, "src": "419:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 38, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "419:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41, "mutability": "mutable", "name": "b", "nameLocation": "438:1:0", "nodeType": "VariableDeclaration", "scope": 46, "src": "430:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "430:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 43, "mutability": "mutable", "name": "reason", "nameLocation": "455:6:0", "nodeType": "VariableDeclaration", "scope": 46, "src": "441:20:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42, "name": "string", "nodeType": "ElementaryTypeName", "src": "441:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "418:44:0"}, "returnParameters": {"id": 45, "nodeType": "ParameterList", "parameters": [], "src": "479:0:0"}, "scope": 81, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 53, "nodeType": "FunctionDefinition", "src": "486:58:0", "nodes": [], "implemented": false, "kind": "function", "modifiers": [], "name": "t", "nameLocation": "495:1:0", "parameters": {"id": 51, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 48, "mutability": "mutable", "name": "b", "nameLocation": "502:1:0", "nodeType": "VariableDeclaration", "scope": 53, "src": "497:6:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 47, "name": "bool", "nodeType": "ElementaryTypeName", "src": "497:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 50, "mutability": "mutable", "name": "reason", "nameLocation": "519:6:0", "nodeType": "VariableDeclaration", "scope": 53, "src": "505:20:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 49, "name": "string", "nodeType": "ElementaryTypeName", "src": "505:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "496:30:0"}, "returnParameters": {"id": 52, "nodeType": "ParameterList", "parameters": [], "src": "543:0:0"}, "scope": 81, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 64, "nodeType": "FunctionDefinition", "src": "550:94:0", "nodes": [], "implemented": false, "kind": "function", "modifiers": [], "name": "between", "nameLocation": "559:7:0", "parameters": {"id": 60, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 55, "mutability": "mutable", "name": "value", "nameLocation": "575:5:0", "nodeType": "VariableDeclaration", "scope": 64, "src": "567:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 54, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "567:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 57, "mutability": "mutable", "name": "low", "nameLocation": "590:3:0", "nodeType": "VariableDeclaration", "scope": 64, "src": "582:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 56, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "582:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 59, "mutability": "mutable", "name": "high", "nameLocation": "603:4:0", "nodeType": "VariableDeclaration", "scope": 64, "src": "595:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 58, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "595:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "566:42:0"}, "returnParameters": {"id": 63, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 62, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 64, "src": "635:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 61, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "635:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "634:9:0"}, "scope": 81, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 75, "nodeType": "FunctionDefinition", "src": "650:90:0", "nodes": [], "implemented": false, "kind": "function", "modifiers": [], "name": "between", "nameLocation": "659:7:0", "parameters": {"id": 71, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 66, "mutability": "mutable", "name": "value", "nameLocation": "674:5:0", "nodeType": "VariableDeclaration", "scope": 75, "src": "667:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 65, "name": "int256", "nodeType": "ElementaryTypeName", "src": "667:6:0", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}, {"constant": false, "id": 68, "mutability": "mutable", "name": "low", "nameLocation": "688:3:0", "nodeType": "VariableDeclaration", "scope": 75, "src": "681:10:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 67, "name": "int256", "nodeType": "ElementaryTypeName", "src": "681:6:0", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}, {"constant": false, "id": 70, "mutability": "mutable", "name": "high", "nameLocation": "700:4:0", "nodeType": "VariableDeclaration", "scope": 75, "src": "693:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 69, "name": "int256", "nodeType": "ElementaryTypeName", "src": "693:6:0", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "666:39:0"}, "returnParameters": {"id": 74, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 73, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 75, "src": "732:6:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 72, "name": "int256", "nodeType": "ElementaryTypeName", "src": "732:6:0", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "731:8:0"}, "scope": 81, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 80, "nodeType": "FunctionDefinition", "src": "746:47:0", "nodes": [], "implemented": false, "kind": "function", "modifiers": [], "name": "precondition", "nameLocation": "755:12:0", "parameters": {"id": 78, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 77, "mutability": "mutable", "name": "p", "nameLocation": "773:1:0", "nodeType": "VariableDeclaration", "scope": 80, "src": "768:6:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 76, "name": "bool", "nodeType": "ElementaryTypeName", "src": "768:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "767:8:0"}, "returnParameters": {"id": 79, "nodeType": "ParameterList", "parameters": [], "src": "792:0:0"}, "scope": 81, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "abstract": true, "baseContracts": [], "canonicalName": "Asserts", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": false, "linearizedBaseContracts": [81], "name": "Asserts", "nameLocation": "75:7:0", "scope": 82, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 0}