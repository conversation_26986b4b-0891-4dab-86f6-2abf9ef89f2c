{"abi": [{"type": "constructor", "inputs": [{"name": "_name", "type": "string", "internalType": "string"}, {"name": "_symbol", "type": "string", "internalType": "string"}, {"name": "_decimals", "type": "uint8", "internalType": "uint8"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "burn", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "permit", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "InsufficientAllowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InsufficientBalance", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "MintOverflow", "inputs": [{"name": "currentSupply", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}], "bytecode": {"object": "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", "sourceMap": "8038:338:29:-:0;;;8072:108;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;8151:5;8158:7;8167:9;2712:5;2705:4;:12;;;;;;:::i;:::-;;2736:7;2727:6;:16;;;;;;:::i;:::-;;2764:9;2753:20;;;;;;;;;;2803:13;2784:32;;;;;;2853:24;:22;;;:24;;:::i;:::-;2826:51;;;;;;2622:262;;;8072:108;;;8038:338;;6483:402;6548:7;6625:95;6754:4;6738:22;;;;;;:::i;:::-;;;;;;;;6778:14;6810:13;6849:4;6597:271;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;6574:304;;;;;;6567:311;;6483:402;:::o;7:75:43:-;40:6;73:2;67:9;57:19;;7:75;:::o;88:117::-;197:1;194;187:12;211:117;320:1;317;310:12;334:117;443:1;440;433:12;457:117;566:1;563;556:12;580:102;621:6;672:2;668:7;663:2;656:5;652:14;648:28;638:38;;580:102;;;:::o;688:180::-;736:77;733:1;726:88;833:4;830:1;823:15;857:4;854:1;847:15;874:281;957:27;979:4;957:27;:::i;:::-;949:6;945:40;1087:6;1075:10;1072:22;1051:18;1039:10;1036:34;1033:62;1030:88;;;1098:18;;:::i;:::-;1030:88;1138:10;1134:2;1127:22;917:238;874:281;;:::o;1161:129::-;1195:6;1222:20;;:::i;:::-;1212:30;;1251:33;1279:4;1271:6;1251:33;:::i;:::-;1161:129;;;:::o;1296:308::-;1358:4;1448:18;1440:6;1437:30;1434:56;;;1470:18;;:::i;:::-;1434:56;1508:29;1530:6;1508:29;:::i;:::-;1500:37;;1592:4;1586;1582:15;1574:23;;1296:308;;;:::o;1610:139::-;1699:6;1694:3;1689;1683:23;1740:1;1731:6;1726:3;1722:16;1715:27;1610:139;;;:::o;1755:434::-;1844:5;1869:66;1885:49;1927:6;1885:49;:::i;:::-;1869:66;:::i;:::-;1860:75;;1958:6;1951:5;1944:21;1996:4;1989:5;1985:16;2034:3;2025:6;2020:3;2016:16;2013:25;2010:112;;;2041:79;;:::i;:::-;2010:112;2131:52;2176:6;2171:3;2166;2131:52;:::i;:::-;1850:339;1755:434;;;;;:::o;2209:355::-;2276:5;2325:3;2318:4;2310:6;2306:17;2302:27;2292:122;;2333:79;;:::i;:::-;2292:122;2443:6;2437:13;2468:90;2554:3;2546:6;2539:4;2531:6;2527:17;2468:90;:::i;:::-;2459:99;;2282:282;2209:355;;;;:::o;2570:86::-;2605:7;2645:4;2638:5;2634:16;2623:27;;2570:86;;;:::o;2662:118::-;2733:22;2749:5;2733:22;:::i;:::-;2726:5;2723:33;2713:61;;2770:1;2767;2760:12;2713:61;2662:118;:::o;2786:139::-;2841:5;2872:6;2866:13;2857:22;;2888:31;2913:5;2888:31;:::i;:::-;2786:139;;;;:::o;2931:1005::-;3037:6;3045;3053;3102:2;3090:9;3081:7;3077:23;3073:32;3070:119;;;3108:79;;:::i;:::-;3070:119;3249:1;3238:9;3234:17;3228:24;3279:18;3271:6;3268:30;3265:117;;;3301:79;;:::i;:::-;3265:117;3406:74;3472:7;3463:6;3452:9;3448:22;3406:74;:::i;:::-;3396:84;;3199:291;3550:2;3539:9;3535:18;3529:25;3581:18;3573:6;3570:30;3567:117;;;3603:79;;:::i;:::-;3567:117;3708:74;3774:7;3765:6;3754:9;3750:22;3708:74;:::i;:::-;3698:84;;3500:292;3831:2;3857:62;3911:7;3902:6;3891:9;3887:22;3857:62;:::i;:::-;3847:72;;3802:127;2931:1005;;;;;:::o;3942:99::-;3994:6;4028:5;4022:12;4012:22;;3942:99;;;:::o;4047:180::-;4095:77;4092:1;4085:88;4192:4;4189:1;4182:15;4216:4;4213:1;4206:15;4233:320;4277:6;4314:1;4308:4;4304:12;4294:22;;4361:1;4355:4;4351:12;4382:18;4372:81;;4438:4;4430:6;4426:17;4416:27;;4372:81;4500:2;4492:6;4489:14;4469:18;4466:38;4463:84;;4519:18;;:::i;:::-;4463:84;4284:269;4233:320;;;:::o;4559:141::-;4608:4;4631:3;4623:11;;4654:3;4651:1;4644:14;4688:4;4685:1;4675:18;4667:26;;4559:141;;;:::o;4706:93::-;4743:6;4790:2;4785;4778:5;4774:14;4770:23;4760:33;;4706:93;;;:::o;4805:107::-;4849:8;4899:5;4893:4;4889:16;4868:37;;4805:107;;;;:::o;4918:393::-;4987:6;5037:1;5025:10;5021:18;5060:97;5090:66;5079:9;5060:97;:::i;:::-;5178:39;5208:8;5197:9;5178:39;:::i;:::-;5166:51;;5250:4;5246:9;5239:5;5235:21;5226:30;;5299:4;5289:8;5285:19;5278:5;5275:30;5265:40;;4994:317;;4918:393;;;;;:::o;5317:77::-;5354:7;5383:5;5372:16;;5317:77;;;:::o;5400:60::-;5428:3;5449:5;5442:12;;5400:60;;;:::o;5466:142::-;5516:9;5549:53;5567:34;5576:24;5594:5;5576:24;:::i;:::-;5567:34;:::i;:::-;5549:53;:::i;:::-;5536:66;;5466:142;;;:::o;5614:75::-;5657:3;5678:5;5671:12;;5614:75;;;:::o;5695:269::-;5805:39;5836:7;5805:39;:::i;:::-;5866:91;5915:41;5939:16;5915:41;:::i;:::-;5907:6;5900:4;5894:11;5866:91;:::i;:::-;5860:4;5853:105;5771:193;5695:269;;;:::o;5970:73::-;6015:3;6036:1;6029:8;;5970:73;:::o;6049:189::-;6126:32;;:::i;:::-;6167:65;6225:6;6217;6211:4;6167:65;:::i;:::-;6102:136;6049:189;;:::o;6244:186::-;6304:120;6321:3;6314:5;6311:14;6304:120;;;6375:39;6412:1;6405:5;6375:39;:::i;:::-;6348:1;6341:5;6337:13;6328:22;;6304:120;;;6244:186;;:::o;6436:543::-;6537:2;6532:3;6529:11;6526:446;;;6571:38;6603:5;6571:38;:::i;:::-;6655:29;6673:10;6655:29;:::i;:::-;6645:8;6641:44;6838:2;6826:10;6823:18;6820:49;;;6859:8;6844:23;;6820:49;6882:80;6938:22;6956:3;6938:22;:::i;:::-;6928:8;6924:37;6911:11;6882:80;:::i;:::-;6541:431;;6526:446;6436:543;;;:::o;6985:117::-;7039:8;7089:5;7083:4;7079:16;7058:37;;6985:117;;;;:::o;7108:169::-;7152:6;7185:51;7233:1;7229:6;7221:5;7218:1;7214:13;7185:51;:::i;:::-;7181:56;7266:4;7260;7256:15;7246:25;;7159:118;7108:169;;;;:::o;7282:295::-;7358:4;7504:29;7529:3;7523:4;7504:29;:::i;:::-;7496:37;;7566:3;7563:1;7559:11;7553:4;7550:21;7542:29;;7282:295;;;;:::o;7582:1395::-;7699:37;7732:3;7699:37;:::i;:::-;7801:18;7793:6;7790:30;7787:56;;;7823:18;;:::i;:::-;7787:56;7867:38;7899:4;7893:11;7867:38;:::i;:::-;7952:67;8012:6;8004;7998:4;7952:67;:::i;:::-;8046:1;8070:4;8057:17;;8102:2;8094:6;8091:14;8119:1;8114:618;;;;8776:1;8793:6;8790:77;;;8842:9;8837:3;8833:19;8827:26;8818:35;;8790:77;8893:67;8953:6;8946:5;8893:67;:::i;:::-;8887:4;8880:81;8749:222;8084:887;;8114:618;8166:4;8162:9;8154:6;8150:22;8200:37;8232:4;8200:37;:::i;:::-;8259:1;8273:208;8287:7;8284:1;8281:14;8273:208;;;8366:9;8361:3;8357:19;8351:26;8343:6;8336:42;8417:1;8409:6;8405:14;8395:24;;8464:2;8453:9;8449:18;8436:31;;8310:4;8307:1;8303:12;8298:17;;8273:208;;;8509:6;8500:7;8497:19;8494:179;;;8567:9;8562:3;8558:19;8552:26;8610:48;8652:4;8644:6;8640:17;8629:9;8610:48;:::i;:::-;8602:6;8595:64;8517:156;8494:179;8719:1;8715;8707:6;8703:14;8699:22;8693:4;8686:36;8121:611;;;8084:887;;7674:1303;;;7582:1395;;:::o;8983:147::-;9084:11;9121:3;9106:18;;8983:147;;;;:::o;9136:144::-;9188:4;9211:3;9203:11;;9234:3;9231:1;9224:14;9268:4;9265:1;9255:18;9247:26;;9136:144;;;:::o;9308:878::-;9413:3;9450:5;9444:12;9479:36;9505:9;9479:36;:::i;:::-;9531:88;9612:6;9607:3;9531:88;:::i;:::-;9524:95;;9650:1;9639:9;9635:17;9666:1;9661:166;;;;9841:1;9836:344;;;;9628:552;;9661:166;9745:4;9741:9;9730;9726:25;9721:3;9714:38;9807:6;9800:14;9793:22;9785:6;9781:35;9776:3;9772:45;9765:52;;9661:166;;9836:344;9903:41;9938:5;9903:41;:::i;:::-;9966:1;9980:154;9994:6;9991:1;9988:13;9980:154;;;10068:7;10062:14;10058:1;10053:3;10049:11;10042:35;10118:1;10109:7;10105:15;10094:26;;10016:4;10013:1;10009:12;10004:17;;9980:154;;;10163:6;10158:3;10154:16;10147:23;;9843:337;;9628:552;;9417:769;;9308:878;;;;:::o;10192:273::-;10323:3;10345:94;10435:3;10426:6;10345:94;:::i;:::-;10338:101;;10456:3;10449:10;;10192:273;;;;:::o;10471:77::-;10508:7;10537:5;10526:16;;10471:77;;;:::o;10554:118::-;10641:24;10659:5;10641:24;:::i;:::-;10636:3;10629:37;10554:118;;:::o;10678:::-;10765:24;10783:5;10765:24;:::i;:::-;10760:3;10753:37;10678:118;;:::o;10802:126::-;10839:7;10879:42;10872:5;10868:54;10857:65;;10802:126;;;:::o;10934:96::-;10971:7;11000:24;11018:5;11000:24;:::i;:::-;10989:35;;10934:96;;;:::o;11036:118::-;11123:24;11141:5;11123:24;:::i;:::-;11118:3;11111:37;11036:118;;:::o;11160:664::-;11365:4;11403:3;11392:9;11388:19;11380:27;;11417:71;11485:1;11474:9;11470:17;11461:6;11417:71;:::i;:::-;11498:72;11566:2;11555:9;11551:18;11542:6;11498:72;:::i;:::-;11580;11648:2;11637:9;11633:18;11624:6;11580:72;:::i;:::-;11662;11730:2;11719:9;11715:18;11706:6;11662:72;:::i;:::-;11744:73;11812:3;11801:9;11797:19;11788:6;11744:73;:::i;:::-;11160:664;;;;;;;;:::o;8038:338:29:-;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "8038:338:29:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1672:18;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3072:211;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1945:26;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3830:834;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1724:31;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;6300:177;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;8186:89;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;1978:44;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2392:41;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1697:20;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;8281:93;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;3289:535;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;4853:1441;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2029:64;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1672:18;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;3072:211::-;3146:4;3195:6;3162:9;:21;3172:10;3162:21;;;;;;;;;;;;;;;:30;3184:7;3162:30;;;;;;;;;;;;;;;:39;;;;3238:7;3217:37;;3226:10;3217:37;;;3247:6;3217:37;;;;;;:::i;:::-;;;;;;;;3272:4;3265:11;;3072:211;;;;:::o;1945:26::-;;;;:::o;3830:834::-;3918:4;3934:15;3952:9;:15;3962:4;3952:15;;;;;;;;;;;;;;;:27;3968:10;3952:27;;;;;;;;;;;;;;;;3934:45;;4025:19;4047:9;:15;4057:4;4047:15;;;;;;;;;;;;;;;;4025:37;;4088:17;4077:7;:28;4073:204;;4135:6;4125:7;:16;4121:85;;;4172:4;4178:10;4190:7;4199:6;4150:56;;;;;;;;;;;;;;:::i;:::-;;;;;;;;4121:85;4260:6;4250:7;:16;;;;:::i;:::-;4220:9;:15;4230:4;4220:15;;;;;;;;;;;;;;;:27;4236:10;4220:27;;;;;;;;;;;;;;;:46;;;;4073:204;4305:6;4291:11;:20;4287:79;;;4340:4;4346:11;4359:6;4320:46;;;;;;;;;;;;;:::i;:::-;;;;;;;;4287:79;4408:6;4394:11;:20;;;;:::i;:::-;4376:9;:15;4386:4;4376:15;;;;;;;;;;;;;;;:38;;;;4577:6;4560:9;:13;4570:2;4560:13;;;;;;;;;;;;;;;;:23;;;;;;;;;;;4624:2;4609:26;;4618:4;4609:26;;;4628:6;4609:26;;;;;;:::i;:::-;;;;;;;;4653:4;4646:11;;;;3830:834;;;;;:::o;1724:31::-;;;:::o;6300:177::-;6357:7;6400:16;6383:13;:33;:87;;6446:24;:22;:24::i;:::-;6383:87;;;6419:24;6383:87;6376:94;;6300:177;:::o;8186:89::-;8252:16;8258:2;8262:5;8252;:16::i;:::-;8186:89;;:::o;1978:44::-;;;;;;;;;;;;;;;;;:::o;2392:41::-;;;;;;;;;;;;;;;;;:::o;1697:20::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;8281:93::-;8349:18;8355:4;8361:5;8349;:18::i;:::-;8281:93;;:::o;3289:535::-;3359:4;3375:19;3397:9;:21;3407:10;3397:21;;;;;;;;;;;;;;;;3375:43;;3446:6;3432:11;:20;3428:85;;;3481:10;3493:11;3506:6;3461:52;;;;;;;;;;;;;:::i;:::-;;;;;;;;3428:85;3562:6;3548:11;:20;;;;:::i;:::-;3524:9;:21;3534:10;3524:21;;;;;;;;;;;;;;;:44;;;;3731:6;3714:9;:13;3724:2;3714:13;;;;;;;;;;;;;;;;:23;;;;;;;;;;;3784:2;3763:32;;3772:10;3763:32;;;3788:6;3763:32;;;;;;:::i;:::-;;;;;;;;3813:4;3806:11;;;3289:535;;;;:::o;4853:1441::-;5030:15;5018:8;:27;;5010:63;;;;;;;;;;;;:::i;:::-;;;;;;;;;5238:24;5265:805;5401:18;:16;:18::i;:::-;5528:165;5727:5;5766:7;5807:5;5846:6;:13;5853:5;5846:13;;;;;;;;;;;;;;;;:15;;;;;;;;;;;;5895:8;5484:449;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;5445:514;;;;;;5323:658;;;;;;;;;:::i;:::-;;;;;;;;;;;;;5292:707;;;;;;6017:1;6036;6055;5265:805;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5238:832;;6121:1;6093:30;;:16;:30;;;;:59;;;;;6147:5;6127:25;;:16;:25;;;6093:59;6085:86;;;;;;;;;;;;:::i;:::-;;;;;;;;;6225:5;6186:9;:27;6196:16;6186:27;;;;;;;;;;;;;;;:36;6214:7;6186:36;;;;;;;;;;;;;;;:44;;;;5214:1027;6272:7;6256:31;;6265:5;6256:31;;;6281:5;6256:31;;;;;;:::i;:::-;;;;;;;;4853:1441;;;;;;;:::o;2029:64::-;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;6483:402::-;6548:7;6625:95;6754:4;6738:22;;;;;;:::i;:::-;;;;;;;;6778:14;6810:13;6849:4;6597:271;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;6574:304;;;;;;6567:311;;6483:402;:::o;7079:471::-;7149:22;7188:6;7174:11;;:20;;;;:::i;:::-;7149:45;;7225:11;;7208:14;:28;7204:74;;;7258:11;;7271:6;7245:33;;;;;;;;;;;;:::i;:::-;;;;;;;;7204:74;7302:14;7288:11;:28;;;;7479:6;7462:9;:13;7472:2;7462:13;;;;;;;;;;;;;;;;:23;;;;;;;;;;;7532:2;7511:32;;7528:1;7511:32;;;7536:6;7511:32;;;;;;:::i;:::-;;;;;;;;7139:411;7079:471;;:::o;7556:478::-;7628:19;7650:9;:15;7660:4;7650:15;;;;;;;;;;;;;;;;7628:37;;7693:6;7679:11;:20;7675:79;;;7728:4;7734:11;7747:6;7708:46;;;;;;;;;;;;;:::i;:::-;;;;;;;;7675:79;7797:6;7783:11;:20;;;;:::i;:::-;7765:9;:15;7775:4;7765:15;;;;;;;;;;;;;;;:38;;;;7961:6;7946:11;;:21;;;;;;;;;;;8016:1;7993:34;;8002:4;7993:34;;;8020:6;7993:34;;;;;;:::i;:::-;;;;;;;;7618:416;7556:478;;:::o;7:99:43:-;59:6;93:5;87:12;77:22;;7:99;;;:::o;112:169::-;196:11;230:6;225:3;218:19;270:4;265:3;261:14;246:29;;112:169;;;;:::o;287:139::-;376:6;371:3;366;360:23;417:1;408:6;403:3;399:16;392:27;287:139;;;:::o;432:102::-;473:6;524:2;520:7;515:2;508:5;504:14;500:28;490:38;;432:102;;;:::o;540:377::-;628:3;656:39;689:5;656:39;:::i;:::-;711:71;775:6;770:3;711:71;:::i;:::-;704:78;;791:65;849:6;844:3;837:4;830:5;826:16;791:65;:::i;:::-;881:29;903:6;881:29;:::i;:::-;876:3;872:39;865:46;;632:285;540:377;;;;:::o;923:313::-;1036:4;1074:2;1063:9;1059:18;1051:26;;1123:9;1117:4;1113:20;1109:1;1098:9;1094:17;1087:47;1151:78;1224:4;1215:6;1151:78;:::i;:::-;1143:86;;923:313;;;;:::o;1323:117::-;1432:1;1429;1422:12;1569:126;1606:7;1646:42;1639:5;1635:54;1624:65;;1569:126;;;:::o;1701:96::-;1738:7;1767:24;1785:5;1767:24;:::i;:::-;1756:35;;1701:96;;;:::o;1803:122::-;1876:24;1894:5;1876:24;:::i;:::-;1869:5;1866:35;1856:63;;1915:1;1912;1905:12;1856:63;1803:122;:::o;1931:139::-;1977:5;2015:6;2002:20;1993:29;;2031:33;2058:5;2031:33;:::i;:::-;1931:139;;;;:::o;2076:77::-;2113:7;2142:5;2131:16;;2076:77;;;:::o;2159:122::-;2232:24;2250:5;2232:24;:::i;:::-;2225:5;2222:35;2212:63;;2271:1;2268;2261:12;2212:63;2159:122;:::o;2287:139::-;2333:5;2371:6;2358:20;2349:29;;2387:33;2414:5;2387:33;:::i;:::-;2287:139;;;;:::o;2432:474::-;2500:6;2508;2557:2;2545:9;2536:7;2532:23;2528:32;2525:119;;;2563:79;;:::i;:::-;2525:119;2683:1;2708:53;2753:7;2744:6;2733:9;2729:22;2708:53;:::i;:::-;2698:63;;2654:117;2810:2;2836:53;2881:7;2872:6;2861:9;2857:22;2836:53;:::i;:::-;2826:63;;2781:118;2432:474;;;;;:::o;2912:90::-;2946:7;2989:5;2982:13;2975:21;2964:32;;2912:90;;;:::o;3008:109::-;3089:21;3104:5;3089:21;:::i;:::-;3084:3;3077:34;3008:109;;:::o;3123:210::-;3210:4;3248:2;3237:9;3233:18;3225:26;;3261:65;3323:1;3312:9;3308:17;3299:6;3261:65;:::i;:::-;3123:210;;;;:::o;3339:118::-;3426:24;3444:5;3426:24;:::i;:::-;3421:3;3414:37;3339:118;;:::o;3463:222::-;3556:4;3594:2;3583:9;3579:18;3571:26;;3607:71;3675:1;3664:9;3660:17;3651:6;3607:71;:::i;:::-;3463:222;;;;:::o;3691:619::-;3768:6;3776;3784;3833:2;3821:9;3812:7;3808:23;3804:32;3801:119;;;3839:79;;:::i;:::-;3801:119;3959:1;3984:53;4029:7;4020:6;4009:9;4005:22;3984:53;:::i;:::-;3974:63;;3930:117;4086:2;4112:53;4157:7;4148:6;4137:9;4133:22;4112:53;:::i;:::-;4102:63;;4057:118;4214:2;4240:53;4285:7;4276:6;4265:9;4261:22;4240:53;:::i;:::-;4230:63;;4185:118;3691:619;;;;;:::o;4316:86::-;4351:7;4391:4;4384:5;4380:16;4369:27;;4316:86;;;:::o;4408:112::-;4491:22;4507:5;4491:22;:::i;:::-;4486:3;4479:35;4408:112;;:::o;4526:214::-;4615:4;4653:2;4642:9;4638:18;4630:26;;4666:67;4730:1;4719:9;4715:17;4706:6;4666:67;:::i;:::-;4526:214;;;;:::o;4746:77::-;4783:7;4812:5;4801:16;;4746:77;;;:::o;4829:118::-;4916:24;4934:5;4916:24;:::i;:::-;4911:3;4904:37;4829:118;;:::o;4953:222::-;5046:4;5084:2;5073:9;5069:18;5061:26;;5097:71;5165:1;5154:9;5150:17;5141:6;5097:71;:::i;:::-;4953:222;;;;:::o;5181:329::-;5240:6;5289:2;5277:9;5268:7;5264:23;5260:32;5257:119;;;5295:79;;:::i;:::-;5257:119;5415:1;5440:53;5485:7;5476:6;5465:9;5461:22;5440:53;:::i;:::-;5430:63;;5386:117;5181:329;;;;:::o;5516:118::-;5587:22;5603:5;5587:22;:::i;:::-;5580:5;5577:33;5567:61;;5624:1;5621;5614:12;5567:61;5516:118;:::o;5640:135::-;5684:5;5722:6;5709:20;5700:29;;5738:31;5763:5;5738:31;:::i;:::-;5640:135;;;;:::o;5781:122::-;5854:24;5872:5;5854:24;:::i;:::-;5847:5;5844:35;5834:63;;5893:1;5890;5883:12;5834:63;5781:122;:::o;5909:139::-;5955:5;5993:6;5980:20;5971:29;;6009:33;6036:5;6009:33;:::i;:::-;5909:139;;;;:::o;6054:1199::-;6165:6;6173;6181;6189;6197;6205;6213;6262:3;6250:9;6241:7;6237:23;6233:33;6230:120;;;6269:79;;:::i;:::-;6230:120;6389:1;6414:53;6459:7;6450:6;6439:9;6435:22;6414:53;:::i;:::-;6404:63;;6360:117;6516:2;6542:53;6587:7;6578:6;6567:9;6563:22;6542:53;:::i;:::-;6532:63;;6487:118;6644:2;6670:53;6715:7;6706:6;6695:9;6691:22;6670:53;:::i;:::-;6660:63;;6615:118;6772:2;6798:53;6843:7;6834:6;6823:9;6819:22;6798:53;:::i;:::-;6788:63;;6743:118;6900:3;6927:51;6970:7;6961:6;6950:9;6946:22;6927:51;:::i;:::-;6917:61;;6871:117;7027:3;7054:53;7099:7;7090:6;7079:9;7075:22;7054:53;:::i;:::-;7044:63;;6998:119;7156:3;7183:53;7228:7;7219:6;7208:9;7204:22;7183:53;:::i;:::-;7173:63;;7127:119;6054:1199;;;;;;;;;;:::o;7259:474::-;7327:6;7335;7384:2;7372:9;7363:7;7359:23;7355:32;7352:119;;;7390:79;;:::i;:::-;7352:119;7510:1;7535:53;7580:7;7571:6;7560:9;7556:22;7535:53;:::i;:::-;7525:63;;7481:117;7637:2;7663:53;7708:7;7699:6;7688:9;7684:22;7663:53;:::i;:::-;7653:63;;7608:118;7259:474;;;;;:::o;7739:180::-;7787:77;7784:1;7777:88;7884:4;7881:1;7874:15;7908:4;7905:1;7898:15;7925:320;7969:6;8006:1;8000:4;7996:12;7986:22;;8053:1;8047:4;8043:12;8074:18;8064:81;;8130:4;8122:6;8118:17;8108:27;;8064:81;8192:2;8184:6;8181:14;8161:18;8158:38;8155:84;;8211:18;;:::i;:::-;8155:84;7976:269;7925:320;;;:::o;8251:118::-;8338:24;8356:5;8338:24;:::i;:::-;8333:3;8326:37;8251:118;;:::o;8375:553::-;8552:4;8590:3;8579:9;8575:19;8567:27;;8604:71;8672:1;8661:9;8657:17;8648:6;8604:71;:::i;:::-;8685:72;8753:2;8742:9;8738:18;8729:6;8685:72;:::i;:::-;8767;8835:2;8824:9;8820:18;8811:6;8767:72;:::i;:::-;8849;8917:2;8906:9;8902:18;8893:6;8849:72;:::i;:::-;8375:553;;;;;;;:::o;8934:180::-;8982:77;8979:1;8972:88;9079:4;9076:1;9069:15;9103:4;9100:1;9093:15;9120:194;9160:4;9180:20;9198:1;9180:20;:::i;:::-;9175:25;;9214:20;9232:1;9214:20;:::i;:::-;9209:25;;9258:1;9255;9251:9;9243:17;;9282:1;9276:4;9273:11;9270:37;;;9287:18;;:::i;:::-;9270:37;9120:194;;;;:::o;9320:442::-;9469:4;9507:2;9496:9;9492:18;9484:26;;9520:71;9588:1;9577:9;9573:17;9564:6;9520:71;:::i;:::-;9601:72;9669:2;9658:9;9654:18;9645:6;9601:72;:::i;:::-;9683;9751:2;9740:9;9736:18;9727:6;9683:72;:::i;:::-;9320:442;;;;;;:::o;9768:173::-;9908:25;9904:1;9896:6;9892:14;9885:49;9768:173;:::o;9947:366::-;10089:3;10110:67;10174:2;10169:3;10110:67;:::i;:::-;10103:74;;10186:93;10275:3;10186:93;:::i;:::-;10304:2;10299:3;10295:12;10288:19;;9947:366;;;:::o;10319:419::-;10485:4;10523:2;10512:9;10508:18;10500:26;;10572:9;10566:4;10562:20;10558:1;10547:9;10543:17;10536:47;10600:131;10726:4;10600:131;:::i;:::-;10592:139;;10319:419;;;:::o;10744:775::-;10977:4;11015:3;11004:9;11000:19;10992:27;;11029:71;11097:1;11086:9;11082:17;11073:6;11029:71;:::i;:::-;11110:72;11178:2;11167:9;11163:18;11154:6;11110:72;:::i;:::-;11192;11260:2;11249:9;11245:18;11236:6;11192:72;:::i;:::-;11274;11342:2;11331:9;11327:18;11318:6;11274:72;:::i;:::-;11356:73;11424:3;11413:9;11409:19;11400:6;11356:73;:::i;:::-;11439;11507:3;11496:9;11492:19;11483:6;11439:73;:::i;:::-;10744:775;;;;;;;;;:::o;11525:148::-;11627:11;11664:3;11649:18;;11525:148;;;;:::o;11679:214::-;11819:66;11815:1;11807:6;11803:14;11796:90;11679:214;:::o;11899:400::-;12059:3;12080:84;12162:1;12157:3;12080:84;:::i;:::-;12073:91;;12173:93;12262:3;12173:93;:::i;:::-;12291:1;12286:3;12282:11;12275:18;;11899:400;;;:::o;12305:79::-;12344:7;12373:5;12362:16;;12305:79;;;:::o;12390:157::-;12495:45;12515:24;12533:5;12515:24;:::i;:::-;12495:45;:::i;:::-;12490:3;12483:58;12390:157;;:::o;12553:663::-;12794:3;12816:148;12960:3;12816:148;:::i;:::-;12809:155;;12974:75;13045:3;13036:6;12974:75;:::i;:::-;13074:2;13069:3;13065:12;13058:19;;13087:75;13158:3;13149:6;13087:75;:::i;:::-;13187:2;13182:3;13178:12;13171:19;;13207:3;13200:10;;12553:663;;;;;:::o;13222:545::-;13395:4;13433:3;13422:9;13418:19;13410:27;;13447:71;13515:1;13504:9;13500:17;13491:6;13447:71;:::i;:::-;13528:68;13592:2;13581:9;13577:18;13568:6;13528:68;:::i;:::-;13606:72;13674:2;13663:9;13659:18;13650:6;13606:72;:::i;:::-;13688;13756:2;13745:9;13741:18;13732:6;13688:72;:::i;:::-;13222:545;;;;;;;:::o;13773:164::-;13913:16;13909:1;13901:6;13897:14;13890:40;13773:164;:::o;13943:366::-;14085:3;14106:67;14170:2;14165:3;14106:67;:::i;:::-;14099:74;;14182:93;14271:3;14182:93;:::i;:::-;14300:2;14295:3;14291:12;14284:19;;13943:366;;;:::o;14315:419::-;14481:4;14519:2;14508:9;14504:18;14496:26;;14568:9;14562:4;14558:20;14554:1;14543:9;14539:17;14532:47;14596:131;14722:4;14596:131;:::i;:::-;14588:139;;14315:419;;;:::o;14740:147::-;14841:11;14878:3;14863:18;;14740:147;;;;:::o;14893:144::-;14945:4;14968:3;14960:11;;14991:3;14988:1;14981:14;15025:4;15022:1;15012:18;15004:26;;14893:144;;;:::o;15065:878::-;15170:3;15207:5;15201:12;15236:36;15262:9;15236:36;:::i;:::-;15288:88;15369:6;15364:3;15288:88;:::i;:::-;15281:95;;15407:1;15396:9;15392:17;15423:1;15418:166;;;;15598:1;15593:344;;;;15385:552;;15418:166;15502:4;15498:9;15487;15483:25;15478:3;15471:38;15564:6;15557:14;15550:22;15542:6;15538:35;15533:3;15529:45;15522:52;;15418:166;;15593:344;15660:41;15695:5;15660:41;:::i;:::-;15723:1;15737:154;15751:6;15748:1;15745:13;15737:154;;;15825:7;15819:14;15815:1;15810:3;15806:11;15799:35;15875:1;15866:7;15862:15;15851:26;;15773:4;15770:1;15766:12;15761:17;;15737:154;;;15920:6;15915:3;15911:16;15904:23;;15600:337;;15385:552;;15174:769;;15065:878;;;;:::o;15949:273::-;16080:3;16102:94;16192:3;16183:6;16102:94;:::i;:::-;16095:101;;16213:3;16206:10;;15949:273;;;;:::o;16228:664::-;16433:4;16471:3;16460:9;16456:19;16448:27;;16485:71;16553:1;16542:9;16538:17;16529:6;16485:71;:::i;:::-;16566:72;16634:2;16623:9;16619:18;16610:6;16566:72;:::i;:::-;16648;16716:2;16705:9;16701:18;16692:6;16648:72;:::i;:::-;16730;16798:2;16787:9;16783:18;16774:6;16730:72;:::i;:::-;16812:73;16880:3;16869:9;16865:19;16856:6;16812:73;:::i;:::-;16228:664;;;;;;;;:::o;16898:191::-;16938:3;16957:20;16975:1;16957:20;:::i;:::-;16952:25;;16991:20;17009:1;16991:20;:::i;:::-;16986:25;;17034:1;17031;17027:9;17020:16;;17055:3;17052:1;17049:10;17046:36;;;17062:18;;:::i;:::-;17046:36;16898:191;;;;:::o;17095:332::-;17216:4;17254:2;17243:9;17239:18;17231:26;;17267:71;17335:1;17324:9;17320:17;17311:6;17267:71;:::i;:::-;17348:72;17416:2;17405:9;17401:18;17392:6;17348:72;:::i;:::-;17095:332;;;;;:::o", "linkReferences": {}, "immutableReferences": {"41531": [{"start": 1921, "length": 32}], "41545": [{"start": 1958, "length": 32}], "41547": [{"start": 2010, "length": 32}]}}, "methodIdentifiers": {"DOMAIN_SEPARATOR()": "3644e515", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "burn(address,uint256)": "9dc29fac", "decimals()": "313ce567", "mint(address,uint256)": "40c10f19", "name()": "06fdde03", "nonces(address)": "7ecebe00", "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": "d505accf", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"_symbol\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"_decimals\",\"type\":\"uint8\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"currentSupply\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"MintOverflow\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DOMAIN_SEPARATOR\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"permit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"errors\":{\"InsufficientAllowance(address,address,uint256,uint256)\":[{\"notice\":\"Thrown when attempting to transfer more tokens than allowed\"}],\"InsufficientBalance(address,uint256,uint256)\":[{\"notice\":\"Thrown when attempting to transfer more tokens than available balance\"}],\"MintOverflow(uint256,uint256)\":[{\"notice\":\"Thrown when minting would cause overflow\"}]},\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/setup-helpers/src/MockERC20.sol\":\"MockERC20\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/setup-helpers/src/MockERC20.sol\":{\"keccak256\":\"0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578\",\"dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_symbol", "type": "string"}, {"internalType": "uint8", "name": "_decimals", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "type": "error", "name": "InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "type": "error", "name": "InsufficientBalance"}, {"inputs": [{"internalType": "uint256", "name": "currentSupply", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "type": "error", "name": "MintOverflow"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "permit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/setup-helpers/src/MockERC20.sol": "MockERC20"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/setup-helpers/src/MockERC20.sol": {"keccak256": "0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f", "urls": ["bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578", "dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE"], "license": "AGPL-3.0-only"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 41527, "contract": "lib/setup-helpers/src/MockERC20.sol:MockERC20", "label": "name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 41529, "contract": "lib/setup-helpers/src/MockERC20.sol:MockERC20", "label": "symbol", "offset": 0, "slot": "1", "type": "t_string_storage"}, {"astId": 41533, "contract": "lib/setup-helpers/src/MockERC20.sol:MockERC20", "label": "totalSupply", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 41537, "contract": "lib/setup-helpers/src/MockERC20.sol:MockERC20", "label": "balanceOf", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 41543, "contract": "lib/setup-helpers/src/MockERC20.sol:MockERC20", "label": "allowance", "offset": 0, "slot": "4", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))"}, {"astId": 41551, "contract": "lib/setup-helpers/src/MockERC20.sol:MockERC20", "label": "nonces", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_uint256)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_uint256)"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "lib/setup-helpers/src/MockERC20.sol", "id": 42023, "exportedSymbols": {"ERC20": [41978], "MockERC20": [42022]}, "nodeType": "SourceUnit", "src": "42:8335:29", "nodes": [{"id": 41481, "nodeType": "PragmaDirective", "src": "42:23:29", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 41978, "nodeType": "ContractDefinition", "src": "473:7563:29", "nodes": [{"id": 41491, "nodeType": "ErrorDefinition", "src": "768:73:29", "nodes": [], "documentation": {"id": 41483, "nodeType": "StructuredDocumentation", "src": "682:81:29", "text": "@notice Thrown when attempting to transfer more tokens than available balance"}, "errorSelector": "db42144d", "name": "InsufficientBalance", "nameLocation": "774:19:29", "parameters": {"id": 41490, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41485, "mutability": "mutable", "name": "from", "nameLocation": "802:4:29", "nodeType": "VariableDeclaration", "scope": 41491, "src": "794:12:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41484, "name": "address", "nodeType": "ElementaryTypeName", "src": "794:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41487, "mutability": "mutable", "name": "balance", "nameLocation": "816:7:29", "nodeType": "VariableDeclaration", "scope": 41491, "src": "808:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41486, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "808:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41489, "mutability": "mutable", "name": "amount", "nameLocation": "833:6:29", "nodeType": "VariableDeclaration", "scope": 41491, "src": "825:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41488, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "825:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "793:47:29"}}, {"id": 41502, "nodeType": "ErrorDefinition", "src": "923:95:29", "nodes": [], "documentation": {"id": 41492, "nodeType": "StructuredDocumentation", "src": "847:71:29", "text": "@notice Thrown when attempting to transfer more tokens than allowed"}, "errorSelector": "91beda24", "name": "InsufficientAllowance", "nameLocation": "929:21:29", "parameters": {"id": 41501, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41494, "mutability": "mutable", "name": "owner", "nameLocation": "959:5:29", "nodeType": "VariableDeclaration", "scope": 41502, "src": "951:13:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41493, "name": "address", "nodeType": "ElementaryTypeName", "src": "951:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41496, "mutability": "mutable", "name": "spender", "nameLocation": "974:7:29", "nodeType": "VariableDeclaration", "scope": 41502, "src": "966:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41495, "name": "address", "nodeType": "ElementaryTypeName", "src": "966:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41498, "mutability": "mutable", "name": "allowance", "nameLocation": "991:9:29", "nodeType": "VariableDeclaration", "scope": 41502, "src": "983:17:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41497, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "983:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41500, "mutability": "mutable", "name": "amount", "nameLocation": "1010:6:29", "nodeType": "VariableDeclaration", "scope": 41502, "src": "1002:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41499, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1002:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "950:67:29"}}, {"id": 41509, "nodeType": "ErrorDefinition", "src": "1081:58:29", "nodes": [], "documentation": {"id": 41503, "nodeType": "StructuredDocumentation", "src": "1024:52:29", "text": "@notice Thrown when minting would cause overflow"}, "errorSelector": "93ea3e04", "name": "MintOverflow", "nameLocation": "1087:12:29", "parameters": {"id": 41508, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41505, "mutability": "mutable", "name": "currentSupply", "nameLocation": "1108:13:29", "nodeType": "VariableDeclaration", "scope": 41509, "src": "1100:21:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41504, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1100:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41507, "mutability": "mutable", "name": "amount", "nameLocation": "1131:6:29", "nodeType": "VariableDeclaration", "scope": 41509, "src": "1123:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41506, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1123:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1099:39:29"}}, {"id": 41517, "nodeType": "EventDefinition", "src": "1324:73:29", "nodes": [], "anonymous": false, "eventSelector": "ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef", "name": "Transfer", "nameLocation": "1330:8:29", "parameters": {"id": 41516, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41511, "indexed": true, "mutability": "mutable", "name": "from", "nameLocation": "1355:4:29", "nodeType": "VariableDeclaration", "scope": 41517, "src": "1339:20:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41510, "name": "address", "nodeType": "ElementaryTypeName", "src": "1339:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41513, "indexed": true, "mutability": "mutable", "name": "to", "nameLocation": "1377:2:29", "nodeType": "VariableDeclaration", "scope": 41517, "src": "1361:18:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41512, "name": "address", "nodeType": "ElementaryTypeName", "src": "1361:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41515, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "1389:6:29", "nodeType": "VariableDeclaration", "scope": 41517, "src": "1381:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41514, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1381:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1338:58:29"}}, {"id": 41525, "nodeType": "EventDefinition", "src": "1403:79:29", "nodes": [], "anonymous": false, "eventSelector": "8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925", "name": "Approval", "nameLocation": "1409:8:29", "parameters": {"id": 41524, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41519, "indexed": true, "mutability": "mutable", "name": "owner", "nameLocation": "1434:5:29", "nodeType": "VariableDeclaration", "scope": 41525, "src": "1418:21:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41518, "name": "address", "nodeType": "ElementaryTypeName", "src": "1418:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41521, "indexed": true, "mutability": "mutable", "name": "spender", "nameLocation": "1457:7:29", "nodeType": "VariableDeclaration", "scope": 41525, "src": "1441:23:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41520, "name": "address", "nodeType": "ElementaryTypeName", "src": "1441:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41523, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "1474:6:29", "nodeType": "VariableDeclaration", "scope": 41525, "src": "1466:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41522, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1466:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1417:64:29"}}, {"id": 41527, "nodeType": "VariableDeclaration", "src": "1672:18:29", "nodes": [], "constant": false, "functionSelector": "06fdde03", "mutability": "mutable", "name": "name", "nameLocation": "1686:4:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string"}, "typeName": {"id": 41526, "name": "string", "nodeType": "ElementaryTypeName", "src": "1672:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "public"}, {"id": 41529, "nodeType": "VariableDeclaration", "src": "1697:20:29", "nodes": [], "constant": false, "functionSelector": "95d89b41", "mutability": "mutable", "name": "symbol", "nameLocation": "1711:6:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string"}, "typeName": {"id": 41528, "name": "string", "nodeType": "ElementaryTypeName", "src": "1697:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "public"}, {"id": 41531, "nodeType": "VariableDeclaration", "src": "1724:31:29", "nodes": [], "constant": false, "functionSelector": "313ce567", "mutability": "immutable", "name": "decimals", "nameLocation": "1747:8:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 41530, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "1724:5:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "public"}, {"id": 41533, "nodeType": "VariableDeclaration", "src": "1945:26:29", "nodes": [], "constant": false, "functionSelector": "18160ddd", "mutability": "mutable", "name": "totalSupply", "nameLocation": "1960:11:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41532, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1945:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"id": 41537, "nodeType": "VariableDeclaration", "src": "1978:44:29", "nodes": [], "constant": false, "functionSelector": "70a08231", "mutability": "mutable", "name": "balanceOf", "nameLocation": "2013:9:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 41536, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 41534, "name": "address", "nodeType": "ElementaryTypeName", "src": "1986:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1978:27:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 41535, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1997:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "visibility": "public"}, {"id": 41543, "nodeType": "VariableDeclaration", "src": "2029:64:29", "nodes": [], "constant": false, "functionSelector": "dd62ed3e", "mutability": "mutable", "name": "allowance", "nameLocation": "2084:9:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "typeName": {"id": 41542, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 41538, "name": "address", "nodeType": "ElementaryTypeName", "src": "2037:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "2029:47:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 41541, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 41539, "name": "address", "nodeType": "ElementaryTypeName", "src": "2056:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "2048:27:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 41540, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2067:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}}, "visibility": "public"}, {"id": 41545, "nodeType": "VariableDeclaration", "src": "2284:43:29", "nodes": [], "constant": false, "mutability": "immutable", "name": "INITIAL_CHAIN_ID", "nameLocation": "2311:16:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41544, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2284:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"id": 41547, "nodeType": "VariableDeclaration", "src": "2334:51:29", "nodes": [], "constant": false, "mutability": "immutable", "name": "INITIAL_DOMAIN_SEPARATOR", "nameLocation": "2361:24:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41546, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2334:7:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"id": 41551, "nodeType": "VariableDeclaration", "src": "2392:41:29", "nodes": [], "constant": false, "functionSelector": "7ecebe00", "mutability": "mutable", "name": "nonces", "nameLocation": "2427:6:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 41550, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 41548, "name": "address", "nodeType": "ElementaryTypeName", "src": "2400:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "2392:27:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 41549, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2411:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "visibility": "public"}, {"id": 41583, "nodeType": "FunctionDefinition", "src": "2622:262:29", "nodes": [], "body": {"id": 41582, "nodeType": "Block", "src": "2695:189:29", "nodes": [], "statements": [{"expression": {"id": 41562, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41560, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41527, "src": "2705:4:29", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41561, "name": "_name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41553, "src": "2712:5:29", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "2705:12:29", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 41563, "nodeType": "ExpressionStatement", "src": "2705:12:29"}, {"expression": {"id": 41566, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41564, "name": "symbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41529, "src": "2727:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41565, "name": "_symbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41555, "src": "2736:7:29", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "2727:16:29", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 41567, "nodeType": "ExpressionStatement", "src": "2727:16:29"}, {"expression": {"id": 41570, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41568, "name": "decimals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41531, "src": "2753:8:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41569, "name": "_decimals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41557, "src": "2764:9:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "src": "2753:20:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 41571, "nodeType": "ExpressionStatement", "src": "2753:20:29"}, {"expression": {"id": 41575, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41572, "name": "INITIAL_CHAIN_ID", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41545, "src": "2784:16:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 41573, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "2803:5:29", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 41574, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2809:7:29", "memberName": "chainid", "nodeType": "MemberAccess", "src": "2803:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2784:32:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41576, "nodeType": "ExpressionStatement", "src": "2784:32:29"}, {"expression": {"id": 41580, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41577, "name": "INITIAL_DOMAIN_SEPARATOR", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41547, "src": "2826:24:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [], "expression": {"argumentTypes": [], "id": 41578, "name": "computeDomainSeparator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41888, "src": "2853:22:29", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bytes32_$", "typeString": "function () view returns (bytes32)"}}, "id": 41579, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2853:24:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "2826:51:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 41581, "nodeType": "ExpressionStatement", "src": "2826:51:29"}]}, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 41558, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41553, "mutability": "mutable", "name": "_name", "nameLocation": "2648:5:29", "nodeType": "VariableDeclaration", "scope": 41583, "src": "2634:19:29", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 41552, "name": "string", "nodeType": "ElementaryTypeName", "src": "2634:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 41555, "mutability": "mutable", "name": "_symbol", "nameLocation": "2669:7:29", "nodeType": "VariableDeclaration", "scope": 41583, "src": "2655:21:29", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 41554, "name": "string", "nodeType": "ElementaryTypeName", "src": "2655:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 41557, "mutability": "mutable", "name": "_decimals", "nameLocation": "2684:9:29", "nodeType": "VariableDeclaration", "scope": 41583, "src": "2678:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 41556, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "2678:5:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "2633:61:29"}, "returnParameters": {"id": 41559, "nodeType": "ParameterList", "parameters": [], "src": "2695:0:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 41611, "nodeType": "FunctionDefinition", "src": "3072:211:29", "nodes": [], "body": {"id": 41610, "nodeType": "Block", "src": "3152:131:29", "nodes": [], "statements": [{"expression": {"id": 41599, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 41592, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41543, "src": "3162:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 41596, "indexExpression": {"expression": {"id": 41593, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3172:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41594, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3176:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3172:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3162:21:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41597, "indexExpression": {"id": 41595, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41585, "src": "3184:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3162:30:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41598, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41587, "src": "3195:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3162:39:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41600, "nodeType": "ExpressionStatement", "src": "3162:39:29"}, {"eventCall": {"arguments": [{"expression": {"id": 41602, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3226:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41603, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3230:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3226:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41604, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41585, "src": "3238:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41605, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41587, "src": "3247:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41601, "name": "Approval", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41525, "src": "3217:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41606, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3217:37:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41607, "nodeType": "EmitStatement", "src": "3212:42:29"}, {"expression": {"hexValue": "74727565", "id": 41608, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3272:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 41591, "id": 41609, "nodeType": "Return", "src": "3265:11:29"}]}, "functionSelector": "095ea7b3", "implemented": true, "kind": "function", "modifiers": [], "name": "approve", "nameLocation": "3081:7:29", "parameters": {"id": 41588, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41585, "mutability": "mutable", "name": "spender", "nameLocation": "3097:7:29", "nodeType": "VariableDeclaration", "scope": 41611, "src": "3089:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41584, "name": "address", "nodeType": "ElementaryTypeName", "src": "3089:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41587, "mutability": "mutable", "name": "amount", "nameLocation": "3114:6:29", "nodeType": "VariableDeclaration", "scope": 41611, "src": "3106:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41586, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3106:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3088:33:29"}, "returnParameters": {"id": 41591, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41590, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41611, "src": "3146:4:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41589, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3146:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3145:6:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"id": 41664, "nodeType": "FunctionDefinition", "src": "3289:535:29", "nodes": [], "body": {"id": 41663, "nodeType": "Block", "src": "3365:459:29", "nodes": [], "statements": [{"assignments": [41621], "declarations": [{"constant": false, "id": 41621, "mutability": "mutable", "name": "fromBalance", "nameLocation": "3383:11:29", "nodeType": "VariableDeclaration", "scope": 41663, "src": "3375:19:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41620, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3375:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 41626, "initialValue": {"baseExpression": {"id": 41622, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "3397:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41625, "indexExpression": {"expression": {"id": 41623, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3407:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41624, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3411:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3407:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3397:21:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3375:43:29"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41629, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41627, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41621, "src": "3432:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 41628, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41615, "src": "3446:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3432:20:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41637, "nodeType": "IfStatement", "src": "3428:85:29", "trueBody": {"errorCall": {"arguments": [{"expression": {"id": 41631, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3481:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41632, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3485:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3481:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41633, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41621, "src": "3493:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41634, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41615, "src": "3506:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41630, "name": "InsufficientBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41491, "src": "3461:19:29", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_uint256_$_t_uint256_$returns$_t_error_$", "typeString": "function (address,uint256,uint256) pure returns (error)"}}, "id": 41635, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3461:52:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 41636, "nodeType": "RevertStatement", "src": "3454:59:29"}}, {"expression": {"id": 41645, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41638, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "3524:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41641, "indexExpression": {"expression": {"id": 41639, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3534:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41640, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3538:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3534:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3524:21:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41644, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41642, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41621, "src": "3548:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 41643, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41615, "src": "3562:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3548:20:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3524:44:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41646, "nodeType": "ExpressionStatement", "src": "3524:44:29"}, {"id": 41653, "nodeType": "UncheckedBlock", "src": "3690:58:29", "statements": [{"expression": {"id": 41651, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41647, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "3714:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41649, "indexExpression": {"id": 41648, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41613, "src": "3724:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3714:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 41650, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41615, "src": "3731:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3714:23:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41652, "nodeType": "ExpressionStatement", "src": "3714:23:29"}]}, {"eventCall": {"arguments": [{"expression": {"id": 41655, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3772:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41656, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3776:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3772:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41657, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41613, "src": "3784:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41658, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41615, "src": "3788:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41654, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41517, "src": "3763:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41659, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3763:32:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41660, "nodeType": "EmitStatement", "src": "3758:37:29"}, {"expression": {"hexValue": "74727565", "id": 41661, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3813:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 41619, "id": 41662, "nodeType": "Return", "src": "3806:11:29"}]}, "functionSelector": "a9059cbb", "implemented": true, "kind": "function", "modifiers": [], "name": "transfer", "nameLocation": "3298:8:29", "parameters": {"id": 41616, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41613, "mutability": "mutable", "name": "to", "nameLocation": "3315:2:29", "nodeType": "VariableDeclaration", "scope": 41664, "src": "3307:10:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41612, "name": "address", "nodeType": "ElementaryTypeName", "src": "3307:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41615, "mutability": "mutable", "name": "amount", "nameLocation": "3327:6:29", "nodeType": "VariableDeclaration", "scope": 41664, "src": "3319:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41614, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3319:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3306:28:29"}, "returnParameters": {"id": 41619, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41618, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41664, "src": "3359:4:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41617, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3359:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3358:6:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"id": 41756, "nodeType": "FunctionDefinition", "src": "3830:834:29", "nodes": [], "body": {"id": 41755, "nodeType": "Block", "src": "3924:740:29", "nodes": [], "statements": [{"assignments": [41676], "declarations": [{"constant": false, "id": 41676, "mutability": "mutable", "name": "allowed", "nameLocation": "3942:7:29", "nodeType": "VariableDeclaration", "scope": 41755, "src": "3934:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41675, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3934:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 41683, "initialValue": {"baseExpression": {"baseExpression": {"id": 41677, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41543, "src": "3952:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 41679, "indexExpression": {"id": 41678, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "3962:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3952:15:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41682, "indexExpression": {"expression": {"id": 41680, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3968:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41681, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3972:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3968:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3952:27:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3934:45:29"}, {"assignments": [41685], "declarations": [{"constant": false, "id": 41685, "mutability": "mutable", "name": "fromBalance", "nameLocation": "4033:11:29", "nodeType": "VariableDeclaration", "scope": 41755, "src": "4025:19:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41684, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4025:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 41689, "initialValue": {"baseExpression": {"id": 41686, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "4047:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41688, "indexExpression": {"id": 41687, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4057:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4047:15:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "4025:37:29"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41696, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41690, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41676, "src": "4077:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"expression": {"arguments": [{"id": 41693, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4093:7:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 41692, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4093:7:29", "typeDescriptions": {}}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}], "id": 41691, "name": "type", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -27, "src": "4088:4:29", "typeDescriptions": {"typeIdentifier": "t_function_metatype_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 41694, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4088:13:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_magic_meta_type_t_uint256", "typeString": "type(uint256)"}}, "id": 41695, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4102:3:29", "memberName": "max", "nodeType": "MemberAccess", "src": "4088:17:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4077:28:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41721, "nodeType": "IfStatement", "src": "4073:204:29", "trueBody": {"id": 41720, "nodeType": "Block", "src": "4107:170:29", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41699, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41697, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41676, "src": "4125:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 41698, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4135:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4125:16:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41708, "nodeType": "IfStatement", "src": "4121:85:29", "trueBody": {"errorCall": {"arguments": [{"id": 41701, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4172:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 41702, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "4178:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41703, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4182:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "4178:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41704, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41676, "src": "4190:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41705, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4199:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41700, "name": "InsufficientAllowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41502, "src": "4150:21:29", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_address_$_t_uint256_$_t_uint256_$returns$_t_error_$", "typeString": "function (address,address,uint256,uint256) pure returns (error)"}}, "id": 41706, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4150:56:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 41707, "nodeType": "RevertStatement", "src": "4143:63:29"}}, {"expression": {"id": 41718, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 41709, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41543, "src": "4220:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 41713, "indexExpression": {"id": 41710, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4230:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4220:15:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41714, "indexExpression": {"expression": {"id": 41711, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "4236:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41712, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4240:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "4236:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "4220:27:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41717, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41715, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41676, "src": "4250:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 41716, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4260:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4250:16:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4220:46:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41719, "nodeType": "ExpressionStatement", "src": "4220:46:29"}]}}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41724, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41722, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41685, "src": "4291:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 41723, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4305:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4291:20:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41731, "nodeType": "IfStatement", "src": "4287:79:29", "trueBody": {"errorCall": {"arguments": [{"id": 41726, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4340:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41727, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41685, "src": "4346:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41728, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4359:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41725, "name": "InsufficientBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41491, "src": "4320:19:29", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_uint256_$_t_uint256_$returns$_t_error_$", "typeString": "function (address,uint256,uint256) pure returns (error)"}}, "id": 41729, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4320:46:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 41730, "nodeType": "RevertStatement", "src": "4313:53:29"}}, {"expression": {"id": 41738, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41732, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "4376:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41734, "indexExpression": {"id": 41733, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4386:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "4376:15:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41737, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41735, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41685, "src": "4394:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 41736, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4408:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4394:20:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4376:38:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41739, "nodeType": "ExpressionStatement", "src": "4376:38:29"}, {"id": 41746, "nodeType": "UncheckedBlock", "src": "4536:58:29", "statements": [{"expression": {"id": 41744, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41740, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "4560:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41742, "indexExpression": {"id": 41741, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41668, "src": "4570:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "4560:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 41743, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4577:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4560:23:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41745, "nodeType": "ExpressionStatement", "src": "4560:23:29"}]}, {"eventCall": {"arguments": [{"id": 41748, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4618:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41749, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41668, "src": "4624:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41750, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4628:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41747, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41517, "src": "4609:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41751, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4609:26:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41752, "nodeType": "EmitStatement", "src": "4604:31:29"}, {"expression": {"hexValue": "74727565", "id": 41753, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4653:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 41674, "id": 41754, "nodeType": "Return", "src": "4646:11:29"}]}, "functionSelector": "23b872dd", "implemented": true, "kind": "function", "modifiers": [], "name": "transferFrom", "nameLocation": "3839:12:29", "parameters": {"id": 41671, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41666, "mutability": "mutable", "name": "from", "nameLocation": "3860:4:29", "nodeType": "VariableDeclaration", "scope": 41756, "src": "3852:12:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41665, "name": "address", "nodeType": "ElementaryTypeName", "src": "3852:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41668, "mutability": "mutable", "name": "to", "nameLocation": "3874:2:29", "nodeType": "VariableDeclaration", "scope": 41756, "src": "3866:10:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41667, "name": "address", "nodeType": "ElementaryTypeName", "src": "3866:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41670, "mutability": "mutable", "name": "amount", "nameLocation": "3886:6:29", "nodeType": "VariableDeclaration", "scope": 41756, "src": "3878:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41669, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3878:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3851:42:29"}, "returnParameters": {"id": 41674, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41673, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41756, "src": "3918:4:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41672, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3918:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3917:6:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"id": 41843, "nodeType": "FunctionDefinition", "src": "4853:1441:29", "nodes": [], "body": {"id": 41842, "nodeType": "Block", "src": "5000:1294:29", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41777, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41774, "name": "deadline", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41764, "src": "5018:8:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"expression": {"id": 41775, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "5030:5:29", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 41776, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5036:9:29", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "5030:15:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5018:27:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5045524d49545f444541444c494e455f45585049524544", "id": 41778, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5047:25:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_dd18cfd81b4c1281b56302a044e7f751a261543590362c41d86af048f8ed4b3e", "typeString": "literal_string \"PERMIT_DEADLINE_EXPIRED\""}, "value": "PERMIT_DEADLINE_EXPIRED"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_dd18cfd81b4c1281b56302a044e7f751a261543590362c41d86af048f8ed4b3e", "typeString": "literal_string \"PERMIT_DEADLINE_EXPIRED\""}], "id": 41773, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "5010:7:29", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 41779, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5010:63:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41780, "nodeType": "ExpressionStatement", "src": "5010:63:29"}, {"id": 41835, "nodeType": "UncheckedBlock", "src": "5214:1027:29", "statements": [{"assignments": [41782], "declarations": [{"constant": false, "id": 41782, "mutability": "mutable", "name": "recovered<PERSON><PERSON><PERSON>", "nameLocation": "5246:16:29", "nodeType": "VariableDeclaration", "scope": 41835, "src": "5238:24:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41781, "name": "address", "nodeType": "ElementaryTypeName", "src": "5238:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 41812, "initialValue": {"arguments": [{"arguments": [{"arguments": [{"hexValue": "1901", "id": 41787, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5365:10:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_301a50b291d33ce1e8e9064e3f6a6c51d902ec22892b50d58abf6357c6a45541", "typeString": "literal_string hex\"1901\""}, "value": "\u0019\u0001"}, {"arguments": [], "expression": {"argumentTypes": [], "id": 41788, "name": "DOMAIN_SEPARATOR", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41858, "src": "5401:16:29", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bytes32_$", "typeString": "function () view returns (bytes32)"}}, "id": 41789, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5401:18:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"arguments": [{"arguments": [{"arguments": [{"hexValue": "5065726d69742861646472657373206f776e65722c61646472657373207370656e6465722c75696e743235362076616c75652c75696e74323536206e6f6e63652c75696e7432353620646561646c696e6529", "id": 41794, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5575:84:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9", "typeString": "literal_string \"Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)\""}, "value": "Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9", "typeString": "literal_string \"Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)\""}], "id": 41793, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "5528:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41795, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5528:165:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 41796, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41758, "src": "5727:5:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41797, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41760, "src": "5766:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41798, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41762, "src": "5807:5:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41802, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "5846:15:29", "subExpression": {"baseExpression": {"id": 41799, "name": "nonces", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41551, "src": "5846:6:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41801, "indexExpression": {"id": 41800, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41758, "src": "5853:5:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5846:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41803, "name": "deadline", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41764, "src": "5895:8:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 41791, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "5484:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 41792, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5488:6:29", "memberName": "encode", "nodeType": "MemberAccess", "src": "5484:10:29", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 41804, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5484:449:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 41790, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "5445:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41805, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5445:514:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_301a50b291d33ce1e8e9064e3f6a6c51d902ec22892b50d58abf6357c6a45541", "typeString": "literal_string hex\"1901\""}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 41785, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "5323:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 41786, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5327:12:29", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "5323:16:29", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 41806, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5323:658:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 41784, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "5292:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41807, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5292:707:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 41808, "name": "v", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41766, "src": "6017:1:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, {"id": 41809, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41768, "src": "6036:1:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 41810, "name": "s", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41770, "src": "6055:1:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_uint8", "typeString": "uint8"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41783, "name": "ecrecover", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -6, "src": "5265:9:29", "typeDescriptions": {"typeIdentifier": "t_function_ecrecover_pure$_t_bytes32_$_t_uint8_$_t_bytes32_$_t_bytes32_$returns$_t_address_$", "typeString": "function (bytes32,uint8,bytes32,bytes32) pure returns (address)"}}, "id": 41811, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5265:805:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "5238:832:29"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 41823, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 41819, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41814, "name": "recovered<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41782, "src": "6093:16:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"hexValue": "30", "id": 41817, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6121:1:29", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 41816, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6113:7:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 41815, "name": "address", "nodeType": "ElementaryTypeName", "src": "6113:7:29", "typeDescriptions": {}}}, "id": 41818, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6113:10:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "6093:30:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 41822, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41820, "name": "recovered<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41782, "src": "6127:16:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 41821, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41758, "src": "6147:5:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "6127:25:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "6093:59:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "494e56414c49445f5349474e4552", "id": 41824, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6154:16:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ba2319f5fa9f0c8e55f0d6899910b7354e6f643d1d349de47190066d85e68a1c", "typeString": "literal_string \"INVALID_SIGNER\""}, "value": "INVALID_SIGNER"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_ba2319f5fa9f0c8e55f0d6899910b7354e6f643d1d349de47190066d85e68a1c", "typeString": "literal_string \"INVALID_SIGNER\""}], "id": 41813, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "6085:7:29", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 41825, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6085:86:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41826, "nodeType": "ExpressionStatement", "src": "6085:86:29"}, {"expression": {"id": 41833, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 41827, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41543, "src": "6186:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 41830, "indexExpression": {"id": 41828, "name": "recovered<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41782, "src": "6196:16:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6186:27:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41831, "indexExpression": {"id": 41829, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41760, "src": "6214:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "6186:36:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41832, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41762, "src": "6225:5:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6186:44:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41834, "nodeType": "ExpressionStatement", "src": "6186:44:29"}]}, {"eventCall": {"arguments": [{"id": 41837, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41758, "src": "6265:5:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41838, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41760, "src": "6272:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41839, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41762, "src": "6281:5:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41836, "name": "Approval", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41525, "src": "6256:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41840, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6256:31:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41841, "nodeType": "EmitStatement", "src": "6251:36:29"}]}, "functionSelector": "d505accf", "implemented": true, "kind": "function", "modifiers": [], "name": "permit", "nameLocation": "4862:6:29", "parameters": {"id": 41771, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41758, "mutability": "mutable", "name": "owner", "nameLocation": "4877:5:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4869:13:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41757, "name": "address", "nodeType": "ElementaryTypeName", "src": "4869:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41760, "mutability": "mutable", "name": "spender", "nameLocation": "4892:7:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4884:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41759, "name": "address", "nodeType": "ElementaryTypeName", "src": "4884:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41762, "mutability": "mutable", "name": "value", "nameLocation": "4909:5:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4901:13:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41761, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4901:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41764, "mutability": "mutable", "name": "deadline", "nameLocation": "4924:8:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4916:16:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41763, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4916:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41766, "mutability": "mutable", "name": "v", "nameLocation": "4940:1:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4934:7:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 41765, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "4934:5:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}, {"constant": false, "id": 41768, "mutability": "mutable", "name": "r", "nameLocation": "4951:1:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4943:9:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41767, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4943:7:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 41770, "mutability": "mutable", "name": "s", "nameLocation": "4962:1:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4954:9:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41769, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4954:7:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "4868:96:29"}, "returnParameters": {"id": 41772, "nodeType": "ParameterList", "parameters": [], "src": "5000:0:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"id": 41858, "nodeType": "FunctionDefinition", "src": "6300:177:29", "nodes": [], "body": {"id": 41857, "nodeType": "Block", "src": "6366:111:29", "nodes": [], "statements": [{"expression": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41851, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 41848, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "6383:5:29", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 41849, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6389:7:29", "memberName": "chainid", "nodeType": "MemberAccess", "src": "6383:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 41850, "name": "INITIAL_CHAIN_ID", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41545, "src": "6400:16:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6383:33:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 41853, "name": "computeDomainSeparator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41888, "src": "6446:22:29", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bytes32_$", "typeString": "function () view returns (bytes32)"}}, "id": 41854, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6446:24:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 41855, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "6383:87:29", "trueExpression": {"id": 41852, "name": "INITIAL_DOMAIN_SEPARATOR", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41547, "src": "6419:24:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 41847, "id": 41856, "nodeType": "Return", "src": "6376:94:29"}]}, "functionSelector": "3644e515", "implemented": true, "kind": "function", "modifiers": [], "name": "DOMAIN_SEPARATOR", "nameLocation": "6309:16:29", "parameters": {"id": 41844, "nodeType": "ParameterList", "parameters": [], "src": "6325:2:29"}, "returnParameters": {"id": 41847, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41846, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41858, "src": "6357:7:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41845, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6357:7:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "6356:9:29"}, "scope": 41978, "stateMutability": "view", "virtual": true, "visibility": "public"}, {"id": 41888, "nodeType": "FunctionDefinition", "src": "6483:402:29", "nodes": [], "body": {"id": 41887, "nodeType": "Block", "src": "6557:328:29", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"arguments": [{"hexValue": "454950373132446f6d61696e28737472696e67206e616d652c737472696e672076657273696f6e2c75696e7432353620636861696e49642c6164647265737320766572696679696e67436f6e747261637429", "id": 41867, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6635:84:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f", "typeString": "literal_string \"EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)\""}, "value": "EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f", "typeString": "literal_string \"EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)\""}], "id": 41866, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "6625:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41868, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6625:95:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"arguments": [{"arguments": [{"id": 41872, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41527, "src": "6754:4:29", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}], "id": 41871, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6748:5:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 41870, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6748:5:29", "typeDescriptions": {}}}, "id": 41873, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6748:11:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes storage pointer"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes storage pointer"}], "id": 41869, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "6738:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41874, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6738:22:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"arguments": [{"hexValue": "31", "id": 41876, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6788:3:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c89efdaa54c0f20c7adf612882df0950f5a951637e0307cdcb4c672f298b8bc6", "typeString": "literal_string \"1\""}, "value": "1"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c89efdaa54c0f20c7adf612882df0950f5a951637e0307cdcb4c672f298b8bc6", "typeString": "literal_string \"1\""}], "id": 41875, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "6778:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41877, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6778:14:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"expression": {"id": 41878, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "6810:5:29", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 41879, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6816:7:29", "memberName": "chainid", "nodeType": "MemberAccess", "src": "6810:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"arguments": [{"id": 41882, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "6849:4:29", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$41978", "typeString": "contract ERC20"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_ERC20_$41978", "typeString": "contract ERC20"}], "id": 41881, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6841:7:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 41880, "name": "address", "nodeType": "ElementaryTypeName", "src": "6841:7:29", "typeDescriptions": {}}}, "id": 41883, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6841:13:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 41864, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "6597:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 41865, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "6601:6:29", "memberName": "encode", "nodeType": "MemberAccess", "src": "6597:10:29", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 41884, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6597:271:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 41863, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "6574:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41885, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6574:304:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 41862, "id": 41886, "nodeType": "Return", "src": "6567:311:29"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "computeDomainSeparator", "nameLocation": "6492:22:29", "parameters": {"id": 41859, "nodeType": "ParameterList", "parameters": [], "src": "6514:2:29"}, "returnParameters": {"id": 41862, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41861, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41888, "src": "6548:7:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41860, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6548:7:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "6547:9:29"}, "scope": 41978, "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"id": 41931, "nodeType": "FunctionDefinition", "src": "7079:471:29", "nodes": [], "body": {"id": 41930, "nodeType": "Block", "src": "7139:411:29", "nodes": [], "statements": [{"assignments": [41896], "declarations": [{"constant": false, "id": 41896, "mutability": "mutable", "name": "newTotalSupply", "nameLocation": "7157:14:29", "nodeType": "VariableDeclaration", "scope": 41930, "src": "7149:22:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41895, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7149:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 41900, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41899, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41897, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41533, "src": "7174:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 41898, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41892, "src": "7188:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7174:20:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "7149:45:29"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41903, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41901, "name": "newTotalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41896, "src": "7208:14:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 41902, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41533, "src": "7225:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7208:28:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41909, "nodeType": "IfStatement", "src": "7204:74:29", "trueBody": {"errorCall": {"arguments": [{"id": 41905, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41533, "src": "7258:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41906, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41892, "src": "7271:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41904, "name": "MintOverflow", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41509, "src": "7245:12:29", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_uint256_$_t_uint256_$returns$_t_error_$", "typeString": "function (uint256,uint256) pure returns (error)"}}, "id": 41907, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7245:33:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 41908, "nodeType": "RevertStatement", "src": "7238:40:29"}}, {"expression": {"id": 41912, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41910, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41533, "src": "7288:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41911, "name": "newTotalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41896, "src": "7302:14:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7288:28:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41913, "nodeType": "ExpressionStatement", "src": "7288:28:29"}, {"id": 41920, "nodeType": "UncheckedBlock", "src": "7438:58:29", "statements": [{"expression": {"id": 41918, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41914, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "7462:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41916, "indexExpression": {"id": 41915, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41890, "src": "7472:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "7462:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 41917, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41892, "src": "7479:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7462:23:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41919, "nodeType": "ExpressionStatement", "src": "7462:23:29"}]}, {"eventCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 41924, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7528:1:29", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 41923, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7520:7:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 41922, "name": "address", "nodeType": "ElementaryTypeName", "src": "7520:7:29", "typeDescriptions": {}}}, "id": 41925, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7520:10:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41926, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41890, "src": "7532:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41927, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41892, "src": "7536:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41921, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41517, "src": "7511:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41928, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7511:32:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41929, "nodeType": "EmitStatement", "src": "7506:37:29"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_mint", "nameLocation": "7088:5:29", "parameters": {"id": 41893, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41890, "mutability": "mutable", "name": "to", "nameLocation": "7102:2:29", "nodeType": "VariableDeclaration", "scope": 41931, "src": "7094:10:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41889, "name": "address", "nodeType": "ElementaryTypeName", "src": "7094:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41892, "mutability": "mutable", "name": "amount", "nameLocation": "7114:6:29", "nodeType": "VariableDeclaration", "scope": 41931, "src": "7106:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41891, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7106:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7093:28:29"}, "returnParameters": {"id": 41894, "nodeType": "ParameterList", "parameters": [], "src": "7139:0:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 41977, "nodeType": "FunctionDefinition", "src": "7556:478:29", "nodes": [], "body": {"id": 41976, "nodeType": "Block", "src": "7618:416:29", "nodes": [], "statements": [{"assignments": [41939], "declarations": [{"constant": false, "id": 41939, "mutability": "mutable", "name": "fromBalance", "nameLocation": "7636:11:29", "nodeType": "VariableDeclaration", "scope": 41976, "src": "7628:19:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41938, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7628:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 41943, "initialValue": {"baseExpression": {"id": 41940, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "7650:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41942, "indexExpression": {"id": 41941, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41933, "src": "7660:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7650:15:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "7628:37:29"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41946, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41944, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41939, "src": "7679:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 41945, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41935, "src": "7693:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7679:20:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41953, "nodeType": "IfStatement", "src": "7675:79:29", "trueBody": {"errorCall": {"arguments": [{"id": 41948, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41933, "src": "7728:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41949, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41939, "src": "7734:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41950, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41935, "src": "7747:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41947, "name": "InsufficientBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41491, "src": "7708:19:29", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_uint256_$_t_uint256_$returns$_t_error_$", "typeString": "function (address,uint256,uint256) pure returns (error)"}}, "id": 41951, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7708:46:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 41952, "nodeType": "RevertStatement", "src": "7701:53:29"}}, {"expression": {"id": 41960, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41954, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "7765:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41956, "indexExpression": {"id": 41955, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41933, "src": "7775:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "7765:15:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41959, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41957, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41939, "src": "7783:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 41958, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41935, "src": "7797:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7783:20:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7765:38:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41961, "nodeType": "ExpressionStatement", "src": "7765:38:29"}, {"id": 41966, "nodeType": "UncheckedBlock", "src": "7922:56:29", "statements": [{"expression": {"id": 41964, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41962, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41533, "src": "7946:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "-=", "rightHandSide": {"id": 41963, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41935, "src": "7961:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7946:21:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41965, "nodeType": "ExpressionStatement", "src": "7946:21:29"}]}, {"eventCall": {"arguments": [{"id": 41968, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41933, "src": "8002:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"arguments": [{"hexValue": "30", "id": 41971, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "8016:1:29", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 41970, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8008:7:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 41969, "name": "address", "nodeType": "ElementaryTypeName", "src": "8008:7:29", "typeDescriptions": {}}}, "id": 41972, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8008:10:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41973, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41935, "src": "8020:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41967, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41517, "src": "7993:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41974, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7993:34:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41975, "nodeType": "EmitStatement", "src": "7988:39:29"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_burn", "nameLocation": "7565:5:29", "parameters": {"id": 41936, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41933, "mutability": "mutable", "name": "from", "nameLocation": "7579:4:29", "nodeType": "VariableDeclaration", "scope": 41977, "src": "7571:12:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41932, "name": "address", "nodeType": "ElementaryTypeName", "src": "7571:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41935, "mutability": "mutable", "name": "amount", "nameLocation": "7593:6:29", "nodeType": "VariableDeclaration", "scope": 41977, "src": "7585:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41934, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7585:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7570:30:29"}, "returnParameters": {"id": 41937, "nodeType": "ParameterList", "parameters": [], "src": "7618:0:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "abstract": true, "baseContracts": [], "canonicalName": "ERC20", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 41482, "nodeType": "StructuredDocumentation", "src": "67:406:29", "text": "@notice Modern and gas efficient ERC20 + EIP-2612 implementation.\n <AUTHOR> (https://github.com/transmissions11/solmate/blob/main/src/tokens/ERC20.sol)\n <AUTHOR> from Uniswap (https://github.com/Uniswap/uniswap-v2-core/blob/master/contracts/UniswapV2ERC20.sol)\n @dev Do not manually set balances without updating totalSupply, as the sum of all user balances must not exceed it."}, "fullyImplemented": true, "linearizedBaseContracts": [41978], "name": "ERC20", "nameLocation": "491:5:29", "scope": 42023, "usedErrors": [41491, 41502, 41509], "usedEvents": [41517, 41525]}, {"id": 42022, "nodeType": "ContractDefinition", "src": "8038:338:29", "nodes": [{"id": 41995, "nodeType": "FunctionDefinition", "src": "8072:108:29", "nodes": [], "body": {"id": 41994, "nodeType": "Block", "src": "8178:2:29", "nodes": [], "statements": []}, "implemented": true, "kind": "constructor", "modifiers": [{"arguments": [{"id": 41989, "name": "_name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41982, "src": "8151:5:29", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 41990, "name": "_symbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41984, "src": "8158:7:29", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 41991, "name": "_decimals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41986, "src": "8167:9:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}], "id": 41992, "kind": "baseConstructorSpecifier", "modifierName": {"id": 41988, "name": "ERC20", "nameLocations": ["8145:5:29"], "nodeType": "IdentifierPath", "referencedDeclaration": 41978, "src": "8145:5:29"}, "nodeType": "ModifierInvocation", "src": "8145:32:29"}], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 41987, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41982, "mutability": "mutable", "name": "_name", "nameLocation": "8098:5:29", "nodeType": "VariableDeclaration", "scope": 41995, "src": "8084:19:29", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 41981, "name": "string", "nodeType": "ElementaryTypeName", "src": "8084:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 41984, "mutability": "mutable", "name": "_symbol", "nameLocation": "8119:7:29", "nodeType": "VariableDeclaration", "scope": 41995, "src": "8105:21:29", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 41983, "name": "string", "nodeType": "ElementaryTypeName", "src": "8105:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 41986, "mutability": "mutable", "name": "_decimals", "nameLocation": "8134:9:29", "nodeType": "VariableDeclaration", "scope": 41995, "src": "8128:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 41985, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "8128:5:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "8083:61:29"}, "returnParameters": {"id": 41993, "nodeType": "ParameterList", "parameters": [], "src": "8178:0:29"}, "scope": 42022, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42008, "nodeType": "FunctionDefinition", "src": "8186:89:29", "nodes": [], "body": {"id": 42007, "nodeType": "Block", "src": "8242:33:29", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42003, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41997, "src": "8258:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 42004, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41999, "src": "8262:5:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 42002, "name": "_mint", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41931, "src": "8252:5:29", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 42005, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8252:16:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42006, "nodeType": "ExpressionStatement", "src": "8252:16:29"}]}, "functionSelector": "40c10f19", "implemented": true, "kind": "function", "modifiers": [], "name": "mint", "nameLocation": "8195:4:29", "parameters": {"id": 42000, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41997, "mutability": "mutable", "name": "to", "nameLocation": "8208:2:29", "nodeType": "VariableDeclaration", "scope": 42008, "src": "8200:10:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41996, "name": "address", "nodeType": "ElementaryTypeName", "src": "8200:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41999, "mutability": "mutable", "name": "value", "nameLocation": "8220:5:29", "nodeType": "VariableDeclaration", "scope": 42008, "src": "8212:13:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41998, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8212:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8199:27:29"}, "returnParameters": {"id": 42001, "nodeType": "ParameterList", "parameters": [], "src": "8242:0:29"}, "scope": 42022, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"id": 42021, "nodeType": "FunctionDefinition", "src": "8281:93:29", "nodes": [], "body": {"id": 42020, "nodeType": "Block", "src": "8339:35:29", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42016, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42010, "src": "8355:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 42017, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42012, "src": "8361:5:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 42015, "name": "_burn", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41977, "src": "8349:5:29", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 42018, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8349:18:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42019, "nodeType": "ExpressionStatement", "src": "8349:18:29"}]}, "functionSelector": "9dc29fac", "implemented": true, "kind": "function", "modifiers": [], "name": "burn", "nameLocation": "8290:4:29", "parameters": {"id": 42013, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42010, "mutability": "mutable", "name": "from", "nameLocation": "8303:4:29", "nodeType": "VariableDeclaration", "scope": 42021, "src": "8295:12:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 42009, "name": "address", "nodeType": "ElementaryTypeName", "src": "8295:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 42012, "mutability": "mutable", "name": "value", "nameLocation": "8317:5:29", "nodeType": "VariableDeclaration", "scope": 42021, "src": "8309:13:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42011, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8309:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8294:29:29"}, "returnParameters": {"id": 42014, "nodeType": "ParameterList", "parameters": [], "src": "8339:0:29"}, "scope": 42022, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}], "abstract": false, "baseContracts": [{"baseName": {"id": 41979, "name": "ERC20", "nameLocations": ["8060:5:29"], "nodeType": "IdentifierPath", "referencedDeclaration": 41978, "src": "8060:5:29"}, "id": 41980, "nodeType": "InheritanceSpecifier", "src": "8060:5:29"}], "canonicalName": "MockERC20", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [42022, 41978], "name": "MockERC20", "nameLocation": "8047:9:29", "scope": 42023, "usedErrors": [41491, 41502, 41509], "usedEvents": [41517, 41525]}], "license": "AGPL-3.0-only"}, "id": 29}