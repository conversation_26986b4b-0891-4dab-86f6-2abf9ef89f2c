{"abi": [{"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "permit", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "InsufficientAllowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InsufficientBalance", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "MintOverflow", "inputs": [{"name": "currentSupply", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"DOMAIN_SEPARATOR()": "3644e515", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "decimals()": "313ce567", "name()": "06fdde03", "nonces(address)": "7ecebe00", "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": "d505accf", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"currentSupply\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"MintOverflow\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DOMAIN_SEPARATOR\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"permit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"author\":\"Solmate (https://github.com/transmissions11/solmate/blob/main/src/tokens/ERC20.sol)Modified from Uniswap (https://github.com/Uniswap/uniswap-v2-core/blob/master/contracts/UniswapV2ERC20.sol)\",\"details\":\"Do not manually set balances without updating totalSupply, as the sum of all user balances must not exceed it.\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"errors\":{\"InsufficientAllowance(address,address,uint256,uint256)\":[{\"notice\":\"Thrown when attempting to transfer more tokens than allowed\"}],\"InsufficientBalance(address,uint256,uint256)\":[{\"notice\":\"Thrown when attempting to transfer more tokens than available balance\"}],\"MintOverflow(uint256,uint256)\":[{\"notice\":\"Thrown when minting would cause overflow\"}]},\"kind\":\"user\",\"methods\":{},\"notice\":\"Modern and gas efficient ERC20 + EIP-2612 implementation.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/setup-helpers/src/MockERC20.sol\":\"ERC20\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/setup-helpers/src/MockERC20.sol\":{\"keccak256\":\"0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578\",\"dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "type": "error", "name": "InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "type": "error", "name": "InsufficientBalance"}, {"inputs": [{"internalType": "uint256", "name": "currentSupply", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "type": "error", "name": "MintOverflow"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "permit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/setup-helpers/src/MockERC20.sol": "ERC20"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/setup-helpers/src/MockERC20.sol": {"keccak256": "0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f", "urls": ["bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578", "dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE"], "license": "AGPL-3.0-only"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 41527, "contract": "lib/setup-helpers/src/MockERC20.sol:ERC20", "label": "name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 41529, "contract": "lib/setup-helpers/src/MockERC20.sol:ERC20", "label": "symbol", "offset": 0, "slot": "1", "type": "t_string_storage"}, {"astId": 41533, "contract": "lib/setup-helpers/src/MockERC20.sol:ERC20", "label": "totalSupply", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 41537, "contract": "lib/setup-helpers/src/MockERC20.sol:ERC20", "label": "balanceOf", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 41543, "contract": "lib/setup-helpers/src/MockERC20.sol:ERC20", "label": "allowance", "offset": 0, "slot": "4", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))"}, {"astId": 41551, "contract": "lib/setup-helpers/src/MockERC20.sol:ERC20", "label": "nonces", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_uint256)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_uint256)"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "lib/setup-helpers/src/MockERC20.sol", "id": 42023, "exportedSymbols": {"ERC20": [41978], "MockERC20": [42022]}, "nodeType": "SourceUnit", "src": "42:8335:29", "nodes": [{"id": 41481, "nodeType": "PragmaDirective", "src": "42:23:29", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 41978, "nodeType": "ContractDefinition", "src": "473:7563:29", "nodes": [{"id": 41491, "nodeType": "ErrorDefinition", "src": "768:73:29", "nodes": [], "documentation": {"id": 41483, "nodeType": "StructuredDocumentation", "src": "682:81:29", "text": "@notice Thrown when attempting to transfer more tokens than available balance"}, "errorSelector": "db42144d", "name": "InsufficientBalance", "nameLocation": "774:19:29", "parameters": {"id": 41490, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41485, "mutability": "mutable", "name": "from", "nameLocation": "802:4:29", "nodeType": "VariableDeclaration", "scope": 41491, "src": "794:12:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41484, "name": "address", "nodeType": "ElementaryTypeName", "src": "794:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41487, "mutability": "mutable", "name": "balance", "nameLocation": "816:7:29", "nodeType": "VariableDeclaration", "scope": 41491, "src": "808:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41486, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "808:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41489, "mutability": "mutable", "name": "amount", "nameLocation": "833:6:29", "nodeType": "VariableDeclaration", "scope": 41491, "src": "825:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41488, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "825:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "793:47:29"}}, {"id": 41502, "nodeType": "ErrorDefinition", "src": "923:95:29", "nodes": [], "documentation": {"id": 41492, "nodeType": "StructuredDocumentation", "src": "847:71:29", "text": "@notice Thrown when attempting to transfer more tokens than allowed"}, "errorSelector": "91beda24", "name": "InsufficientAllowance", "nameLocation": "929:21:29", "parameters": {"id": 41501, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41494, "mutability": "mutable", "name": "owner", "nameLocation": "959:5:29", "nodeType": "VariableDeclaration", "scope": 41502, "src": "951:13:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41493, "name": "address", "nodeType": "ElementaryTypeName", "src": "951:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41496, "mutability": "mutable", "name": "spender", "nameLocation": "974:7:29", "nodeType": "VariableDeclaration", "scope": 41502, "src": "966:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41495, "name": "address", "nodeType": "ElementaryTypeName", "src": "966:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41498, "mutability": "mutable", "name": "allowance", "nameLocation": "991:9:29", "nodeType": "VariableDeclaration", "scope": 41502, "src": "983:17:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41497, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "983:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41500, "mutability": "mutable", "name": "amount", "nameLocation": "1010:6:29", "nodeType": "VariableDeclaration", "scope": 41502, "src": "1002:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41499, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1002:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "950:67:29"}}, {"id": 41509, "nodeType": "ErrorDefinition", "src": "1081:58:29", "nodes": [], "documentation": {"id": 41503, "nodeType": "StructuredDocumentation", "src": "1024:52:29", "text": "@notice Thrown when minting would cause overflow"}, "errorSelector": "93ea3e04", "name": "MintOverflow", "nameLocation": "1087:12:29", "parameters": {"id": 41508, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41505, "mutability": "mutable", "name": "currentSupply", "nameLocation": "1108:13:29", "nodeType": "VariableDeclaration", "scope": 41509, "src": "1100:21:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41504, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1100:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41507, "mutability": "mutable", "name": "amount", "nameLocation": "1131:6:29", "nodeType": "VariableDeclaration", "scope": 41509, "src": "1123:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41506, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1123:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1099:39:29"}}, {"id": 41517, "nodeType": "EventDefinition", "src": "1324:73:29", "nodes": [], "anonymous": false, "eventSelector": "ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef", "name": "Transfer", "nameLocation": "1330:8:29", "parameters": {"id": 41516, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41511, "indexed": true, "mutability": "mutable", "name": "from", "nameLocation": "1355:4:29", "nodeType": "VariableDeclaration", "scope": 41517, "src": "1339:20:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41510, "name": "address", "nodeType": "ElementaryTypeName", "src": "1339:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41513, "indexed": true, "mutability": "mutable", "name": "to", "nameLocation": "1377:2:29", "nodeType": "VariableDeclaration", "scope": 41517, "src": "1361:18:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41512, "name": "address", "nodeType": "ElementaryTypeName", "src": "1361:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41515, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "1389:6:29", "nodeType": "VariableDeclaration", "scope": 41517, "src": "1381:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41514, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1381:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1338:58:29"}}, {"id": 41525, "nodeType": "EventDefinition", "src": "1403:79:29", "nodes": [], "anonymous": false, "eventSelector": "8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925", "name": "Approval", "nameLocation": "1409:8:29", "parameters": {"id": 41524, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41519, "indexed": true, "mutability": "mutable", "name": "owner", "nameLocation": "1434:5:29", "nodeType": "VariableDeclaration", "scope": 41525, "src": "1418:21:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41518, "name": "address", "nodeType": "ElementaryTypeName", "src": "1418:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41521, "indexed": true, "mutability": "mutable", "name": "spender", "nameLocation": "1457:7:29", "nodeType": "VariableDeclaration", "scope": 41525, "src": "1441:23:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41520, "name": "address", "nodeType": "ElementaryTypeName", "src": "1441:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41523, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "1474:6:29", "nodeType": "VariableDeclaration", "scope": 41525, "src": "1466:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41522, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1466:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1417:64:29"}}, {"id": 41527, "nodeType": "VariableDeclaration", "src": "1672:18:29", "nodes": [], "constant": false, "functionSelector": "06fdde03", "mutability": "mutable", "name": "name", "nameLocation": "1686:4:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string"}, "typeName": {"id": 41526, "name": "string", "nodeType": "ElementaryTypeName", "src": "1672:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "public"}, {"id": 41529, "nodeType": "VariableDeclaration", "src": "1697:20:29", "nodes": [], "constant": false, "functionSelector": "95d89b41", "mutability": "mutable", "name": "symbol", "nameLocation": "1711:6:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string"}, "typeName": {"id": 41528, "name": "string", "nodeType": "ElementaryTypeName", "src": "1697:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "public"}, {"id": 41531, "nodeType": "VariableDeclaration", "src": "1724:31:29", "nodes": [], "constant": false, "functionSelector": "313ce567", "mutability": "immutable", "name": "decimals", "nameLocation": "1747:8:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 41530, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "1724:5:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "public"}, {"id": 41533, "nodeType": "VariableDeclaration", "src": "1945:26:29", "nodes": [], "constant": false, "functionSelector": "18160ddd", "mutability": "mutable", "name": "totalSupply", "nameLocation": "1960:11:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41532, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1945:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"id": 41537, "nodeType": "VariableDeclaration", "src": "1978:44:29", "nodes": [], "constant": false, "functionSelector": "70a08231", "mutability": "mutable", "name": "balanceOf", "nameLocation": "2013:9:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 41536, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 41534, "name": "address", "nodeType": "ElementaryTypeName", "src": "1986:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1978:27:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 41535, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1997:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "visibility": "public"}, {"id": 41543, "nodeType": "VariableDeclaration", "src": "2029:64:29", "nodes": [], "constant": false, "functionSelector": "dd62ed3e", "mutability": "mutable", "name": "allowance", "nameLocation": "2084:9:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "typeName": {"id": 41542, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 41538, "name": "address", "nodeType": "ElementaryTypeName", "src": "2037:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "2029:47:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 41541, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 41539, "name": "address", "nodeType": "ElementaryTypeName", "src": "2056:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "2048:27:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 41540, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2067:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}}, "visibility": "public"}, {"id": 41545, "nodeType": "VariableDeclaration", "src": "2284:43:29", "nodes": [], "constant": false, "mutability": "immutable", "name": "INITIAL_CHAIN_ID", "nameLocation": "2311:16:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41544, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2284:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"id": 41547, "nodeType": "VariableDeclaration", "src": "2334:51:29", "nodes": [], "constant": false, "mutability": "immutable", "name": "INITIAL_DOMAIN_SEPARATOR", "nameLocation": "2361:24:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41546, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2334:7:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"id": 41551, "nodeType": "VariableDeclaration", "src": "2392:41:29", "nodes": [], "constant": false, "functionSelector": "7ecebe00", "mutability": "mutable", "name": "nonces", "nameLocation": "2427:6:29", "scope": 41978, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 41550, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 41548, "name": "address", "nodeType": "ElementaryTypeName", "src": "2400:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "2392:27:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 41549, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2411:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "visibility": "public"}, {"id": 41583, "nodeType": "FunctionDefinition", "src": "2622:262:29", "nodes": [], "body": {"id": 41582, "nodeType": "Block", "src": "2695:189:29", "nodes": [], "statements": [{"expression": {"id": 41562, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41560, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41527, "src": "2705:4:29", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41561, "name": "_name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41553, "src": "2712:5:29", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "2705:12:29", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 41563, "nodeType": "ExpressionStatement", "src": "2705:12:29"}, {"expression": {"id": 41566, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41564, "name": "symbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41529, "src": "2727:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41565, "name": "_symbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41555, "src": "2736:7:29", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "2727:16:29", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 41567, "nodeType": "ExpressionStatement", "src": "2727:16:29"}, {"expression": {"id": 41570, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41568, "name": "decimals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41531, "src": "2753:8:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41569, "name": "_decimals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41557, "src": "2764:9:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "src": "2753:20:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 41571, "nodeType": "ExpressionStatement", "src": "2753:20:29"}, {"expression": {"id": 41575, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41572, "name": "INITIAL_CHAIN_ID", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41545, "src": "2784:16:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 41573, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "2803:5:29", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 41574, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2809:7:29", "memberName": "chainid", "nodeType": "MemberAccess", "src": "2803:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2784:32:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41576, "nodeType": "ExpressionStatement", "src": "2784:32:29"}, {"expression": {"id": 41580, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41577, "name": "INITIAL_DOMAIN_SEPARATOR", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41547, "src": "2826:24:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [], "expression": {"argumentTypes": [], "id": 41578, "name": "computeDomainSeparator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41888, "src": "2853:22:29", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bytes32_$", "typeString": "function () view returns (bytes32)"}}, "id": 41579, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2853:24:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "2826:51:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 41581, "nodeType": "ExpressionStatement", "src": "2826:51:29"}]}, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 41558, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41553, "mutability": "mutable", "name": "_name", "nameLocation": "2648:5:29", "nodeType": "VariableDeclaration", "scope": 41583, "src": "2634:19:29", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 41552, "name": "string", "nodeType": "ElementaryTypeName", "src": "2634:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 41555, "mutability": "mutable", "name": "_symbol", "nameLocation": "2669:7:29", "nodeType": "VariableDeclaration", "scope": 41583, "src": "2655:21:29", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 41554, "name": "string", "nodeType": "ElementaryTypeName", "src": "2655:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 41557, "mutability": "mutable", "name": "_decimals", "nameLocation": "2684:9:29", "nodeType": "VariableDeclaration", "scope": 41583, "src": "2678:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 41556, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "2678:5:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "2633:61:29"}, "returnParameters": {"id": 41559, "nodeType": "ParameterList", "parameters": [], "src": "2695:0:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 41611, "nodeType": "FunctionDefinition", "src": "3072:211:29", "nodes": [], "body": {"id": 41610, "nodeType": "Block", "src": "3152:131:29", "nodes": [], "statements": [{"expression": {"id": 41599, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 41592, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41543, "src": "3162:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 41596, "indexExpression": {"expression": {"id": 41593, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3172:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41594, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3176:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3172:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3162:21:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41597, "indexExpression": {"id": 41595, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41585, "src": "3184:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3162:30:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41598, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41587, "src": "3195:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3162:39:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41600, "nodeType": "ExpressionStatement", "src": "3162:39:29"}, {"eventCall": {"arguments": [{"expression": {"id": 41602, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3226:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41603, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3230:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3226:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41604, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41585, "src": "3238:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41605, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41587, "src": "3247:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41601, "name": "Approval", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41525, "src": "3217:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41606, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3217:37:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41607, "nodeType": "EmitStatement", "src": "3212:42:29"}, {"expression": {"hexValue": "74727565", "id": 41608, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3272:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 41591, "id": 41609, "nodeType": "Return", "src": "3265:11:29"}]}, "functionSelector": "095ea7b3", "implemented": true, "kind": "function", "modifiers": [], "name": "approve", "nameLocation": "3081:7:29", "parameters": {"id": 41588, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41585, "mutability": "mutable", "name": "spender", "nameLocation": "3097:7:29", "nodeType": "VariableDeclaration", "scope": 41611, "src": "3089:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41584, "name": "address", "nodeType": "ElementaryTypeName", "src": "3089:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41587, "mutability": "mutable", "name": "amount", "nameLocation": "3114:6:29", "nodeType": "VariableDeclaration", "scope": 41611, "src": "3106:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41586, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3106:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3088:33:29"}, "returnParameters": {"id": 41591, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41590, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41611, "src": "3146:4:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41589, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3146:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3145:6:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"id": 41664, "nodeType": "FunctionDefinition", "src": "3289:535:29", "nodes": [], "body": {"id": 41663, "nodeType": "Block", "src": "3365:459:29", "nodes": [], "statements": [{"assignments": [41621], "declarations": [{"constant": false, "id": 41621, "mutability": "mutable", "name": "fromBalance", "nameLocation": "3383:11:29", "nodeType": "VariableDeclaration", "scope": 41663, "src": "3375:19:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41620, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3375:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 41626, "initialValue": {"baseExpression": {"id": 41622, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "3397:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41625, "indexExpression": {"expression": {"id": 41623, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3407:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41624, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3411:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3407:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3397:21:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3375:43:29"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41629, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41627, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41621, "src": "3432:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 41628, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41615, "src": "3446:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3432:20:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41637, "nodeType": "IfStatement", "src": "3428:85:29", "trueBody": {"errorCall": {"arguments": [{"expression": {"id": 41631, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3481:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41632, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3485:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3481:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41633, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41621, "src": "3493:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41634, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41615, "src": "3506:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41630, "name": "InsufficientBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41491, "src": "3461:19:29", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_uint256_$_t_uint256_$returns$_t_error_$", "typeString": "function (address,uint256,uint256) pure returns (error)"}}, "id": 41635, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3461:52:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 41636, "nodeType": "RevertStatement", "src": "3454:59:29"}}, {"expression": {"id": 41645, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41638, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "3524:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41641, "indexExpression": {"expression": {"id": 41639, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3534:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41640, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3538:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3534:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3524:21:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41644, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41642, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41621, "src": "3548:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 41643, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41615, "src": "3562:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3548:20:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3524:44:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41646, "nodeType": "ExpressionStatement", "src": "3524:44:29"}, {"id": 41653, "nodeType": "UncheckedBlock", "src": "3690:58:29", "statements": [{"expression": {"id": 41651, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41647, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "3714:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41649, "indexExpression": {"id": 41648, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41613, "src": "3724:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3714:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 41650, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41615, "src": "3731:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3714:23:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41652, "nodeType": "ExpressionStatement", "src": "3714:23:29"}]}, {"eventCall": {"arguments": [{"expression": {"id": 41655, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3772:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41656, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3776:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3772:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41657, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41613, "src": "3784:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41658, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41615, "src": "3788:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41654, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41517, "src": "3763:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41659, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3763:32:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41660, "nodeType": "EmitStatement", "src": "3758:37:29"}, {"expression": {"hexValue": "74727565", "id": 41661, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3813:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 41619, "id": 41662, "nodeType": "Return", "src": "3806:11:29"}]}, "functionSelector": "a9059cbb", "implemented": true, "kind": "function", "modifiers": [], "name": "transfer", "nameLocation": "3298:8:29", "parameters": {"id": 41616, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41613, "mutability": "mutable", "name": "to", "nameLocation": "3315:2:29", "nodeType": "VariableDeclaration", "scope": 41664, "src": "3307:10:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41612, "name": "address", "nodeType": "ElementaryTypeName", "src": "3307:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41615, "mutability": "mutable", "name": "amount", "nameLocation": "3327:6:29", "nodeType": "VariableDeclaration", "scope": 41664, "src": "3319:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41614, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3319:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3306:28:29"}, "returnParameters": {"id": 41619, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41618, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41664, "src": "3359:4:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41617, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3359:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3358:6:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"id": 41756, "nodeType": "FunctionDefinition", "src": "3830:834:29", "nodes": [], "body": {"id": 41755, "nodeType": "Block", "src": "3924:740:29", "nodes": [], "statements": [{"assignments": [41676], "declarations": [{"constant": false, "id": 41676, "mutability": "mutable", "name": "allowed", "nameLocation": "3942:7:29", "nodeType": "VariableDeclaration", "scope": 41755, "src": "3934:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41675, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3934:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 41683, "initialValue": {"baseExpression": {"baseExpression": {"id": 41677, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41543, "src": "3952:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 41679, "indexExpression": {"id": 41678, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "3962:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3952:15:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41682, "indexExpression": {"expression": {"id": 41680, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3968:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41681, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3972:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "3968:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3952:27:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3934:45:29"}, {"assignments": [41685], "declarations": [{"constant": false, "id": 41685, "mutability": "mutable", "name": "fromBalance", "nameLocation": "4033:11:29", "nodeType": "VariableDeclaration", "scope": 41755, "src": "4025:19:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41684, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4025:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 41689, "initialValue": {"baseExpression": {"id": 41686, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "4047:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41688, "indexExpression": {"id": 41687, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4057:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4047:15:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "4025:37:29"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41696, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41690, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41676, "src": "4077:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"expression": {"arguments": [{"id": 41693, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4093:7:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 41692, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4093:7:29", "typeDescriptions": {}}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}], "id": 41691, "name": "type", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -27, "src": "4088:4:29", "typeDescriptions": {"typeIdentifier": "t_function_metatype_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 41694, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4088:13:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_magic_meta_type_t_uint256", "typeString": "type(uint256)"}}, "id": 41695, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4102:3:29", "memberName": "max", "nodeType": "MemberAccess", "src": "4088:17:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4077:28:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41721, "nodeType": "IfStatement", "src": "4073:204:29", "trueBody": {"id": 41720, "nodeType": "Block", "src": "4107:170:29", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41699, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41697, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41676, "src": "4125:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 41698, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4135:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4125:16:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41708, "nodeType": "IfStatement", "src": "4121:85:29", "trueBody": {"errorCall": {"arguments": [{"id": 41701, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4172:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 41702, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "4178:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41703, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4182:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "4178:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41704, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41676, "src": "4190:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41705, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4199:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41700, "name": "InsufficientAllowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41502, "src": "4150:21:29", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_address_$_t_uint256_$_t_uint256_$returns$_t_error_$", "typeString": "function (address,address,uint256,uint256) pure returns (error)"}}, "id": 41706, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4150:56:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 41707, "nodeType": "RevertStatement", "src": "4143:63:29"}}, {"expression": {"id": 41718, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 41709, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41543, "src": "4220:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 41713, "indexExpression": {"id": 41710, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4230:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4220:15:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41714, "indexExpression": {"expression": {"id": 41711, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "4236:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 41712, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4240:6:29", "memberName": "sender", "nodeType": "MemberAccess", "src": "4236:10:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "4220:27:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41717, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41715, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41676, "src": "4250:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 41716, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4260:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4250:16:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4220:46:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41719, "nodeType": "ExpressionStatement", "src": "4220:46:29"}]}}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41724, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41722, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41685, "src": "4291:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 41723, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4305:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4291:20:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41731, "nodeType": "IfStatement", "src": "4287:79:29", "trueBody": {"errorCall": {"arguments": [{"id": 41726, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4340:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41727, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41685, "src": "4346:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41728, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4359:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41725, "name": "InsufficientBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41491, "src": "4320:19:29", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_uint256_$_t_uint256_$returns$_t_error_$", "typeString": "function (address,uint256,uint256) pure returns (error)"}}, "id": 41729, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4320:46:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 41730, "nodeType": "RevertStatement", "src": "4313:53:29"}}, {"expression": {"id": 41738, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41732, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "4376:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41734, "indexExpression": {"id": 41733, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4386:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "4376:15:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41737, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41735, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41685, "src": "4394:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 41736, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4408:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4394:20:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4376:38:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41739, "nodeType": "ExpressionStatement", "src": "4376:38:29"}, {"id": 41746, "nodeType": "UncheckedBlock", "src": "4536:58:29", "statements": [{"expression": {"id": 41744, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41740, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "4560:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41742, "indexExpression": {"id": 41741, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41668, "src": "4570:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "4560:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 41743, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4577:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4560:23:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41745, "nodeType": "ExpressionStatement", "src": "4560:23:29"}]}, {"eventCall": {"arguments": [{"id": 41748, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41666, "src": "4618:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41749, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41668, "src": "4624:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41750, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41670, "src": "4628:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41747, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41517, "src": "4609:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41751, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4609:26:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41752, "nodeType": "EmitStatement", "src": "4604:31:29"}, {"expression": {"hexValue": "74727565", "id": 41753, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4653:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 41674, "id": 41754, "nodeType": "Return", "src": "4646:11:29"}]}, "functionSelector": "23b872dd", "implemented": true, "kind": "function", "modifiers": [], "name": "transferFrom", "nameLocation": "3839:12:29", "parameters": {"id": 41671, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41666, "mutability": "mutable", "name": "from", "nameLocation": "3860:4:29", "nodeType": "VariableDeclaration", "scope": 41756, "src": "3852:12:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41665, "name": "address", "nodeType": "ElementaryTypeName", "src": "3852:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41668, "mutability": "mutable", "name": "to", "nameLocation": "3874:2:29", "nodeType": "VariableDeclaration", "scope": 41756, "src": "3866:10:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41667, "name": "address", "nodeType": "ElementaryTypeName", "src": "3866:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41670, "mutability": "mutable", "name": "amount", "nameLocation": "3886:6:29", "nodeType": "VariableDeclaration", "scope": 41756, "src": "3878:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41669, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3878:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3851:42:29"}, "returnParameters": {"id": 41674, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41673, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41756, "src": "3918:4:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41672, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3918:4:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3917:6:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"id": 41843, "nodeType": "FunctionDefinition", "src": "4853:1441:29", "nodes": [], "body": {"id": 41842, "nodeType": "Block", "src": "5000:1294:29", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41777, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41774, "name": "deadline", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41764, "src": "5018:8:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"expression": {"id": 41775, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "5030:5:29", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 41776, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5036:9:29", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "5030:15:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5018:27:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5045524d49545f444541444c494e455f45585049524544", "id": 41778, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5047:25:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_dd18cfd81b4c1281b56302a044e7f751a261543590362c41d86af048f8ed4b3e", "typeString": "literal_string \"PERMIT_DEADLINE_EXPIRED\""}, "value": "PERMIT_DEADLINE_EXPIRED"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_dd18cfd81b4c1281b56302a044e7f751a261543590362c41d86af048f8ed4b3e", "typeString": "literal_string \"PERMIT_DEADLINE_EXPIRED\""}], "id": 41773, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "5010:7:29", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 41779, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5010:63:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41780, "nodeType": "ExpressionStatement", "src": "5010:63:29"}, {"id": 41835, "nodeType": "UncheckedBlock", "src": "5214:1027:29", "statements": [{"assignments": [41782], "declarations": [{"constant": false, "id": 41782, "mutability": "mutable", "name": "recovered<PERSON><PERSON><PERSON>", "nameLocation": "5246:16:29", "nodeType": "VariableDeclaration", "scope": 41835, "src": "5238:24:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41781, "name": "address", "nodeType": "ElementaryTypeName", "src": "5238:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 41812, "initialValue": {"arguments": [{"arguments": [{"arguments": [{"hexValue": "1901", "id": 41787, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5365:10:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_301a50b291d33ce1e8e9064e3f6a6c51d902ec22892b50d58abf6357c6a45541", "typeString": "literal_string hex\"1901\""}, "value": "\u0019\u0001"}, {"arguments": [], "expression": {"argumentTypes": [], "id": 41788, "name": "DOMAIN_SEPARATOR", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41858, "src": "5401:16:29", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bytes32_$", "typeString": "function () view returns (bytes32)"}}, "id": 41789, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5401:18:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"arguments": [{"arguments": [{"arguments": [{"hexValue": "5065726d69742861646472657373206f776e65722c61646472657373207370656e6465722c75696e743235362076616c75652c75696e74323536206e6f6e63652c75696e7432353620646561646c696e6529", "id": 41794, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5575:84:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9", "typeString": "literal_string \"Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)\""}, "value": "Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9", "typeString": "literal_string \"Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)\""}], "id": 41793, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "5528:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41795, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5528:165:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 41796, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41758, "src": "5727:5:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41797, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41760, "src": "5766:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41798, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41762, "src": "5807:5:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41802, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "5846:15:29", "subExpression": {"baseExpression": {"id": 41799, "name": "nonces", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41551, "src": "5846:6:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41801, "indexExpression": {"id": 41800, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41758, "src": "5853:5:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5846:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41803, "name": "deadline", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41764, "src": "5895:8:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 41791, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "5484:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 41792, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5488:6:29", "memberName": "encode", "nodeType": "MemberAccess", "src": "5484:10:29", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 41804, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5484:449:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 41790, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "5445:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41805, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5445:514:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_301a50b291d33ce1e8e9064e3f6a6c51d902ec22892b50d58abf6357c6a45541", "typeString": "literal_string hex\"1901\""}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 41785, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "5323:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 41786, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5327:12:29", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "5323:16:29", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 41806, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5323:658:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 41784, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "5292:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41807, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5292:707:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 41808, "name": "v", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41766, "src": "6017:1:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, {"id": 41809, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41768, "src": "6036:1:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 41810, "name": "s", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41770, "src": "6055:1:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_uint8", "typeString": "uint8"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41783, "name": "ecrecover", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -6, "src": "5265:9:29", "typeDescriptions": {"typeIdentifier": "t_function_ecrecover_pure$_t_bytes32_$_t_uint8_$_t_bytes32_$_t_bytes32_$returns$_t_address_$", "typeString": "function (bytes32,uint8,bytes32,bytes32) pure returns (address)"}}, "id": 41811, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5265:805:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "5238:832:29"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 41823, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 41819, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41814, "name": "recovered<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41782, "src": "6093:16:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"hexValue": "30", "id": 41817, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6121:1:29", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 41816, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6113:7:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 41815, "name": "address", "nodeType": "ElementaryTypeName", "src": "6113:7:29", "typeDescriptions": {}}}, "id": 41818, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6113:10:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "6093:30:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 41822, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41820, "name": "recovered<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41782, "src": "6127:16:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 41821, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41758, "src": "6147:5:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "6127:25:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "6093:59:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "494e56414c49445f5349474e4552", "id": 41824, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6154:16:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ba2319f5fa9f0c8e55f0d6899910b7354e6f643d1d349de47190066d85e68a1c", "typeString": "literal_string \"INVALID_SIGNER\""}, "value": "INVALID_SIGNER"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_ba2319f5fa9f0c8e55f0d6899910b7354e6f643d1d349de47190066d85e68a1c", "typeString": "literal_string \"INVALID_SIGNER\""}], "id": 41813, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "6085:7:29", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 41825, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6085:86:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41826, "nodeType": "ExpressionStatement", "src": "6085:86:29"}, {"expression": {"id": 41833, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 41827, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41543, "src": "6186:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 41830, "indexExpression": {"id": 41828, "name": "recovered<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41782, "src": "6196:16:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6186:27:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41831, "indexExpression": {"id": 41829, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41760, "src": "6214:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "6186:36:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41832, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41762, "src": "6225:5:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6186:44:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41834, "nodeType": "ExpressionStatement", "src": "6186:44:29"}]}, {"eventCall": {"arguments": [{"id": 41837, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41758, "src": "6265:5:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41838, "name": "spender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41760, "src": "6272:7:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41839, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41762, "src": "6281:5:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41836, "name": "Approval", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41525, "src": "6256:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41840, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6256:31:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41841, "nodeType": "EmitStatement", "src": "6251:36:29"}]}, "functionSelector": "d505accf", "implemented": true, "kind": "function", "modifiers": [], "name": "permit", "nameLocation": "4862:6:29", "parameters": {"id": 41771, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41758, "mutability": "mutable", "name": "owner", "nameLocation": "4877:5:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4869:13:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41757, "name": "address", "nodeType": "ElementaryTypeName", "src": "4869:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41760, "mutability": "mutable", "name": "spender", "nameLocation": "4892:7:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4884:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41759, "name": "address", "nodeType": "ElementaryTypeName", "src": "4884:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41762, "mutability": "mutable", "name": "value", "nameLocation": "4909:5:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4901:13:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41761, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4901:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41764, "mutability": "mutable", "name": "deadline", "nameLocation": "4924:8:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4916:16:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41763, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4916:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 41766, "mutability": "mutable", "name": "v", "nameLocation": "4940:1:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4934:7:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 41765, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "4934:5:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}, {"constant": false, "id": 41768, "mutability": "mutable", "name": "r", "nameLocation": "4951:1:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4943:9:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41767, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4943:7:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 41770, "mutability": "mutable", "name": "s", "nameLocation": "4962:1:29", "nodeType": "VariableDeclaration", "scope": 41843, "src": "4954:9:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41769, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4954:7:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "4868:96:29"}, "returnParameters": {"id": 41772, "nodeType": "ParameterList", "parameters": [], "src": "5000:0:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"id": 41858, "nodeType": "FunctionDefinition", "src": "6300:177:29", "nodes": [], "body": {"id": 41857, "nodeType": "Block", "src": "6366:111:29", "nodes": [], "statements": [{"expression": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41851, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 41848, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "6383:5:29", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 41849, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6389:7:29", "memberName": "chainid", "nodeType": "MemberAccess", "src": "6383:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 41850, "name": "INITIAL_CHAIN_ID", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41545, "src": "6400:16:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6383:33:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 41853, "name": "computeDomainSeparator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41888, "src": "6446:22:29", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bytes32_$", "typeString": "function () view returns (bytes32)"}}, "id": 41854, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6446:24:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 41855, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "6383:87:29", "trueExpression": {"id": 41852, "name": "INITIAL_DOMAIN_SEPARATOR", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41547, "src": "6419:24:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 41847, "id": 41856, "nodeType": "Return", "src": "6376:94:29"}]}, "functionSelector": "3644e515", "implemented": true, "kind": "function", "modifiers": [], "name": "DOMAIN_SEPARATOR", "nameLocation": "6309:16:29", "parameters": {"id": 41844, "nodeType": "ParameterList", "parameters": [], "src": "6325:2:29"}, "returnParameters": {"id": 41847, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41846, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41858, "src": "6357:7:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41845, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6357:7:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "6356:9:29"}, "scope": 41978, "stateMutability": "view", "virtual": true, "visibility": "public"}, {"id": 41888, "nodeType": "FunctionDefinition", "src": "6483:402:29", "nodes": [], "body": {"id": 41887, "nodeType": "Block", "src": "6557:328:29", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"arguments": [{"hexValue": "454950373132446f6d61696e28737472696e67206e616d652c737472696e672076657273696f6e2c75696e7432353620636861696e49642c6164647265737320766572696679696e67436f6e747261637429", "id": 41867, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6635:84:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f", "typeString": "literal_string \"EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)\""}, "value": "EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f", "typeString": "literal_string \"EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)\""}], "id": 41866, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "6625:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41868, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6625:95:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"arguments": [{"arguments": [{"id": 41872, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41527, "src": "6754:4:29", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}], "id": 41871, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6748:5:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 41870, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6748:5:29", "typeDescriptions": {}}}, "id": 41873, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6748:11:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes storage pointer"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes storage pointer"}], "id": 41869, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "6738:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41874, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6738:22:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"arguments": [{"hexValue": "31", "id": 41876, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6788:3:29", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c89efdaa54c0f20c7adf612882df0950f5a951637e0307cdcb4c672f298b8bc6", "typeString": "literal_string \"1\""}, "value": "1"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c89efdaa54c0f20c7adf612882df0950f5a951637e0307cdcb4c672f298b8bc6", "typeString": "literal_string \"1\""}], "id": 41875, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "6778:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41877, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6778:14:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"expression": {"id": 41878, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "6810:5:29", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 41879, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6816:7:29", "memberName": "chainid", "nodeType": "MemberAccess", "src": "6810:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"arguments": [{"id": 41882, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "6849:4:29", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$41978", "typeString": "contract ERC20"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_ERC20_$41978", "typeString": "contract ERC20"}], "id": 41881, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6841:7:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 41880, "name": "address", "nodeType": "ElementaryTypeName", "src": "6841:7:29", "typeDescriptions": {}}}, "id": 41883, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6841:13:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 41864, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "6597:3:29", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 41865, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "6601:6:29", "memberName": "encode", "nodeType": "MemberAccess", "src": "6597:10:29", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 41884, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6597:271:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 41863, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "6574:9:29", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 41885, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6574:304:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 41862, "id": 41886, "nodeType": "Return", "src": "6567:311:29"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "computeDomainSeparator", "nameLocation": "6492:22:29", "parameters": {"id": 41859, "nodeType": "ParameterList", "parameters": [], "src": "6514:2:29"}, "returnParameters": {"id": 41862, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41861, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41888, "src": "6548:7:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41860, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6548:7:29", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "6547:9:29"}, "scope": 41978, "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"id": 41931, "nodeType": "FunctionDefinition", "src": "7079:471:29", "nodes": [], "body": {"id": 41930, "nodeType": "Block", "src": "7139:411:29", "nodes": [], "statements": [{"assignments": [41896], "declarations": [{"constant": false, "id": 41896, "mutability": "mutable", "name": "newTotalSupply", "nameLocation": "7157:14:29", "nodeType": "VariableDeclaration", "scope": 41930, "src": "7149:22:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41895, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7149:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 41900, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41899, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41897, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41533, "src": "7174:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 41898, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41892, "src": "7188:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7174:20:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "7149:45:29"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41903, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41901, "name": "newTotalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41896, "src": "7208:14:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 41902, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41533, "src": "7225:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7208:28:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41909, "nodeType": "IfStatement", "src": "7204:74:29", "trueBody": {"errorCall": {"arguments": [{"id": 41905, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41533, "src": "7258:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41906, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41892, "src": "7271:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41904, "name": "MintOverflow", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41509, "src": "7245:12:29", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_uint256_$_t_uint256_$returns$_t_error_$", "typeString": "function (uint256,uint256) pure returns (error)"}}, "id": 41907, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7245:33:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 41908, "nodeType": "RevertStatement", "src": "7238:40:29"}}, {"expression": {"id": 41912, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41910, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41533, "src": "7288:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 41911, "name": "newTotalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41896, "src": "7302:14:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7288:28:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41913, "nodeType": "ExpressionStatement", "src": "7288:28:29"}, {"id": 41920, "nodeType": "UncheckedBlock", "src": "7438:58:29", "statements": [{"expression": {"id": 41918, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41914, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "7462:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41916, "indexExpression": {"id": 41915, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41890, "src": "7472:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "7462:13:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 41917, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41892, "src": "7479:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7462:23:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41919, "nodeType": "ExpressionStatement", "src": "7462:23:29"}]}, {"eventCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 41924, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7528:1:29", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 41923, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7520:7:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 41922, "name": "address", "nodeType": "ElementaryTypeName", "src": "7520:7:29", "typeDescriptions": {}}}, "id": 41925, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7520:10:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41926, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41890, "src": "7532:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41927, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41892, "src": "7536:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41921, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41517, "src": "7511:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41928, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7511:32:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41929, "nodeType": "EmitStatement", "src": "7506:37:29"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_mint", "nameLocation": "7088:5:29", "parameters": {"id": 41893, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41890, "mutability": "mutable", "name": "to", "nameLocation": "7102:2:29", "nodeType": "VariableDeclaration", "scope": 41931, "src": "7094:10:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41889, "name": "address", "nodeType": "ElementaryTypeName", "src": "7094:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41892, "mutability": "mutable", "name": "amount", "nameLocation": "7114:6:29", "nodeType": "VariableDeclaration", "scope": 41931, "src": "7106:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41891, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7106:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7093:28:29"}, "returnParameters": {"id": 41894, "nodeType": "ParameterList", "parameters": [], "src": "7139:0:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 41977, "nodeType": "FunctionDefinition", "src": "7556:478:29", "nodes": [], "body": {"id": 41976, "nodeType": "Block", "src": "7618:416:29", "nodes": [], "statements": [{"assignments": [41939], "declarations": [{"constant": false, "id": 41939, "mutability": "mutable", "name": "fromBalance", "nameLocation": "7636:11:29", "nodeType": "VariableDeclaration", "scope": 41976, "src": "7628:19:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41938, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7628:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 41943, "initialValue": {"baseExpression": {"id": 41940, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "7650:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41942, "indexExpression": {"id": 41941, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41933, "src": "7660:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7650:15:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "7628:37:29"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41946, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41944, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41939, "src": "7679:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 41945, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41935, "src": "7693:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7679:20:29", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 41953, "nodeType": "IfStatement", "src": "7675:79:29", "trueBody": {"errorCall": {"arguments": [{"id": 41948, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41933, "src": "7728:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41949, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41939, "src": "7734:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 41950, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41935, "src": "7747:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41947, "name": "InsufficientBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41491, "src": "7708:19:29", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_uint256_$_t_uint256_$returns$_t_error_$", "typeString": "function (address,uint256,uint256) pure returns (error)"}}, "id": 41951, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7708:46:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 41952, "nodeType": "RevertStatement", "src": "7701:53:29"}}, {"expression": {"id": 41960, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 41954, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41537, "src": "7765:9:29", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 41956, "indexExpression": {"id": 41955, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41933, "src": "7775:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "7765:15:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41959, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 41957, "name": "fromBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41939, "src": "7783:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 41958, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41935, "src": "7797:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7783:20:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7765:38:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41961, "nodeType": "ExpressionStatement", "src": "7765:38:29"}, {"id": 41966, "nodeType": "UncheckedBlock", "src": "7922:56:29", "statements": [{"expression": {"id": 41964, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 41962, "name": "totalSupply", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41533, "src": "7946:11:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "-=", "rightHandSide": {"id": 41963, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41935, "src": "7961:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7946:21:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41965, "nodeType": "ExpressionStatement", "src": "7946:21:29"}]}, {"eventCall": {"arguments": [{"id": 41968, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41933, "src": "8002:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"arguments": [{"hexValue": "30", "id": 41971, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "8016:1:29", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 41970, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8008:7:29", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 41969, "name": "address", "nodeType": "ElementaryTypeName", "src": "8008:7:29", "typeDescriptions": {}}}, "id": 41972, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8008:10:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 41973, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41935, "src": "8020:6:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41967, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41517, "src": "7993:8:29", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 41974, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7993:34:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 41975, "nodeType": "EmitStatement", "src": "7988:39:29"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_burn", "nameLocation": "7565:5:29", "parameters": {"id": 41936, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41933, "mutability": "mutable", "name": "from", "nameLocation": "7579:4:29", "nodeType": "VariableDeclaration", "scope": 41977, "src": "7571:12:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41932, "name": "address", "nodeType": "ElementaryTypeName", "src": "7571:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41935, "mutability": "mutable", "name": "amount", "nameLocation": "7593:6:29", "nodeType": "VariableDeclaration", "scope": 41977, "src": "7585:14:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41934, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7585:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7570:30:29"}, "returnParameters": {"id": 41937, "nodeType": "ParameterList", "parameters": [], "src": "7618:0:29"}, "scope": 41978, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "abstract": true, "baseContracts": [], "canonicalName": "ERC20", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 41482, "nodeType": "StructuredDocumentation", "src": "67:406:29", "text": "@notice Modern and gas efficient ERC20 + EIP-2612 implementation.\n <AUTHOR> (https://github.com/transmissions11/solmate/blob/main/src/tokens/ERC20.sol)\n <AUTHOR> from Uniswap (https://github.com/Uniswap/uniswap-v2-core/blob/master/contracts/UniswapV2ERC20.sol)\n @dev Do not manually set balances without updating totalSupply, as the sum of all user balances must not exceed it."}, "fullyImplemented": true, "linearizedBaseContracts": [41978], "name": "ERC20", "nameLocation": "491:5:29", "scope": 42023, "usedErrors": [41491, 41502, 41509], "usedEvents": [41517, 41525]}, {"id": 42022, "nodeType": "ContractDefinition", "src": "8038:338:29", "nodes": [{"id": 41995, "nodeType": "FunctionDefinition", "src": "8072:108:29", "nodes": [], "body": {"id": 41994, "nodeType": "Block", "src": "8178:2:29", "nodes": [], "statements": []}, "implemented": true, "kind": "constructor", "modifiers": [{"arguments": [{"id": 41989, "name": "_name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41982, "src": "8151:5:29", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 41990, "name": "_symbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41984, "src": "8158:7:29", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 41991, "name": "_decimals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41986, "src": "8167:9:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}], "id": 41992, "kind": "baseConstructorSpecifier", "modifierName": {"id": 41988, "name": "ERC20", "nameLocations": ["8145:5:29"], "nodeType": "IdentifierPath", "referencedDeclaration": 41978, "src": "8145:5:29"}, "nodeType": "ModifierInvocation", "src": "8145:32:29"}], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 41987, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41982, "mutability": "mutable", "name": "_name", "nameLocation": "8098:5:29", "nodeType": "VariableDeclaration", "scope": 41995, "src": "8084:19:29", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 41981, "name": "string", "nodeType": "ElementaryTypeName", "src": "8084:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 41984, "mutability": "mutable", "name": "_symbol", "nameLocation": "8119:7:29", "nodeType": "VariableDeclaration", "scope": 41995, "src": "8105:21:29", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 41983, "name": "string", "nodeType": "ElementaryTypeName", "src": "8105:6:29", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 41986, "mutability": "mutable", "name": "_decimals", "nameLocation": "8134:9:29", "nodeType": "VariableDeclaration", "scope": 41995, "src": "8128:15:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 41985, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "8128:5:29", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "8083:61:29"}, "returnParameters": {"id": 41993, "nodeType": "ParameterList", "parameters": [], "src": "8178:0:29"}, "scope": 42022, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42008, "nodeType": "FunctionDefinition", "src": "8186:89:29", "nodes": [], "body": {"id": 42007, "nodeType": "Block", "src": "8242:33:29", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42003, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41997, "src": "8258:2:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 42004, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41999, "src": "8262:5:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 42002, "name": "_mint", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41931, "src": "8252:5:29", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 42005, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8252:16:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42006, "nodeType": "ExpressionStatement", "src": "8252:16:29"}]}, "functionSelector": "40c10f19", "implemented": true, "kind": "function", "modifiers": [], "name": "mint", "nameLocation": "8195:4:29", "parameters": {"id": 42000, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41997, "mutability": "mutable", "name": "to", "nameLocation": "8208:2:29", "nodeType": "VariableDeclaration", "scope": 42008, "src": "8200:10:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41996, "name": "address", "nodeType": "ElementaryTypeName", "src": "8200:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 41999, "mutability": "mutable", "name": "value", "nameLocation": "8220:5:29", "nodeType": "VariableDeclaration", "scope": 42008, "src": "8212:13:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41998, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8212:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8199:27:29"}, "returnParameters": {"id": 42001, "nodeType": "ParameterList", "parameters": [], "src": "8242:0:29"}, "scope": 42022, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"id": 42021, "nodeType": "FunctionDefinition", "src": "8281:93:29", "nodes": [], "body": {"id": 42020, "nodeType": "Block", "src": "8339:35:29", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42016, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42010, "src": "8355:4:29", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 42017, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42012, "src": "8361:5:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 42015, "name": "_burn", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41977, "src": "8349:5:29", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 42018, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8349:18:29", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42019, "nodeType": "ExpressionStatement", "src": "8349:18:29"}]}, "functionSelector": "9dc29fac", "implemented": true, "kind": "function", "modifiers": [], "name": "burn", "nameLocation": "8290:4:29", "parameters": {"id": 42013, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42010, "mutability": "mutable", "name": "from", "nameLocation": "8303:4:29", "nodeType": "VariableDeclaration", "scope": 42021, "src": "8295:12:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 42009, "name": "address", "nodeType": "ElementaryTypeName", "src": "8295:7:29", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 42012, "mutability": "mutable", "name": "value", "nameLocation": "8317:5:29", "nodeType": "VariableDeclaration", "scope": 42021, "src": "8309:13:29", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42011, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8309:7:29", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8294:29:29"}, "returnParameters": {"id": 42014, "nodeType": "ParameterList", "parameters": [], "src": "8339:0:29"}, "scope": 42022, "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}], "abstract": false, "baseContracts": [{"baseName": {"id": 41979, "name": "ERC20", "nameLocations": ["8060:5:29"], "nodeType": "IdentifierPath", "referencedDeclaration": 41978, "src": "8060:5:29"}, "id": 41980, "nodeType": "InheritanceSpecifier", "src": "8060:5:29"}], "canonicalName": "MockERC20", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [42022, 41978], "name": "MockERC20", "nameLocation": "8047:9:29", "scope": 42023, "usedErrors": [41491, 41502, 41509], "usedEvents": [41517, 41525]}], "license": "AGPL-3.0-only"}, "id": 29}