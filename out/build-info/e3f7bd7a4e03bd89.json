{"id": "e3f7bd7a4e03bd89", "source_id_to_path": {"0": "lib/forge-std/src/Base.sol", "1": "lib/forge-std/src/Script.sol", "2": "lib/forge-std/src/StdAssertions.sol", "3": "lib/forge-std/src/StdChains.sol", "4": "lib/forge-std/src/StdCheats.sol", "5": "lib/forge-std/src/StdConstants.sol", "6": "lib/forge-std/src/StdError.sol", "7": "lib/forge-std/src/StdInvariant.sol", "8": "lib/forge-std/src/StdJson.sol", "9": "lib/forge-std/src/StdMath.sol", "10": "lib/forge-std/src/StdStorage.sol", "11": "lib/forge-std/src/StdStyle.sol", "12": "lib/forge-std/src/StdToml.sol", "13": "lib/forge-std/src/StdUtils.sol", "14": "lib/forge-std/src/Test.sol", "15": "lib/forge-std/src/Vm.sol", "16": "lib/forge-std/src/console.sol", "17": "lib/forge-std/src/console2.sol", "18": "lib/forge-std/src/interfaces/IMulticall3.sol", "19": "lib/forge-std/src/safeconsole.sol", "20": "script/Counter.s.sol", "21": "src/Counter.sol", "22": "test/Counter.t.sol", "23": "test/CounterHalmos.t.sol"}, "language": "Solidity"}