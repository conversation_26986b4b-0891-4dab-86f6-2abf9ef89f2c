{"id": "a74cedb45c32d659", "source_id_to_path": {"0": "lib/chimera/src/Asserts.sol", "1": "lib/chimera/src/BaseProperties.sol", "2": "lib/chimera/src/BaseSetup.sol", "3": "lib/chimera/src/BaseTargetFunctions.sol", "4": "lib/chimera/src/CryticAsserts.sol", "5": "lib/chimera/src/FoundryAsserts.sol", "6": "lib/chimera/src/Hevm.sol", "7": "lib/forge-std/src/Base.sol", "8": "lib/forge-std/src/StdAssertions.sol", "9": "lib/forge-std/src/StdChains.sol", "10": "lib/forge-std/src/StdCheats.sol", "11": "lib/forge-std/src/StdConstants.sol", "12": "lib/forge-std/src/StdError.sol", "13": "lib/forge-std/src/StdInvariant.sol", "14": "lib/forge-std/src/StdJson.sol", "15": "lib/forge-std/src/StdMath.sol", "16": "lib/forge-std/src/StdStorage.sol", "17": "lib/forge-std/src/StdStyle.sol", "18": "lib/forge-std/src/StdToml.sol", "19": "lib/forge-std/src/StdUtils.sol", "20": "lib/forge-std/src/Test.sol", "21": "lib/forge-std/src/Vm.sol", "22": "lib/forge-std/src/console.sol", "23": "lib/forge-std/src/console2.sol", "24": "lib/forge-std/src/interfaces/IMulticall3.sol", "25": "lib/forge-std/src/safeconsole.sol", "26": "lib/setup-helpers/src/ActorManager.sol", "27": "lib/setup-helpers/src/AssetManager.sol", "28": "lib/setup-helpers/src/EnumerableSet.sol", "29": "lib/setup-helpers/src/MockERC20.sol", "30": "lib/setup-helpers/src/Panic.sol", "31": "lib/setup-helpers/src/Utils.sol", "32": "src/Counter.sol", "33": "test/recon/BeforeAfter.sol", "34": "test/recon/CryticTester.sol", "35": "test/recon/CryticToFoundry.sol", "36": "test/recon/Properties.sol", "37": "test/recon/Setup.sol", "38": "test/recon/TargetFunctions.sol", "39": "test/recon/targets/AdminTargets.sol", "40": "test/recon/targets/CounterTargets.sol", "41": "test/recon/targets/DoomsdayTargets.sol", "42": "test/recon/targets/ManagersTargets.sol"}, "language": "Solidity"}