{"abi": [], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"StdChains provides information about EVM compatible chains that can be used in scripts/tests. For each chain, the chain's name, chain ID, and a default RPC URL are provided. Chains are identified by their alias, which is the same as the alias in the `[rpc_endpoints]` section of the `foundry.toml` file. For best UX, ensure the alias in the `foundry.toml` file match the alias used in this contract, which can be found as the first argument to the `setChainWithDefaultRpcUrl` call in the `initializeStdChains` function. There are two main ways to use this contract:   1. Set a chain with `setChain(string memory chainAlias, ChainData memory chain)` or      `setChain(string memory chainAlias, Chain memory chain)`   2. Get a chain with `get<PERSON>hain(string memory chainAlias)` or `get<PERSON>hai<PERSON>(uint256 chainId)`. The first time either of those are used, chains are initialized with the default set of RPC URLs. This is done in `initializeStdChains`, which uses `setChainWithDefaultRpcUrl`. Defaults are recorded in `defaultRpcUrls`. The `setChain` function is straightforward, and it simply saves off the given chain data. The `getChain` methods use `getChainWithUpdatedRpcUrl` to return a chain. For example, let's say we want to retrieve the RPC URL for `mainnet`:   - If you have specified data with `setChain`, it will return that.   - If you have configured a mainnet RPC URL in `foundry.toml`, it will return the URL, provided it     is valid (e.g. a URL is specified, or an environment variable is given and exists).   - If neither of the above conditions is met, the default data is returned. Summarizing the above, the prioritization hierarchy is `setChain` -> `foundry.toml` -> environment variable -> defaults.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdChains.sol\":\"StdChains\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdChains.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 2983, "contract": "lib/forge-std/src/StdChains.sol:StdChains", "label": "stdChainsInitialized", "offset": 0, "slot": "0", "type": "t_bool"}, {"astId": 3004, "contract": "lib/forge-std/src/StdChains.sol:StdChains", "label": "chains", "offset": 0, "slot": "1", "type": "t_mapping(t_string_memory_ptr,t_struct(Chain)2999_storage)"}, {"astId": 3008, "contract": "lib/forge-std/src/StdChains.sol:StdChains", "label": "defaultRpcUrls", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_string_storage)"}, {"astId": 3012, "contract": "lib/forge-std/src/StdChains.sol:StdChains", "label": "idToAlias", "offset": 0, "slot": "3", "type": "t_mapping(t_uint256,t_string_storage)"}, {"astId": 3015, "contract": "lib/forge-std/src/StdChains.sol:StdChains", "label": "fallbackToDefaultRpcUrls", "offset": 0, "slot": "4", "type": "t_bool"}], "types": {"t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_mapping(t_string_memory_ptr,t_string_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_mapping(t_string_memory_ptr,t_struct(Chain)2999_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => struct StdChains.Chain)", "numberOfBytes": "32", "value": "t_struct(Chain)2999_storage"}, "t_mapping(t_uint256,t_string_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_string_memory_ptr": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Chain)2999_storage": {"encoding": "inplace", "label": "struct StdChains.Chain", "numberOfBytes": "128", "members": [{"astId": 2992, "contract": "lib/forge-std/src/StdChains.sol:StdChains", "label": "name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 2994, "contract": "lib/forge-std/src/StdChains.sol:StdChains", "label": "chainId", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 2996, "contract": "lib/forge-std/src/StdChains.sol:StdChains", "label": "chainAlias", "offset": 0, "slot": "2", "type": "t_string_storage"}, {"astId": 2998, "contract": "lib/forge-std/src/StdChains.sol:StdChains", "label": "rpcUrl", "offset": 0, "slot": "3", "type": "t_string_storage"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "lib/forge-std/src/StdChains.sol", "id": 3922, "exportedSymbols": {"StdChains": [3921], "VmSafe": [17281]}, "nodeType": "SourceUnit", "src": "32:14200:3", "nodes": [{"id": 2961, "nodeType": "PragmaDirective", "src": "32:31:3", "nodes": [], "literals": ["solidity", ">=", "0.6", ".2", "<", "0.9", ".0"]}, {"id": 2963, "nodeType": "ImportDirective", "src": "65:32:3", "nodes": [], "absolutePath": "lib/forge-std/src/Vm.sol", "file": "./Vm.sol", "nameLocation": "-1:-1:-1", "scope": 3922, "sourceUnit": 18353, "symbolAliases": [{"foreign": {"id": 2962, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "73:6:3", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 3921, "nodeType": "ContractDefinition", "src": "1899:12332:3", "nodes": [{"id": 2981, "nodeType": "VariableDeclaration", "src": "1933:92:3", "nodes": [], "constant": true, "mutability": "constant", "name": "vm", "nameLocation": "1957:2:3", "scope": 3921, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}, "typeName": {"id": 2966, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 2965, "name": "VmSafe", "nameLocations": ["1933:6:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 17281, "src": "1933:6:3"}, "referencedDeclaration": 17281, "src": "1933:6:3", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "value": {"arguments": [{"arguments": [{"arguments": [{"arguments": [{"arguments": [{"hexValue": "6865766d20636865617420636f6465", "id": 2975, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2003:17:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_885cb69240a935d632d79c317109709ecfa91a80626ff3989d68f67f5b1dd12d", "typeString": "literal_string \"hevm cheat code\""}, "value": "hevm cheat code"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_885cb69240a935d632d79c317109709ecfa91a80626ff3989d68f67f5b1dd12d", "typeString": "literal_string \"hevm cheat code\""}], "id": 2974, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "1993:9:3", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 2976, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1993:28:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 2973, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1985:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 2972, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1985:7:3", "typeDescriptions": {}}}, "id": 2977, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1985:37:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 2971, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1977:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 2970, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "1977:7:3", "typeDescriptions": {}}}, "id": 2978, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1977:46:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 2969, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1969:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 2968, "name": "address", "nodeType": "ElementaryTypeName", "src": "1969:7:3", "typeDescriptions": {}}}, "id": 2979, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1969:55:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 2967, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "1962:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_VmSafe_$17281_$", "typeString": "type(contract VmSafe)"}}, "id": 2980, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1962:63:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "visibility": "private"}, {"id": 2983, "nodeType": "VariableDeclaration", "src": "2032:33:3", "nodes": [], "constant": false, "mutability": "mutable", "name": "stdChainsInitialized", "nameLocation": "2045:20:3", "scope": 3921, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 2982, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2032:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "private"}, {"id": 2990, "nodeType": "StructDefinition", "src": "2072:93:3", "nodes": [], "canonicalName": "StdChains.ChainData", "members": [{"constant": false, "id": 2985, "mutability": "mutable", "name": "name", "nameLocation": "2106:4:3", "nodeType": "VariableDeclaration", "scope": 2990, "src": "2099:11:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 2984, "name": "string", "nodeType": "ElementaryTypeName", "src": "2099:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 2987, "mutability": "mutable", "name": "chainId", "nameLocation": "2128:7:3", "nodeType": "VariableDeclaration", "scope": 2990, "src": "2120:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2986, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2120:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 2989, "mutability": "mutable", "name": "rpcUrl", "nameLocation": "2152:6:3", "nodeType": "VariableDeclaration", "scope": 2990, "src": "2145:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 2988, "name": "string", "nodeType": "ElementaryTypeName", "src": "2145:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "name": "ChainData", "nameLocation": "2079:9:3", "scope": 3921, "visibility": "public"}, {"id": 2999, "nodeType": "StructDefinition", "src": "2171:598:3", "nodes": [], "canonicalName": "StdChains.Chain", "members": [{"constant": false, "id": 2992, "mutability": "mutable", "name": "name", "nameLocation": "2228:4:3", "nodeType": "VariableDeclaration", "scope": 2999, "src": "2221:11:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 2991, "name": "string", "nodeType": "ElementaryTypeName", "src": "2221:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 2994, "mutability": "mutable", "name": "chainId", "nameLocation": "2283:7:3", "nodeType": "VariableDeclaration", "scope": 2999, "src": "2275:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2993, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2275:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 2996, "mutability": "mutable", "name": "chainAlias", "nameLocation": "2383:10:3", "nodeType": "VariableDeclaration", "scope": 2999, "src": "2376:17:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 2995, "name": "string", "nodeType": "ElementaryTypeName", "src": "2376:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 2998, "mutability": "mutable", "name": "rpcUrl", "nameLocation": "2756:6:3", "nodeType": "VariableDeclaration", "scope": 2999, "src": "2749:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 2997, "name": "string", "nodeType": "ElementaryTypeName", "src": "2749:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "name": "Chain", "nameLocation": "2178:5:3", "scope": 3921, "visibility": "public"}, {"id": 3004, "nodeType": "VariableDeclaration", "src": "2873:39:3", "nodes": [], "constant": false, "mutability": "mutable", "name": "chains", "nameLocation": "2906:6:3", "scope": 3921, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_string_memory_ptr_$_t_struct$_Chain_$2999_storage_$", "typeString": "mapping(string => struct StdChains.Chain)"}, "typeName": {"id": 3003, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 3000, "name": "string", "nodeType": "ElementaryTypeName", "src": "2881:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "nodeType": "Mapping", "src": "2873:24:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_string_memory_ptr_$_t_struct$_Chain_$2999_storage_$", "typeString": "mapping(string => struct StdChains.Chain)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 3002, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 3001, "name": "Chain", "nameLocations": ["2891:5:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 2999, "src": "2891:5:3"}, "referencedDeclaration": 2999, "src": "2891:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage_ptr", "typeString": "struct StdChains.Chain"}}}, "visibility": "private"}, {"id": 3008, "nodeType": "VariableDeclaration", "src": "2978:48:3", "nodes": [], "constant": false, "mutability": "mutable", "name": "defaultRpcUrls", "nameLocation": "3012:14:3", "scope": 3921, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_string_memory_ptr_$_t_string_storage_$", "typeString": "mapping(string => string)"}, "typeName": {"id": 3007, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 3005, "name": "string", "nodeType": "ElementaryTypeName", "src": "2986:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "nodeType": "Mapping", "src": "2978:25:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_string_memory_ptr_$_t_string_storage_$", "typeString": "mapping(string => string)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 3006, "name": "string", "nodeType": "ElementaryTypeName", "src": "2996:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}}, "visibility": "private"}, {"id": 3012, "nodeType": "VariableDeclaration", "src": "3075:44:3", "nodes": [], "constant": false, "mutability": "mutable", "name": "idToAlias", "nameLocation": "3110:9:3", "scope": 3921, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_string_storage_$", "typeString": "mapping(uint256 => string)"}, "typeName": {"id": 3011, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 3009, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3083:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "3075:26:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_string_storage_$", "typeString": "mapping(uint256 => string)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 3010, "name": "string", "nodeType": "ElementaryTypeName", "src": "3094:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}}, "visibility": "private"}, {"id": 3015, "nodeType": "VariableDeclaration", "src": "3126:44:3", "nodes": [], "constant": false, "mutability": "mutable", "name": "fallbackToDefaultRpcUrls", "nameLocation": "3139:24:3", "scope": 3921, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 3013, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3126:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": {"hexValue": "74727565", "id": 3014, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3166:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "visibility": "private"}, {"id": 3067, "nodeType": "FunctionDefinition", "src": "3255:524:3", "nodes": [], "body": {"id": 3066, "nodeType": "Block", "src": "3345:434:3", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3030, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"arguments": [{"id": 3026, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3017, "src": "3369:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3025, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3363:5:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 3024, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "3363:5:3", "typeDescriptions": {}}}, "id": 3027, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3363:17:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 3028, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3381:6:3", "memberName": "length", "nodeType": "MemberAccess", "src": "3363:24:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 3029, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3391:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3363:29:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "537464436861696e7320676574436861696e28737472696e67293a20436861696e20616c6961732063616e6e6f742062652074686520656d70747920737472696e672e", "id": 3031, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3394:69:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_3d920aad82cc068f1a73b0fb2c703d0169baa46c8c67097012e1aca0cc8c8b70", "typeString": "literal_string \"<PERSON><PERSON><PERSON><PERSON><PERSON> get<PERSON>hain(string): Chain alias cannot be the empty string.\""}, "value": "<PERSON>d<PERSON><PERSON><PERSON> get<PERSON><PERSON><PERSON>(string): Chain alias cannot be the empty string."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_3d920aad82cc068f1a73b0fb2c703d0169baa46c8c67097012e1aca0cc8c8b70", "typeString": "literal_string \"<PERSON><PERSON><PERSON><PERSON><PERSON> get<PERSON>hain(string): Chain alias cannot be the empty string.\""}], "id": 3023, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "3355:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 3032, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3355:109:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3033, "nodeType": "ExpressionStatement", "src": "3355:109:3"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 3034, "name": "initializeStdChains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3883, "src": "3475:19:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 3035, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3475:21:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3036, "nodeType": "ExpressionStatement", "src": "3475:21:3"}, {"expression": {"id": 3041, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 3037, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3021, "src": "3506:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"baseExpression": {"id": 3038, "name": "chains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3004, "src": "3514:6:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_string_memory_ptr_$_t_struct$_Chain_$2999_storage_$", "typeString": "mapping(string memory => struct StdChains.Chain storage ref)"}}, "id": 3040, "indexExpression": {"id": 3039, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3017, "src": "3521:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3514:18:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage", "typeString": "struct StdChains.Chain storage ref"}}, "src": "3506:26:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3042, "nodeType": "ExpressionStatement", "src": "3506:26:3"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3047, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 3044, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3021, "src": "3563:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3045, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3569:7:3", "memberName": "chainId", "nodeType": "MemberAccess", "referencedDeclaration": 2994, "src": "3563:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 3046, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3580:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3563:18:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"arguments": [{"arguments": [{"hexValue": "537464436861696e7320676574436861696e28737472696e67293a20436861696e207769746820616c6961732022", "id": 3052, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3619:49:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_be183459e9329da9bfc4a2fec17224f102b8a68c1139772e954a2d6fd9877e00", "typeString": "literal_string \"<PERSON><PERSON><PERSON><PERSON><PERSON> getChain(string): Chain with alias \"\""}, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> get<PERSON><PERSON><PERSON>(string): Chain with alias \""}, {"id": 3053, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3017, "src": "3670:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "22206e6f7420666f756e642e", "id": 3054, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3682:15:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_be956cec6682d51b49f30c9beff2857436402411b7eee4082594e44819bcd397", "typeString": "literal_string \"\" not found.\""}, "value": "\" not found."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_be183459e9329da9bfc4a2fec17224f102b8a68c1139772e954a2d6fd9877e00", "typeString": "literal_string \"<PERSON><PERSON><PERSON><PERSON><PERSON> getChain(string): Chain with alias \"\""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_stringliteral_be956cec6682d51b49f30c9beff2857436402411b7eee4082594e44819bcd397", "typeString": "literal_string \"\" not found.\""}], "expression": {"id": 3050, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "3602:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 3051, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3606:12:3", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "3602:16:3", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 3055, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3602:96:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3049, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3595:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 3048, "name": "string", "nodeType": "ElementaryTypeName", "src": "3595:6:3", "typeDescriptions": {}}}, "id": 3056, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3595:104:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3043, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "3542:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 3057, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3542:167:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3058, "nodeType": "ExpressionStatement", "src": "3542:167:3"}, {"expression": {"id": 3064, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 3059, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3021, "src": "3720:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 3061, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3017, "src": "3754:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 3062, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3021, "src": "3766:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}], "id": 3060, "name": "getChainWithUpdatedRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3475, "src": "3728:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_struct$_Chain_$2999_memory_ptr_$returns$_t_struct$_Chain_$2999_memory_ptr_$", "typeString": "function (string memory,struct StdChains.Chain memory) view returns (struct StdChains.Chain memory)"}}, "id": 3063, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3728:44:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "src": "3720:52:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3065, "nodeType": "ExpressionStatement", "src": "3720:52:3"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "3264:8:3", "parameters": {"id": 3018, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3017, "mutability": "mutable", "name": "chainAlias", "nameLocation": "3287:10:3", "nodeType": "VariableDeclaration", "scope": 3067, "src": "3273:24:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3016, "name": "string", "nodeType": "ElementaryTypeName", "src": "3273:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3272:26:3"}, "returnParameters": {"id": 3022, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3021, "mutability": "mutable", "name": "chain", "nameLocation": "3338:5:3", "nodeType": "VariableDeclaration", "scope": 3067, "src": "3325:18:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain"}, "typeName": {"id": 3020, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 3019, "name": "Chain", "nameLocations": ["3325:5:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 2999, "src": "3325:5:3"}, "referencedDeclaration": 2999, "src": "3325:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage_ptr", "typeString": "struct StdChains.Chain"}}, "visibility": "internal"}], "src": "3324:20:3"}, "scope": 3921, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 3124, "nodeType": "FunctionDefinition", "src": "3785:541:3", "nodes": [], "body": {"id": 3123, "nodeType": "Block", "src": "3866:460:3", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3078, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 3076, "name": "chainId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3069, "src": "3884:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 3077, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3895:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3884:12:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "537464436861696e7320676574436861696e2875696e74323536293a20436861696e2049442063616e6e6f7420626520302e", "id": 3079, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3898:52:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_64f1cd082b277ed92a70b6890cc1e3b6ebd77bc6c9299e7ce82305de04926a4a", "typeString": "literal_string \"<PERSON>d<PERSON><PERSON><PERSON> getChain(uint256): Chain ID cannot be 0.\""}, "value": "Std<PERSON><PERSON><PERSON> getChain(uint256): Chain ID cannot be 0."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_64f1cd082b277ed92a70b6890cc1e3b6ebd77bc6c9299e7ce82305de04926a4a", "typeString": "literal_string \"<PERSON>d<PERSON><PERSON><PERSON> getChain(uint256): Chain ID cannot be 0.\""}], "id": 3075, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "3876:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 3080, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3876:75:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3081, "nodeType": "ExpressionStatement", "src": "3876:75:3"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 3082, "name": "initializeStdChains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3883, "src": "3961:19:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 3083, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3961:21:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3084, "nodeType": "ExpressionStatement", "src": "3961:21:3"}, {"assignments": [3086], "declarations": [{"constant": false, "id": 3086, "mutability": "mutable", "name": "chainAlias", "nameLocation": "4006:10:3", "nodeType": "VariableDeclaration", "scope": 3123, "src": "3992:24:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3085, "name": "string", "nodeType": "ElementaryTypeName", "src": "3992:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "id": 3090, "initialValue": {"baseExpression": {"id": 3087, "name": "idToAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3012, "src": "4019:9:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_string_storage_$", "typeString": "mapping(uint256 => string storage ref)"}}, "id": 3089, "indexExpression": {"id": 3088, "name": "chainId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3069, "src": "4029:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4019:18:3", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "3992:45:3"}, {"expression": {"id": 3095, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 3091, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3073, "src": "4048:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"baseExpression": {"id": 3092, "name": "chains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3004, "src": "4056:6:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_string_memory_ptr_$_t_struct$_Chain_$2999_storage_$", "typeString": "mapping(string memory => struct StdChains.Chain storage ref)"}}, "id": 3094, "indexExpression": {"id": 3093, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3086, "src": "4063:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4056:18:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage", "typeString": "struct StdChains.Chain storage ref"}}, "src": "4048:26:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3096, "nodeType": "ExpressionStatement", "src": "4048:26:3"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3101, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 3098, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3073, "src": "4106:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3099, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4112:7:3", "memberName": "chainId", "nodeType": "MemberAccess", "referencedDeclaration": 2994, "src": "4106:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 3100, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4123:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4106:18:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"arguments": [{"arguments": [{"hexValue": "537464436861696e7320676574436861696e2875696e74323536293a20436861696e207769746820494420", "id": 3106, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4162:45:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ce7b2cad45f1a6d0b9b7bb125e9a8742fce8fed7d742c83265d4a2da4caf457d", "typeString": "literal_string \"<PERSON><PERSON><PERSON><PERSON><PERSON> getChain(uint256): Chain with ID \""}, "value": "<PERSON>d<PERSON><PERSON><PERSON> get<PERSON>hain(uint256): Chain with ID "}, {"arguments": [{"id": 3109, "name": "chainId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3069, "src": "4221:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 3107, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2981, "src": "4209:2:3", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 3108, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4212:8:3", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "4209:11:3", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 3110, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4209:20:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "206e6f7420666f756e642e", "id": 3111, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4231:13:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f310d2efb88747fac959fa7567a0a1a161dd43a77ba9af074f6191cf5bcf4f8b", "typeString": "literal_string \" not found.\""}, "value": " not found."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_ce7b2cad45f1a6d0b9b7bb125e9a8742fce8fed7d742c83265d4a2da4caf457d", "typeString": "literal_string \"<PERSON><PERSON><PERSON><PERSON><PERSON> getChain(uint256): Chain with ID \""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_stringliteral_f310d2efb88747fac959fa7567a0a1a161dd43a77ba9af074f6191cf5bcf4f8b", "typeString": "literal_string \" not found.\""}], "expression": {"id": 3104, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "4145:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 3105, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4149:12:3", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "4145:16:3", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 3112, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4145:100:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3103, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4138:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 3102, "name": "string", "nodeType": "ElementaryTypeName", "src": "4138:6:3", "typeDescriptions": {}}}, "id": 3113, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4138:108:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3097, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "4085:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 3114, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4085:171:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3115, "nodeType": "ExpressionStatement", "src": "4085:171:3"}, {"expression": {"id": 3121, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 3116, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3073, "src": "4267:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 3118, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3086, "src": "4301:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 3119, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3073, "src": "4313:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}], "id": 3117, "name": "getChainWithUpdatedRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3475, "src": "4275:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_struct$_Chain_$2999_memory_ptr_$returns$_t_struct$_Chain_$2999_memory_ptr_$", "typeString": "function (string memory,struct StdChains.Chain memory) view returns (struct StdChains.Chain memory)"}}, "id": 3120, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4275:44:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "src": "4267:52:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3122, "nodeType": "ExpressionStatement", "src": "4267:52:3"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "3794:8:3", "parameters": {"id": 3070, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3069, "mutability": "mutable", "name": "chainId", "nameLocation": "3811:7:3", "nodeType": "VariableDeclaration", "scope": 3124, "src": "3803:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3068, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3803:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3802:17:3"}, "returnParameters": {"id": 3074, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3073, "mutability": "mutable", "name": "chain", "nameLocation": "3859:5:3", "nodeType": "VariableDeclaration", "scope": 3124, "src": "3846:18:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain"}, "typeName": {"id": 3072, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 3071, "name": "Chain", "nameLocations": ["3846:5:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 2999, "src": "3846:5:3"}, "referencedDeclaration": 2999, "src": "3846:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage_ptr", "typeString": "struct StdChains.Chain"}}, "visibility": "internal"}], "src": "3845:20:3"}, "scope": 3921, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 3234, "nodeType": "FunctionDefinition", "src": "4397:1173:3", "nodes": [], "body": {"id": 3233, "nodeType": "Block", "src": "4482:1088:3", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3139, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"arguments": [{"id": 3135, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3126, "src": "4519:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3134, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4513:5:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 3133, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4513:5:3", "typeDescriptions": {}}}, "id": 3136, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4513:17:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 3137, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4531:6:3", "memberName": "length", "nodeType": "MemberAccess", "src": "4513:24:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 3138, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4541:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4513:29:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "537464436861696e7320736574436861696e28737472696e672c436861696e44617461293a20436861696e20616c6961732063616e6e6f742062652074686520656d70747920737472696e672e", "id": 3140, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4556:79:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_30b2334ec57cbeeece39c6405e10d3437560135ecd84835d6b9144db1d575354", "typeString": "literal_string \"<PERSON><PERSON><PERSON><PERSON><PERSON> set<PERSON>hain(string,ChainData): Chain alias cannot be the empty string.\""}, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> set<PERSON><PERSON><PERSON>(string,ChainData): Chain alias cannot be the empty string."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_30b2334ec57cbeeece39c6405e10d3437560135ecd84835d6b9144db1d575354", "typeString": "literal_string \"<PERSON><PERSON><PERSON><PERSON><PERSON> set<PERSON>hain(string,ChainData): Chain alias cannot be the empty string.\""}], "id": 3132, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "4492:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 3141, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4492:153:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3142, "nodeType": "ExpressionStatement", "src": "4492:153:3"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3147, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 3144, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3129, "src": "4664:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}, "id": 3145, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4670:7:3", "memberName": "chainId", "nodeType": "MemberAccess", "referencedDeclaration": 2987, "src": "4664:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 3146, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4681:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4664:18:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "537464436861696e7320736574436861696e28737472696e672c436861696e44617461293a20436861696e2049442063616e6e6f7420626520302e", "id": 3148, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4684:61:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ab0ba8dace83d80dc1941286e8d0551223497db1b420e58abff2f3db2ad3fbf4", "typeString": "literal_string \"<PERSON>d<PERSON><PERSON><PERSON> setChain(string,ChainData): Chain ID cannot be 0.\""}, "value": "<PERSON>d<PERSON><PERSON><PERSON> setChain(string,ChainData): Chain ID cannot be 0."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_ab0ba8dace83d80dc1941286e8d0551223497db1b420e58abff2f3db2ad3fbf4", "typeString": "literal_string \"<PERSON>d<PERSON><PERSON><PERSON> setChain(string,ChainData): Chain ID cannot be 0.\""}], "id": 3143, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "4656:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 3149, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4656:90:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3150, "nodeType": "ExpressionStatement", "src": "4656:90:3"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 3151, "name": "initializeStdChains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3883, "src": "4757:19:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 3152, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4757:21:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3153, "nodeType": "ExpressionStatement", "src": "4757:21:3"}, {"assignments": [3155], "declarations": [{"constant": false, "id": 3155, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "4802:10:3", "nodeType": "VariableDeclaration", "scope": 3233, "src": "4788:24:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3154, "name": "string", "nodeType": "ElementaryTypeName", "src": "4788:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "id": 3160, "initialValue": {"baseExpression": {"id": 3156, "name": "idToAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3012, "src": "4815:9:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_string_storage_$", "typeString": "mapping(uint256 => string storage ref)"}}, "id": 3159, "indexExpression": {"expression": {"id": 3157, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3129, "src": "4825:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}, "id": 3158, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4831:7:3", "memberName": "chainId", "nodeType": "MemberAccess", "referencedDeclaration": 2987, "src": "4825:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4815:24:3", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "4788:51:3"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 3182, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3168, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"arguments": [{"id": 3164, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3155, "src": "4877:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3163, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4871:5:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 3162, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4871:5:3", "typeDescriptions": {}}}, "id": 3165, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4871:17:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 3166, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4889:6:3", "memberName": "length", "nodeType": "MemberAccess", "src": "4871:24:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 3167, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4899:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4871:29:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "||", "rightExpression": {"commonType": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "id": 3181, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [{"arguments": [{"id": 3172, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3155, "src": "4920:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3171, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4914:5:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 3170, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4914:5:3", "typeDescriptions": {}}}, "id": 3173, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4914:17:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3169, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "4904:9:3", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 3174, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4904:28:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"arguments": [{"id": 3178, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3126, "src": "4952:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3177, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4946:5:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 3176, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4946:5:3", "typeDescriptions": {}}}, "id": 3179, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4946:17:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3175, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "4936:9:3", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 3180, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4936:28:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "4904:60:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4871:93:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"arguments": [{"arguments": [{"hexValue": "537464436861696e7320736574436861696e28737472696e672c436861696e44617461293a20436861696e20494420", "id": 3187, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5040:49:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2f5ddfff35cec202bbf760c515d7332e259c9b0c330efa0b2d03073b34906ba0", "typeString": "literal_string \"<PERSON>d<PERSON><PERSON><PERSON> setChain(string,ChainData): Chain ID \""}, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(string,ChainData): Chain ID "}, {"arguments": [{"expression": {"id": 3190, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3129, "src": "5123:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}, "id": 3191, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5129:7:3", "memberName": "chainId", "nodeType": "MemberAccess", "referencedDeclaration": 2987, "src": "5123:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 3188, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2981, "src": "5111:2:3", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 3189, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5114:8:3", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "5111:11:3", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 3192, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5111:26:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "20616c726561647920757365642062792022", "id": 3193, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5159:21:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_03dcc98944d744f10105f4b63a1d5b4f5b14493812e66201e5f21a3da2662077", "typeString": "literal_string \" already used by \"\""}, "value": " already used by \""}, {"id": 3194, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3155, "src": "5202:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "222e", "id": 3195, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5234:5:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_cb54fc3dbdac1cb7b87378fdaddeb9e7549db2a108b5270efaa4bcd576270193", "typeString": "literal_string \"\".\""}, "value": "\"."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_2f5ddfff35cec202bbf760c515d7332e259c9b0c330efa0b2d03073b34906ba0", "typeString": "literal_string \"<PERSON>d<PERSON><PERSON><PERSON> setChain(string,ChainData): Chain ID \""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_stringliteral_03dcc98944d744f10105f4b63a1d5b4f5b14493812e66201e5f21a3da2662077", "typeString": "literal_string \" already used by \"\""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_stringliteral_cb54fc3dbdac1cb7b87378fdaddeb9e7549db2a108b5270efaa4bcd576270193", "typeString": "literal_string \"\".\""}], "expression": {"id": 3185, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "5002:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 3186, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5006:12:3", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "5002:16:3", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 3196, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5002:255:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3184, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4978:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 3183, "name": "string", "nodeType": "ElementaryTypeName", "src": "4978:6:3", "typeDescriptions": {}}}, "id": 3197, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4978:293:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3161, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "4850:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 3198, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4850:431:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3199, "nodeType": "ExpressionStatement", "src": "4850:431:3"}, {"assignments": [3201], "declarations": [{"constant": false, "id": 3201, "mutability": "mutable", "name": "oldChainId", "nameLocation": "5300:10:3", "nodeType": "VariableDeclaration", "scope": 3233, "src": "5292:18:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3200, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5292:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 3206, "initialValue": {"expression": {"baseExpression": {"id": 3202, "name": "chains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3004, "src": "5313:6:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_string_memory_ptr_$_t_struct$_Chain_$2999_storage_$", "typeString": "mapping(string memory => struct StdChains.Chain storage ref)"}}, "id": 3204, "indexExpression": {"id": 3203, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3126, "src": "5320:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5313:18:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage", "typeString": "struct StdChains.Chain storage ref"}}, "id": 3205, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5332:7:3", "memberName": "chainId", "nodeType": "MemberAccess", "referencedDeclaration": 2994, "src": "5313:26:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "5292:47:3"}, {"expression": {"id": 3210, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "delete", "prefix": true, "src": "5349:28:3", "subExpression": {"baseExpression": {"id": 3207, "name": "idToAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3012, "src": "5356:9:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_string_storage_$", "typeString": "mapping(uint256 => string storage ref)"}}, "id": 3209, "indexExpression": {"id": 3208, "name": "oldChainId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3201, "src": "5366:10:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5356:21:3", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3211, "nodeType": "ExpressionStatement", "src": "5349:28:3"}, {"expression": {"id": 3224, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 3212, "name": "chains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3004, "src": "5388:6:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_string_memory_ptr_$_t_struct$_Chain_$2999_storage_$", "typeString": "mapping(string memory => struct StdChains.Chain storage ref)"}}, "id": 3214, "indexExpression": {"id": 3213, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3126, "src": "5395:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5388:18:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage", "typeString": "struct StdChains.Chain storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"expression": {"id": 3216, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3129, "src": "5434:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}, "id": 3217, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5440:4:3", "memberName": "name", "nodeType": "MemberAccess", "referencedDeclaration": 2985, "src": "5434:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"expression": {"id": 3218, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3129, "src": "5455:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}, "id": 3219, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5461:7:3", "memberName": "chainId", "nodeType": "MemberAccess", "referencedDeclaration": 2987, "src": "5455:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 3220, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3126, "src": "5482:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"expression": {"id": 3221, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3129, "src": "5502:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}, "id": 3222, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5508:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 2989, "src": "5502:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3215, "name": "Chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2999, "src": "5421:5:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Chain_$2999_storage_ptr_$", "typeString": "type(struct StdChains.Chain storage pointer)"}}, "id": 3223, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["5428:4:3", "5446:7:3", "5470:10:3", "5494:6:3"], "names": ["name", "chainId", "chainAlias", "rpcUrl"], "nodeType": "FunctionCall", "src": "5421:95:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "src": "5388:128:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage", "typeString": "struct StdChains.Chain storage ref"}}, "id": 3225, "nodeType": "ExpressionStatement", "src": "5388:128:3"}, {"expression": {"id": 3231, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 3226, "name": "idToAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3012, "src": "5526:9:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_string_storage_$", "typeString": "mapping(uint256 => string storage ref)"}}, "id": 3229, "indexExpression": {"expression": {"id": 3227, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3129, "src": "5536:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}, "id": 3228, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5542:7:3", "memberName": "chainId", "nodeType": "MemberAccess", "referencedDeclaration": 2987, "src": "5536:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5526:24:3", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 3230, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3126, "src": "5553:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "5526:37:3", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 3232, "nodeType": "ExpressionStatement", "src": "5526:37:3"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "4406:8:3", "parameters": {"id": 3130, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3126, "mutability": "mutable", "name": "chainAlias", "nameLocation": "4429:10:3", "nodeType": "VariableDeclaration", "scope": 3234, "src": "4415:24:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3125, "name": "string", "nodeType": "ElementaryTypeName", "src": "4415:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 3129, "mutability": "mutable", "name": "chain", "nameLocation": "4458:5:3", "nodeType": "VariableDeclaration", "scope": 3234, "src": "4441:22:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData"}, "typeName": {"id": 3128, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 3127, "name": "ChainData", "nameLocations": ["4441:9:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 2990, "src": "4441:9:3"}, "referencedDeclaration": 2990, "src": "4441:9:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_storage_ptr", "typeString": "struct StdChains.ChainData"}}, "visibility": "internal"}], "src": "4414:50:3"}, "returnParameters": {"id": 3131, "nodeType": "ParameterList", "parameters": [], "src": "4482:0:3"}, "scope": 3921, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 3255, "nodeType": "FunctionDefinition", "src": "5641:195:3", "nodes": [], "body": {"id": 3254, "nodeType": "Block", "src": "5722:114:3", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 3243, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3236, "src": "5741:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"arguments": [{"expression": {"id": 3245, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3239, "src": "5770:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3246, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5776:4:3", "memberName": "name", "nodeType": "MemberAccess", "referencedDeclaration": 2992, "src": "5770:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"expression": {"id": 3247, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3239, "src": "5791:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3248, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5797:7:3", "memberName": "chainId", "nodeType": "MemberAccess", "referencedDeclaration": 2994, "src": "5791:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 3249, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3239, "src": "5814:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3250, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5820:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 2998, "src": "5814:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3244, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "5753:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3251, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["5764:4:3", "5782:7:3", "5806:6:3"], "names": ["name", "chainId", "rpcUrl"], "nodeType": "FunctionCall", "src": "5753:75:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3242, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [3234, 3255], "referencedDeclaration": 3234, "src": "5732:8:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3252, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5732:97:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3253, "nodeType": "ExpressionStatement", "src": "5732:97:3"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "5650:8:3", "parameters": {"id": 3240, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3236, "mutability": "mutable", "name": "chainAlias", "nameLocation": "5673:10:3", "nodeType": "VariableDeclaration", "scope": 3255, "src": "5659:24:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3235, "name": "string", "nodeType": "ElementaryTypeName", "src": "5659:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 3239, "mutability": "mutable", "name": "chain", "nameLocation": "5698:5:3", "nodeType": "VariableDeclaration", "scope": 3255, "src": "5685:18:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain"}, "typeName": {"id": 3238, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 3237, "name": "Chain", "nameLocations": ["5685:5:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 2999, "src": "5685:5:3"}, "referencedDeclaration": 2999, "src": "5685:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage_ptr", "typeString": "struct StdChains.Chain"}}, "visibility": "internal"}], "src": "5658:46:3"}, "returnParameters": {"id": 3241, "nodeType": "ParameterList", "parameters": [], "src": "5722:0:3"}, "scope": 3921, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 3332, "nodeType": "FunctionDefinition", "src": "5842:451:3", "nodes": [], "body": {"id": 3331, "nodeType": "Block", "src": "5916:377:3", "nodes": [], "statements": [{"assignments": [3263], "declarations": [{"constant": false, "id": 3263, "mutability": "mutable", "name": "strb", "nameLocation": "5939:4:3", "nodeType": "VariableDeclaration", "scope": 3331, "src": "5926:17:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 3262, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "5926:5:3", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 3268, "initialValue": {"arguments": [{"id": 3266, "name": "str", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3257, "src": "5952:3:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3265, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5946:5:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 3264, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "5946:5:3", "typeDescriptions": {}}}, "id": 3267, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5946:10:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "5926:30:3"}, {"assignments": [3270], "declarations": [{"constant": false, "id": 3270, "mutability": "mutable", "name": "copy", "nameLocation": "5979:4:3", "nodeType": "VariableDeclaration", "scope": 3331, "src": "5966:17:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 3269, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "5966:5:3", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 3276, "initialValue": {"arguments": [{"expression": {"id": 3273, "name": "strb", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3263, "src": "5996:4:3", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 3274, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6001:6:3", "memberName": "length", "nodeType": "MemberAccess", "src": "5996:11:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 3272, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "5986:9:3", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_bytes_memory_ptr_$", "typeString": "function (uint256) pure returns (bytes memory)"}, "typeName": {"id": 3271, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "5990:5:3", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}}, "id": 3275, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5986:22:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "5966:42:3"}, {"body": {"id": 3324, "nodeType": "Block", "src": "6060:198:3", "statements": [{"assignments": [3289], "declarations": [{"constant": false, "id": 3289, "mutability": "mutable", "name": "b", "nameLocation": "6081:1:3", "nodeType": "VariableDeclaration", "scope": 3324, "src": "6074:8:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}, "typeName": {"id": 3288, "name": "bytes1", "nodeType": "ElementaryTypeName", "src": "6074:6:3", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "visibility": "internal"}], "id": 3293, "initialValue": {"baseExpression": {"id": 3290, "name": "strb", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3263, "src": "6085:4:3", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 3292, "indexExpression": {"id": 3291, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3278, "src": "6090:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6085:7:3", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "nodeType": "VariableDeclarationStatement", "src": "6074:18:3"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 3300, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}, "id": 3296, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 3294, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3289, "src": "6110:1:3", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"hexValue": "30783631", "id": 3295, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6115:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_97_by_1", "typeString": "int_const 97"}, "value": "0x61"}, "src": "6110:9:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}, "id": 3299, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 3297, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3289, "src": "6123:1:3", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "30783741", "id": 3298, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6128:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_122_by_1", "typeString": "int_const 122"}, "value": "0x7A"}, "src": "6123:9:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "6110:22:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 3322, "nodeType": "Block", "src": "6204:44:3", "statements": [{"expression": {"id": 3320, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 3316, "name": "copy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3270, "src": "6222:4:3", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 3318, "indexExpression": {"id": 3317, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3278, "src": "6227:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "6222:7:3", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 3319, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3289, "src": "6232:1:3", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "src": "6222:11:3", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "id": 3321, "nodeType": "ExpressionStatement", "src": "6222:11:3"}]}, "id": 3323, "nodeType": "IfStatement", "src": "6106:142:3", "trueBody": {"id": 3315, "nodeType": "Block", "src": "6134:64:3", "statements": [{"expression": {"id": 3313, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 3301, "name": "copy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3270, "src": "6152:4:3", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 3303, "indexExpression": {"id": 3302, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3278, "src": "6157:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "6152:7:3", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 3311, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [{"id": 3308, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3289, "src": "6175:1:3", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes1", "typeString": "bytes1"}], "id": 3307, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6169:5:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint8_$", "typeString": "type(uint8)"}, "typeName": {"id": 3306, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "6169:5:3", "typeDescriptions": {}}}, "id": 3309, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6169:8:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "3332", "id": 3310, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6180:2:3", "typeDescriptions": {"typeIdentifier": "t_rational_32_by_1", "typeString": "int_const 32"}, "value": "32"}, "src": "6169:13:3", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint8", "typeString": "uint8"}], "id": 3305, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6162:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes1_$", "typeString": "type(bytes1)"}, "typeName": {"id": 3304, "name": "bytes1", "nodeType": "ElementaryTypeName", "src": "6162:6:3", "typeDescriptions": {}}}, "id": 3312, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6162:21:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "src": "6152:31:3", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "id": 3314, "nodeType": "ExpressionStatement", "src": "6152:31:3"}]}}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3284, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 3281, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3278, "src": "6038:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 3282, "name": "strb", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3263, "src": "6042:4:3", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 3283, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6047:6:3", "memberName": "length", "nodeType": "MemberAccess", "src": "6042:11:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6038:15:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 3325, "initializationExpression": {"assignments": [3278], "declarations": [{"constant": false, "id": 3278, "mutability": "mutable", "name": "i", "nameLocation": "6031:1:3", "nodeType": "VariableDeclaration", "scope": 3325, "src": "6023:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3277, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6023:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 3280, "initialValue": {"hexValue": "30", "id": 3279, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6035:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "6023:13:3"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 3286, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "6055:3:3", "subExpression": {"id": 3285, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3278, "src": "6055:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3287, "nodeType": "ExpressionStatement", "src": "6055:3:3"}, "nodeType": "ForStatement", "src": "6018:240:3"}, {"expression": {"arguments": [{"id": 3328, "name": "copy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3270, "src": "6281:4:3", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3327, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6274:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 3326, "name": "string", "nodeType": "ElementaryTypeName", "src": "6274:6:3", "typeDescriptions": {}}}, "id": 3329, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6274:12:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 3261, "id": 3330, "nodeType": "Return", "src": "6267:19:3"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_toUpper", "nameLocation": "5851:8:3", "parameters": {"id": 3258, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3257, "mutability": "mutable", "name": "str", "nameLocation": "5874:3:3", "nodeType": "VariableDeclaration", "scope": 3332, "src": "5860:17:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3256, "name": "string", "nodeType": "ElementaryTypeName", "src": "5860:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5859:19:3"}, "returnParameters": {"id": 3261, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3260, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 3332, "src": "5901:13:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3259, "name": "string", "nodeType": "ElementaryTypeName", "src": "5901:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5900:15:3"}, "scope": 3921, "stateMutability": "pure", "virtual": false, "visibility": "private"}, {"id": 3475, "nodeType": "FunctionDefinition", "src": "6429:1725:3", "nodes": [], "body": {"id": 3474, "nodeType": "Block", "src": "6574:1580:3", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3350, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"arguments": [{"expression": {"id": 3345, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3337, "src": "6594:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3346, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6600:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 2998, "src": "6594:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3344, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6588:5:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 3343, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6588:5:3", "typeDescriptions": {}}}, "id": 3347, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6588:19:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 3348, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6608:6:3", "memberName": "length", "nodeType": "MemberAccess", "src": "6588:26:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 3349, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6618:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "6588:31:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 3471, "nodeType": "IfStatement", "src": "6584:1542:3", "trueBody": {"id": 3470, "nodeType": "Block", "src": "6621:1505:3", "statements": [{"clauses": [{"block": {"id": 3364, "nodeType": "Block", "src": "6698:60:3", "statements": [{"expression": {"id": 3362, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 3358, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3337, "src": "6716:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3360, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "6722:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 2998, "src": "6716:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 3361, "name": "configRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3356, "src": "6731:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "6716:27:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 3363, "nodeType": "ExpressionStatement", "src": "6716:27:3"}]}, "errorName": "", "id": 3365, "nodeType": "TryCatchClause", "parameters": {"id": 3357, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3356, "mutability": "mutable", "name": "configRpcUrl", "nameLocation": "6684:12:3", "nodeType": "VariableDeclaration", "scope": 3365, "src": "6670:26:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3355, "name": "string", "nodeType": "ElementaryTypeName", "src": "6670:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6669:28:3"}, "src": "6661:97:3"}, {"block": {"id": 3467, "nodeType": "Block", "src": "6784:1332:3", "statements": [{"assignments": [3370], "declarations": [{"constant": false, "id": 3370, "mutability": "mutable", "name": "envName", "nameLocation": "6816:7:3", "nodeType": "VariableDeclaration", "scope": 3467, "src": "6802:21:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3369, "name": "string", "nodeType": "ElementaryTypeName", "src": "6802:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "id": 3381, "initialValue": {"arguments": [{"arguments": [{"arguments": [{"id": 3376, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3334, "src": "6859:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3375, "name": "_toUpper", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3332, "src": "6850:8:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 3377, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6850:20:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "5f5250435f55524c", "id": 3378, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6872:10:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2186fe596dea1a615b7a1cb43899fd18c5b434aa29c8de36d4b8fcc67e3d6ad9", "typeString": "literal_string \"_RPC_URL\""}, "value": "_RPC_URL"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_stringliteral_2186fe596dea1a615b7a1cb43899fd18c5b434aa29c8de36d4b8fcc67e3d6ad9", "typeString": "literal_string \"_RPC_URL\""}], "expression": {"id": 3373, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "6833:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 3374, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "6837:12:3", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "6833:16:3", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 3379, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6833:50:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3372, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6826:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 3371, "name": "string", "nodeType": "ElementaryTypeName", "src": "6826:6:3", "typeDescriptions": {}}}, "id": 3380, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6826:58:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "nodeType": "VariableDeclarationStatement", "src": "6802:82:3"}, {"condition": {"id": 3382, "name": "fallbackToDefaultRpcUrls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3015, "src": "6906:24:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 3405, "nodeType": "Block", "src": "7039:77:3", "statements": [{"expression": {"id": 3403, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 3396, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3337, "src": "7061:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3398, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "7067:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 2998, "src": "7061:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 3401, "name": "envName", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3370, "src": "7089:7:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 3399, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2981, "src": "7076:2:3", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 3400, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7079:9:3", "memberName": "envString", "nodeType": "MemberAccess", "referencedDeclaration": 13995, "src": "7076:12:3", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) view external returns (string memory)"}}, "id": 3402, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7076:21:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "7061:36:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 3404, "nodeType": "ExpressionStatement", "src": "7061:36:3"}]}, "id": 3406, "nodeType": "IfStatement", "src": "6902:214:3", "trueBody": {"id": 3395, "nodeType": "Block", "src": "6932:101:3", "statements": [{"expression": {"id": 3393, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 3383, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3337, "src": "6954:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3385, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "6960:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 2998, "src": "6954:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 3388, "name": "envName", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3370, "src": "6978:7:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"baseExpression": {"id": 3389, "name": "defaultRpcUrls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3008, "src": "6987:14:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_string_memory_ptr_$_t_string_storage_$", "typeString": "mapping(string memory => string storage ref)"}}, "id": 3391, "indexExpression": {"id": 3390, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3334, "src": "7002:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6987:26:3", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}], "expression": {"id": 3386, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2981, "src": "6969:2:3", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 3387, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6972:5:3", "memberName": "envOr", "nodeType": "MemberAccess", "referencedDeclaration": 13935, "src": "6969:8:3", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) view external returns (string memory)"}}, "id": 3392, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6969:45:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "6954:60:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 3394, "nodeType": "ExpressionStatement", "src": "6954:60:3"}]}}, {"assignments": [3408], "declarations": [{"constant": false, "id": 3408, "mutability": "mutable", "name": "oldNotFoundError", "nameLocation": "7331:16:3", "nodeType": "VariableDeclaration", "scope": 3467, "src": "7318:29:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 3407, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "7318:5:3", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 3421, "initialValue": {"arguments": [{"hexValue": "4368656174436f64654572726f72", "id": 3411, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "7394:16:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0bc445031644df03923eb2ab981d332f4354ceab11a95efce72a938e57beaadf", "typeString": "literal_string \"CheatCodeError\""}, "value": "CheatCodeError"}, {"arguments": [{"arguments": [{"hexValue": "696e76616c6964207270632075726c20", "id": 3416, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "7436:18:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2baf3da7b122675739218e635e969f0d1b560b915d35635239551f70fe123eed", "typeString": "literal_string \"invalid rpc url \""}, "value": "invalid rpc url "}, {"id": 3417, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3334, "src": "7456:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_2baf3da7b122675739218e635e969f0d1b560b915d35635239551f70fe123eed", "typeString": "literal_string \"invalid rpc url \""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 3414, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "7419:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 3415, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "7423:12:3", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "7419:16:3", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 3418, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7419:48:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3413, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7412:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 3412, "name": "string", "nodeType": "ElementaryTypeName", "src": "7412:6:3", "typeDescriptions": {}}}, "id": 3419, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7412:56:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_0bc445031644df03923eb2ab981d332f4354ceab11a95efce72a938e57beaadf", "typeString": "literal_string \"CheatCodeError\""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 3409, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "7370:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 3410, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "7374:19:3", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "7370:23:3", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 3420, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7370:99:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "7318:151:3"}, {"assignments": [3423], "declarations": [{"constant": false, "id": 3423, "mutability": "mutable", "name": "newNotFoundError", "nameLocation": "7500:16:3", "nodeType": "VariableDeclaration", "scope": 3467, "src": "7487:29:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 3422, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "7487:5:3", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 3436, "initialValue": {"arguments": [{"hexValue": "4368656174636f64654572726f7228737472696e6729", "id": 3426, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "7564:24:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_eeaa9e6f35c22929478456dd64e8453f06b33521fed71b747719abfbccbe6492", "typeString": "literal_string \"CheatcodeError(string)\""}, "value": "CheatcodeError(string)"}, {"arguments": [{"arguments": [{"hexValue": "696e76616c6964207270632075726c3a20", "id": 3431, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "7614:19:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4888507059bbf849006832c209cb94797be8c857a4984252b438e37098512c6a", "typeString": "literal_string \"invalid rpc url: \""}, "value": "invalid rpc url: "}, {"id": 3432, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3334, "src": "7635:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4888507059bbf849006832c209cb94797be8c857a4984252b438e37098512c6a", "typeString": "literal_string \"invalid rpc url: \""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 3429, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "7597:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 3430, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "7601:12:3", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "7597:16:3", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 3433, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7597:49:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3428, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7590:6:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 3427, "name": "string", "nodeType": "ElementaryTypeName", "src": "7590:6:3", "typeDescriptions": {}}}, "id": 3434, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7590:57:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_eeaa9e6f35c22929478456dd64e8453f06b33521fed71b747719abfbccbe6492", "typeString": "literal_string \"CheatcodeError(string)\""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 3424, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "7519:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 3425, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "7523:19:3", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "7519:23:3", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 3435, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7519:146:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "7487:178:3"}, {"assignments": [3438], "declarations": [{"constant": false, "id": 3438, "mutability": "mutable", "name": "err<PERSON><PERSON>", "nameLocation": "7691:7:3", "nodeType": "VariableDeclaration", "scope": 3467, "src": "7683:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 3437, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "7683:7:3", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 3442, "initialValue": {"arguments": [{"id": 3440, "name": "err", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3367, "src": "7711:3:3", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3439, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "7701:9:3", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 3441, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7701:14:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "VariableDeclarationStatement", "src": "7683:32:3"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 3463, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"components": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 3453, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "id": 3447, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 3443, "name": "err<PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3438, "src": "7759:7:3", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"id": 3445, "name": "oldNotFoundError", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3408, "src": "7780:16:3", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3444, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "7770:9:3", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 3446, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7770:27:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "7759:38:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "id": 3452, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 3448, "name": "err<PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3438, "src": "7801:7:3", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"id": 3450, "name": "newNotFoundError", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3423, "src": "7822:16:3", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 3449, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "7812:9:3", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 3451, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7812:27:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "7801:38:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "7759:80:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "id": 3454, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "7758:82:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "||", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3462, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"arguments": [{"expression": {"id": 3457, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3337, "src": "7874:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "id": 3458, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7880:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 2998, "src": "7874:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 3456, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7868:5:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 3455, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "7868:5:3", "typeDescriptions": {}}}, "id": 3459, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7868:19:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 3460, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7888:6:3", "memberName": "length", "nodeType": "MemberAccess", "src": "7868:26:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 3461, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7898:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "7868:31:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "7758:141:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 3466, "nodeType": "IfStatement", "src": "7733:369:3", "trueBody": {"id": 3465, "nodeType": "Block", "src": "7918:184:3", "statements": [{"AST": {"nativeSrc": "8004:80:3", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8004:80:3", "statements": [{"expression": {"arguments": [{"arguments": [{"kind": "number", "nativeSrc": "8041:2:3", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8041:2:3", "type": "", "value": "32"}, {"name": "err", "nativeSrc": "8045:3:3", "nodeType": "YulIdentifier", "src": "8045:3:3"}], "functionName": {"name": "add", "nativeSrc": "8037:3:3", "nodeType": "YulIdentifier", "src": "8037:3:3"}, "nativeSrc": "8037:12:3", "nodeType": "YulFunctionCall", "src": "8037:12:3"}, {"arguments": [{"name": "err", "nativeSrc": "8057:3:3", "nodeType": "YulIdentifier", "src": "8057:3:3"}], "functionName": {"name": "mload", "nativeSrc": "8051:5:3", "nodeType": "YulIdentifier", "src": "8051:5:3"}, "nativeSrc": "8051:10:3", "nodeType": "YulFunctionCall", "src": "8051:10:3"}], "functionName": {"name": "revert", "nativeSrc": "8030:6:3", "nodeType": "YulIdentifier", "src": "8030:6:3"}, "nativeSrc": "8030:32:3", "nodeType": "YulFunctionCall", "src": "8030:32:3"}, "nativeSrc": "8030:32:3", "nodeType": "YulExpressionStatement", "src": "8030:32:3"}]}, "documentation": "@solidity memory-safe-assembly", "evmVersion": "prague", "externalReferences": [{"declaration": 3367, "isOffset": false, "isSlot": false, "src": "8045:3:3", "valueSize": 1}, {"declaration": 3367, "isOffset": false, "isSlot": false, "src": "8057:3:3", "valueSize": 1}], "id": 3464, "nodeType": "InlineAssembly", "src": "7995:89:3"}]}}]}, "errorName": "", "id": 3468, "nodeType": "TryCatchClause", "parameters": {"id": 3368, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3367, "mutability": "mutable", "name": "err", "nameLocation": "6779:3:3", "nodeType": "VariableDeclaration", "scope": 3468, "src": "6766:16:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 3366, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6766:5:3", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "6765:18:3"}, "src": "6759:1357:3"}], "externalCall": {"arguments": [{"id": 3353, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3334, "src": "6649:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 3351, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2981, "src": "6639:2:3", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 3352, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6642:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 16757, "src": "6639:9:3", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) view external returns (string memory)"}}, "id": 3354, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6639:21:3", "tryCall": true, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 3469, "nodeType": "TryStatement", "src": "6635:1481:3"}]}}, {"expression": {"id": 3472, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3337, "src": "8142:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain memory"}}, "functionReturnParameters": 3342, "id": 3473, "nodeType": "Return", "src": "8135:12:3"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "getChainWithUpdatedRpcUrl", "nameLocation": "6438:25:3", "parameters": {"id": 3338, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3334, "mutability": "mutable", "name": "chainAlias", "nameLocation": "6478:10:3", "nodeType": "VariableDeclaration", "scope": 3475, "src": "6464:24:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3333, "name": "string", "nodeType": "ElementaryTypeName", "src": "6464:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 3337, "mutability": "mutable", "name": "chain", "nameLocation": "6503:5:3", "nodeType": "VariableDeclaration", "scope": 3475, "src": "6490:18:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain"}, "typeName": {"id": 3336, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 3335, "name": "Chain", "nameLocations": ["6490:5:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 2999, "src": "6490:5:3"}, "referencedDeclaration": 2999, "src": "6490:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage_ptr", "typeString": "struct StdChains.Chain"}}, "visibility": "internal"}], "src": "6463:46:3"}, "returnParameters": {"id": 3342, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3341, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 3475, "src": "6556:12:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_memory_ptr", "typeString": "struct StdChains.Chain"}, "typeName": {"id": 3340, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 3339, "name": "Chain", "nameLocations": ["6556:5:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 2999, "src": "6556:5:3"}, "referencedDeclaration": 2999, "src": "6556:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_Chain_$2999_storage_ptr", "typeString": "struct StdChains.Chain"}}, "visibility": "internal"}], "src": "6555:14:3"}, "scope": 3921, "stateMutability": "view", "virtual": false, "visibility": "private"}, {"id": 3485, "nodeType": "FunctionDefinition", "src": "8160:117:3", "nodes": [], "body": {"id": 3484, "nodeType": "Block", "src": "8223:54:3", "nodes": [], "statements": [{"expression": {"id": 3482, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 3480, "name": "fallbackToDefaultRpcUrls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3015, "src": "8233:24:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 3481, "name": "useDefault", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3477, "src": "8260:10:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "8233:37:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 3483, "nodeType": "ExpressionStatement", "src": "8233:37:3"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "setFallbackToDefaultRpcUrls", "nameLocation": "8169:27:3", "parameters": {"id": 3478, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3477, "mutability": "mutable", "name": "useDefault", "nameLocation": "8202:10:3", "nodeType": "VariableDeclaration", "scope": 3485, "src": "8197:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 3476, "name": "bool", "nodeType": "ElementaryTypeName", "src": "8197:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "8196:17:3"}, "returnParameters": {"id": 3479, "nodeType": "ParameterList", "parameters": [], "src": "8223:0:3"}, "scope": 3921, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 3883, "nodeType": "FunctionDefinition", "src": "8283:5559:3", "nodes": [], "body": {"id": 3882, "nodeType": "Block", "src": "8322:5520:3", "nodes": [], "statements": [{"condition": {"id": 3488, "name": "stdChainsInitialized", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2983, "src": "8336:20:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 3490, "nodeType": "IfStatement", "src": "8332:33:3", "trueBody": {"functionReturnParameters": 3487, "id": 3489, "nodeType": "Return", "src": "8358:7:3"}}, {"expression": {"id": 3493, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 3491, "name": "stdChainsInitialized", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2983, "src": "8375:20:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 3492, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "8398:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "8375:27:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 3494, "nodeType": "ExpressionStatement", "src": "8375:27:3"}, {"expression": {"arguments": [{"hexValue": "616e76696c", "id": 3496, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8547:7:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_a3d859b77cebfdf9da3b485434702c5090ff9e91b7b86c670ebb15f8a00eb72b", "typeString": "literal_string \"anvil\""}, "value": "anvil"}, {"arguments": [{"hexValue": "416e76696c", "id": 3498, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8566:7:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_1ab1bd2f543bf53e1036abfe292a89809c7285bff756db6e274686afe6fb41b4", "typeString": "literal_string \"Anvil\""}, "value": "An<PERSON>"}, {"hexValue": "3331333337", "id": 3499, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "8575:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_31337_by_1", "typeString": "int_const 31337"}, "value": "31337"}, {"hexValue": "687474703a2f2f3132372e302e302e313a38353435", "id": 3500, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8582:23:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_308a18cf3d9de3b161a842ef1e873581d7b16a5d4ea08170e123f95d25f33fe0", "typeString": "literal_string \"http://127.0.0.1:8545\""}, "value": "http://127.0.0.1:8545"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_1ab1bd2f543bf53e1036abfe292a89809c7285bff756db6e274686afe6fb41b4", "typeString": "literal_string \"Anvil\""}, {"typeIdentifier": "t_rational_31337_by_1", "typeString": "int_const 31337"}, {"typeIdentifier": "t_stringliteral_308a18cf3d9de3b161a842ef1e873581d7b16a5d4ea08170e123f95d25f33fe0", "typeString": "literal_string \"http://127.0.0.1:8545\""}], "id": 3497, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "8556:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3501, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8556:50:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_a3d859b77cebfdf9da3b485434702c5090ff9e91b7b86c670ebb15f8a00eb72b", "typeString": "literal_string \"anvil\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3495, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "8521:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3502, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8521:86:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3503, "nodeType": "ExpressionStatement", "src": "8521:86:3"}, {"expression": {"arguments": [{"hexValue": "6d61696e6e6574", "id": 3505, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8643:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_7beafa94c8bfb8f1c1a43104a34f72c524268aafbfe83bff17485539345c66ff", "typeString": "literal_string \"mainnet\""}, "value": "mainnet"}, {"arguments": [{"hexValue": "4d61696e6e6574", "id": 3507, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8664:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8d646f556e5d9d6f1edcf7a39b77f5ac253776eb34efcfd688aacbee518efc26", "typeString": "literal_string \"Mainnet\""}, "value": "Mainnet"}, {"hexValue": "31", "id": 3508, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "8675:1:3", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, {"hexValue": "68747470733a2f2f6574682e6c6c616d617270632e636f6d", "id": 3509, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8678:26:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_411c24d116d5f5de112b5b4156059b8fdbbc19282a79184bf17a72a8fec93e2a", "typeString": "literal_string \"https://eth.llamarpc.com\""}, "value": "https://eth.llamarpc.com"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_8d646f556e5d9d6f1edcf7a39b77f5ac253776eb34efcfd688aacbee518efc26", "typeString": "literal_string \"Mainnet\""}, {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, {"typeIdentifier": "t_stringliteral_411c24d116d5f5de112b5b4156059b8fdbbc19282a79184bf17a72a8fec93e2a", "typeString": "literal_string \"https://eth.llamarpc.com\""}], "id": 3506, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "8654:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3510, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8654:51:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_7beafa94c8bfb8f1c1a43104a34f72c524268aafbfe83bff17485539345c66ff", "typeString": "literal_string \"mainnet\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3504, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "8617:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3511, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8617:89:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3512, "nodeType": "ExpressionStatement", "src": "8617:89:3"}, {"expression": {"arguments": [{"hexValue": "7365706f6c6961", "id": 3514, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8755:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e1f58df0b51f34f4835aba989f0aa2f2e66218cab53207bafd3dbf37270bd39a", "typeString": "literal_string \"sepolia\""}, "value": "sepolia"}, {"arguments": [{"hexValue": "5365706f6c6961", "id": 3516, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8776:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_a6b54cd124a84bb64f1808905ed95fb171a09730726f85e60eefcd47a4831b27", "typeString": "literal_string \"Sepolia\""}, "value": "Sepolia"}, {"hexValue": "3131313535313131", "id": 3517, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "8787:8:3", "typeDescriptions": {"typeIdentifier": "t_rational_11155111_by_1", "typeString": "int_const 11155111"}, "value": "11155111"}, {"hexValue": "68747470733a2f2f7365706f6c69612e696e667572612e696f2f76332f6239373934616431646466383464666238633334643662623564636132303031", "id": 3518, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8797:63:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_167447379e730a7d89231aec25edd721d4e0b02c818e31467228ef4a7c09810f", "typeString": "literal_string \"https://sepolia.infura.io/v3/********************************\""}, "value": "https://sepolia.infura.io/v3/********************************"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_a6b54cd124a84bb64f1808905ed95fb171a09730726f85e60eefcd47a4831b27", "typeString": "literal_string \"Sepolia\""}, {"typeIdentifier": "t_rational_11155111_by_1", "typeString": "int_const 11155111"}, {"typeIdentifier": "t_stringliteral_167447379e730a7d89231aec25edd721d4e0b02c818e31467228ef4a7c09810f", "typeString": "literal_string \"https://sepolia.infura.io/v3/********************************\""}], "id": 3515, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "8766:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3519, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8766:95:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_e1f58df0b51f34f4835aba989f0aa2f2e66218cab53207bafd3dbf37270bd39a", "typeString": "literal_string \"sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3513, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "8716:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3520, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8716:155:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3521, "nodeType": "ExpressionStatement", "src": "8716:155:3"}, {"expression": {"arguments": [{"hexValue": "686f6c65736b79", "id": 3523, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8907:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_225ab7cecf443e288dc4894ee98610f8cbeaa4a3718c6f21ab130c706fc789a0", "typeString": "literal_string \"holesky\""}, "value": "holesky"}, {"arguments": [{"hexValue": "486f6c65736b79", "id": 3525, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8928:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8aa9e57bbfdbc36333797576aff48d01df8af373d958a7cf043bdc0117ce4b2f", "typeString": "literal_string \"Holesky\""}, "value": "<PERSON><PERSON>"}, {"hexValue": "3137303030", "id": 3526, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "8939:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_17000_by_1", "typeString": "int_const 17000"}, "value": "17000"}, {"hexValue": "68747470733a2f2f7270632e686f6c65736b792e65746870616e64616f70732e696f", "id": 3527, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "8946:36:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e7f02b0bd3afa86b1ed2e1c20ef09a4a86f096b37bcea73edd85b6f0d7974399", "typeString": "literal_string \"https://rpc.holesky.ethpandaops.io\""}, "value": "https://rpc.holesky.ethpandaops.io"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_8aa9e57bbfdbc36333797576aff48d01df8af373d958a7cf043bdc0117ce4b2f", "typeString": "literal_string \"Holesky\""}, {"typeIdentifier": "t_rational_17000_by_1", "typeString": "int_const 17000"}, {"typeIdentifier": "t_stringliteral_e7f02b0bd3afa86b1ed2e1c20ef09a4a86f096b37bcea73edd85b6f0d7974399", "typeString": "literal_string \"https://rpc.holesky.ethpandaops.io\""}], "id": 3524, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "8918:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3528, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8918:65:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_225ab7cecf443e288dc4894ee98610f8cbeaa4a3718c6f21ab130c706fc789a0", "typeString": "literal_string \"holesky\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3522, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "8881:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3529, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8881:103:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3530, "nodeType": "ExpressionStatement", "src": "8881:103:3"}, {"expression": {"arguments": [{"hexValue": "686f6f6469", "id": 3532, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9020:7:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_1555cc0b82398581166a8ac2edec622973f44b5cf266c8c66328fd55958e30c9", "typeString": "literal_string \"hoodi\""}, "value": "hoodi"}, {"arguments": [{"hexValue": "486f6f6469", "id": 3534, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9039:7:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d9874302513726ffaff8c32078b1c27fb48b7dd64fae72faa08410655afc37e0", "typeString": "literal_string \"<PERSON>i\""}, "value": "<PERSON><PERSON>"}, {"hexValue": "353630303438", "id": 3535, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9048:6:3", "typeDescriptions": {"typeIdentifier": "t_rational_560048_by_1", "typeString": "int_const 560048"}, "value": "560048"}, {"hexValue": "68747470733a2f2f7270632e686f6f64692e65746870616e64616f70732e696f", "id": 3536, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9056:34:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_9614316eb163fa19c470a54a4ac254d43c38fd0d13b06fe5b4c51677242d8a4e", "typeString": "literal_string \"https://rpc.hoodi.ethpandaops.io\""}, "value": "https://rpc.hoodi.ethpandaops.io"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_d9874302513726ffaff8c32078b1c27fb48b7dd64fae72faa08410655afc37e0", "typeString": "literal_string \"<PERSON>i\""}, {"typeIdentifier": "t_rational_560048_by_1", "typeString": "int_const 560048"}, {"typeIdentifier": "t_stringliteral_9614316eb163fa19c470a54a4ac254d43c38fd0d13b06fe5b4c51677242d8a4e", "typeString": "literal_string \"https://rpc.hoodi.ethpandaops.io\""}], "id": 3533, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "9029:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3537, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9029:62:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_1555cc0b82398581166a8ac2edec622973f44b5cf266c8c66328fd55958e30c9", "typeString": "literal_string \"hoodi\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3531, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "8994:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3538, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8994:98:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3539, "nodeType": "ExpressionStatement", "src": "8994:98:3"}, {"expression": {"arguments": [{"hexValue": "6f7074696d69736d", "id": 3541, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9128:10:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_09d0f27659ee556a8134fa56941e42400e672aecc2d4cfc61cdb0fcea4590e05", "typeString": "literal_string \"optimism\""}, "value": "optimism"}, {"arguments": [{"hexValue": "4f7074696d69736d", "id": 3543, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9150:10:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f997187c3c319ef9e33fa05f852d1612b66e309dc48d97a4b6b39832090a3bec", "typeString": "literal_string \"Optimism\""}, "value": "Optimism"}, {"hexValue": "3130", "id": 3544, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9162:2:3", "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}, {"hexValue": "68747470733a2f2f6d61696e6e65742e6f7074696d69736d2e696f", "id": 3545, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9166:29:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_38b9211512154272cdc8d9677b3720aef06041b8d31b5e68a6ffc7a4bb22d93e", "typeString": "literal_string \"https://mainnet.optimism.io\""}, "value": "https://mainnet.optimism.io"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_f997187c3c319ef9e33fa05f852d1612b66e309dc48d97a4b6b39832090a3bec", "typeString": "literal_string \"Optimism\""}, {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, {"typeIdentifier": "t_stringliteral_38b9211512154272cdc8d9677b3720aef06041b8d31b5e68a6ffc7a4bb22d93e", "typeString": "literal_string \"https://mainnet.optimism.io\""}], "id": 3542, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "9140:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3546, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9140:56:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_09d0f27659ee556a8134fa56941e42400e672aecc2d4cfc61cdb0fcea4590e05", "typeString": "literal_string \"optimism\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3540, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "9102:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3547, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9102:95:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3548, "nodeType": "ExpressionStatement", "src": "9102:95:3"}, {"expression": {"arguments": [{"hexValue": "6f7074696d69736d5f7365706f6c6961", "id": 3550, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9246:18:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2b81bd4766608fc7dbedcd427f8ec9931a3fdfc6ca839a7cb742fea7b200d95e", "typeString": "literal_string \"optimism_sepolia\""}, "value": "optimism_sepolia"}, {"arguments": [{"hexValue": "4f7074696d69736d205365706f6c6961", "id": 3552, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9276:18:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_a4b5483d4d1690e6b8c441cf97a5dc0dbd350e5a7a13eae7c4892b5ce23a0143", "typeString": "literal_string \"Optimism Sepolia\""}, "value": "Optimism Sepolia"}, {"hexValue": "3131313535343230", "id": 3553, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9296:8:3", "typeDescriptions": {"typeIdentifier": "t_rational_11155420_by_1", "typeString": "int_const 11155420"}, "value": "11155420"}, {"hexValue": "68747470733a2f2f7365706f6c69612e6f7074696d69736d2e696f", "id": 3554, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9306:29:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_9637e6347106e6dff5406560d0751fa58cd1cbad2dbe2b9933bfff29a3398eca", "typeString": "literal_string \"https://sepolia.optimism.io\""}, "value": "https://sepolia.optimism.io"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_a4b5483d4d1690e6b8c441cf97a5dc0dbd350e5a7a13eae7c4892b5ce23a0143", "typeString": "literal_string \"Optimism Sepolia\""}, {"typeIdentifier": "t_rational_11155420_by_1", "typeString": "int_const 11155420"}, {"typeIdentifier": "t_stringliteral_9637e6347106e6dff5406560d0751fa58cd1cbad2dbe2b9933bfff29a3398eca", "typeString": "literal_string \"https://sepolia.optimism.io\""}], "id": 3551, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "9266:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3555, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9266:70:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_2b81bd4766608fc7dbedcd427f8ec9931a3fdfc6ca839a7cb742fea7b200d95e", "typeString": "literal_string \"optimism_sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3549, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "9207:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3556, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9207:139:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3557, "nodeType": "ExpressionStatement", "src": "9207:139:3"}, {"expression": {"arguments": [{"hexValue": "617262697472756d5f6f6e65", "id": 3559, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9382:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e4b44cea7839e0679ac5072602932da9b25ebfb3a9ac42625d9c583a7b6b2eb4", "typeString": "literal_string \"arbitrum_one\""}, "value": "arbitrum_one"}, {"arguments": [{"hexValue": "417262697472756d204f6e65", "id": 3561, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9408:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_9e42b1aebd5463751aea2c5f6ee37505334a82b4085315a5f4b8b0f81d3b9004", "typeString": "literal_string \"Arbitrum One\""}, "value": "Arbitrum One"}, {"hexValue": "3432313631", "id": 3562, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9424:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_42161_by_1", "typeString": "int_const 42161"}, "value": "42161"}, {"hexValue": "68747470733a2f2f617262312e617262697472756d2e696f2f727063", "id": 3563, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9431:30:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ff28c1a1bf3c117d5956efad529d0ee22dcfc0fe5cbf5a03e0bdfcc3c6cac126", "typeString": "literal_string \"https://arb1.arbitrum.io/rpc\""}, "value": "https://arb1.arbitrum.io/rpc"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_9e42b1aebd5463751aea2c5f6ee37505334a82b4085315a5f4b8b0f81d3b9004", "typeString": "literal_string \"Arbitrum One\""}, {"typeIdentifier": "t_rational_42161_by_1", "typeString": "int_const 42161"}, {"typeIdentifier": "t_stringliteral_ff28c1a1bf3c117d5956efad529d0ee22dcfc0fe5cbf5a03e0bdfcc3c6cac126", "typeString": "literal_string \"https://arb1.arbitrum.io/rpc\""}], "id": 3560, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "9398:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3564, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9398:64:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_e4b44cea7839e0679ac5072602932da9b25ebfb3a9ac42625d9c583a7b6b2eb4", "typeString": "literal_string \"arbitrum_one\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3558, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "9356:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3565, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9356:107:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3566, "nodeType": "ExpressionStatement", "src": "9356:107:3"}, {"expression": {"arguments": [{"hexValue": "617262697472756d5f6f6e655f7365706f6c6961", "id": 3568, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9512:22:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_26a1db5cffcc70784b9844e4c62ac247af8d01d7d77a3015f5a0ba29007cf771", "typeString": "literal_string \"arbitrum_one_sepolia\""}, "value": "arbitrum_one_sepolia"}, {"arguments": [{"hexValue": "417262697472756d204f6e65205365706f6c6961", "id": 3570, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9546:22:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_654cc796e821b4114751c4dea67fa0b307483fcd277683183f805d644727e1bd", "typeString": "literal_string \"Arbitrum One Sepolia\""}, "value": "Arbitrum One Sepolia"}, {"hexValue": "343231363134", "id": 3571, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9570:6:3", "typeDescriptions": {"typeIdentifier": "t_rational_421614_by_1", "typeString": "int_const 421614"}, "value": "421614"}, {"hexValue": "68747470733a2f2f7365706f6c69612d726f6c6c75702e617262697472756d2e696f2f727063", "id": 3572, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9578:40:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_688e89820a952a5c42996d2164181a6293f1bd5425540e39328100c40b6ce79e", "typeString": "literal_string \"https://sepolia-rollup.arbitrum.io/rpc\""}, "value": "https://sepolia-rollup.arbitrum.io/rpc"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_654cc796e821b4114751c4dea67fa0b307483fcd277683183f805d644727e1bd", "typeString": "literal_string \"Arbitrum One Sepolia\""}, {"typeIdentifier": "t_rational_421614_by_1", "typeString": "int_const 421614"}, {"typeIdentifier": "t_stringliteral_688e89820a952a5c42996d2164181a6293f1bd5425540e39328100c40b6ce79e", "typeString": "literal_string \"https://sepolia-rollup.arbitrum.io/rpc\""}], "id": 3569, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "9536:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3573, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9536:83:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_26a1db5cffcc70784b9844e4c62ac247af8d01d7d77a3015f5a0ba29007cf771", "typeString": "literal_string \"arbitrum_one_sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3567, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "9473:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3574, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9473:156:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3575, "nodeType": "ExpressionStatement", "src": "9473:156:3"}, {"expression": {"arguments": [{"hexValue": "617262697472756d5f6e6f7661", "id": 3577, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9665:15:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_9338ed1403277416ebb39d4e992ebf5c49e6dded5ec79963ea5fc261cbd7fdac", "typeString": "literal_string \"arbitrum_nova\""}, "value": "arbitrum_nova"}, {"arguments": [{"hexValue": "417262697472756d204e6f7661", "id": 3579, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9692:15:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_25c77b7679bf463420c39c7728b9f65b6a8f1ae05b3335eb9e394b1b61bf8f21", "typeString": "literal_string \"Arbitrum Nova\""}, "value": "Arbitrum Nova"}, {"hexValue": "3432313730", "id": 3580, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9709:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_42170_by_1", "typeString": "int_const 42170"}, "value": "42170"}, {"hexValue": "68747470733a2f2f6e6f76612e617262697472756d2e696f2f727063", "id": 3581, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9716:30:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_a77f0a686c95785c75ada33247e30dc9ac80330a7f8eb521bebdf48f492ee4ac", "typeString": "literal_string \"https://nova.arbitrum.io/rpc\""}, "value": "https://nova.arbitrum.io/rpc"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_25c77b7679bf463420c39c7728b9f65b6a8f1ae05b3335eb9e394b1b61bf8f21", "typeString": "literal_string \"Arbitrum Nova\""}, {"typeIdentifier": "t_rational_42170_by_1", "typeString": "int_const 42170"}, {"typeIdentifier": "t_stringliteral_a77f0a686c95785c75ada33247e30dc9ac80330a7f8eb521bebdf48f492ee4ac", "typeString": "literal_string \"https://nova.arbitrum.io/rpc\""}], "id": 3578, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "9682:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3582, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9682:65:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_9338ed1403277416ebb39d4e992ebf5c49e6dded5ec79963ea5fc261cbd7fdac", "typeString": "literal_string \"arbitrum_nova\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3576, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "9639:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3583, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9639:109:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3584, "nodeType": "ExpressionStatement", "src": "9639:109:3"}, {"expression": {"arguments": [{"hexValue": "706f6c79676f6e", "id": 3586, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9784:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ac63fa1fe369e75c38d62f0f4d465b48b3cd5159f0fb416332899402031d1408", "typeString": "literal_string \"polygon\""}, "value": "polygon"}, {"arguments": [{"hexValue": "506f6c79676f6e", "id": 3588, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9805:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_890af8db8ca1aa1e915857edbc2717639ebd8a22c786f9e0e776d6a1aacb5e71", "typeString": "literal_string \"Polygon\""}, "value": "Polygon"}, {"hexValue": "313337", "id": 3589, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9816:3:3", "typeDescriptions": {"typeIdentifier": "t_rational_137_by_1", "typeString": "int_const 137"}, "value": "137"}, {"hexValue": "68747470733a2f2f706f6c79676f6e2d7270632e636f6d", "id": 3590, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9821:25:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_fda46ab670b83929623b4aa9bcfa97ff7b7376fa90a24a450a8561482232c5c0", "typeString": "literal_string \"https://polygon-rpc.com\""}, "value": "https://polygon-rpc.com"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_890af8db8ca1aa1e915857edbc2717639ebd8a22c786f9e0e776d6a1aacb5e71", "typeString": "literal_string \"Polygon\""}, {"typeIdentifier": "t_rational_137_by_1", "typeString": "int_const 137"}, {"typeIdentifier": "t_stringliteral_fda46ab670b83929623b4aa9bcfa97ff7b7376fa90a24a450a8561482232c5c0", "typeString": "literal_string \"https://polygon-rpc.com\""}], "id": 3587, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "9795:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3591, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9795:52:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_ac63fa1fe369e75c38d62f0f4d465b48b3cd5159f0fb416332899402031d1408", "typeString": "literal_string \"polygon\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3585, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "9758:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3592, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9758:90:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3593, "nodeType": "ExpressionStatement", "src": "9758:90:3"}, {"expression": {"arguments": [{"hexValue": "706f6c79676f6e5f616d6f79", "id": 3595, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9897:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_99386ebe04f891bb96d67bac6a8c404b5f67fb13158954ea2c9e2362a932e070", "typeString": "literal_string \"polygon_amoy\""}, "value": "polygon_amoy"}, {"arguments": [{"hexValue": "506f6c79676f6e20416d6f79", "id": 3597, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9923:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_70d1ef84663b7252febfdf23a787d7e693d0b8647f0d6d014e089199f6cb2946", "typeString": "literal_string \"Polygon Amoy\""}, "value": "Polygon Amoy"}, {"hexValue": "3830303032", "id": 3598, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9939:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_80002_by_1", "typeString": "int_const 80002"}, "value": "80002"}, {"hexValue": "68747470733a2f2f7270632d616d6f792e706f6c79676f6e2e746563686e6f6c6f6779", "id": 3599, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9946:37:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2373c58e9dd62de041a01e2a45a8fce997a1bfaf90c1491c1a766e3d1cc947a6", "typeString": "literal_string \"https://rpc-amoy.polygon.technology\""}, "value": "https://rpc-amoy.polygon.technology"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_70d1ef84663b7252febfdf23a787d7e693d0b8647f0d6d014e089199f6cb2946", "typeString": "literal_string \"Polygon Amoy\""}, {"typeIdentifier": "t_rational_80002_by_1", "typeString": "int_const 80002"}, {"typeIdentifier": "t_stringliteral_2373c58e9dd62de041a01e2a45a8fce997a1bfaf90c1491c1a766e3d1cc947a6", "typeString": "literal_string \"https://rpc-amoy.polygon.technology\""}], "id": 3596, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "9913:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3600, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9913:71:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_99386ebe04f891bb96d67bac6a8c404b5f67fb13158954ea2c9e2362a932e070", "typeString": "literal_string \"polygon_amoy\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3594, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "9858:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3601, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9858:136:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3602, "nodeType": "ExpressionStatement", "src": "9858:136:3"}, {"expression": {"arguments": [{"hexValue": "6176616c616e636865", "id": 3604, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10030:11:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_6e8b0d92516ee4289145e3b78cea58daac177b1c618beeedbc6cdabd388a6e55", "typeString": "literal_string \"avalanche\""}, "value": "avalanche"}, {"arguments": [{"hexValue": "4176616c616e636865", "id": 3606, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10053:11:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_6585177c3aba6cb7ffc0a37e831a958c4ee9278e4c62c7bdad7175ca09883c40", "typeString": "literal_string \"Avalanche\""}, "value": "Avalanche"}, {"hexValue": "3433313134", "id": 3607, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "10066:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_43114_by_1", "typeString": "int_const 43114"}, "value": "43114"}, {"hexValue": "68747470733a2f2f6170692e617661782e6e6574776f726b2f6578742f62632f432f727063", "id": 3608, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10073:39:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_429365eac47ed6b261c38927d854e528b743fc5a678b1b4ba631c511f305886a", "typeString": "literal_string \"https://api.avax.network/ext/bc/C/rpc\""}, "value": "https://api.avax.network/ext/bc/C/rpc"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_6585177c3aba6cb7ffc0a37e831a958c4ee9278e4c62c7bdad7175ca09883c40", "typeString": "literal_string \"Avalanche\""}, {"typeIdentifier": "t_rational_43114_by_1", "typeString": "int_const 43114"}, {"typeIdentifier": "t_stringliteral_429365eac47ed6b261c38927d854e528b743fc5a678b1b4ba631c511f305886a", "typeString": "literal_string \"https://api.avax.network/ext/bc/C/rpc\""}], "id": 3605, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "10043:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3609, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10043:70:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_6e8b0d92516ee4289145e3b78cea58daac177b1c618beeedbc6cdabd388a6e55", "typeString": "literal_string \"avalanche\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3603, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "10004:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3610, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10004:110:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3611, "nodeType": "ExpressionStatement", "src": "10004:110:3"}, {"expression": {"arguments": [{"hexValue": "6176616c616e6368655f66756a69", "id": 3613, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10163:16:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_a1920d2f80060f1c83444622c7eb5adf4484bed8a537b8d13eae53bd800aa692", "typeString": "literal_string \"avalanche_fuji\""}, "value": "avalanche_fuji"}, {"arguments": [{"hexValue": "4176616c616e6368652046756a69", "id": 3615, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10191:16:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_522b176494c651b1a4c5779e66ed19f885df62891abfb18fd5e45b69bdabe11b", "typeString": "literal_string \"Avalanche Fuji\""}, "value": "Ava<PERSON>"}, {"hexValue": "3433313133", "id": 3616, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "10209:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_43113_by_1", "typeString": "int_const 43113"}, "value": "43113"}, {"hexValue": "68747470733a2f2f6170692e617661782d746573742e6e6574776f726b2f6578742f62632f432f727063", "id": 3617, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10216:44:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d6621ea822eabf6c190358ea82de0c52d3503dcce8117b3366a8a3bd96eb422d", "typeString": "literal_string \"https://api.avax-test.network/ext/bc/C/rpc\""}, "value": "https://api.avax-test.network/ext/bc/C/rpc"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_522b176494c651b1a4c5779e66ed19f885df62891abfb18fd5e45b69bdabe11b", "typeString": "literal_string \"Avalanche Fuji\""}, {"typeIdentifier": "t_rational_43113_by_1", "typeString": "int_const 43113"}, {"typeIdentifier": "t_stringliteral_d6621ea822eabf6c190358ea82de0c52d3503dcce8117b3366a8a3bd96eb422d", "typeString": "literal_string \"https://api.avax-test.network/ext/bc/C/rpc\""}], "id": 3614, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "10181:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3618, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10181:80:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_a1920d2f80060f1c83444622c7eb5adf4484bed8a537b8d13eae53bd800aa692", "typeString": "literal_string \"avalanche_fuji\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3612, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "10124:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3619, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10124:147:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3620, "nodeType": "ExpressionStatement", "src": "10124:147:3"}, {"expression": {"arguments": [{"hexValue": "626e625f736d6172745f636861696e", "id": 3622, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10320:17:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_fa8b17ae9aa26749f5dc3a3bb333e0019db0c257f3541e870f73bb48b574361e", "typeString": "literal_string \"bnb_smart_chain\""}, "value": "bnb_smart_chain"}, {"arguments": [{"hexValue": "424e4220536d61727420436861696e", "id": 3624, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10349:17:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_3606544ee65d30d7c7f7d6a1f6618e0d836299fa5b85b88d71a59535c6a1550f", "typeString": "literal_string \"BNB Smart Chain\""}, "value": "BNB Smart Chain"}, {"hexValue": "3536", "id": 3625, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "10368:2:3", "typeDescriptions": {"typeIdentifier": "t_rational_56_by_1", "typeString": "int_const 56"}, "value": "56"}, {"hexValue": "68747470733a2f2f6273632d6461746173656564312e62696e616e63652e6f7267", "id": 3626, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10372:35:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e2b4215bd50ab260c8c9f18e36ea07b1f952450853bcf024123d5767a40d4719", "typeString": "literal_string \"https://bsc-dataseed1.binance.org\""}, "value": "https://bsc-dataseed1.binance.org"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_3606544ee65d30d7c7f7d6a1f6618e0d836299fa5b85b88d71a59535c6a1550f", "typeString": "literal_string \"BNB Smart Chain\""}, {"typeIdentifier": "t_rational_56_by_1", "typeString": "int_const 56"}, {"typeIdentifier": "t_stringliteral_e2b4215bd50ab260c8c9f18e36ea07b1f952450853bcf024123d5767a40d4719", "typeString": "literal_string \"https://bsc-dataseed1.binance.org\""}], "id": 3623, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "10339:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3627, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10339:69:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_fa8b17ae9aa26749f5dc3a3bb333e0019db0c257f3541e870f73bb48b574361e", "typeString": "literal_string \"bnb_smart_chain\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3621, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "10281:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3628, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10281:137:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3629, "nodeType": "ExpressionStatement", "src": "10281:137:3"}, {"expression": {"arguments": [{"hexValue": "626e625f736d6172745f636861696e5f746573746e6574", "id": 3631, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10467:25:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_1813de9892ab9db3d0c3b0c3eed9c8b820fe0c7e205bed860e6e89f4d7f75f29", "typeString": "literal_string \"bnb_smart_chain_testnet\""}, "value": "bnb_smart_chain_testnet"}, {"arguments": [{"hexValue": "424e4220536d61727420436861696e20546573746e6574", "id": 3633, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10516:25:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_3b1d88342c4ab079c9a8243ef8dfeb0bb41e1da5dc9fe62ca728dfe4ea21092c", "typeString": "literal_string \"BNB Smart Chain Testnet\""}, "value": "BNB Smart Chain Testnet"}, {"hexValue": "3937", "id": 3634, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "10543:2:3", "typeDescriptions": {"typeIdentifier": "t_rational_97_by_1", "typeString": "int_const 97"}, "value": "97"}, {"hexValue": "68747470733a2f2f7270632e616e6b722e636f6d2f6273635f746573746e65745f63686170656c", "id": 3635, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10547:41:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_6660930de41ed298fb6a2348f33b08e5736a3823e6ffb86942097b237e075960", "typeString": "literal_string \"https://rpc.ankr.com/bsc_testnet_chapel\""}, "value": "https://rpc.ankr.com/bsc_testnet_chapel"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_3b1d88342c4ab079c9a8243ef8dfeb0bb41e1da5dc9fe62ca728dfe4ea21092c", "typeString": "literal_string \"BNB Smart Chain Testnet\""}, {"typeIdentifier": "t_rational_97_by_1", "typeString": "int_const 97"}, {"typeIdentifier": "t_stringliteral_6660930de41ed298fb6a2348f33b08e5736a3823e6ffb86942097b237e075960", "typeString": "literal_string \"https://rpc.ankr.com/bsc_testnet_chapel\""}], "id": 3632, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "10506:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3636, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10506:83:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_1813de9892ab9db3d0c3b0c3eed9c8b820fe0c7e205bed860e6e89f4d7f75f29", "typeString": "literal_string \"bnb_smart_chain_testnet\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3630, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "10428:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3637, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10428:171:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3638, "nodeType": "ExpressionStatement", "src": "10428:171:3"}, {"expression": {"arguments": [{"hexValue": "676e6f7369735f636861696e", "id": 3640, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10635:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_847b7ed4df59b2dfcdba377bf4ac481c502926169e9af948ee2dd45c0e6df595", "typeString": "literal_string \"gnosis_chain\""}, "value": "gnosis_chain"}, {"arguments": [{"hexValue": "476e6f73697320436861696e", "id": 3642, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10661:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_9bfc6ae4a1f5d8ea33b4f631c2f7dfbfa7d613af42ef38137c06d4cd03619b02", "typeString": "literal_string \"Gnosis Chain\""}, "value": "Gnosis Chain"}, {"hexValue": "313030", "id": 3643, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "10677:3:3", "typeDescriptions": {"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}, "value": "100"}, {"hexValue": "68747470733a2f2f7270632e676e6f736973636861696e2e636f6d", "id": 3644, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10682:29:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_127e02590d58e22164456f76136047039faabc2ca27eb41939081a3e775b50df", "typeString": "literal_string \"https://rpc.gnosischain.com\""}, "value": "https://rpc.gnosischain.com"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_9bfc6ae4a1f5d8ea33b4f631c2f7dfbfa7d613af42ef38137c06d4cd03619b02", "typeString": "literal_string \"Gnosis Chain\""}, {"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}, {"typeIdentifier": "t_stringliteral_127e02590d58e22164456f76136047039faabc2ca27eb41939081a3e775b50df", "typeString": "literal_string \"https://rpc.gnosischain.com\""}], "id": 3641, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "10651:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3645, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10651:61:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_847b7ed4df59b2dfcdba377bf4ac481c502926169e9af948ee2dd45c0e6df595", "typeString": "literal_string \"gnosis_chain\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3639, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "10609:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3646, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10609:104:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3647, "nodeType": "ExpressionStatement", "src": "10609:104:3"}, {"expression": {"arguments": [{"hexValue": "6d6f6f6e6265616d", "id": 3649, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10749:10:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_26aaddd9933ae745bc6e39b5e8962c0d0eef85597e0bdcb35ce7e0d96b84735d", "typeString": "literal_string \"moonbeam\""}, "value": "moonbeam"}, {"arguments": [{"hexValue": "4d6f6f6e6265616d", "id": 3651, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10771:10:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_99a49606e97aa9d58789783bd4cdfcc3ab4072167b449d1e303cb1135216531b", "typeString": "literal_string \"Moonbeam\""}, "value": "Moonbeam"}, {"hexValue": "31323834", "id": 3652, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "10783:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_1284_by_1", "typeString": "int_const 1284"}, "value": "1284"}, {"hexValue": "68747470733a2f2f7270632e6170692e6d6f6f6e6265616d2e6e6574776f726b", "id": 3653, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10789:34:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_cf5d37a68a82777d3f0adcdf64b39d98f1e820688e4ced698cd753bbd1e32191", "typeString": "literal_string \"https://rpc.api.moonbeam.network\""}, "value": "https://rpc.api.moonbeam.network"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_99a49606e97aa9d58789783bd4cdfcc3ab4072167b449d1e303cb1135216531b", "typeString": "literal_string \"Moonbeam\""}, {"typeIdentifier": "t_rational_1284_by_1", "typeString": "int_const 1284"}, {"typeIdentifier": "t_stringliteral_cf5d37a68a82777d3f0adcdf64b39d98f1e820688e4ced698cd753bbd1e32191", "typeString": "literal_string \"https://rpc.api.moonbeam.network\""}], "id": 3650, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "10761:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3654, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10761:63:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_26aaddd9933ae745bc6e39b5e8962c0d0eef85597e0bdcb35ce7e0d96b84735d", "typeString": "literal_string \"moonbeam\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3648, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "10723:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3655, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10723:102:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3656, "nodeType": "ExpressionStatement", "src": "10723:102:3"}, {"expression": {"arguments": [{"hexValue": "6d6f6f6e7269766572", "id": 3658, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10874:11:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2eb4cae4af32e190d8881d6d0a59016ff55092d3a70bcf6b321432516acfd74a", "typeString": "literal_string \"moonriver\""}, "value": "moonriver"}, {"arguments": [{"hexValue": "4d6f6f6e7269766572", "id": 3660, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10897:11:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_65d5ad77d0dd38eb7219d1087db2cb9c2440e3f70be3ee1567aa2329d21dad8a", "typeString": "literal_string \"Moonriver\""}, "value": "Moonriver"}, {"hexValue": "31323835", "id": 3661, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "10910:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_1285_by_1", "typeString": "int_const 1285"}, "value": "1285"}, {"hexValue": "68747470733a2f2f7270632e6170692e6d6f6f6e72697665722e6d6f6f6e6265616d2e6e6574776f726b", "id": 3662, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "10916:44:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_cdf0715ef9b420dea4501d55a4c023de5bc6e2be267c3e3ec8345021a77f3e46", "typeString": "literal_string \"https://rpc.api.moonriver.moonbeam.network\""}, "value": "https://rpc.api.moonriver.moonbeam.network"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_65d5ad77d0dd38eb7219d1087db2cb9c2440e3f70be3ee1567aa2329d21dad8a", "typeString": "literal_string \"Moonriver\""}, {"typeIdentifier": "t_rational_1285_by_1", "typeString": "int_const 1285"}, {"typeIdentifier": "t_stringliteral_cdf0715ef9b420dea4501d55a4c023de5bc6e2be267c3e3ec8345021a77f3e46", "typeString": "literal_string \"https://rpc.api.moonriver.moonbeam.network\""}], "id": 3659, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "10887:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3663, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10887:74:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_2eb4cae4af32e190d8881d6d0a59016ff55092d3a70bcf6b321432516acfd74a", "typeString": "literal_string \"moonriver\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3657, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "10835:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3664, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10835:136:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3665, "nodeType": "ExpressionStatement", "src": "10835:136:3"}, {"expression": {"arguments": [{"hexValue": "6d6f6f6e62617365", "id": 3667, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11007:10:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ccd05eb377a4954d8471e48341881dadc4d2a36094f09ce309d35b3b6204f44e", "typeString": "literal_string \"moonbase\""}, "value": "moonbase"}, {"arguments": [{"hexValue": "4d6f6f6e62617365", "id": 3669, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11029:10:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_6f3c53069778183912da77a05fe67c3d6edb208ffdf1ca0161d51543035e3c68", "typeString": "literal_string \"Moonbase\""}, "value": "Moonbase"}, {"hexValue": "31323837", "id": 3670, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "11041:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_1287_by_1", "typeString": "int_const 1287"}, "value": "1287"}, {"hexValue": "68747470733a2f2f7270632e746573746e65742e6d6f6f6e6265616d2e6e6574776f726b", "id": 3671, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11047:38:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_611da7a50d9bf940412b47209c78030562dd2047afcf97dad69e15217355b585", "typeString": "literal_string \"https://rpc.testnet.moonbeam.network\""}, "value": "https://rpc.testnet.moonbeam.network"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_6f3c53069778183912da77a05fe67c3d6edb208ffdf1ca0161d51543035e3c68", "typeString": "literal_string \"Moonbase\""}, {"typeIdentifier": "t_rational_1287_by_1", "typeString": "int_const 1287"}, {"typeIdentifier": "t_stringliteral_611da7a50d9bf940412b47209c78030562dd2047afcf97dad69e15217355b585", "typeString": "literal_string \"https://rpc.testnet.moonbeam.network\""}], "id": 3668, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "11019:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3672, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11019:67:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_ccd05eb377a4954d8471e48341881dadc4d2a36094f09ce309d35b3b6204f44e", "typeString": "literal_string \"moonbase\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3666, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "10981:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3673, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10981:106:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3674, "nodeType": "ExpressionStatement", "src": "10981:106:3"}, {"expression": {"arguments": [{"hexValue": "626173655f7365706f6c6961", "id": 3676, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11123:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_40f5ce1e060576e5bb027cec6e47b8e25f62225f6819b727a8b3b65f474b0579", "typeString": "literal_string \"base_sepolia\""}, "value": "base_sepolia"}, {"arguments": [{"hexValue": "42617365205365706f6c6961", "id": 3678, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11149:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4302f54daff87a391f27ad11679cb16c1ec4c4676bf1145291eff47852bb3951", "typeString": "literal_string \"Base Sepolia\""}, "value": "Base Sepolia"}, {"hexValue": "3834353332", "id": 3679, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "11165:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_84532_by_1", "typeString": "int_const 84532"}, "value": "84532"}, {"hexValue": "68747470733a2f2f7365706f6c69612e626173652e6f7267", "id": 3680, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11172:26:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_64dd31038d7f53a8cfd73e6409052ea93b6797747302995b002ca2468e7a19f5", "typeString": "literal_string \"https://sepolia.base.org\""}, "value": "https://sepolia.base.org"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4302f54daff87a391f27ad11679cb16c1ec4c4676bf1145291eff47852bb3951", "typeString": "literal_string \"Base Sepolia\""}, {"typeIdentifier": "t_rational_84532_by_1", "typeString": "int_const 84532"}, {"typeIdentifier": "t_stringliteral_64dd31038d7f53a8cfd73e6409052ea93b6797747302995b002ca2468e7a19f5", "typeString": "literal_string \"https://sepolia.base.org\""}], "id": 3677, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "11139:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3681, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11139:60:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_40f5ce1e060576e5bb027cec6e47b8e25f62225f6819b727a8b3b65f474b0579", "typeString": "literal_string \"base_sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3675, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "11097:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3682, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11097:103:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3683, "nodeType": "ExpressionStatement", "src": "11097:103:3"}, {"expression": {"arguments": [{"hexValue": "62617365", "id": 3685, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11236:6:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f1f3eb40f5bc1ad1344716ced8b8a0431d840b5783aea1fd01786bc26f35ac0f", "typeString": "literal_string \"base\""}, "value": "base"}, {"arguments": [{"hexValue": "42617365", "id": 3687, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11254:6:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0ae0ac2f852a779a7f563e86fd9f7493133d36d105b67aa4ae634de521805c78", "typeString": "literal_string \"Base\""}, "value": "Base"}, {"hexValue": "38343533", "id": 3688, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "11262:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_8453_by_1", "typeString": "int_const 8453"}, "value": "8453"}, {"hexValue": "68747470733a2f2f6d61696e6e65742e626173652e6f7267", "id": 3689, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11268:26:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_a7cada1c9191e2f8d595127a4d3f6fa90fd263d9c81f2466ebe2e780722f9202", "typeString": "literal_string \"https://mainnet.base.org\""}, "value": "https://mainnet.base.org"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_0ae0ac2f852a779a7f563e86fd9f7493133d36d105b67aa4ae634de521805c78", "typeString": "literal_string \"Base\""}, {"typeIdentifier": "t_rational_8453_by_1", "typeString": "int_const 8453"}, {"typeIdentifier": "t_stringliteral_a7cada1c9191e2f8d595127a4d3f6fa90fd263d9c81f2466ebe2e780722f9202", "typeString": "literal_string \"https://mainnet.base.org\""}], "id": 3686, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "11244:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3690, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11244:51:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_f1f3eb40f5bc1ad1344716ced8b8a0431d840b5783aea1fd01786bc26f35ac0f", "typeString": "literal_string \"base\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3684, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "11210:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3691, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11210:86:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3692, "nodeType": "ExpressionStatement", "src": "11210:86:3"}, {"expression": {"arguments": [{"hexValue": "626c6173745f7365706f6c6961", "id": 3694, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11332:15:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_994871fd85735b80fc101a7bc1ba23b652d20d656ed6bdf5b26d974bbe38a8ce", "typeString": "literal_string \"blast_sepolia\""}, "value": "blast_sepolia"}, {"arguments": [{"hexValue": "426c617374205365706f6c6961", "id": 3696, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11359:15:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_aca96c68944e335d2b1293b78f53b9df0a29846df8dce2ed5b0da1ae3cb18429", "typeString": "literal_string \"Blast Sepolia\""}, "value": "Blast Sepolia"}, {"hexValue": "313638353837373733", "id": 3697, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "11376:9:3", "typeDescriptions": {"typeIdentifier": "t_rational_168587773_by_1", "typeString": "int_const 168587773"}, "value": "168587773"}, {"hexValue": "68747470733a2f2f7365706f6c69612e626c6173742e696f", "id": 3698, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11387:26:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_1cf31ff7d880bc9630920378a25b6d66eee96794c2c50cb2d200ff7a0ce5768c", "typeString": "literal_string \"https://sepolia.blast.io\""}, "value": "https://sepolia.blast.io"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_aca96c68944e335d2b1293b78f53b9df0a29846df8dce2ed5b0da1ae3cb18429", "typeString": "literal_string \"Blast Sepolia\""}, {"typeIdentifier": "t_rational_168587773_by_1", "typeString": "int_const 168587773"}, {"typeIdentifier": "t_stringliteral_1cf31ff7d880bc9630920378a25b6d66eee96794c2c50cb2d200ff7a0ce5768c", "typeString": "literal_string \"https://sepolia.blast.io\""}], "id": 3695, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "11349:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3699, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11349:65:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_994871fd85735b80fc101a7bc1ba23b652d20d656ed6bdf5b26d974bbe38a8ce", "typeString": "literal_string \"blast_sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3693, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "11306:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3700, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11306:109:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3701, "nodeType": "ExpressionStatement", "src": "11306:109:3"}, {"expression": {"arguments": [{"hexValue": "626c617374", "id": 3703, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11451:7:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_edf839a6b71363a2663cc0c8ffcf15606693adcc9ca9c568aeb87895fd70b0ec", "typeString": "literal_string \"blast\""}, "value": "blast"}, {"arguments": [{"hexValue": "426c617374", "id": 3705, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11470:7:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4b94971ac04d596524d45bcf53505c621ede60829afbe43ffb3789c8d10810a8", "typeString": "literal_string \"Blast\""}, "value": "Blast"}, {"hexValue": "3831343537", "id": 3706, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "11479:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_81457_by_1", "typeString": "int_const 81457"}, "value": "81457"}, {"hexValue": "68747470733a2f2f7270632e626c6173742e696f", "id": 3707, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11486:22:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f3358c4aeeee5f7c57914d7763a157022d948cd26527b58cb169c56b42ba12a8", "typeString": "literal_string \"https://rpc.blast.io\""}, "value": "https://rpc.blast.io"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4b94971ac04d596524d45bcf53505c621ede60829afbe43ffb3789c8d10810a8", "typeString": "literal_string \"Blast\""}, {"typeIdentifier": "t_rational_81457_by_1", "typeString": "int_const 81457"}, {"typeIdentifier": "t_stringliteral_f3358c4aeeee5f7c57914d7763a157022d948cd26527b58cb169c56b42ba12a8", "typeString": "literal_string \"https://rpc.blast.io\""}], "id": 3704, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "11460:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3708, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11460:49:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_edf839a6b71363a2663cc0c8ffcf15606693adcc9ca9c568aeb87895fd70b0ec", "typeString": "literal_string \"blast\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3702, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "11425:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3709, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11425:85:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3710, "nodeType": "ExpressionStatement", "src": "11425:85:3"}, {"expression": {"arguments": [{"hexValue": "66616e746f6d5f6f70657261", "id": 3712, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11546:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_efab6b6b003f1806d03e940e3af2c8bdf94c0c15afcd0102f79e1131fb05c2d8", "typeString": "literal_string \"fantom_opera\""}, "value": "fantom_opera"}, {"arguments": [{"hexValue": "46616e746f6d204f70657261", "id": 3714, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11572:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_70e6474318e559e3d7232f43dcb4c49a66cb83d3b46f20c3c7348fba762247cd", "typeString": "literal_string \"Fantom Opera\""}, "value": "Fantom Opera"}, {"hexValue": "323530", "id": 3715, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "11588:3:3", "typeDescriptions": {"typeIdentifier": "t_rational_250_by_1", "typeString": "int_const 250"}, "value": "250"}, {"hexValue": "68747470733a2f2f7270632e616e6b722e636f6d2f66616e746f6d2f", "id": 3716, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11593:30:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_86e240464e047cac971d5f86fa6a105c7a5200638459dd69bf187edaf36e1590", "typeString": "literal_string \"https://rpc.ankr.com/fantom/\""}, "value": "https://rpc.ankr.com/fantom/"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_70e6474318e559e3d7232f43dcb4c49a66cb83d3b46f20c3c7348fba762247cd", "typeString": "literal_string \"Fantom Opera\""}, {"typeIdentifier": "t_rational_250_by_1", "typeString": "int_const 250"}, {"typeIdentifier": "t_stringliteral_86e240464e047cac971d5f86fa6a105c7a5200638459dd69bf187edaf36e1590", "typeString": "literal_string \"https://rpc.ankr.com/fantom/\""}], "id": 3713, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "11562:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3717, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11562:62:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_efab6b6b003f1806d03e940e3af2c8bdf94c0c15afcd0102f79e1131fb05c2d8", "typeString": "literal_string \"fantom_opera\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3711, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "11520:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3718, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11520:105:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3719, "nodeType": "ExpressionStatement", "src": "11520:105:3"}, {"expression": {"arguments": [{"hexValue": "66616e746f6d5f6f706572615f746573746e6574", "id": 3721, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11674:22:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e81f9053d1530b1141c62bc583685976f395bd82d5e5a191ca56bde88753243c", "typeString": "literal_string \"fantom_opera_testnet\""}, "value": "fantom_opera_testnet"}, {"arguments": [{"hexValue": "46616e746f6d204f7065726120546573746e6574", "id": 3723, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11708:22:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_774e6dd0df25c08862c1aba23c14a65527538bc21375c3b4779f0ab53c8a6387", "typeString": "literal_string \"Fantom Opera Testnet\""}, "value": "Fantom Opera Testnet"}, {"hexValue": "34303032", "id": 3724, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "11732:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_4002_by_1", "typeString": "int_const 4002"}, "value": "4002"}, {"hexValue": "68747470733a2f2f7270632e616e6b722e636f6d2f66616e746f6d5f746573746e65742f", "id": 3725, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11738:38:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0c2ee0e3736d2ffc13dc640036e456ff8581e9526282a3ebd5020acb016a2f0f", "typeString": "literal_string \"https://rpc.ankr.com/fantom_testnet/\""}, "value": "https://rpc.ankr.com/fantom_testnet/"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_774e6dd0df25c08862c1aba23c14a65527538bc21375c3b4779f0ab53c8a6387", "typeString": "literal_string \"Fantom Opera Testnet\""}, {"typeIdentifier": "t_rational_4002_by_1", "typeString": "int_const 4002"}, {"typeIdentifier": "t_stringliteral_0c2ee0e3736d2ffc13dc640036e456ff8581e9526282a3ebd5020acb016a2f0f", "typeString": "literal_string \"https://rpc.ankr.com/fantom_testnet/\""}], "id": 3722, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "11698:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3726, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11698:79:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_e81f9053d1530b1141c62bc583685976f395bd82d5e5a191ca56bde88753243c", "typeString": "literal_string \"fantom_opera_testnet\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3720, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "11635:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3727, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11635:152:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3728, "nodeType": "ExpressionStatement", "src": "11635:152:3"}, {"expression": {"arguments": [{"hexValue": "6672617874616c", "id": 3730, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11823:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_39520897016aaf0ab8e5bf7b0c72c0875359483112298e4b64220a3abfb31c1a", "typeString": "literal_string \"fraxtal\""}, "value": "fraxtal"}, {"arguments": [{"hexValue": "4672617874616c", "id": 3732, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11844:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_258a91ae779c05105302c0ca8434df9790a9dacc2a8d962203ef42cdff863a26", "typeString": "literal_string \"Fraxtal\""}, "value": "<PERSON><PERSON><PERSON>"}, {"hexValue": "323532", "id": 3733, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "11855:3:3", "typeDescriptions": {"typeIdentifier": "t_rational_252_by_1", "typeString": "int_const 252"}, "value": "252"}, {"hexValue": "68747470733a2f2f7270632e667261782e636f6d", "id": 3734, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11860:22:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_1b64bb600df7e2957113c841c567f3ce6aa968babbf2ca546497c7c808b6975e", "typeString": "literal_string \"https://rpc.frax.com\""}, "value": "https://rpc.frax.com"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_258a91ae779c05105302c0ca8434df9790a9dacc2a8d962203ef42cdff863a26", "typeString": "literal_string \"Fraxtal\""}, {"typeIdentifier": "t_rational_252_by_1", "typeString": "int_const 252"}, {"typeIdentifier": "t_stringliteral_1b64bb600df7e2957113c841c567f3ce6aa968babbf2ca546497c7c808b6975e", "typeString": "literal_string \"https://rpc.frax.com\""}], "id": 3731, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "11834:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3735, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11834:49:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_39520897016aaf0ab8e5bf7b0c72c0875359483112298e4b64220a3abfb31c1a", "typeString": "literal_string \"fraxtal\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3729, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "11797:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3736, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11797:87:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3737, "nodeType": "ExpressionStatement", "src": "11797:87:3"}, {"expression": {"arguments": [{"hexValue": "6672617874616c5f746573746e6574", "id": 3739, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11920:17:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_25a8d3f8b42e5ee6eb46a7e906575c3f65c7d75f89e14d4f1980b180625cf40d", "typeString": "literal_string \"fraxtal_testnet\""}, "value": "fraxtal_testnet"}, {"arguments": [{"hexValue": "4672617874616c20546573746e6574", "id": 3741, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11949:17:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c3fd54ce348914a0de2945cd0a56373f7fc69c9aa205c9e9f7836ef06688a009", "typeString": "literal_string \"Fraxtal Testnet\""}, "value": "Fraxtal Testnet"}, {"hexValue": "32353232", "id": 3742, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "11968:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_2522_by_1", "typeString": "int_const 2522"}, "value": "2522"}, {"hexValue": "68747470733a2f2f7270632e746573746e65742e667261782e636f6d", "id": 3743, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11974:30:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_12e6821fb9893e70ea8e6b012b4fcfb4682180e2d4c75ac5fb9c7e85c0a0d241", "typeString": "literal_string \"https://rpc.testnet.frax.com\""}, "value": "https://rpc.testnet.frax.com"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c3fd54ce348914a0de2945cd0a56373f7fc69c9aa205c9e9f7836ef06688a009", "typeString": "literal_string \"Fraxtal Testnet\""}, {"typeIdentifier": "t_rational_2522_by_1", "typeString": "int_const 2522"}, {"typeIdentifier": "t_stringliteral_12e6821fb9893e70ea8e6b012b4fcfb4682180e2d4c75ac5fb9c7e85c0a0d241", "typeString": "literal_string \"https://rpc.testnet.frax.com\""}], "id": 3740, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "11939:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3744, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11939:66:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_25a8d3f8b42e5ee6eb46a7e906575c3f65c7d75f89e14d4f1980b180625cf40d", "typeString": "literal_string \"fraxtal_testnet\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3738, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "11894:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3745, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11894:112:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3746, "nodeType": "ExpressionStatement", "src": "11894:112:3"}, {"expression": {"arguments": [{"hexValue": "62657261636861696e5f62617274696f5f746573746e6574", "id": 3748, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12055:26:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d1b25da3f610bf5b5175af87c49eb357a02eb70056225877297844a59fb4f4f8", "typeString": "literal_string \"be<PERSON>hain_bartio_testnet\""}, "value": "<PERSON><PERSON><PERSON><PERSON>_bartio_testnet"}, {"arguments": [{"hexValue": "42657261636861696e2062417274696f20546573746e6574", "id": 3750, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12093:26:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_b167f92573ac2bdc8409b33d6661c61294f1237898227341da2e3b368cb5bc05", "typeString": "literal_string \"<PERSON><PERSON><PERSON><PERSON> bArtio Testnet\""}, "value": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>io Testnet"}, {"hexValue": "3830303834", "id": 3751, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "12121:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_80084_by_1", "typeString": "int_const 80084"}, "value": "80084"}, {"hexValue": "68747470733a2f2f62617274696f2e7270632e62657261636861696e2e636f6d", "id": 3752, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12128:34:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_897c63542fb738805bf0d62cbd83a47219016d3e63992786094c0c012671bdaa", "typeString": "literal_string \"https://bartio.rpc.berachain.com\""}, "value": "https://bartio.rpc.berachain.com"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_b167f92573ac2bdc8409b33d6661c61294f1237898227341da2e3b368cb5bc05", "typeString": "literal_string \"<PERSON><PERSON><PERSON><PERSON> bArtio Testnet\""}, {"typeIdentifier": "t_rational_80084_by_1", "typeString": "int_const 80084"}, {"typeIdentifier": "t_stringliteral_897c63542fb738805bf0d62cbd83a47219016d3e63992786094c0c012671bdaa", "typeString": "literal_string \"https://bartio.rpc.berachain.com\""}], "id": 3749, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "12083:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3753, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12083:80:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_d1b25da3f610bf5b5175af87c49eb357a02eb70056225877297844a59fb4f4f8", "typeString": "literal_string \"be<PERSON>hain_bartio_testnet\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3747, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "12016:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3754, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12016:157:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3755, "nodeType": "ExpressionStatement", "src": "12016:157:3"}, {"expression": {"arguments": [{"hexValue": "666c617265", "id": 3757, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12209:7:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f18d3cef363ef3d9b2f893c01a845645821d99eb19f05a6a335a4ffcded27a57", "typeString": "literal_string \"flare\""}, "value": "flare"}, {"arguments": [{"hexValue": "466c617265", "id": 3759, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12228:7:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d8548a93f9ff6cd6257dd7ca62095cb0e26ca88adfc7d9de9897d5f8b3422acc", "typeString": "literal_string \"Flare\""}, "value": "Flare"}, {"hexValue": "3134", "id": 3760, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "12237:2:3", "typeDescriptions": {"typeIdentifier": "t_rational_14_by_1", "typeString": "int_const 14"}, "value": "14"}, {"hexValue": "68747470733a2f2f666c6172652d6170692e666c6172652e6e6574776f726b2f6578742f432f727063", "id": 3761, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12241:43:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d8af3ebfbaaff92c3d7e36647eebca0601e026a34b8f2db5d800ab1b40bd8fe3", "typeString": "literal_string \"https://flare-api.flare.network/ext/C/rpc\""}, "value": "https://flare-api.flare.network/ext/C/rpc"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_d8548a93f9ff6cd6257dd7ca62095cb0e26ca88adfc7d9de9897d5f8b3422acc", "typeString": "literal_string \"Flare\""}, {"typeIdentifier": "t_rational_14_by_1", "typeString": "int_const 14"}, {"typeIdentifier": "t_stringliteral_d8af3ebfbaaff92c3d7e36647eebca0601e026a34b8f2db5d800ab1b40bd8fe3", "typeString": "literal_string \"https://flare-api.flare.network/ext/C/rpc\""}], "id": 3758, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "12218:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3762, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12218:67:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_f18d3cef363ef3d9b2f893c01a845645821d99eb19f05a6a335a4ffcded27a57", "typeString": "literal_string \"flare\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3756, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "12183:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3763, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12183:103:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3764, "nodeType": "ExpressionStatement", "src": "12183:103:3"}, {"expression": {"arguments": [{"hexValue": "666c6172655f636f73746f6e32", "id": 3766, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12335:15:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c5f9c1a8f191c1c83cb750d2196b8bf7e0aec4ee77ddcbdcee181bc95d559029", "typeString": "literal_string \"flare_coston2\""}, "value": "flare_coston2"}, {"arguments": [{"hexValue": "466c61726520436f73746f6e32", "id": 3768, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12362:15:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_7f8cb824946e5729c9a680a4578ad73e102e293c5883743909742a53d48d7046", "typeString": "literal_string \"Flare Coston2\""}, "value": "Flare Coston2"}, {"hexValue": "313134", "id": 3769, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "12379:3:3", "typeDescriptions": {"typeIdentifier": "t_rational_114_by_1", "typeString": "int_const 114"}, "value": "114"}, {"hexValue": "68747470733a2f2f636f73746f6e322d6170692e666c6172652e6e6574776f726b2f6578742f432f727063", "id": 3770, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12384:45:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c3cb269ef22af0ba99bbdba566b50ef497d19b5fd15b9d3f08f3a579d7f3cb84", "typeString": "literal_string \"https://coston2-api.flare.network/ext/C/rpc\""}, "value": "https://coston2-api.flare.network/ext/C/rpc"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_7f8cb824946e5729c9a680a4578ad73e102e293c5883743909742a53d48d7046", "typeString": "literal_string \"Flare Coston2\""}, {"typeIdentifier": "t_rational_114_by_1", "typeString": "int_const 114"}, {"typeIdentifier": "t_stringliteral_c3cb269ef22af0ba99bbdba566b50ef497d19b5fd15b9d3f08f3a579d7f3cb84", "typeString": "literal_string \"https://coston2-api.flare.network/ext/C/rpc\""}], "id": 3767, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "12352:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3771, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12352:78:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c5f9c1a8f191c1c83cb750d2196b8bf7e0aec4ee77ddcbdcee181bc95d559029", "typeString": "literal_string \"flare_coston2\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3765, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "12296:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3772, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12296:144:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3773, "nodeType": "ExpressionStatement", "src": "12296:144:3"}, {"expression": {"arguments": [{"hexValue": "6d6f6465", "id": 3775, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12477:6:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_93ad29622c7a43be4ba50a1e97ec04a11d86740d6e621d111f2e1b8ce170acae", "typeString": "literal_string \"mode\""}, "value": "mode"}, {"arguments": [{"hexValue": "4d6f6465", "id": 3777, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12495:6:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_b083c1fb3605071b501d7ccea5973ccb4daea9d66fc6201f9648679019164744", "typeString": "literal_string \"Mode\""}, "value": "Mode"}, {"hexValue": "3334343433", "id": 3778, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "12503:5:3", "typeDescriptions": {"typeIdentifier": "t_rational_34443_by_1", "typeString": "int_const 34443"}, "value": "34443"}, {"hexValue": "68747470733a2f2f6d6f64652e647270632e6f7267", "id": 3779, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12510:23:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_5507c21af74ffc4262ed1cccb692d29bdfe542934a89073f8df0ef6a8687bb99", "typeString": "literal_string \"https://mode.drpc.org\""}, "value": "https://mode.drpc.org"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_b083c1fb3605071b501d7ccea5973ccb4daea9d66fc6201f9648679019164744", "typeString": "literal_string \"Mode\""}, {"typeIdentifier": "t_rational_34443_by_1", "typeString": "int_const 34443"}, {"typeIdentifier": "t_stringliteral_5507c21af74ffc4262ed1cccb692d29bdfe542934a89073f8df0ef6a8687bb99", "typeString": "literal_string \"https://mode.drpc.org\""}], "id": 3776, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "12485:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3780, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12485:49:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_93ad29622c7a43be4ba50a1e97ec04a11d86740d6e621d111f2e1b8ce170acae", "typeString": "literal_string \"mode\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3774, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "12451:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3781, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12451:84:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3782, "nodeType": "ExpressionStatement", "src": "12451:84:3"}, {"expression": {"arguments": [{"hexValue": "6d6f64655f7365706f6c6961", "id": 3784, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12571:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_615913243468d25d30af1a00c8c7c731015175e8162de72bfa2d4f0cf1f75615", "typeString": "literal_string \"mode_sepolia\""}, "value": "mode_sepolia"}, {"arguments": [{"hexValue": "4d6f6465205365706f6c6961", "id": 3786, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12597:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_cc042c0c71914faccd09a991413f304a784bf30676e686991b3266f0de1f77fd", "typeString": "literal_string \"Mode Sepolia\""}, "value": "Mode Sepolia"}, {"hexValue": "393139", "id": 3787, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "12613:3:3", "typeDescriptions": {"typeIdentifier": "t_rational_919_by_1", "typeString": "int_const 919"}, "value": "919"}, {"hexValue": "68747470733a2f2f7365706f6c69612e6d6f64652e6e6574776f726b", "id": 3788, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12618:30:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_7c46328c11a1a8b2fb9e9ca6e68a01ec4b52521e68ed7d83e4014987bf8ba76f", "typeString": "literal_string \"https://sepolia.mode.network\""}, "value": "https://sepolia.mode.network"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_cc042c0c71914faccd09a991413f304a784bf30676e686991b3266f0de1f77fd", "typeString": "literal_string \"Mode Sepolia\""}, {"typeIdentifier": "t_rational_919_by_1", "typeString": "int_const 919"}, {"typeIdentifier": "t_stringliteral_7c46328c11a1a8b2fb9e9ca6e68a01ec4b52521e68ed7d83e4014987bf8ba76f", "typeString": "literal_string \"https://sepolia.mode.network\""}], "id": 3785, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "12587:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3789, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12587:62:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_615913243468d25d30af1a00c8c7c731015175e8162de72bfa2d4f0cf1f75615", "typeString": "literal_string \"mode_sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3783, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "12545:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3790, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12545:105:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3791, "nodeType": "ExpressionStatement", "src": "12545:105:3"}, {"expression": {"arguments": [{"hexValue": "7a6f7261", "id": 3793, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12687:6:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_9cd8441f092e5a18599b85f90db915274c2c2e41af27b6e4df466c9c091553d0", "typeString": "literal_string \"zora\""}, "value": "zora"}, {"arguments": [{"hexValue": "5a6f7261", "id": 3795, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12705:6:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_91beda2a71cae260ce24b7d0ba9253f7212b59cbe39b0f303ac34fac7c00047d", "typeString": "literal_string \"<PERSON>ora\""}, "value": "<PERSON><PERSON>"}, {"hexValue": "37373737373737", "id": 3796, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "12713:7:3", "typeDescriptions": {"typeIdentifier": "t_rational_7777777_by_1", "typeString": "int_const 7777777"}, "value": "7777777"}, {"hexValue": "68747470733a2f2f7a6f72612e647270632e6f7267", "id": 3797, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12722:23:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_83756f94519c3d72802947d8ced23d2d44132e55bc08ebe2e7705a90896a8253", "typeString": "literal_string \"https://zora.drpc.org\""}, "value": "https://zora.drpc.org"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_91beda2a71cae260ce24b7d0ba9253f7212b59cbe39b0f303ac34fac7c00047d", "typeString": "literal_string \"<PERSON>ora\""}, {"typeIdentifier": "t_rational_7777777_by_1", "typeString": "int_const 7777777"}, {"typeIdentifier": "t_stringliteral_83756f94519c3d72802947d8ced23d2d44132e55bc08ebe2e7705a90896a8253", "typeString": "literal_string \"https://zora.drpc.org\""}], "id": 3794, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "12695:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3798, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12695:51:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_9cd8441f092e5a18599b85f90db915274c2c2e41af27b6e4df466c9c091553d0", "typeString": "literal_string \"zora\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3792, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "12661:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3799, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12661:86:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3800, "nodeType": "ExpressionStatement", "src": "12661:86:3"}, {"expression": {"arguments": [{"hexValue": "7a6f72615f7365706f6c6961", "id": 3802, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12796:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8730143b0b00fc2fcb9e8bb3d7ee42f220c40f670d47c89317ce107b768efbb6", "typeString": "literal_string \"zora_sepolia\""}, "value": "zora_sepolia"}, {"arguments": [{"hexValue": "5a6f7261205365706f6c6961", "id": 3804, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12822:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_daefb4bac2caa4d8c54d0dbe02e430895b0d45abea7ca560100ece36c8374662", "typeString": "literal_string \"<PERSON><PERSON>\""}, "value": "Zora <PERSON>"}, {"hexValue": "393939393939393939", "id": 3805, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "12838:9:3", "typeDescriptions": {"typeIdentifier": "t_rational_999999999_by_1", "typeString": "int_const 999999999"}, "value": "999999999"}, {"hexValue": "68747470733a2f2f7365706f6c69612e7270632e7a6f72612e656e65726779", "id": 3806, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12849:33:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8d670a424d428055103ac32edd1b30a8f0b037b80f6dfe181938ef88e9a34e5c", "typeString": "literal_string \"https://sepolia.rpc.zora.energy\""}, "value": "https://sepolia.rpc.zora.energy"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_daefb4bac2caa4d8c54d0dbe02e430895b0d45abea7ca560100ece36c8374662", "typeString": "literal_string \"<PERSON><PERSON>\""}, {"typeIdentifier": "t_rational_999999999_by_1", "typeString": "int_const 999999999"}, {"typeIdentifier": "t_stringliteral_8d670a424d428055103ac32edd1b30a8f0b037b80f6dfe181938ef88e9a34e5c", "typeString": "literal_string \"https://sepolia.rpc.zora.energy\""}], "id": 3803, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "12812:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3807, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12812:71:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_8730143b0b00fc2fcb9e8bb3d7ee42f220c40f670d47c89317ce107b768efbb6", "typeString": "literal_string \"zora_sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3801, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "12757:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3808, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12757:136:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3809, "nodeType": "ExpressionStatement", "src": "12757:136:3"}, {"expression": {"arguments": [{"hexValue": "72616365", "id": 3811, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12930:6:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4ad2aa4c12d96722dfceb21f2268c3f1a6ca86b0ae708fb2fef212f0be618a04", "typeString": "literal_string \"race\""}, "value": "race"}, {"arguments": [{"hexValue": "52616365", "id": 3813, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12948:6:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_07f73149c5b0cbfa2a84e787fa37bcf27f364fb4bcda526bfa767820094a3480", "typeString": "literal_string \"Race\""}, "value": "Race"}, {"hexValue": "36383035", "id": 3814, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "12956:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_6805_by_1", "typeString": "int_const 6805"}, "value": "6805"}, {"hexValue": "68747470733a2f2f726163656d61696e6e65742e696f", "id": 3815, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12962:24:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8895a50bde51e556473d668c8208164e2d04afcafbc71cc0b714e68ca1491cb5", "typeString": "literal_string \"https://racemainnet.io\""}, "value": "https://racemainnet.io"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_07f73149c5b0cbfa2a84e787fa37bcf27f364fb4bcda526bfa767820094a3480", "typeString": "literal_string \"Race\""}, {"typeIdentifier": "t_rational_6805_by_1", "typeString": "int_const 6805"}, {"typeIdentifier": "t_stringliteral_8895a50bde51e556473d668c8208164e2d04afcafbc71cc0b714e68ca1491cb5", "typeString": "literal_string \"https://racemainnet.io\""}], "id": 3812, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "12938:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3816, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12938:49:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4ad2aa4c12d96722dfceb21f2268c3f1a6ca86b0ae708fb2fef212f0be618a04", "typeString": "literal_string \"race\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3810, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "12904:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3817, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12904:84:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3818, "nodeType": "ExpressionStatement", "src": "12904:84:3"}, {"expression": {"arguments": [{"hexValue": "726163655f7365706f6c6961", "id": 3820, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13024:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_14aedb5333da63e7f32927d4a2f4db1b2024a5457e27a26af4a133340a6f636e", "typeString": "literal_string \"race_sepolia\""}, "value": "race_sepolia"}, {"arguments": [{"hexValue": "52616365205365706f6c6961", "id": 3822, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13050:14:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_936668e281d29420bc428f3f1e6990ef0a6b4e2663ca1933b122d411c07e7fe9", "typeString": "literal_string \"Race Sepolia\""}, "value": "Race Sep<PERSON>"}, {"hexValue": "36383036", "id": 3823, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "13066:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_6806_by_1", "typeString": "int_const 6806"}, "value": "6806"}, {"hexValue": "68747470733a2f2f726163656d61696e6e65742e696f", "id": 3824, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13072:24:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8895a50bde51e556473d668c8208164e2d04afcafbc71cc0b714e68ca1491cb5", "typeString": "literal_string \"https://racemainnet.io\""}, "value": "https://racemainnet.io"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_936668e281d29420bc428f3f1e6990ef0a6b4e2663ca1933b122d411c07e7fe9", "typeString": "literal_string \"Race Sepolia\""}, {"typeIdentifier": "t_rational_6806_by_1", "typeString": "int_const 6806"}, {"typeIdentifier": "t_stringliteral_8895a50bde51e556473d668c8208164e2d04afcafbc71cc0b714e68ca1491cb5", "typeString": "literal_string \"https://racemainnet.io\""}], "id": 3821, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "13040:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3825, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13040:57:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_14aedb5333da63e7f32927d4a2f4db1b2024a5457e27a26af4a133340a6f636e", "typeString": "literal_string \"race_sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3819, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "12998:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3826, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12998:100:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3827, "nodeType": "ExpressionStatement", "src": "12998:100:3"}, {"expression": {"arguments": [{"hexValue": "6d6574616c", "id": 3829, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13135:7:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_5aa9e9a91810a37381965e10e482ee971c45f0a345e483b98f2d30be02d66531", "typeString": "literal_string \"metal\""}, "value": "metal"}, {"arguments": [{"hexValue": "4d6574616c", "id": 3831, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13154:7:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_44806556e440d835a6556d2a21cd4d59f4c8280adda7f9588c822c3ac0710e16", "typeString": "literal_string \"Metal\""}, "value": "Metal"}, {"hexValue": "31373530", "id": 3832, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "13163:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_1750_by_1", "typeString": "int_const 1750"}, "value": "1750"}, {"hexValue": "68747470733a2f2f6d6574616c6c322e647270632e6f7267", "id": 3833, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13169:26:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_a5b708eb96b733c0e63f0f8abd0882bb40c09b33bea4c96e69604f4f9485cc52", "typeString": "literal_string \"https://metall2.drpc.org\""}, "value": "https://metall2.drpc.org"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_44806556e440d835a6556d2a21cd4d59f4c8280adda7f9588c822c3ac0710e16", "typeString": "literal_string \"Metal\""}, {"typeIdentifier": "t_rational_1750_by_1", "typeString": "int_const 1750"}, {"typeIdentifier": "t_stringliteral_a5b708eb96b733c0e63f0f8abd0882bb40c09b33bea4c96e69604f4f9485cc52", "typeString": "literal_string \"https://metall2.drpc.org\""}], "id": 3830, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "13144:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3834, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13144:52:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_5aa9e9a91810a37381965e10e482ee971c45f0a345e483b98f2d30be02d66531", "typeString": "literal_string \"metal\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3828, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "13109:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3835, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13109:88:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3836, "nodeType": "ExpressionStatement", "src": "13109:88:3"}, {"expression": {"arguments": [{"hexValue": "6d6574616c5f7365706f6c6961", "id": 3838, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13233:15:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4fa56e5554cc6d35f0585438cfaea3f09de71232dcc98626bab7cd13167260dd", "typeString": "literal_string \"metal_sepolia\""}, "value": "metal_sepolia"}, {"arguments": [{"hexValue": "4d6574616c205365706f6c6961", "id": 3840, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13260:15:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_678db06aa0e01ef48de85b845d0253ec9d349dbd394deef4f225eb46cea15ad7", "typeString": "literal_string \"Metal Sepolia\""}, "value": "Metal Sepolia"}, {"hexValue": "31373430", "id": 3841, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "13277:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_1740_by_1", "typeString": "int_const 1740"}, "value": "1740"}, {"hexValue": "68747470733a2f2f746573746e65742e7270632e6d6574616c6c322e636f6d", "id": 3842, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13283:33:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_50811606e12ae2091161f930288148c86d6408c2333ecd75b46b674515cef1e0", "typeString": "literal_string \"https://testnet.rpc.metall2.com\""}, "value": "https://testnet.rpc.metall2.com"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_678db06aa0e01ef48de85b845d0253ec9d349dbd394deef4f225eb46cea15ad7", "typeString": "literal_string \"Metal Sepolia\""}, {"typeIdentifier": "t_rational_1740_by_1", "typeString": "int_const 1740"}, {"typeIdentifier": "t_stringliteral_50811606e12ae2091161f930288148c86d6408c2333ecd75b46b674515cef1e0", "typeString": "literal_string \"https://testnet.rpc.metall2.com\""}], "id": 3839, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "13250:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3843, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13250:67:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4fa56e5554cc6d35f0585438cfaea3f09de71232dcc98626bab7cd13167260dd", "typeString": "literal_string \"metal_sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3837, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "13207:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3844, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13207:111:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3845, "nodeType": "ExpressionStatement", "src": "13207:111:3"}, {"expression": {"arguments": [{"hexValue": "62696e617279", "id": 3847, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13355:8:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_29b663aa319210a8ea1df752e4fba32b1df8d64dcd24815fae2aac3f4afc8e85", "typeString": "literal_string \"binary\""}, "value": "binary"}, {"arguments": [{"hexValue": "42696e617279", "id": 3849, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13375:8:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_016d658c935a4c4ebdb218d1bbdb9ec6616d456bd4575509224c2587e85f3ad4", "typeString": "literal_string \"Binary\""}, "value": "Binary"}, {"hexValue": "363234", "id": 3850, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "13385:3:3", "typeDescriptions": {"typeIdentifier": "t_rational_624_by_1", "typeString": "int_const 624"}, "value": "624"}, {"hexValue": "68747470733a2f2f7270632e7a65726f2e74686562696e617279686f6c64696e67732e636f6d", "id": 3851, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13390:40:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_423eb7effe2828bcfac3f054abba1dfc18cf06b1463836f25e3980b9ad935d65", "typeString": "literal_string \"https://rpc.zero.thebinaryholdings.com\""}, "value": "https://rpc.zero.thebinaryholdings.com"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_016d658c935a4c4ebdb218d1bbdb9ec6616d456bd4575509224c2587e85f3ad4", "typeString": "literal_string \"Binary\""}, {"typeIdentifier": "t_rational_624_by_1", "typeString": "int_const 624"}, {"typeIdentifier": "t_stringliteral_423eb7effe2828bcfac3f054abba1dfc18cf06b1463836f25e3980b9ad935d65", "typeString": "literal_string \"https://rpc.zero.thebinaryholdings.com\""}], "id": 3848, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "13365:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3852, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13365:66:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_29b663aa319210a8ea1df752e4fba32b1df8d64dcd24815fae2aac3f4afc8e85", "typeString": "literal_string \"binary\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3846, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "13329:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3853, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13329:103:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3854, "nodeType": "ExpressionStatement", "src": "13329:103:3"}, {"expression": {"arguments": [{"hexValue": "62696e6172795f7365706f6c6961", "id": 3856, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13481:16:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f42891460a8b750ed84d65191f6a8b0f8c2cf4579e838da4647883c473d38586", "typeString": "literal_string \"binary_sepolia\""}, "value": "binary_sepolia"}, {"arguments": [{"hexValue": "42696e617279205365706f6c6961", "id": 3858, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13509:16:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_304655cef9d2cd5f68dd5d8a176a4133b08ce153db3c75c0de816437115b4206", "typeString": "literal_string \"Binary Sepolia\""}, "value": "Binary Sepolia"}, {"hexValue": "363235", "id": 3859, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "13527:3:3", "typeDescriptions": {"typeIdentifier": "t_rational_625_by_1", "typeString": "int_const 625"}, "value": "625"}, {"hexValue": "68747470733a2f2f7270632e7a65726f2e74686562696e617279686f6c64696e67732e636f6d", "id": 3860, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13532:40:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_423eb7effe2828bcfac3f054abba1dfc18cf06b1463836f25e3980b9ad935d65", "typeString": "literal_string \"https://rpc.zero.thebinaryholdings.com\""}, "value": "https://rpc.zero.thebinaryholdings.com"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_304655cef9d2cd5f68dd5d8a176a4133b08ce153db3c75c0de816437115b4206", "typeString": "literal_string \"Binary Sepolia\""}, {"typeIdentifier": "t_rational_625_by_1", "typeString": "int_const 625"}, {"typeIdentifier": "t_stringliteral_423eb7effe2828bcfac3f054abba1dfc18cf06b1463836f25e3980b9ad935d65", "typeString": "literal_string \"https://rpc.zero.thebinaryholdings.com\""}], "id": 3857, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "13499:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3861, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13499:74:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_f42891460a8b750ed84d65191f6a8b0f8c2cf4579e838da4647883c473d38586", "typeString": "literal_string \"binary_sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3855, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "13442:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3862, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13442:141:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3863, "nodeType": "ExpressionStatement", "src": "13442:141:3"}, {"expression": {"arguments": [{"hexValue": "6f726465726c79", "id": 3865, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13620:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_95d85ced8adb371760e4b6437896a075632fbd6cefe699f8125a8bc1d9b19e5b", "typeString": "literal_string \"orderly\""}, "value": "orderly"}, {"arguments": [{"hexValue": "4f726465726c79", "id": 3867, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13641:9:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c1a3bdc74975983ef8e9e74f5ca7b15aa21f916bc5135b38332d4b395d83b437", "typeString": "literal_string \"Orderly\""}, "value": "Orderly"}, {"hexValue": "323931", "id": 3868, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "13652:3:3", "typeDescriptions": {"typeIdentifier": "t_rational_291_by_1", "typeString": "int_const 291"}, "value": "291"}, {"hexValue": "68747470733a2f2f7270632e6f726465726c792e6e6574776f726b", "id": 3869, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13657:29:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d7f4cd4553e5162711fac16ca5e64dea931629c51f483779d8f760dda758d74b", "typeString": "literal_string \"https://rpc.orderly.network\""}, "value": "https://rpc.orderly.network"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c1a3bdc74975983ef8e9e74f5ca7b15aa21f916bc5135b38332d4b395d83b437", "typeString": "literal_string \"Orderly\""}, {"typeIdentifier": "t_rational_291_by_1", "typeString": "int_const 291"}, {"typeIdentifier": "t_stringliteral_d7f4cd4553e5162711fac16ca5e64dea931629c51f483779d8f760dda758d74b", "typeString": "literal_string \"https://rpc.orderly.network\""}], "id": 3866, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "13631:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3870, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13631:56:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_95d85ced8adb371760e4b6437896a075632fbd6cefe699f8125a8bc1d9b19e5b", "typeString": "literal_string \"orderly\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3864, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "13594:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3871, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13594:94:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3872, "nodeType": "ExpressionStatement", "src": "13594:94:3"}, {"expression": {"arguments": [{"hexValue": "6f726465726c795f7365706f6c6961", "id": 3874, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13737:17:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_22e6733aad9db6cb6b3609c584796619a207a3026748b338bd3e46539805584c", "typeString": "literal_string \"orderly_sepolia\""}, "value": "orderly_sepolia"}, {"arguments": [{"hexValue": "4f726465726c79205365706f6c6961", "id": 3876, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13766:17:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_615530907dc64bebc72ba912c0aed8fa593a2db7476908cce6c19274f068bdbb", "typeString": "literal_string \"Orderly Sepolia\""}, "value": "Orderly Sepolia"}, {"hexValue": "34343630", "id": 3877, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "13785:4:3", "typeDescriptions": {"typeIdentifier": "t_rational_4460_by_1", "typeString": "int_const 4460"}, "value": "4460"}, {"hexValue": "68747470733a2f2f746573746e65742d7270632e6f726465726c792e6f7267", "id": 3878, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13791:33:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_bce3cfac849d1fcfd1298517233abd947fa3fb019f8442b86dc92adc11eae29b", "typeString": "literal_string \"https://testnet-rpc.orderly.org\""}, "value": "https://testnet-rpc.orderly.org"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_615530907dc64bebc72ba912c0aed8fa593a2db7476908cce6c19274f068bdbb", "typeString": "literal_string \"Orderly Sepolia\""}, {"typeIdentifier": "t_rational_4460_by_1", "typeString": "int_const 4460"}, {"typeIdentifier": "t_stringliteral_bce3cfac849d1fcfd1298517233abd947fa3fb019f8442b86dc92adc11eae29b", "typeString": "literal_string \"https://testnet-rpc.orderly.org\""}], "id": 3875, "name": "ChainData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2990, "src": "13756:9:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ChainData_$2990_storage_ptr_$", "typeString": "type(struct StdChains.ChainData storage pointer)"}}, "id": 3879, "isConstant": false, "isLValue": false, "isPure": true, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13756:69:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_22e6733aad9db6cb6b3609c584796619a207a3026748b338bd3e46539805584c", "typeString": "literal_string \"orderly_sepolia\""}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3873, "name": "setChainWithDefaultRpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3920, "src": "13698:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3880, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "13698:137:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3881, "nodeType": "ExpressionStatement", "src": "13698:137:3"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "initializeStdChains", "nameLocation": "8292:19:3", "parameters": {"id": 3486, "nodeType": "ParameterList", "parameters": [], "src": "8311:2:3"}, "returnParameters": {"id": 3487, "nodeType": "ParameterList", "parameters": [], "src": "8322:0:3"}, "scope": 3921, "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"id": 3920, "nodeType": "FunctionDefinition", "src": "13924:305:3", "nodes": [], "body": {"id": 3919, "nodeType": "Block", "src": "14017:212:3", "nodes": [], "statements": [{"assignments": [3892], "declarations": [{"constant": false, "id": 3892, "mutability": "mutable", "name": "rpcUrl", "nameLocation": "14041:6:3", "nodeType": "VariableDeclaration", "scope": 3919, "src": "14027:20:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3891, "name": "string", "nodeType": "ElementaryTypeName", "src": "14027:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "id": 3895, "initialValue": {"expression": {"id": 3893, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3888, "src": "14050:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}, "id": 3894, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "14056:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 2989, "src": "14050:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "nodeType": "VariableDeclarationStatement", "src": "14027:35:3"}, {"expression": {"id": 3900, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 3896, "name": "defaultRpcUrls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3008, "src": "14072:14:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_string_memory_ptr_$_t_string_storage_$", "typeString": "mapping(string memory => string storage ref)"}}, "id": 3898, "indexExpression": {"id": 3897, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3885, "src": "14087:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "14072:26:3", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 3899, "name": "rpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3892, "src": "14101:6:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "14072:35:3", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 3901, "nodeType": "ExpressionStatement", "src": "14072:35:3"}, {"expression": {"id": 3906, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 3902, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3888, "src": "14117:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}, "id": 3904, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "14123:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 2989, "src": "14117:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "", "id": 3905, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "14132:2:3", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}, "value": ""}, "src": "14117:17:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 3907, "nodeType": "ExpressionStatement", "src": "14117:17:3"}, {"expression": {"arguments": [{"id": 3909, "name": "chainAlias", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3885, "src": "14153:10:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 3910, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3888, "src": "14165:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}], "id": 3908, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [3234, 3255], "referencedDeclaration": 3234, "src": "14144:8:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_struct$_ChainData_$2990_memory_ptr_$returns$__$", "typeString": "function (string memory,struct StdChains.ChainData memory)"}}, "id": 3911, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "14144:27:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3912, "nodeType": "ExpressionStatement", "src": "14144:27:3"}, {"expression": {"id": 3917, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 3913, "name": "chain", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3888, "src": "14181:5:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData memory"}}, "id": 3915, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "14187:6:3", "memberName": "rpcUrl", "nodeType": "MemberAccess", "referencedDeclaration": 2989, "src": "14181:12:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 3916, "name": "rpcUrl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3892, "src": "14196:6:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "14181:21:3", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 3918, "nodeType": "ExpressionStatement", "src": "14181:21:3"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "setChainWithDefaultRpcUrl", "nameLocation": "13933:25:3", "parameters": {"id": 3889, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3885, "mutability": "mutable", "name": "chainAlias", "nameLocation": "13973:10:3", "nodeType": "VariableDeclaration", "scope": 3920, "src": "13959:24:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 3884, "name": "string", "nodeType": "ElementaryTypeName", "src": "13959:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 3888, "mutability": "mutable", "name": "chain", "nameLocation": "14002:5:3", "nodeType": "VariableDeclaration", "scope": 3920, "src": "13985:22:3", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_memory_ptr", "typeString": "struct StdChains.ChainData"}, "typeName": {"id": 3887, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 3886, "name": "ChainData", "nameLocations": ["13985:9:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 2990, "src": "13985:9:3"}, "referencedDeclaration": 2990, "src": "13985:9:3", "typeDescriptions": {"typeIdentifier": "t_struct$_ChainData_$2990_storage_ptr", "typeString": "struct StdChains.ChainData"}}, "visibility": "internal"}], "src": "13958:50:3"}, "returnParameters": {"id": 3890, "nodeType": "ParameterList", "parameters": [], "src": "14017:0:3"}, "scope": 3921, "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}], "abstract": true, "baseContracts": [], "canonicalName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 2964, "nodeType": "StructuredDocumentation", "src": "99:1799:3", "text": " StdChains provides information about EVM compatible chains that can be used in scripts/tests.\n For each chain, the chain's name, chain ID, and a default RPC URL are provided. Chains are\n identified by their alias, which is the same as the alias in the `[rpc_endpoints]` section of\n the `foundry.toml` file. For best UX, ensure the alias in the `foundry.toml` file match the\n alias used in this contract, which can be found as the first argument to the\n `setChainWithDefaultRpcUrl` call in the `initializeStdChains` function.\n There are two main ways to use this contract:\n   1. Set a chain with `set<PERSON>hain(string memory chainAlias, ChainData memory chain)` or\n      `setChain(string memory chainAlias, Chain memory chain)`\n   2. Get a chain with `getChain(string memory chainAlias)` or `getChain(uint256 chainId)`.\n The first time either of those are used, chains are initialized with the default set of RPC URLs.\n This is done in `initializeStdChains`, which uses `setChainWithDefaultRpcUrl`. Defaults are recorded in\n `defaultRpcUrls`.\n The `setChain` function is straightforward, and it simply saves off the given chain data.\n The `getChain` methods use `getChainWithUpdatedRpcUrl` to return a chain. For example, let's say\n we want to retrieve the RPC URL for `mainnet`:\n   - If you have specified data with `setChain`, it will return that.\n   - If you have configured a mainnet RPC URL in `foundry.toml`, it will return the URL, provided it\n     is valid (e.g. a URL is specified, or an environment variable is given and exists).\n   - If neither of the above conditions is met, the default data is returned.\n Summarizing the above, the prioritization hierarchy is `setChain` -> `foundry.toml` -> environment variable -> defaults."}, "fullyImplemented": true, "linearizedBaseContracts": [3921], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocation": "1917:9:3", "scope": 3922, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 3}