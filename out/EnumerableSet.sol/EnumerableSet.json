{"abi": [], "bytecode": {"object": "0x6055604b600b8282823980515f1a607314603f577f4e487b71000000000000000000000000000000000000000000000000000000005f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220d4ef2afb903afb97d66d8a41688fd34c3ad11c68595614de047bc11a1650aebe64736f6c634300081d0033", "sourceMap": "1329:11630:28:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220d4ef2afb903afb97d66d8a41688fd34c3ad11c68595614de047bc11a1650aebe64736f6c634300081d0033", "sourceMap": "1329:11630:28:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"details\":\"Library for managing https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive types. Sets have the following properties: - Elements are added, removed, and checked for existence in constant time (O(1)). - Elements are enumerated in O(n). No guarantees are made on the ordering. ```solidity contract Example {     // Add the library methods     using EnumerableSet for EnumerableSet.AddressSet;     // Declare a set state variable     EnumerableSet.AddressSet private mySet; } ``` As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`) and `uint256` (`UintSet`) are supported. [WARNING] ==== Trying to delete such a structure from storage will likely result in data corruption, rendering the structure unusable. See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info. In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an array of EnumerableSet. ====\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/setup-helpers/src/EnumerableSet.sol\":\"EnumerableSet\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/setup-helpers/src/EnumerableSet.sol\":{\"keccak256\":\"0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df\",\"dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/setup-helpers/src/EnumerableSet.sol": "EnumerableSet"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/setup-helpers/src/EnumerableSet.sol": {"keccak256": "0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514", "urls": ["bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df", "dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/setup-helpers/src/EnumerableSet.sol", "id": 41480, "exportedSymbols": {"EnumerableSet": [41479]}, "nodeType": "SourceUnit", "src": "205:12755:28", "nodes": [{"id": 40868, "nodeType": "PragmaDirective", "src": "205:23:28", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 41479, "nodeType": "ContractDefinition", "src": "1329:11630:28", "nodes": [{"id": 40877, "nodeType": "StructDefinition", "src": "1797:247:28", "nodes": [], "canonicalName": "EnumerableSet.Set", "members": [{"constant": false, "id": 40872, "mutability": "mutable", "name": "_values", "nameLocation": "1861:7:28", "nodeType": "VariableDeclaration", "scope": 40877, "src": "1851:17:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 40870, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1851:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 40871, "nodeType": "ArrayTypeName", "src": "1851:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}, {"constant": false, "id": 40876, "mutability": "mutable", "name": "_indexes", "nameLocation": "2029:8:28", "nodeType": "VariableDeclaration", "scope": 40877, "src": "2001:36:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_uint256_$", "typeString": "mapping(bytes32 => uint256)"}, "typeName": {"id": 40875, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 40873, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2009:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Mapping", "src": "2001:27:28", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_uint256_$", "typeString": "mapping(bytes32 => uint256)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 40874, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2020:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "visibility": "internal"}], "name": "Set", "nameLocation": "1804:3:28", "scope": 41479, "visibility": "public"}, {"id": 40919, "nodeType": "FunctionDefinition", "src": "2214:404:28", "nodes": [], "body": {"id": 40918, "nodeType": "Block", "src": "2283:335:28", "nodes": [], "statements": [{"condition": {"id": 40892, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "2297:22:28", "subExpression": {"arguments": [{"id": 40889, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40881, "src": "2308:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, {"id": 40890, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40883, "src": "2313:5:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 40888, "name": "_contains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41022, "src": "2298:9:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (struct EnumerableSet.Set storage pointer,bytes32) view returns (bool)"}}, "id": 40891, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2298:21:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 40916, "nodeType": "Block", "src": "2575:37:28", "statements": [{"expression": {"hexValue": "66616c7365", "id": 40914, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2596:5:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "functionReturnParameters": 40887, "id": 40915, "nodeType": "Return", "src": "2589:12:28"}]}, "id": 40917, "nodeType": "IfStatement", "src": "2293:319:28", "trueBody": {"id": 40913, "nodeType": "Block", "src": "2321:248:28", "statements": [{"expression": {"arguments": [{"id": 40898, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40883, "src": "2352:5:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"expression": {"id": 40893, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40881, "src": "2335:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 40896, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2339:7:28", "memberName": "_values", "nodeType": "MemberAccess", "referencedDeclaration": 40872, "src": "2335:11:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "id": 40897, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2347:4:28", "memberName": "push", "nodeType": "MemberAccess", "src": "2335:16:28", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_bytes32_$dyn_storage_ptr_$_t_bytes32_$returns$__$attached_to$_t_array$_t_bytes32_$dyn_storage_ptr_$", "typeString": "function (bytes32[] storage pointer,bytes32)"}}, "id": 40899, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2335:23:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40900, "nodeType": "ExpressionStatement", "src": "2335:23:28"}, {"expression": {"id": 40909, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"expression": {"id": 40901, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40881, "src": "2493:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 40904, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2497:8:28", "memberName": "_indexes", "nodeType": "MemberAccess", "referencedDeclaration": 40876, "src": "2493:12:28", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_uint256_$", "typeString": "mapping(bytes32 => uint256)"}}, "id": 40905, "indexExpression": {"id": 40903, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40883, "src": "2506:5:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2493:19:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"expression": {"id": 40906, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40881, "src": "2515:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 40907, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2519:7:28", "memberName": "_values", "nodeType": "MemberAccess", "referencedDeclaration": 40872, "src": "2515:11:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "id": 40908, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2527:6:28", "memberName": "length", "nodeType": "MemberAccess", "src": "2515:18:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2493:40:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 40910, "nodeType": "ExpressionStatement", "src": "2493:40:28"}, {"expression": {"hexValue": "74727565", "id": 40911, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2554:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 40887, "id": 40912, "nodeType": "Return", "src": "2547:11:28"}]}}]}, "documentation": {"id": 40878, "nodeType": "StructuredDocumentation", "src": "2050:159:28", "text": " @dev Add a value to a set. O(1).\n Returns true if the value was added to the set, that is if it was not\n already present."}, "implemented": true, "kind": "function", "modifiers": [], "name": "_add", "nameLocation": "2223:4:28", "parameters": {"id": 40884, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40881, "mutability": "mutable", "name": "set", "nameLocation": "2240:3:28", "nodeType": "VariableDeclaration", "scope": 40919, "src": "2228:15:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}, "typeName": {"id": 40880, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 40879, "name": "Set", "nameLocations": ["2228:3:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 40877, "src": "2228:3:28"}, "referencedDeclaration": 40877, "src": "2228:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}}, "visibility": "internal"}, {"constant": false, "id": 40883, "mutability": "mutable", "name": "value", "nameLocation": "2253:5:28", "nodeType": "VariableDeclaration", "scope": 40919, "src": "2245:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 40882, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2245:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "2227:32:28"}, "returnParameters": {"id": 40887, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40886, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 40919, "src": "2277:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 40885, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2277:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2276:6:28"}, "scope": 41479, "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"id": 41003, "nodeType": "FunctionDefinition", "src": "2786:1388:28", "nodes": [], "body": {"id": 41002, "nodeType": "Block", "src": "2858:1316:28", "nodes": [], "statements": [{"assignments": [40931], "declarations": [{"constant": false, "id": 40931, "mutability": "mutable", "name": "valueIndex", "nameLocation": "2976:10:28", "nodeType": "VariableDeclaration", "scope": 41002, "src": "2968:18:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40930, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2968:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40936, "initialValue": {"baseExpression": {"expression": {"id": 40932, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40923, "src": "2989:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 40933, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2993:8:28", "memberName": "_indexes", "nodeType": "MemberAccess", "referencedDeclaration": 40876, "src": "2989:12:28", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_uint256_$", "typeString": "mapping(bytes32 => uint256)"}}, "id": 40935, "indexExpression": {"id": 40934, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40925, "src": "3002:5:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2989:19:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2968:40:28"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40939, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40937, "name": "valueIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40931, "src": "3023:10:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 40938, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3037:1:28", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3023:15:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 41000, "nodeType": "Block", "src": "4131:37:28", "statements": [{"expression": {"hexValue": "66616c7365", "id": 40998, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4152:5:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "functionReturnParameters": 40929, "id": 40999, "nodeType": "Return", "src": "4145:12:28"}]}, "id": 41001, "nodeType": "IfStatement", "src": "3019:1149:28", "trueBody": {"id": 40997, "nodeType": "Block", "src": "3040:1085:28", "statements": [{"assignments": [40941], "declarations": [{"constant": false, "id": 40941, "mutability": "mutable", "name": "toDeleteIndex", "nameLocation": "3400:13:28", "nodeType": "VariableDeclaration", "scope": 40997, "src": "3392:21:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40940, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3392:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40945, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40944, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40942, "name": "valueIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40931, "src": "3416:10:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "31", "id": 40943, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3429:1:28", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "3416:14:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3392:38:28"}, {"assignments": [40947], "declarations": [{"constant": false, "id": 40947, "mutability": "mutable", "name": "lastIndex", "nameLocation": "3452:9:28", "nodeType": "VariableDeclaration", "scope": 40997, "src": "3444:17:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40946, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3444:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40953, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40952, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"expression": {"id": 40948, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40923, "src": "3464:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 40949, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3468:7:28", "memberName": "_values", "nodeType": "MemberAccess", "referencedDeclaration": 40872, "src": "3464:11:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "id": 40950, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3476:6:28", "memberName": "length", "nodeType": "MemberAccess", "src": "3464:18:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "31", "id": 40951, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3485:1:28", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "3464:22:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3444:42:28"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40956, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40954, "name": "lastIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40947, "src": "3505:9:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"id": 40955, "name": "toDeleteIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40941, "src": "3518:13:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3505:26:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40981, "nodeType": "IfStatement", "src": "3501:398:28", "trueBody": {"id": 40980, "nodeType": "Block", "src": "3533:366:28", "statements": [{"assignments": [40958], "declarations": [{"constant": false, "id": 40958, "mutability": "mutable", "name": "lastValue", "nameLocation": "3559:9:28", "nodeType": "VariableDeclaration", "scope": 40980, "src": "3551:17:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 40957, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "3551:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 40963, "initialValue": {"baseExpression": {"expression": {"id": 40959, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40923, "src": "3571:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 40960, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3575:7:28", "memberName": "_values", "nodeType": "MemberAccess", "referencedDeclaration": 40872, "src": "3571:11:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "id": 40962, "indexExpression": {"id": 40961, "name": "lastIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40947, "src": "3583:9:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3571:22:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "VariableDeclarationStatement", "src": "3551:42:28"}, {"expression": {"id": 40970, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"expression": {"id": 40964, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40923, "src": "3693:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 40967, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3697:7:28", "memberName": "_values", "nodeType": "MemberAccess", "referencedDeclaration": 40872, "src": "3693:11:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "id": 40968, "indexExpression": {"id": 40966, "name": "toDeleteIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40941, "src": "3705:13:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3693:26:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 40969, "name": "lastValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40958, "src": "3722:9:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "3693:38:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 40971, "nodeType": "ExpressionStatement", "src": "3693:38:28"}, {"expression": {"id": 40978, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"expression": {"id": 40972, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40923, "src": "3805:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 40975, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3809:8:28", "memberName": "_indexes", "nodeType": "MemberAccess", "referencedDeclaration": 40876, "src": "3805:12:28", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_uint256_$", "typeString": "mapping(bytes32 => uint256)"}}, "id": 40976, "indexExpression": {"id": 40974, "name": "lastValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40958, "src": "3818:9:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3805:23:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 40977, "name": "valueIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40931, "src": "3831:10:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3805:36:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 40979, "nodeType": "ExpressionStatement", "src": "3805:36:28"}]}}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"expression": {"id": 40982, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40923, "src": "3977:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 40985, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3981:7:28", "memberName": "_values", "nodeType": "MemberAccess", "referencedDeclaration": 40872, "src": "3977:11:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "id": 40986, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3989:3:28", "memberName": "pop", "nodeType": "MemberAccess", "src": "3977:15:28", "typeDescriptions": {"typeIdentifier": "t_function_arraypop_nonpayable$_t_array$_t_bytes32_$dyn_storage_ptr_$returns$__$attached_to$_t_array$_t_bytes32_$dyn_storage_ptr_$", "typeString": "function (bytes32[] storage pointer)"}}, "id": 40987, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3977:17:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40988, "nodeType": "ExpressionStatement", "src": "3977:17:28"}, {"expression": {"id": 40993, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "delete", "prefix": true, "src": "4062:26:28", "subExpression": {"baseExpression": {"expression": {"id": 40989, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40923, "src": "4069:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 40990, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4073:8:28", "memberName": "_indexes", "nodeType": "MemberAccess", "referencedDeclaration": 40876, "src": "4069:12:28", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_uint256_$", "typeString": "mapping(bytes32 => uint256)"}}, "id": 40992, "indexExpression": {"id": 40991, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40925, "src": "4082:5:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "4069:19:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40994, "nodeType": "ExpressionStatement", "src": "4062:26:28"}, {"expression": {"hexValue": "74727565", "id": 40995, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4110:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 40929, "id": 40996, "nodeType": "Return", "src": "4103:11:28"}]}}]}, "documentation": {"id": 40920, "nodeType": "StructuredDocumentation", "src": "2624:157:28", "text": " @dev Removes a value from a set. O(1).\n Returns true if the value was removed from the set, that is if it was\n present."}, "implemented": true, "kind": "function", "modifiers": [], "name": "_remove", "nameLocation": "2795:7:28", "parameters": {"id": 40926, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40923, "mutability": "mutable", "name": "set", "nameLocation": "2815:3:28", "nodeType": "VariableDeclaration", "scope": 41003, "src": "2803:15:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}, "typeName": {"id": 40922, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 40921, "name": "Set", "nameLocations": ["2803:3:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 40877, "src": "2803:3:28"}, "referencedDeclaration": 40877, "src": "2803:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}}, "visibility": "internal"}, {"constant": false, "id": 40925, "mutability": "mutable", "name": "value", "nameLocation": "2828:5:28", "nodeType": "VariableDeclaration", "scope": 41003, "src": "2820:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 40924, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2820:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "2802:32:28"}, "returnParameters": {"id": 40929, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40928, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41003, "src": "2852:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 40927, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2852:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2851:6:28"}, "scope": 41479, "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"id": 41022, "nodeType": "FunctionDefinition", "src": "4255:127:28", "nodes": [], "body": {"id": 41021, "nodeType": "Block", "src": "4334:48:28", "nodes": [], "statements": [{"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 41019, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"baseExpression": {"expression": {"id": 41014, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41007, "src": "4351:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 41015, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4355:8:28", "memberName": "_indexes", "nodeType": "MemberAccess", "referencedDeclaration": 40876, "src": "4351:12:28", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_uint256_$", "typeString": "mapping(bytes32 => uint256)"}}, "id": 41017, "indexExpression": {"id": 41016, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41009, "src": "4364:5:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4351:19:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 41018, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4374:1:28", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4351:24:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 41013, "id": 41020, "nodeType": "Return", "src": "4344:31:28"}]}, "documentation": {"id": 41004, "nodeType": "StructuredDocumentation", "src": "4180:70:28", "text": " @dev Returns true if the value is in the set. O(1)."}, "implemented": true, "kind": "function", "modifiers": [], "name": "_contains", "nameLocation": "4264:9:28", "parameters": {"id": 41010, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41007, "mutability": "mutable", "name": "set", "nameLocation": "4286:3:28", "nodeType": "VariableDeclaration", "scope": 41022, "src": "4274:15:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}, "typeName": {"id": 41006, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41005, "name": "Set", "nameLocations": ["4274:3:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 40877, "src": "4274:3:28"}, "referencedDeclaration": 40877, "src": "4274:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}}, "visibility": "internal"}, {"constant": false, "id": 41009, "mutability": "mutable", "name": "value", "nameLocation": "4299:5:28", "nodeType": "VariableDeclaration", "scope": 41022, "src": "4291:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41008, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4291:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "4273:32:28"}, "returnParameters": {"id": 41013, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41012, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41022, "src": "4328:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41011, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4328:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "4327:6:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "private"}, {"id": 41036, "nodeType": "FunctionDefinition", "src": "4463:107:28", "nodes": [], "body": {"id": 41035, "nodeType": "Block", "src": "4528:42:28", "nodes": [], "statements": [{"expression": {"expression": {"expression": {"id": 41031, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41026, "src": "4545:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 41032, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4549:7:28", "memberName": "_values", "nodeType": "MemberAccess", "referencedDeclaration": 40872, "src": "4545:11:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "id": 41033, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4557:6:28", "memberName": "length", "nodeType": "MemberAccess", "src": "4545:18:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 41030, "id": 41034, "nodeType": "Return", "src": "4538:25:28"}]}, "documentation": {"id": 41023, "nodeType": "StructuredDocumentation", "src": "4388:70:28", "text": " @dev Returns the number of values on the set. O(1)."}, "implemented": true, "kind": "function", "modifiers": [], "name": "_length", "nameLocation": "4472:7:28", "parameters": {"id": 41027, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41026, "mutability": "mutable", "name": "set", "nameLocation": "4492:3:28", "nodeType": "VariableDeclaration", "scope": 41036, "src": "4480:15:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}, "typeName": {"id": 41025, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41024, "name": "Set", "nameLocations": ["4480:3:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 40877, "src": "4480:3:28"}, "referencedDeclaration": 40877, "src": "4480:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}}, "visibility": "internal"}], "src": "4479:17:28"}, "returnParameters": {"id": 41030, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41029, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41036, "src": "4519:7:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41028, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4519:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4518:9:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "private"}, {"id": 41053, "nodeType": "FunctionDefinition", "src": "4912:118:28", "nodes": [], "body": {"id": 41052, "nodeType": "Block", "src": "4988:42:28", "nodes": [], "statements": [{"expression": {"baseExpression": {"expression": {"id": 41047, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41040, "src": "5005:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 41048, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5009:7:28", "memberName": "_values", "nodeType": "MemberAccess", "referencedDeclaration": 40872, "src": "5005:11:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "id": 41050, "indexExpression": {"id": 41049, "name": "index", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41042, "src": "5017:5:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5005:18:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 41046, "id": 41051, "nodeType": "Return", "src": "4998:25:28"}]}, "documentation": {"id": 41037, "nodeType": "StructuredDocumentation", "src": "4576:331:28", "text": " @dev Returns the value stored at position `index` in the set. O(1).\n Note that there are no guarantees on the ordering of values inside the\n array, and it may change when more values are added or removed.\n Requirements:\n - `index` must be strictly less than {length}."}, "implemented": true, "kind": "function", "modifiers": [], "name": "_at", "nameLocation": "4921:3:28", "parameters": {"id": 41043, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41040, "mutability": "mutable", "name": "set", "nameLocation": "4937:3:28", "nodeType": "VariableDeclaration", "scope": 41053, "src": "4925:15:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}, "typeName": {"id": 41039, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41038, "name": "Set", "nameLocations": ["4925:3:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 40877, "src": "4925:3:28"}, "referencedDeclaration": 40877, "src": "4925:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}}, "visibility": "internal"}, {"constant": false, "id": 41042, "mutability": "mutable", "name": "index", "nameLocation": "4950:5:28", "nodeType": "VariableDeclaration", "scope": 41053, "src": "4942:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41041, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4942:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4924:32:28"}, "returnParameters": {"id": 41046, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41045, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41053, "src": "4979:7:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41044, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4979:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "4978:9:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "private"}, {"id": 41067, "nodeType": "FunctionDefinition", "src": "5570:109:28", "nodes": [], "body": {"id": 41066, "nodeType": "Block", "src": "5644:35:28", "nodes": [], "statements": [{"expression": {"expression": {"id": 41063, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41057, "src": "5661:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set storage pointer"}}, "id": 41064, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5665:7:28", "memberName": "_values", "nodeType": "MemberAccess", "referencedDeclaration": 40872, "src": "5661:11:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "functionReturnParameters": 41062, "id": 41065, "nodeType": "Return", "src": "5654:18:28"}]}, "documentation": {"id": 41054, "nodeType": "StructuredDocumentation", "src": "5036:529:28", "text": " @dev Return the entire set in an array\n WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\n to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\n this function has an unbounded cost, and using it as part of a state-changing function may render the function\n uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block."}, "implemented": true, "kind": "function", "modifiers": [], "name": "_values", "nameLocation": "5579:7:28", "parameters": {"id": 41058, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41057, "mutability": "mutable", "name": "set", "nameLocation": "5599:3:28", "nodeType": "VariableDeclaration", "scope": 41067, "src": "5587:15:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}, "typeName": {"id": 41056, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41055, "name": "Set", "nameLocations": ["5587:3:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 40877, "src": "5587:3:28"}, "referencedDeclaration": 40877, "src": "5587:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}}, "visibility": "internal"}], "src": "5586:17:28"}, "returnParameters": {"id": 41062, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41061, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41067, "src": "5626:16:28", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 41059, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5626:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 41060, "nodeType": "ArrayTypeName", "src": "5626:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "5625:18:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "private"}, {"id": 41071, "nodeType": "StructDefinition", "src": "5704:45:28", "nodes": [], "canonicalName": "EnumerableSet.Bytes32Set", "members": [{"constant": false, "id": 41070, "mutability": "mutable", "name": "_inner", "nameLocation": "5736:6:28", "nodeType": "VariableDeclaration", "scope": 41071, "src": "5732:10:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}, "typeName": {"id": 41069, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41068, "name": "Set", "nameLocations": ["5732:3:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 40877, "src": "5732:3:28"}, "referencedDeclaration": 40877, "src": "5732:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}}, "visibility": "internal"}], "name": "Bytes32Set", "nameLocation": "5711:10:28", "scope": 41479, "visibility": "public"}, {"id": 41089, "nodeType": "FunctionDefinition", "src": "5919:123:28", "nodes": [], "body": {"id": 41088, "nodeType": "Block", "src": "5995:47:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41083, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41075, "src": "6017:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set storage pointer"}}, "id": 41084, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6021:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41070, "src": "6017:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"id": 41085, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41077, "src": "6029:5:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41082, "name": "_add", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40919, "src": "6012:4:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_Set_$40877_storage_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (struct EnumerableSet.Set storage pointer,bytes32) returns (bool)"}}, "id": 41086, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6012:23:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 41081, "id": 41087, "nodeType": "Return", "src": "6005:30:28"}]}, "documentation": {"id": 41072, "nodeType": "StructuredDocumentation", "src": "5755:159:28", "text": " @dev Add a value to a set. O(1).\n Returns true if the value was added to the set, that is if it was not\n already present."}, "implemented": true, "kind": "function", "modifiers": [], "name": "add", "nameLocation": "5928:3:28", "parameters": {"id": 41078, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41075, "mutability": "mutable", "name": "set", "nameLocation": "5951:3:28", "nodeType": "VariableDeclaration", "scope": 41089, "src": "5932:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}, "typeName": {"id": 41074, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41073, "name": "Bytes32Set", "nameLocations": ["5932:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41071, "src": "5932:10:28"}, "referencedDeclaration": 41071, "src": "5932:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}}, "visibility": "internal"}, {"constant": false, "id": 41077, "mutability": "mutable", "name": "value", "nameLocation": "5964:5:28", "nodeType": "VariableDeclaration", "scope": 41089, "src": "5956:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41076, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5956:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "5931:39:28"}, "returnParameters": {"id": 41081, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41080, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41089, "src": "5989:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41079, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5989:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "5988:6:28"}, "scope": 41479, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 41107, "nodeType": "FunctionDefinition", "src": "6210:129:28", "nodes": [], "body": {"id": 41106, "nodeType": "Block", "src": "6289:50:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41101, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41093, "src": "6314:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set storage pointer"}}, "id": 41102, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6318:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41070, "src": "6314:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"id": 41103, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41095, "src": "6326:5:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41100, "name": "_remove", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41003, "src": "6306:7:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_Set_$40877_storage_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (struct EnumerableSet.Set storage pointer,bytes32) returns (bool)"}}, "id": 41104, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6306:26:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 41099, "id": 41105, "nodeType": "Return", "src": "6299:33:28"}]}, "documentation": {"id": 41090, "nodeType": "StructuredDocumentation", "src": "6048:157:28", "text": " @dev Removes a value from a set. O(1).\n Returns true if the value was removed from the set, that is if it was\n present."}, "implemented": true, "kind": "function", "modifiers": [], "name": "remove", "nameLocation": "6219:6:28", "parameters": {"id": 41096, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41093, "mutability": "mutable", "name": "set", "nameLocation": "6245:3:28", "nodeType": "VariableDeclaration", "scope": 41107, "src": "6226:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}, "typeName": {"id": 41092, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41091, "name": "Bytes32Set", "nameLocations": ["6226:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41071, "src": "6226:10:28"}, "referencedDeclaration": 41071, "src": "6226:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}}, "visibility": "internal"}, {"constant": false, "id": 41095, "mutability": "mutable", "name": "value", "nameLocation": "6258:5:28", "nodeType": "VariableDeclaration", "scope": 41107, "src": "6250:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41094, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6250:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "6225:39:28"}, "returnParameters": {"id": 41099, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41098, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41107, "src": "6283:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41097, "name": "bool", "nodeType": "ElementaryTypeName", "src": "6283:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "6282:6:28"}, "scope": 41479, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 41125, "nodeType": "FunctionDefinition", "src": "6420:138:28", "nodes": [], "body": {"id": 41124, "nodeType": "Block", "src": "6506:52:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41119, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41111, "src": "6533:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set storage pointer"}}, "id": 41120, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6537:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41070, "src": "6533:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"id": 41121, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41113, "src": "6545:5:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41118, "name": "_contains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41022, "src": "6523:9:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (struct EnumerableSet.Set storage pointer,bytes32) view returns (bool)"}}, "id": 41122, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6523:28:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 41117, "id": 41123, "nodeType": "Return", "src": "6516:35:28"}]}, "documentation": {"id": 41108, "nodeType": "StructuredDocumentation", "src": "6345:70:28", "text": " @dev Returns true if the value is in the set. O(1)."}, "implemented": true, "kind": "function", "modifiers": [], "name": "contains", "nameLocation": "6429:8:28", "parameters": {"id": 41114, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41111, "mutability": "mutable", "name": "set", "nameLocation": "6457:3:28", "nodeType": "VariableDeclaration", "scope": 41125, "src": "6438:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}, "typeName": {"id": 41110, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41109, "name": "Bytes32Set", "nameLocations": ["6438:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41071, "src": "6438:10:28"}, "referencedDeclaration": 41071, "src": "6438:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}}, "visibility": "internal"}, {"constant": false, "id": 41113, "mutability": "mutable", "name": "value", "nameLocation": "6470:5:28", "nodeType": "VariableDeclaration", "scope": 41125, "src": "6462:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41112, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6462:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "6437:39:28"}, "returnParameters": {"id": 41117, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41116, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41125, "src": "6500:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41115, "name": "bool", "nodeType": "ElementaryTypeName", "src": "6500:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "6499:6:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41140, "nodeType": "FunctionDefinition", "src": "6639:115:28", "nodes": [], "body": {"id": 41139, "nodeType": "Block", "src": "6711:43:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41135, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41129, "src": "6736:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set storage pointer"}}, "id": 41136, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6740:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41070, "src": "6736:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}], "id": 41134, "name": "_length", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41036, "src": "6728:7:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$returns$_t_uint256_$", "typeString": "function (struct EnumerableSet.Set storage pointer) view returns (uint256)"}}, "id": 41137, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6728:19:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 41133, "id": 41138, "nodeType": "Return", "src": "6721:26:28"}]}, "documentation": {"id": 41126, "nodeType": "StructuredDocumentation", "src": "6564:70:28", "text": " @dev Returns the number of values in the set. O(1)."}, "implemented": true, "kind": "function", "modifiers": [], "name": "length", "nameLocation": "6648:6:28", "parameters": {"id": 41130, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41129, "mutability": "mutable", "name": "set", "nameLocation": "6674:3:28", "nodeType": "VariableDeclaration", "scope": 41140, "src": "6655:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}, "typeName": {"id": 41128, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41127, "name": "Bytes32Set", "nameLocations": ["6655:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41071, "src": "6655:10:28"}, "referencedDeclaration": 41071, "src": "6655:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}}, "visibility": "internal"}], "src": "6654:24:28"}, "returnParameters": {"id": 41133, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41132, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41140, "src": "6702:7:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41131, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6702:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "6701:9:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41158, "nodeType": "FunctionDefinition", "src": "7096:129:28", "nodes": [], "body": {"id": 41157, "nodeType": "Block", "src": "7179:46:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41152, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41144, "src": "7200:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set storage pointer"}}, "id": 41153, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7204:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41070, "src": "7200:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"id": 41154, "name": "index", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41146, "src": "7212:5:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41151, "name": "_at", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41053, "src": "7196:3:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$_t_uint256_$returns$_t_bytes32_$", "typeString": "function (struct EnumerableSet.Set storage pointer,uint256) view returns (bytes32)"}}, "id": 41155, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7196:22:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 41150, "id": 41156, "nodeType": "Return", "src": "7189:29:28"}]}, "documentation": {"id": 41141, "nodeType": "StructuredDocumentation", "src": "6760:331:28", "text": " @dev Returns the value stored at position `index` in the set. O(1).\n Note that there are no guarantees on the ordering of values inside the\n array, and it may change when more values are added or removed.\n Requirements:\n - `index` must be strictly less than {length}."}, "implemented": true, "kind": "function", "modifiers": [], "name": "at", "nameLocation": "7105:2:28", "parameters": {"id": 41147, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41144, "mutability": "mutable", "name": "set", "nameLocation": "7127:3:28", "nodeType": "VariableDeclaration", "scope": 41158, "src": "7108:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}, "typeName": {"id": 41143, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41142, "name": "Bytes32Set", "nameLocations": ["7108:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41071, "src": "7108:10:28"}, "referencedDeclaration": 41071, "src": "7108:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}}, "visibility": "internal"}, {"constant": false, "id": 41146, "mutability": "mutable", "name": "index", "nameLocation": "7140:5:28", "nodeType": "VariableDeclaration", "scope": 41158, "src": "7132:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41145, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7132:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7107:39:28"}, "returnParameters": {"id": 41150, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41149, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41158, "src": "7170:7:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 41148, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "7170:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "7169:9:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41188, "nodeType": "FunctionDefinition", "src": "7765:300:28", "nodes": [], "body": {"id": 41187, "nodeType": "Block", "src": "7846:219:28", "nodes": [], "statements": [{"assignments": [41172], "declarations": [{"constant": false, "id": 41172, "mutability": "mutable", "name": "store", "nameLocation": "7873:5:28", "nodeType": "VariableDeclaration", "scope": 41187, "src": "7856:22:28", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 41170, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "7856:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 41171, "nodeType": "ArrayTypeName", "src": "7856:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "id": 41177, "initialValue": {"arguments": [{"expression": {"id": 41174, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41162, "src": "7889:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set storage pointer"}}, "id": 41175, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7893:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41070, "src": "7889:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}], "id": 41173, "name": "_values", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41067, "src": "7881:7:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$returns$_t_array$_t_bytes32_$dyn_memory_ptr_$", "typeString": "function (struct EnumerableSet.Set storage pointer) view returns (bytes32[] memory)"}}, "id": 41176, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7881:19:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "7856:44:28"}, {"assignments": [41182], "declarations": [{"constant": false, "id": 41182, "mutability": "mutable", "name": "result", "nameLocation": "7927:6:28", "nodeType": "VariableDeclaration", "scope": 41187, "src": "7910:23:28", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 41180, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "7910:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 41181, "nodeType": "ArrayTypeName", "src": "7910:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "id": 41183, "nodeType": "VariableDeclarationStatement", "src": "7910:23:28"}, {"AST": {"nativeSrc": "7996:39:28", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7996:39:28", "statements": [{"nativeSrc": "8010:15:28", "nodeType": "YulAssignment", "src": "8010:15:28", "value": {"name": "store", "nativeSrc": "8020:5:28", "nodeType": "YulIdentifier", "src": "8020:5:28"}, "variableNames": [{"name": "result", "nativeSrc": "8010:6:28", "nodeType": "YulIdentifier", "src": "8010:6:28"}]}]}, "documentation": "@solidity memory-safe-assembly", "evmVersion": "prague", "externalReferences": [{"declaration": 41182, "isOffset": false, "isSlot": false, "src": "8010:6:28", "valueSize": 1}, {"declaration": 41172, "isOffset": false, "isSlot": false, "src": "8020:5:28", "valueSize": 1}], "id": 41184, "nodeType": "InlineAssembly", "src": "7987:48:28"}, {"expression": {"id": 41185, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41182, "src": "8052:6:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "functionReturnParameters": 41167, "id": 41186, "nodeType": "Return", "src": "8045:13:28"}]}, "documentation": {"id": 41159, "nodeType": "StructuredDocumentation", "src": "7231:529:28", "text": " @dev Return the entire set in an array\n WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\n to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\n this function has an unbounded cost, and using it as part of a state-changing function may render the function\n uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block."}, "implemented": true, "kind": "function", "modifiers": [], "name": "values", "nameLocation": "7774:6:28", "parameters": {"id": 41163, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41162, "mutability": "mutable", "name": "set", "nameLocation": "7800:3:28", "nodeType": "VariableDeclaration", "scope": 41188, "src": "7781:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}, "typeName": {"id": 41161, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41160, "name": "Bytes32Set", "nameLocations": ["7781:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41071, "src": "7781:10:28"}, "referencedDeclaration": 41071, "src": "7781:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Bytes32Set_$41071_storage_ptr", "typeString": "struct EnumerableSet.Bytes32Set"}}, "visibility": "internal"}], "src": "7780:24:28"}, "returnParameters": {"id": 41167, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41166, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41188, "src": "7828:16:28", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 41164, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "7828:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 41165, "nodeType": "ArrayTypeName", "src": "7828:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "7827:18:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41192, "nodeType": "StructDefinition", "src": "8090:45:28", "nodes": [], "canonicalName": "EnumerableSet.AddressSet", "members": [{"constant": false, "id": 41191, "mutability": "mutable", "name": "_inner", "nameLocation": "8122:6:28", "nodeType": "VariableDeclaration", "scope": 41192, "src": "8118:10:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}, "typeName": {"id": 41190, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41189, "name": "Set", "nameLocations": ["8118:3:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 40877, "src": "8118:3:28"}, "referencedDeclaration": 40877, "src": "8118:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}}, "visibility": "internal"}], "name": "AddressSet", "nameLocation": "8097:10:28", "scope": 41479, "visibility": "public"}, {"id": 41219, "nodeType": "FunctionDefinition", "src": "8305:150:28", "nodes": [], "body": {"id": 41218, "nodeType": "Block", "src": "8381:74:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41204, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41196, "src": "8403:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet storage pointer"}}, "id": 41205, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "8407:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41191, "src": "8403:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"arguments": [{"arguments": [{"arguments": [{"id": 41212, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41198, "src": "8439:5:28", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 41211, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8431:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 41210, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "8431:7:28", "typeDescriptions": {}}}, "id": 41213, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8431:14:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 41209, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8423:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 41208, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8423:7:28", "typeDescriptions": {}}}, "id": 41214, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8423:23:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41207, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8415:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes32_$", "typeString": "type(bytes32)"}, "typeName": {"id": 41206, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8415:7:28", "typeDescriptions": {}}}, "id": 41215, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8415:32:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41203, "name": "_add", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40919, "src": "8398:4:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_Set_$40877_storage_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (struct EnumerableSet.Set storage pointer,bytes32) returns (bool)"}}, "id": 41216, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8398:50:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 41202, "id": 41217, "nodeType": "Return", "src": "8391:57:28"}]}, "documentation": {"id": 41193, "nodeType": "StructuredDocumentation", "src": "8141:159:28", "text": " @dev Add a value to a set. O(1).\n Returns true if the value was added to the set, that is if it was not\n already present."}, "implemented": true, "kind": "function", "modifiers": [], "name": "add", "nameLocation": "8314:3:28", "parameters": {"id": 41199, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41196, "mutability": "mutable", "name": "set", "nameLocation": "8337:3:28", "nodeType": "VariableDeclaration", "scope": 41219, "src": "8318:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}, "typeName": {"id": 41195, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41194, "name": "AddressSet", "nameLocations": ["8318:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41192, "src": "8318:10:28"}, "referencedDeclaration": 41192, "src": "8318:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}}, "visibility": "internal"}, {"constant": false, "id": 41198, "mutability": "mutable", "name": "value", "nameLocation": "8350:5:28", "nodeType": "VariableDeclaration", "scope": 41219, "src": "8342:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41197, "name": "address", "nodeType": "ElementaryTypeName", "src": "8342:7:28", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "8317:39:28"}, "returnParameters": {"id": 41202, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41201, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41219, "src": "8375:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41200, "name": "bool", "nodeType": "ElementaryTypeName", "src": "8375:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "8374:6:28"}, "scope": 41479, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 41246, "nodeType": "FunctionDefinition", "src": "8623:156:28", "nodes": [], "body": {"id": 41245, "nodeType": "Block", "src": "8702:77:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41231, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41223, "src": "8727:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet storage pointer"}}, "id": 41232, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "8731:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41191, "src": "8727:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"arguments": [{"arguments": [{"arguments": [{"id": 41239, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41225, "src": "8763:5:28", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 41238, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8755:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 41237, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "8755:7:28", "typeDescriptions": {}}}, "id": 41240, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8755:14:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 41236, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8747:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 41235, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8747:7:28", "typeDescriptions": {}}}, "id": 41241, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8747:23:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41234, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8739:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes32_$", "typeString": "type(bytes32)"}, "typeName": {"id": 41233, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8739:7:28", "typeDescriptions": {}}}, "id": 41242, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8739:32:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41230, "name": "_remove", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41003, "src": "8719:7:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_Set_$40877_storage_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (struct EnumerableSet.Set storage pointer,bytes32) returns (bool)"}}, "id": 41243, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8719:53:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 41229, "id": 41244, "nodeType": "Return", "src": "8712:60:28"}]}, "documentation": {"id": 41220, "nodeType": "StructuredDocumentation", "src": "8461:157:28", "text": " @dev Removes a value from a set. O(1).\n Returns true if the value was removed from the set, that is if it was\n present."}, "implemented": true, "kind": "function", "modifiers": [], "name": "remove", "nameLocation": "8632:6:28", "parameters": {"id": 41226, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41223, "mutability": "mutable", "name": "set", "nameLocation": "8658:3:28", "nodeType": "VariableDeclaration", "scope": 41246, "src": "8639:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}, "typeName": {"id": 41222, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41221, "name": "AddressSet", "nameLocations": ["8639:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41192, "src": "8639:10:28"}, "referencedDeclaration": 41192, "src": "8639:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}}, "visibility": "internal"}, {"constant": false, "id": 41225, "mutability": "mutable", "name": "value", "nameLocation": "8671:5:28", "nodeType": "VariableDeclaration", "scope": 41246, "src": "8663:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41224, "name": "address", "nodeType": "ElementaryTypeName", "src": "8663:7:28", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "8638:39:28"}, "returnParameters": {"id": 41229, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41228, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41246, "src": "8696:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41227, "name": "bool", "nodeType": "ElementaryTypeName", "src": "8696:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "8695:6:28"}, "scope": 41479, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 41273, "nodeType": "FunctionDefinition", "src": "8860:165:28", "nodes": [], "body": {"id": 41272, "nodeType": "Block", "src": "8946:79:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41258, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41250, "src": "8973:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet storage pointer"}}, "id": 41259, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "8977:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41191, "src": "8973:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"arguments": [{"arguments": [{"arguments": [{"id": 41266, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41252, "src": "9009:5:28", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 41265, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "9001:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 41264, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "9001:7:28", "typeDescriptions": {}}}, "id": 41267, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9001:14:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 41263, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8993:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 41262, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8993:7:28", "typeDescriptions": {}}}, "id": 41268, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8993:23:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41261, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8985:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes32_$", "typeString": "type(bytes32)"}, "typeName": {"id": 41260, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8985:7:28", "typeDescriptions": {}}}, "id": 41269, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8985:32:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41257, "name": "_contains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41022, "src": "8963:9:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (struct EnumerableSet.Set storage pointer,bytes32) view returns (bool)"}}, "id": 41270, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8963:55:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 41256, "id": 41271, "nodeType": "Return", "src": "8956:62:28"}]}, "documentation": {"id": 41247, "nodeType": "StructuredDocumentation", "src": "8785:70:28", "text": " @dev Returns true if the value is in the set. O(1)."}, "implemented": true, "kind": "function", "modifiers": [], "name": "contains", "nameLocation": "8869:8:28", "parameters": {"id": 41253, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41250, "mutability": "mutable", "name": "set", "nameLocation": "8897:3:28", "nodeType": "VariableDeclaration", "scope": 41273, "src": "8878:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}, "typeName": {"id": 41249, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41248, "name": "AddressSet", "nameLocations": ["8878:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41192, "src": "8878:10:28"}, "referencedDeclaration": 41192, "src": "8878:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}}, "visibility": "internal"}, {"constant": false, "id": 41252, "mutability": "mutable", "name": "value", "nameLocation": "8910:5:28", "nodeType": "VariableDeclaration", "scope": 41273, "src": "8902:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41251, "name": "address", "nodeType": "ElementaryTypeName", "src": "8902:7:28", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "8877:39:28"}, "returnParameters": {"id": 41256, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41255, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41273, "src": "8940:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41254, "name": "bool", "nodeType": "ElementaryTypeName", "src": "8940:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "8939:6:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41288, "nodeType": "FunctionDefinition", "src": "9106:115:28", "nodes": [], "body": {"id": 41287, "nodeType": "Block", "src": "9178:43:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41283, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41277, "src": "9203:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet storage pointer"}}, "id": 41284, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "9207:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41191, "src": "9203:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}], "id": 41282, "name": "_length", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41036, "src": "9195:7:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$returns$_t_uint256_$", "typeString": "function (struct EnumerableSet.Set storage pointer) view returns (uint256)"}}, "id": 41285, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9195:19:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 41281, "id": 41286, "nodeType": "Return", "src": "9188:26:28"}]}, "documentation": {"id": 41274, "nodeType": "StructuredDocumentation", "src": "9031:70:28", "text": " @dev Returns the number of values in the set. O(1)."}, "implemented": true, "kind": "function", "modifiers": [], "name": "length", "nameLocation": "9115:6:28", "parameters": {"id": 41278, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41277, "mutability": "mutable", "name": "set", "nameLocation": "9141:3:28", "nodeType": "VariableDeclaration", "scope": 41288, "src": "9122:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}, "typeName": {"id": 41276, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41275, "name": "AddressSet", "nameLocations": ["9122:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41192, "src": "9122:10:28"}, "referencedDeclaration": 41192, "src": "9122:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}}, "visibility": "internal"}], "src": "9121:24:28"}, "returnParameters": {"id": 41281, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41280, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41288, "src": "9169:7:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41279, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "9169:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "9168:9:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41315, "nodeType": "FunctionDefinition", "src": "9563:156:28", "nodes": [], "body": {"id": 41314, "nodeType": "Block", "src": "9646:73:28", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"arguments": [{"arguments": [{"expression": {"id": 41306, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41292, "src": "9691:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet storage pointer"}}, "id": 41307, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "9695:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41191, "src": "9691:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"id": 41308, "name": "index", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41294, "src": "9703:5:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41305, "name": "_at", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41053, "src": "9687:3:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$_t_uint256_$returns$_t_bytes32_$", "typeString": "function (struct EnumerableSet.Set storage pointer,uint256) view returns (bytes32)"}}, "id": 41309, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9687:22:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41304, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "9679:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 41303, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "9679:7:28", "typeDescriptions": {}}}, "id": 41310, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9679:31:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41302, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "9671:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 41301, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "9671:7:28", "typeDescriptions": {}}}, "id": 41311, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9671:40:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 41300, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "9663:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 41299, "name": "address", "nodeType": "ElementaryTypeName", "src": "9663:7:28", "typeDescriptions": {}}}, "id": 41312, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9663:49:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 41298, "id": 41313, "nodeType": "Return", "src": "9656:56:28"}]}, "documentation": {"id": 41289, "nodeType": "StructuredDocumentation", "src": "9227:331:28", "text": " @dev Returns the value stored at position `index` in the set. O(1).\n Note that there are no guarantees on the ordering of values inside the\n array, and it may change when more values are added or removed.\n Requirements:\n - `index` must be strictly less than {length}."}, "implemented": true, "kind": "function", "modifiers": [], "name": "at", "nameLocation": "9572:2:28", "parameters": {"id": 41295, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41292, "mutability": "mutable", "name": "set", "nameLocation": "9594:3:28", "nodeType": "VariableDeclaration", "scope": 41315, "src": "9575:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}, "typeName": {"id": 41291, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41290, "name": "AddressSet", "nameLocations": ["9575:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41192, "src": "9575:10:28"}, "referencedDeclaration": 41192, "src": "9575:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}}, "visibility": "internal"}, {"constant": false, "id": 41294, "mutability": "mutable", "name": "index", "nameLocation": "9607:5:28", "nodeType": "VariableDeclaration", "scope": 41315, "src": "9599:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41293, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "9599:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "9574:39:28"}, "returnParameters": {"id": 41298, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41297, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41315, "src": "9637:7:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 41296, "name": "address", "nodeType": "ElementaryTypeName", "src": "9637:7:28", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "9636:9:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41345, "nodeType": "FunctionDefinition", "src": "10259:300:28", "nodes": [], "body": {"id": 41344, "nodeType": "Block", "src": "10340:219:28", "nodes": [], "statements": [{"assignments": [41329], "declarations": [{"constant": false, "id": 41329, "mutability": "mutable", "name": "store", "nameLocation": "10367:5:28", "nodeType": "VariableDeclaration", "scope": 41344, "src": "10350:22:28", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 41327, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "10350:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 41328, "nodeType": "ArrayTypeName", "src": "10350:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "id": 41334, "initialValue": {"arguments": [{"expression": {"id": 41331, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41319, "src": "10383:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet storage pointer"}}, "id": 41332, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "10387:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41191, "src": "10383:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}], "id": 41330, "name": "_values", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41067, "src": "10375:7:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$returns$_t_array$_t_bytes32_$dyn_memory_ptr_$", "typeString": "function (struct EnumerableSet.Set storage pointer) view returns (bytes32[] memory)"}}, "id": 41333, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10375:19:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "10350:44:28"}, {"assignments": [41339], "declarations": [{"constant": false, "id": 41339, "mutability": "mutable", "name": "result", "nameLocation": "10421:6:28", "nodeType": "VariableDeclaration", "scope": 41344, "src": "10404:23:28", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 41337, "name": "address", "nodeType": "ElementaryTypeName", "src": "10404:7:28", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 41338, "nodeType": "ArrayTypeName", "src": "10404:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "id": 41340, "nodeType": "VariableDeclarationStatement", "src": "10404:23:28"}, {"AST": {"nativeSrc": "10490:39:28", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "10490:39:28", "statements": [{"nativeSrc": "10504:15:28", "nodeType": "YulAssignment", "src": "10504:15:28", "value": {"name": "store", "nativeSrc": "10514:5:28", "nodeType": "YulIdentifier", "src": "10514:5:28"}, "variableNames": [{"name": "result", "nativeSrc": "10504:6:28", "nodeType": "YulIdentifier", "src": "10504:6:28"}]}]}, "documentation": "@solidity memory-safe-assembly", "evmVersion": "prague", "externalReferences": [{"declaration": 41339, "isOffset": false, "isSlot": false, "src": "10504:6:28", "valueSize": 1}, {"declaration": 41329, "isOffset": false, "isSlot": false, "src": "10514:5:28", "valueSize": 1}], "id": 41341, "nodeType": "InlineAssembly", "src": "10481:48:28"}, {"expression": {"id": 41342, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41339, "src": "10546:6:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "functionReturnParameters": 41324, "id": 41343, "nodeType": "Return", "src": "10539:13:28"}]}, "documentation": {"id": 41316, "nodeType": "StructuredDocumentation", "src": "9725:529:28", "text": " @dev Return the entire set in an array\n WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\n to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\n this function has an unbounded cost, and using it as part of a state-changing function may render the function\n uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block."}, "implemented": true, "kind": "function", "modifiers": [], "name": "values", "nameLocation": "10268:6:28", "parameters": {"id": 41320, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41319, "mutability": "mutable", "name": "set", "nameLocation": "10294:3:28", "nodeType": "VariableDeclaration", "scope": 41345, "src": "10275:22:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}, "typeName": {"id": 41318, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41317, "name": "AddressSet", "nameLocations": ["10275:10:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41192, "src": "10275:10:28"}, "referencedDeclaration": 41192, "src": "10275:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}}, "visibility": "internal"}], "src": "10274:24:28"}, "returnParameters": {"id": 41324, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41323, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41345, "src": "10322:16:28", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 41321, "name": "address", "nodeType": "ElementaryTypeName", "src": "10322:7:28", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 41322, "nodeType": "ArrayTypeName", "src": "10322:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "10321:18:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41349, "nodeType": "StructDefinition", "src": "10581:42:28", "nodes": [], "canonicalName": "EnumerableSet.UintSet", "members": [{"constant": false, "id": 41348, "mutability": "mutable", "name": "_inner", "nameLocation": "10610:6:28", "nodeType": "VariableDeclaration", "scope": 41349, "src": "10606:10:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}, "typeName": {"id": 41347, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41346, "name": "Set", "nameLocations": ["10606:3:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 40877, "src": "10606:3:28"}, "referencedDeclaration": 40877, "src": "10606:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage_ptr", "typeString": "struct EnumerableSet.Set"}}, "visibility": "internal"}], "name": "UintSet", "nameLocation": "10588:7:28", "scope": 41479, "visibility": "public"}, {"id": 41370, "nodeType": "FunctionDefinition", "src": "10793:129:28", "nodes": [], "body": {"id": 41369, "nodeType": "Block", "src": "10866:56:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41361, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41353, "src": "10888:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet storage pointer"}}, "id": 41362, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "10892:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41348, "src": "10888:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"arguments": [{"id": 41365, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41355, "src": "10908:5:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41364, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "10900:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes32_$", "typeString": "type(bytes32)"}, "typeName": {"id": 41363, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "10900:7:28", "typeDescriptions": {}}}, "id": 41366, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10900:14:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41360, "name": "_add", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40919, "src": "10883:4:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_Set_$40877_storage_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (struct EnumerableSet.Set storage pointer,bytes32) returns (bool)"}}, "id": 41367, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10883:32:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 41359, "id": 41368, "nodeType": "Return", "src": "10876:39:28"}]}, "documentation": {"id": 41350, "nodeType": "StructuredDocumentation", "src": "10629:159:28", "text": " @dev Add a value to a set. O(1).\n Returns true if the value was added to the set, that is if it was not\n already present."}, "implemented": true, "kind": "function", "modifiers": [], "name": "add", "nameLocation": "10802:3:28", "parameters": {"id": 41356, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41353, "mutability": "mutable", "name": "set", "nameLocation": "10822:3:28", "nodeType": "VariableDeclaration", "scope": 41370, "src": "10806:19:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}, "typeName": {"id": 41352, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41351, "name": "UintSet", "nameLocations": ["10806:7:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41349, "src": "10806:7:28"}, "referencedDeclaration": 41349, "src": "10806:7:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}}, "visibility": "internal"}, {"constant": false, "id": 41355, "mutability": "mutable", "name": "value", "nameLocation": "10835:5:28", "nodeType": "VariableDeclaration", "scope": 41370, "src": "10827:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41354, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "10827:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "10805:36:28"}, "returnParameters": {"id": 41359, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41358, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41370, "src": "10860:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41357, "name": "bool", "nodeType": "ElementaryTypeName", "src": "10860:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "10859:6:28"}, "scope": 41479, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 41391, "nodeType": "FunctionDefinition", "src": "11090:135:28", "nodes": [], "body": {"id": 41390, "nodeType": "Block", "src": "11166:59:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41382, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41374, "src": "11191:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet storage pointer"}}, "id": 41383, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "11195:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41348, "src": "11191:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"arguments": [{"id": 41386, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41376, "src": "11211:5:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41385, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "11203:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes32_$", "typeString": "type(bytes32)"}, "typeName": {"id": 41384, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "11203:7:28", "typeDescriptions": {}}}, "id": 41387, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11203:14:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41381, "name": "_remove", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41003, "src": "11183:7:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_Set_$40877_storage_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (struct EnumerableSet.Set storage pointer,bytes32) returns (bool)"}}, "id": 41388, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11183:35:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 41380, "id": 41389, "nodeType": "Return", "src": "11176:42:28"}]}, "documentation": {"id": 41371, "nodeType": "StructuredDocumentation", "src": "10928:157:28", "text": " @dev Removes a value from a set. O(1).\n Returns true if the value was removed from the set, that is if it was\n present."}, "implemented": true, "kind": "function", "modifiers": [], "name": "remove", "nameLocation": "11099:6:28", "parameters": {"id": 41377, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41374, "mutability": "mutable", "name": "set", "nameLocation": "11122:3:28", "nodeType": "VariableDeclaration", "scope": 41391, "src": "11106:19:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}, "typeName": {"id": 41373, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41372, "name": "UintSet", "nameLocations": ["11106:7:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41349, "src": "11106:7:28"}, "referencedDeclaration": 41349, "src": "11106:7:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}}, "visibility": "internal"}, {"constant": false, "id": 41376, "mutability": "mutable", "name": "value", "nameLocation": "11135:5:28", "nodeType": "VariableDeclaration", "scope": 41391, "src": "11127:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41375, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "11127:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "11105:36:28"}, "returnParameters": {"id": 41380, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41379, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41391, "src": "11160:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41378, "name": "bool", "nodeType": "ElementaryTypeName", "src": "11160:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "11159:6:28"}, "scope": 41479, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 41412, "nodeType": "FunctionDefinition", "src": "11306:144:28", "nodes": [], "body": {"id": 41411, "nodeType": "Block", "src": "11389:61:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41403, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41395, "src": "11416:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet storage pointer"}}, "id": 41404, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "11420:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41348, "src": "11416:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"arguments": [{"id": 41407, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41397, "src": "11436:5:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41406, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "11428:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes32_$", "typeString": "type(bytes32)"}, "typeName": {"id": 41405, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "11428:7:28", "typeDescriptions": {}}}, "id": 41408, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11428:14:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41402, "name": "_contains", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41022, "src": "11406:9:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (struct EnumerableSet.Set storage pointer,bytes32) view returns (bool)"}}, "id": 41409, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11406:37:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 41401, "id": 41410, "nodeType": "Return", "src": "11399:44:28"}]}, "documentation": {"id": 41392, "nodeType": "StructuredDocumentation", "src": "11231:70:28", "text": " @dev Returns true if the value is in the set. O(1)."}, "implemented": true, "kind": "function", "modifiers": [], "name": "contains", "nameLocation": "11315:8:28", "parameters": {"id": 41398, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41395, "mutability": "mutable", "name": "set", "nameLocation": "11340:3:28", "nodeType": "VariableDeclaration", "scope": 41412, "src": "11324:19:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}, "typeName": {"id": 41394, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41393, "name": "UintSet", "nameLocations": ["11324:7:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41349, "src": "11324:7:28"}, "referencedDeclaration": 41349, "src": "11324:7:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}}, "visibility": "internal"}, {"constant": false, "id": 41397, "mutability": "mutable", "name": "value", "nameLocation": "11353:5:28", "nodeType": "VariableDeclaration", "scope": 41412, "src": "11345:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41396, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "11345:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "11323:36:28"}, "returnParameters": {"id": 41401, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41400, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41412, "src": "11383:4:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 41399, "name": "bool", "nodeType": "ElementaryTypeName", "src": "11383:4:28", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "11382:6:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41427, "nodeType": "FunctionDefinition", "src": "11531:112:28", "nodes": [], "body": {"id": 41426, "nodeType": "Block", "src": "11600:43:28", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 41422, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41416, "src": "11625:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet storage pointer"}}, "id": 41423, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "11629:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41348, "src": "11625:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}], "id": 41421, "name": "_length", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41036, "src": "11617:7:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$returns$_t_uint256_$", "typeString": "function (struct EnumerableSet.Set storage pointer) view returns (uint256)"}}, "id": 41424, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "11617:19:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 41420, "id": 41425, "nodeType": "Return", "src": "11610:26:28"}]}, "documentation": {"id": 41413, "nodeType": "StructuredDocumentation", "src": "11456:70:28", "text": " @dev Returns the number of values in the set. O(1)."}, "implemented": true, "kind": "function", "modifiers": [], "name": "length", "nameLocation": "11540:6:28", "parameters": {"id": 41417, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41416, "mutability": "mutable", "name": "set", "nameLocation": "11563:3:28", "nodeType": "VariableDeclaration", "scope": 41427, "src": "11547:19:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}, "typeName": {"id": 41415, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41414, "name": "UintSet", "nameLocations": ["11547:7:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41349, "src": "11547:7:28"}, "referencedDeclaration": 41349, "src": "11547:7:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}}, "visibility": "internal"}], "src": "11546:21:28"}, "returnParameters": {"id": 41420, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41419, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41427, "src": "11591:7:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41418, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "11591:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "11590:9:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41448, "nodeType": "FunctionDefinition", "src": "11985:135:28", "nodes": [], "body": {"id": 41447, "nodeType": "Block", "src": "12065:55:28", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"expression": {"id": 41441, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41431, "src": "12094:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet storage pointer"}}, "id": 41442, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "12098:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41348, "src": "12094:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}, {"id": 41443, "name": "index", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41433, "src": "12106:5:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 41440, "name": "_at", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41053, "src": "12090:3:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$_t_uint256_$returns$_t_bytes32_$", "typeString": "function (struct EnumerableSet.Set storage pointer,uint256) view returns (bytes32)"}}, "id": 41444, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12090:22:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 41439, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "12082:7:28", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 41438, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "12082:7:28", "typeDescriptions": {}}}, "id": 41445, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12082:31:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 41437, "id": 41446, "nodeType": "Return", "src": "12075:38:28"}]}, "documentation": {"id": 41428, "nodeType": "StructuredDocumentation", "src": "11649:331:28", "text": " @dev Returns the value stored at position `index` in the set. O(1).\n Note that there are no guarantees on the ordering of values inside the\n array, and it may change when more values are added or removed.\n Requirements:\n - `index` must be strictly less than {length}."}, "implemented": true, "kind": "function", "modifiers": [], "name": "at", "nameLocation": "11994:2:28", "parameters": {"id": 41434, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41431, "mutability": "mutable", "name": "set", "nameLocation": "12013:3:28", "nodeType": "VariableDeclaration", "scope": 41448, "src": "11997:19:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}, "typeName": {"id": 41430, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41429, "name": "UintSet", "nameLocations": ["11997:7:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41349, "src": "11997:7:28"}, "referencedDeclaration": 41349, "src": "11997:7:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}}, "visibility": "internal"}, {"constant": false, "id": 41433, "mutability": "mutable", "name": "index", "nameLocation": "12026:5:28", "nodeType": "VariableDeclaration", "scope": 41448, "src": "12018:13:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41432, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "12018:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "11996:36:28"}, "returnParameters": {"id": 41437, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41436, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41448, "src": "12056:7:28", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 41435, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "12056:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "12055:9:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 41478, "nodeType": "FunctionDefinition", "src": "12660:297:28", "nodes": [], "body": {"id": 41477, "nodeType": "Block", "src": "12738:219:28", "nodes": [], "statements": [{"assignments": [41462], "declarations": [{"constant": false, "id": 41462, "mutability": "mutable", "name": "store", "nameLocation": "12765:5:28", "nodeType": "VariableDeclaration", "scope": 41477, "src": "12748:22:28", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 41460, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "12748:7:28", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 41461, "nodeType": "ArrayTypeName", "src": "12748:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "id": 41467, "initialValue": {"arguments": [{"expression": {"id": 41464, "name": "set", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41452, "src": "12781:3:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet storage pointer"}}, "id": 41465, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "12785:6:28", "memberName": "_inner", "nodeType": "MemberAccess", "referencedDeclaration": 41348, "src": "12781:10:28", "typeDescriptions": {"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Set_$40877_storage", "typeString": "struct EnumerableSet.Set storage ref"}], "id": 41463, "name": "_values", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41067, "src": "12773:7:28", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Set_$40877_storage_ptr_$returns$_t_array$_t_bytes32_$dyn_memory_ptr_$", "typeString": "function (struct EnumerableSet.Set storage pointer) view returns (bytes32[] memory)"}}, "id": 41466, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "12773:19:28", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "12748:44:28"}, {"assignments": [41472], "declarations": [{"constant": false, "id": 41472, "mutability": "mutable", "name": "result", "nameLocation": "12819:6:28", "nodeType": "VariableDeclaration", "scope": 41477, "src": "12802:23:28", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 41470, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "12802:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41471, "nodeType": "ArrayTypeName", "src": "12802:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "id": 41473, "nodeType": "VariableDeclarationStatement", "src": "12802:23:28"}, {"AST": {"nativeSrc": "12888:39:28", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "12888:39:28", "statements": [{"nativeSrc": "12902:15:28", "nodeType": "YulAssignment", "src": "12902:15:28", "value": {"name": "store", "nativeSrc": "12912:5:28", "nodeType": "YulIdentifier", "src": "12912:5:28"}, "variableNames": [{"name": "result", "nativeSrc": "12902:6:28", "nodeType": "YulIdentifier", "src": "12902:6:28"}]}]}, "documentation": "@solidity memory-safe-assembly", "evmVersion": "prague", "externalReferences": [{"declaration": 41472, "isOffset": false, "isSlot": false, "src": "12902:6:28", "valueSize": 1}, {"declaration": 41462, "isOffset": false, "isSlot": false, "src": "12912:5:28", "valueSize": 1}], "id": 41474, "nodeType": "InlineAssembly", "src": "12879:48:28"}, {"expression": {"id": 41475, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41472, "src": "12944:6:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "functionReturnParameters": 41457, "id": 41476, "nodeType": "Return", "src": "12937:13:28"}]}, "documentation": {"id": 41449, "nodeType": "StructuredDocumentation", "src": "12126:529:28", "text": " @dev Return the entire set in an array\n WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\n to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\n this function has an unbounded cost, and using it as part of a state-changing function may render the function\n uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block."}, "implemented": true, "kind": "function", "modifiers": [], "name": "values", "nameLocation": "12669:6:28", "parameters": {"id": 41453, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41452, "mutability": "mutable", "name": "set", "nameLocation": "12692:3:28", "nodeType": "VariableDeclaration", "scope": 41478, "src": "12676:19:28", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}, "typeName": {"id": 41451, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 41450, "name": "UintSet", "nameLocations": ["12676:7:28"], "nodeType": "IdentifierPath", "referencedDeclaration": 41349, "src": "12676:7:28"}, "referencedDeclaration": 41349, "src": "12676:7:28", "typeDescriptions": {"typeIdentifier": "t_struct$_UintSet_$41349_storage_ptr", "typeString": "struct EnumerableSet.UintSet"}}, "visibility": "internal"}], "src": "12675:21:28"}, "returnParameters": {"id": 41457, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41456, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 41478, "src": "12720:16:28", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 41454, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "12720:7:28", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 41455, "nodeType": "ArrayTypeName", "src": "12720:9:28", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "12719:18:28"}, "scope": 41479, "stateMutability": "view", "virtual": false, "visibility": "internal"}], "abstract": false, "baseContracts": [], "canonicalName": "EnumerableSet", "contractDependencies": [], "contractKind": "library", "documentation": {"id": 40869, "nodeType": "StructuredDocumentation", "src": "230:1098:28", "text": " @dev Library for managing\n https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\n types.\n Sets have the following properties:\n - Elements are added, removed, and checked for existence in constant time\n (O(1)).\n - Elements are enumerated in O(n). No guarantees are made on the ordering.\n ```solidity\n contract Example {\n     // Add the library methods\n     using EnumerableSet for EnumerableSet.AddressSet;\n     // Declare a set state variable\n     EnumerableSet.AddressSet private mySet;\n }\n ```\n As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\n and `uint256` (`UintSet`) are supported.\n [WARNING]\n ====\n Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\n unusable.\n See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\n In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\n array of EnumerableSet.\n ===="}, "fullyImplemented": true, "linearizedBaseContracts": [41479], "name": "EnumerableSet", "nameLocation": "1337:13:28", "scope": 41480, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 28}