{"abi": [{"type": "function", "name": "activeFork", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "addr", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "addr", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "assume", "inputs": [{"name": "b", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "createFork", "inputs": [{"name": "url<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "deal", "inputs": [{"name": "usr", "type": "address", "internalType": "address"}, {"name": "amt", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "etch", "inputs": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "newRuntimeBytecode", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ffi", "inputs": [{"name": "inputs", "type": "string[]", "internalType": "string[]"}], "outputs": [{"name": "result", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "label", "inputs": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "label", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "load", "inputs": [{"name": "where", "type": "address", "internalType": "address"}, {"name": "slot", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "prank", "inputs": [{"name": "newSender", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "roll", "inputs": [{"name": "newNumber", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "selectFork", "inputs": [{"name": "forkId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "sign", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}, {"name": "digest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "startPrank", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stopPrank", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "store", "inputs": [{"name": "where", "type": "address", "internalType": "address"}, {"name": "slot", "type": "bytes32", "internalType": "bytes32"}, {"name": "value", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "warp", "inputs": [{"name": "newTimestamp", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"activeFork()": "2f103f22", "addr(uint256)": "ffa18649", "assume(bool)": "4c63e562", "createFork(string)": "31ba3498", "deal(address,uint256)": "c88a5e6d", "etch(address,bytes)": "b4d6c782", "ffi(string[])": "89160467", "label(address,string)": "c657c718", "load(address,bytes32)": "667f9d70", "prank(address)": "ca669fa7", "roll(uint256)": "1f7b4f30", "selectFork(uint256)": "9ebf6827", "sign(uint256,bytes32)": "e341eaa4", "startPrank(address)": "06447d56", "stopPrank()": "90c5013b", "store(address,bytes32,bytes32)": "70ca10bb", "warp(uint256)": "e5d6bf02"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"activeFork\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"name\":\"addr\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"b\",\"type\":\"bool\"}],\"name\":\"assume\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"url<PERSON>r<PERSON><PERSON><PERSON>\",\"type\":\"string\"}],\"name\":\"createFork\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"usr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amt\",\"type\":\"uint256\"}],\"name\":\"deal\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"newRuntimeBytecode\",\"type\":\"bytes\"}],\"name\":\"etch\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"inputs\",\"type\":\"string[]\"}],\"name\":\"ffi\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"result\",\"type\":\"bytes\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"label\",\"type\":\"string\"}],\"name\":\"label\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"slot\",\"type\":\"bytes32\"}],\"name\":\"load\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newSender\",\"type\":\"address\"}],\"name\":\"prank\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newNumber\",\"type\":\"uint256\"}],\"name\":\"roll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"forkId\",\"type\":\"uint256\"}],\"name\":\"selectFork\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"digest\",\"type\":\"bytes32\"}],\"name\":\"sign\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"startPrank\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"stopPrank\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"slot\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"value\",\"type\":\"bytes32\"}],\"name\":\"store\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newTimestamp\",\"type\":\"uint256\"}],\"name\":\"warp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"etch(address,bytes)\":{\"notice\":\"Sets an address' code.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/chimera/src/Hevm.sol\":\"IHevm\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/Hevm.sol\":{\"keccak256\":\"0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b\",\"dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "activeFork", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "addr", "outputs": [{"internalType": "address", "name": "addr", "type": "address"}]}, {"inputs": [{"internalType": "bool", "name": "b", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "assume"}, {"inputs": [{"internalType": "string", "name": "url<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "createFork", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "usr", "type": "address"}, {"internalType": "uint256", "name": "amt", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "deal"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "newRuntimeBytecode", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "etch"}, {"inputs": [{"internalType": "string[]", "name": "inputs", "type": "string[]"}], "stateMutability": "nonpayable", "type": "function", "name": "ffi", "outputs": [{"internalType": "bytes", "name": "result", "type": "bytes"}]}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string", "name": "label", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "label"}, {"inputs": [{"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "load", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "newSender", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "prank"}, {"inputs": [{"internalType": "uint256", "name": "newNumber", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "roll"}, {"inputs": [{"internalType": "uint256", "name": "forkId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "selectFork"}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}, {"internalType": "bytes32", "name": "digest", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "sign", "outputs": [{"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "startPrank"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "stopPrank"}, {"inputs": [{"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes32", "name": "slot", "type": "bytes32"}, {"internalType": "bytes32", "name": "value", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "store"}, {"inputs": [{"internalType": "uint256", "name": "newTimestamp", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "warp"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {"etch(address,bytes)": {"notice": "Sets an address' code."}}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/chimera/src/Hevm.sol": "IHevm"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/Hevm.sol": {"keccak256": "0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe", "urls": ["bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b", "dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/chimera/src/Hevm.sol", "id": 720, "exportedSymbols": {"IHevm": [713], "vm": [719]}, "nodeType": "SourceUnit", "src": "32:2334:6", "nodes": [{"id": 599, "nodeType": "PragmaDirective", "src": "32:23:6", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 713, "nodeType": "ContractDefinition", "src": "99:2154:6", "nodes": [{"id": 604, "nodeType": "FunctionDefinition", "src": "164:45:6", "nodes": [], "functionSelector": "e5d6bf02", "implemented": false, "kind": "function", "modifiers": [], "name": "warp", "nameLocation": "173:4:6", "parameters": {"id": 602, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 601, "mutability": "mutable", "name": "newTimestamp", "nameLocation": "186:12:6", "nodeType": "VariableDeclaration", "scope": 604, "src": "178:20:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 600, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "178:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "177:22:6"}, "returnParameters": {"id": 603, "nodeType": "ParameterList", "parameters": [], "src": "208:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 609, "nodeType": "FunctionDefinition", "src": "252:42:6", "nodes": [], "functionSelector": "1f7b4f30", "implemented": false, "kind": "function", "modifiers": [], "name": "roll", "nameLocation": "261:4:6", "parameters": {"id": 607, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 606, "mutability": "mutable", "name": "newNumber", "nameLocation": "274:9:6", "nodeType": "VariableDeclaration", "scope": 609, "src": "266:17:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 605, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "266:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "265:19:6"}, "returnParameters": {"id": 608, "nodeType": "ParameterList", "parameters": [], "src": "293:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 614, "nodeType": "FunctionDefinition", "src": "425:33:6", "nodes": [], "functionSelector": "4c63e562", "implemented": false, "kind": "function", "modifiers": [], "name": "assume", "nameLocation": "434:6:6", "parameters": {"id": 612, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 611, "mutability": "mutable", "name": "b", "nameLocation": "446:1:6", "nodeType": "VariableDeclaration", "scope": 614, "src": "441:6:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 610, "name": "bool", "nodeType": "ElementaryTypeName", "src": "441:4:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "440:8:6"}, "returnParameters": {"id": 613, "nodeType": "ParameterList", "parameters": [], "src": "457:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 621, "nodeType": "FunctionDefinition", "src": "506:49:6", "nodes": [], "functionSelector": "c88a5e6d", "implemented": false, "kind": "function", "modifiers": [], "name": "deal", "nameLocation": "515:4:6", "parameters": {"id": 619, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 616, "mutability": "mutable", "name": "usr", "nameLocation": "528:3:6", "nodeType": "VariableDeclaration", "scope": 621, "src": "520:11:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 615, "name": "address", "nodeType": "ElementaryTypeName", "src": "520:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 618, "mutability": "mutable", "name": "amt", "nameLocation": "541:3:6", "nodeType": "VariableDeclaration", "scope": 621, "src": "533:11:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 617, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "533:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "519:26:6"}, "returnParameters": {"id": 620, "nodeType": "ParameterList", "parameters": [], "src": "554:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 630, "nodeType": "FunctionDefinition", "src": "605:70:6", "nodes": [], "functionSelector": "667f9d70", "implemented": false, "kind": "function", "modifiers": [], "name": "load", "nameLocation": "614:4:6", "parameters": {"id": 626, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 623, "mutability": "mutable", "name": "where", "nameLocation": "627:5:6", "nodeType": "VariableDeclaration", "scope": 630, "src": "619:13:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 622, "name": "address", "nodeType": "ElementaryTypeName", "src": "619:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 625, "mutability": "mutable", "name": "slot", "nameLocation": "642:4:6", "nodeType": "VariableDeclaration", "scope": 630, "src": "634:12:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 624, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "634:7:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "618:29:6"}, "returnParameters": {"id": 629, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 628, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 630, "src": "666:7:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 627, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "666:7:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "665:9:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 639, "nodeType": "FunctionDefinition", "src": "731:68:6", "nodes": [], "functionSelector": "70ca10bb", "implemented": false, "kind": "function", "modifiers": [], "name": "store", "nameLocation": "740:5:6", "parameters": {"id": 637, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 632, "mutability": "mutable", "name": "where", "nameLocation": "754:5:6", "nodeType": "VariableDeclaration", "scope": 639, "src": "746:13:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 631, "name": "address", "nodeType": "ElementaryTypeName", "src": "746:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 634, "mutability": "mutable", "name": "slot", "nameLocation": "769:4:6", "nodeType": "VariableDeclaration", "scope": 639, "src": "761:12:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 633, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "761:7:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 636, "mutability": "mutable", "name": "value", "nameLocation": "783:5:6", "nodeType": "VariableDeclaration", "scope": 639, "src": "775:13:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 635, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "775:7:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "745:44:6"}, "returnParameters": {"id": 638, "nodeType": "ParameterList", "parameters": [], "src": "798:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 652, "nodeType": "FunctionDefinition", "src": "857:99:6", "nodes": [], "functionSelector": "e341eaa4", "implemented": false, "kind": "function", "modifiers": [], "name": "sign", "nameLocation": "866:4:6", "parameters": {"id": 644, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 641, "mutability": "mutable", "name": "privateKey", "nameLocation": "879:10:6", "nodeType": "VariableDeclaration", "scope": 652, "src": "871:18:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 640, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "871:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 643, "mutability": "mutable", "name": "digest", "nameLocation": "899:6:6", "nodeType": "VariableDeclaration", "scope": 652, "src": "891:14:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 642, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "891:7:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "870:36:6"}, "returnParameters": {"id": 651, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 646, "mutability": "mutable", "name": "v", "nameLocation": "931:1:6", "nodeType": "VariableDeclaration", "scope": 652, "src": "925:7:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 645, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "925:5:6", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}, {"constant": false, "id": 648, "mutability": "mutable", "name": "r", "nameLocation": "942:1:6", "nodeType": "VariableDeclaration", "scope": 652, "src": "934:9:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 647, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "934:7:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 650, "mutability": "mutable", "name": "s", "nameLocation": "953:1:6", "nodeType": "VariableDeclaration", "scope": 652, "src": "945:9:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 649, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "945:7:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "924:31:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 659, "nodeType": "FunctionDefinition", "src": "1006:66:6", "nodes": [], "functionSelector": "ffa18649", "implemented": false, "kind": "function", "modifiers": [], "name": "addr", "nameLocation": "1015:4:6", "parameters": {"id": 655, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 654, "mutability": "mutable", "name": "privateKey", "nameLocation": "1028:10:6", "nodeType": "VariableDeclaration", "scope": 659, "src": "1020:18:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 653, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1020:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1019:20:6"}, "returnParameters": {"id": 658, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 657, "mutability": "mutable", "name": "addr", "nameLocation": "1066:4:6", "nodeType": "VariableDeclaration", "scope": 659, "src": "1058:12:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 656, "name": "address", "nodeType": "ElementaryTypeName", "src": "1058:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1057:14:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 667, "nodeType": "FunctionDefinition", "src": "1131:78:6", "nodes": [], "functionSelector": "89160467", "implemented": false, "kind": "function", "modifiers": [], "name": "ffi", "nameLocation": "1140:3:6", "parameters": {"id": 663, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 662, "mutability": "mutable", "name": "inputs", "nameLocation": "1162:6:6", "nodeType": "VariableDeclaration", "scope": 667, "src": "1144:24:6", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_calldata_ptr_$dyn_calldata_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 660, "name": "string", "nodeType": "ElementaryTypeName", "src": "1144:6:6", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 661, "nodeType": "ArrayTypeName", "src": "1144:8:6", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "1143:26:6"}, "returnParameters": {"id": 666, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 665, "mutability": "mutable", "name": "result", "nameLocation": "1201:6:6", "nodeType": "VariableDeclaration", "scope": 667, "src": "1188:19:6", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 664, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1188:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "1187:21:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 672, "nodeType": "FunctionDefinition", "src": "1288:43:6", "nodes": [], "functionSelector": "ca669fa7", "implemented": false, "kind": "function", "modifiers": [], "name": "prank", "nameLocation": "1297:5:6", "parameters": {"id": 670, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 669, "mutability": "mutable", "name": "newSender", "nameLocation": "1311:9:6", "nodeType": "VariableDeclaration", "scope": 672, "src": "1303:17:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 668, "name": "address", "nodeType": "ElementaryTypeName", "src": "1303:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1302:19:6"}, "returnParameters": {"id": 671, "nodeType": "ParameterList", "parameters": [], "src": "1330:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 677, "nodeType": "FunctionDefinition", "src": "1459:45:6", "nodes": [], "functionSelector": "06447d56", "implemented": false, "kind": "function", "modifiers": [], "name": "startPrank", "nameLocation": "1468:10:6", "parameters": {"id": 675, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 674, "mutability": "mutable", "name": "sender", "nameLocation": "1487:6:6", "nodeType": "VariableDeclaration", "scope": 677, "src": "1479:14:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 673, "name": "address", "nodeType": "ElementaryTypeName", "src": "1479:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1478:16:6"}, "returnParameters": {"id": 676, "nodeType": "ParameterList", "parameters": [], "src": "1503:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 680, "nodeType": "FunctionDefinition", "src": "1557:30:6", "nodes": [], "functionSelector": "90c5013b", "implemented": false, "kind": "function", "modifiers": [], "name": "stopPrank", "nameLocation": "1566:9:6", "parameters": {"id": 678, "nodeType": "ParameterList", "parameters": [], "src": "1575:2:6"}, "returnParameters": {"id": 679, "nodeType": "ParameterList", "parameters": [], "src": "1586:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 687, "nodeType": "FunctionDefinition", "src": "1703:75:6", "nodes": [], "functionSelector": "31ba3498", "implemented": false, "kind": "function", "modifiers": [], "name": "createFork", "nameLocation": "1712:10:6", "parameters": {"id": 683, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 682, "mutability": "mutable", "name": "url<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocation": "1739:10:6", "nodeType": "VariableDeclaration", "scope": 687, "src": "1723:26:6", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string"}, "typeName": {"id": 681, "name": "string", "nodeType": "ElementaryTypeName", "src": "1723:6:6", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1722:28:6"}, "returnParameters": {"id": 686, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 685, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 687, "src": "1769:7:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 684, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1769:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1768:9:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 692, "nodeType": "FunctionDefinition", "src": "1887:45:6", "nodes": [], "functionSelector": "9ebf6827", "implemented": false, "kind": "function", "modifiers": [], "name": "selectFork", "nameLocation": "1896:10:6", "parameters": {"id": 690, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 689, "mutability": "mutable", "name": "forkId", "nameLocation": "1915:6:6", "nodeType": "VariableDeclaration", "scope": 692, "src": "1907:14:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 688, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1907:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1906:16:6"}, "returnParameters": {"id": 691, "nodeType": "ParameterList", "parameters": [], "src": "1931:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 697, "nodeType": "FunctionDefinition", "src": "1988:49:6", "nodes": [], "functionSelector": "2f103f22", "implemented": false, "kind": "function", "modifiers": [], "name": "activeFork", "nameLocation": "1997:10:6", "parameters": {"id": 693, "nodeType": "ParameterList", "parameters": [], "src": "2007:2:6"}, "returnParameters": {"id": 696, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 695, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 697, "src": "2028:7:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 694, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2028:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2027:9:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 704, "nodeType": "FunctionDefinition", "src": "2079:61:6", "nodes": [], "functionSelector": "c657c718", "implemented": false, "kind": "function", "modifiers": [], "name": "label", "nameLocation": "2088:5:6", "parameters": {"id": 702, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 699, "mutability": "mutable", "name": "addr", "nameLocation": "2102:4:6", "nodeType": "VariableDeclaration", "scope": 704, "src": "2094:12:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 698, "name": "address", "nodeType": "ElementaryTypeName", "src": "2094:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 701, "mutability": "mutable", "name": "label", "nameLocation": "2124:5:6", "nodeType": "VariableDeclaration", "scope": 704, "src": "2108:21:6", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string"}, "typeName": {"id": 700, "name": "string", "nodeType": "ElementaryTypeName", "src": "2108:6:6", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2093:37:6"}, "returnParameters": {"id": 703, "nodeType": "ParameterList", "parameters": [], "src": "2139:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 712, "nodeType": "FunctionDefinition", "src": "2177:74:6", "nodes": [], "documentation": {"id": 705, "nodeType": "StructuredDocumentation", "src": "2146:26:6", "text": "Sets an address' code."}, "functionSelector": "b4d6c782", "implemented": false, "kind": "function", "modifiers": [], "name": "etch", "nameLocation": "2186:4:6", "parameters": {"id": 710, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 707, "mutability": "mutable", "name": "target", "nameLocation": "2199:6:6", "nodeType": "VariableDeclaration", "scope": 712, "src": "2191:14:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 706, "name": "address", "nodeType": "ElementaryTypeName", "src": "2191:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 709, "mutability": "mutable", "name": "newRuntimeBytecode", "nameLocation": "2222:18:6", "nodeType": "VariableDeclaration", "scope": 712, "src": "2207:33:6", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 708, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "2207:5:6", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "2190:51:6"}, "returnParameters": {"id": 711, "nodeType": "ParameterList", "parameters": [], "src": "2250:0:6"}, "scope": 713, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IHevm", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "linearizedBaseContracts": [713], "name": "IHevm", "nameLocation": "109:5:6", "scope": 720, "usedErrors": [], "usedEvents": []}, {"id": 719, "nodeType": "VariableDeclaration", "src": "2255:69:6", "nodes": [], "constant": true, "mutability": "constant", "name": "vm", "nameLocation": "2270:2:6", "scope": 720, "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IHevm_$713", "typeString": "contract IHevm"}, "typeName": {"id": 715, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 714, "name": "IHevm", "nameLocations": ["2255:5:6"], "nodeType": "IdentifierPath", "referencedDeclaration": 713, "src": "2255:5:6"}, "referencedDeclaration": 713, "src": "2255:5:6", "typeDescriptions": {"typeIdentifier": "t_contract$_IHevm_$713", "typeString": "contract IHevm"}}, "value": {"arguments": [{"hexValue": "307837313039373039454366613931613830363236664633393839443638663637463562314444313244", "id": 717, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2281:42:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x7109709ECfa91a80626fF3989D68f67F5b1DD12D"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 716, "name": "IHevm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 713, "src": "2275:5:6", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IHevm_$713_$", "typeString": "type(contract IHevm)"}}, "id": 718, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2275:49:6", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IHevm_$713", "typeString": "contract IHevm"}}, "visibility": "internal"}], "license": "MIT"}, "id": 6}