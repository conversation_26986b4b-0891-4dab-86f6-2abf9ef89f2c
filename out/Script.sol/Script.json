{"abi": [{"type": "function", "name": "IS_SCRIPT", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"IS_SCRIPT()": "f8ccbf47"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IS_SCRIPT\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/Script.sol\":\"Script\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/Script.sol\":{\"keccak256\":\"0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98\",\"dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_SCRIPT", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/Script.sol": "<PERSON><PERSON><PERSON>"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/Script.sol": {"keccak256": "0xc942e27c7baae499beb01afbbae99f24d42af9a6e4aae675bc6901b704aa8e9b", "urls": ["bzz-raw://0456008adf68947247f358b62863af4a8e349549d2260f2ff9569ff0e3cf5c98", "dweb:/ipfs/QmdviSUj2i7o3TPN5vd2xocqGMFVqjUzaiJTZRYyPxyHPx"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 46, "contract": "lib/forge-std/src/Script.sol:Script", "label": "stdstore", "offset": 0, "slot": "0", "type": "t_struct(StdStorage)8331_storage"}, {"astId": 2983, "contract": "lib/forge-std/src/Script.sol:Script", "label": "stdChainsInitialized", "offset": 0, "slot": "8", "type": "t_bool"}, {"astId": 3004, "contract": "lib/forge-std/src/Script.sol:Script", "label": "chains", "offset": 0, "slot": "9", "type": "t_mapping(t_string_memory_ptr,t_struct(Chain)2999_storage)"}, {"astId": 3008, "contract": "lib/forge-std/src/Script.sol:Script", "label": "defaultRpcUrls", "offset": 0, "slot": "10", "type": "t_mapping(t_string_memory_ptr,t_string_storage)"}, {"astId": 3012, "contract": "lib/forge-std/src/Script.sol:Script", "label": "idToAlias", "offset": 0, "slot": "11", "type": "t_mapping(t_uint256,t_string_storage)"}, {"astId": 3015, "contract": "lib/forge-std/src/Script.sol:Script", "label": "fallbackToDefaultRpcUrls", "offset": 0, "slot": "12", "type": "t_bool"}, {"astId": 3953, "contract": "lib/forge-std/src/Script.sol:Script", "label": "gasMeteringOff", "offset": 1, "slot": "12", "type": "t_bool"}, {"astId": 99, "contract": "lib/forge-std/src/Script.sol:Script", "label": "IS_SCRIPT", "offset": 2, "slot": "12", "type": "t_bool"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_bytes4": {"encoding": "inplace", "label": "bytes4", "numberOfBytes": "4"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage)))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(bytes4 => mapping(bytes32 => struct FindData)))", "numberOfBytes": "32", "value": "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage))"}, "t_mapping(t_bytes32,t_struct(FindData)8306_storage)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => struct FindData)", "numberOfBytes": "32", "value": "t_struct(FindData)8306_storage"}, "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage))": {"encoding": "mapping", "key": "t_bytes4", "label": "mapping(bytes4 => mapping(bytes32 => struct FindData))", "numberOfBytes": "32", "value": "t_mapping(t_bytes32,t_struct(FindData)8306_storage)"}, "t_mapping(t_string_memory_ptr,t_string_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_mapping(t_string_memory_ptr,t_struct(Chain)2999_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => struct StdChains.Chain)", "numberOfBytes": "32", "value": "t_struct(Chain)2999_storage"}, "t_mapping(t_uint256,t_string_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_string_memory_ptr": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Chain)2999_storage": {"encoding": "inplace", "label": "struct StdChains.Chain", "numberOfBytes": "128", "members": [{"astId": 2992, "contract": "lib/forge-std/src/Script.sol:Script", "label": "name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 2994, "contract": "lib/forge-std/src/Script.sol:Script", "label": "chainId", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 2996, "contract": "lib/forge-std/src/Script.sol:Script", "label": "chainAlias", "offset": 0, "slot": "2", "type": "t_string_storage"}, {"astId": 2998, "contract": "lib/forge-std/src/Script.sol:Script", "label": "rpcUrl", "offset": 0, "slot": "3", "type": "t_string_storage"}]}, "t_struct(FindData)8306_storage": {"encoding": "inplace", "label": "struct FindData", "numberOfBytes": "128", "members": [{"astId": 8299, "contract": "lib/forge-std/src/Script.sol:Script", "label": "slot", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 8301, "contract": "lib/forge-std/src/Script.sol:Script", "label": "offsetLeft", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 8303, "contract": "lib/forge-std/src/Script.sol:Script", "label": "offsetRight", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 8305, "contract": "lib/forge-std/src/Script.sol:Script", "label": "found", "offset": 0, "slot": "3", "type": "t_bool"}]}, "t_struct(StdStorage)8331_storage": {"encoding": "inplace", "label": "struct StdStorage", "numberOfBytes": "256", "members": [{"astId": 8315, "contract": "lib/forge-std/src/Script.sol:Script", "label": "finds", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage)))"}, {"astId": 8318, "contract": "lib/forge-std/src/Script.sol:Script", "label": "_keys", "offset": 0, "slot": "1", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 8320, "contract": "lib/forge-std/src/Script.sol:Script", "label": "_sig", "offset": 0, "slot": "2", "type": "t_bytes4"}, {"astId": 8322, "contract": "lib/forge-std/src/Script.sol:Script", "label": "_depth", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 8324, "contract": "lib/forge-std/src/Script.sol:Script", "label": "_target", "offset": 0, "slot": "4", "type": "t_address"}, {"astId": 8326, "contract": "lib/forge-std/src/Script.sol:Script", "label": "_set", "offset": 0, "slot": "5", "type": "t_bytes32"}, {"astId": 8328, "contract": "lib/forge-std/src/Script.sol:Script", "label": "_enable_packed_slots", "offset": 0, "slot": "6", "type": "t_bool"}, {"astId": 8330, "contract": "lib/forge-std/src/Script.sol:Script", "label": "_calldata", "offset": 0, "slot": "7", "type": "t_bytes_storage"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "lib/forge-std/src/Script.sol", "id": 101, "exportedSymbols": {"Script": [100], "ScriptBase": [59], "StdChains": [3921], "StdCheatsSafe": [6011], "StdConstants": [6847], "StdStorage": [8331], "StdStyle": [11499], "StdUtils": [13170], "VmSafe": [17281], "console": [26468], "console2": [26468], "safeconsole": [39710], "stdJson": [8151], "stdMath": [8293], "stdStorageSafe": [9683]}, "nodeType": "SourceUnit", "src": "32:849:1", "nodes": [{"id": 61, "nodeType": "PragmaDirective", "src": "32:31:1", "nodes": [], "literals": ["solidity", ">=", "0.6", ".2", "<", "0.9", ".0"]}, {"id": 63, "nodeType": "ImportDirective", "src": "127:38:1", "nodes": [], "absolutePath": "lib/forge-std/src/console.sol", "file": "./console.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 26469, "symbolAliases": [{"foreign": {"id": 62, "name": "console", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26468, "src": "135:7:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 65, "nodeType": "ImportDirective", "src": "166:40:1", "nodes": [], "absolutePath": "lib/forge-std/src/console2.sol", "file": "./console2.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 26473, "symbolAliases": [{"foreign": {"id": 64, "name": "console2", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26468, "src": "174:8:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 67, "nodeType": "ImportDirective", "src": "207:46:1", "nodes": [], "absolutePath": "lib/forge-std/src/safeconsole.sol", "file": "./safeconsole.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 39711, "symbolAliases": [{"foreign": {"id": 66, "name": "safeconsole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39710, "src": "215:11:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 69, "nodeType": "ImportDirective", "src": "254:42:1", "nodes": [], "absolutePath": "lib/forge-std/src/StdChains.sol", "file": "./StdChains.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 3922, "symbolAliases": [{"foreign": {"id": 68, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3921, "src": "262:9:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 71, "nodeType": "ImportDirective", "src": "297:46:1", "nodes": [], "absolutePath": "lib/forge-std/src/StdCheats.sol", "file": "./StdCheats.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 6807, "symbolAliases": [{"foreign": {"id": 70, "name": "StdCheatsSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6011, "src": "305:13:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 73, "nodeType": "ImportDirective", "src": "344:48:1", "nodes": [], "absolutePath": "lib/forge-std/src/StdConstants.sol", "file": "./StdConstants.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 6848, "symbolAliases": [{"foreign": {"id": 72, "name": "StdConstants", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6847, "src": "352:12:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 75, "nodeType": "ImportDirective", "src": "393:38:1", "nodes": [], "absolutePath": "lib/forge-std/src/StdJson.sol", "file": "./StdJson.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 8152, "symbolAliases": [{"foreign": {"id": 74, "name": "stdJson", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8151, "src": "401:7:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 77, "nodeType": "ImportDirective", "src": "432:38:1", "nodes": [], "absolutePath": "lib/forge-std/src/StdMath.sol", "file": "./StdMath.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 8294, "symbolAliases": [{"foreign": {"id": 76, "name": "stdMath", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8293, "src": "440:7:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 80, "nodeType": "ImportDirective", "src": "471:60:1", "nodes": [], "absolutePath": "lib/forge-std/src/StdStorage.sol", "file": "./StdStorage.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 10289, "symbolAliases": [{"foreign": {"id": 78, "name": "StdStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8331, "src": "479:10:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}, {"foreign": {"id": 79, "name": "stdStorageSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 9683, "src": "491:14:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 82, "nodeType": "ImportDirective", "src": "532:40:1", "nodes": [], "absolutePath": "lib/forge-std/src/StdStyle.sol", "file": "./StdStyle.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 11500, "symbolAliases": [{"foreign": {"id": 81, "name": "StdStyle", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11499, "src": "540:8:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 84, "nodeType": "ImportDirective", "src": "573:40:1", "nodes": [], "absolutePath": "lib/forge-std/src/StdUtils.sol", "file": "./StdUtils.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 13171, "symbolAliases": [{"foreign": {"id": 83, "name": "StdUtils", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13170, "src": "581:8:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 86, "nodeType": "ImportDirective", "src": "614:32:1", "nodes": [], "absolutePath": "lib/forge-std/src/Vm.sol", "file": "./Vm.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 18353, "symbolAliases": [{"foreign": {"id": 85, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "622:6:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 88, "nodeType": "ImportDirective", "src": "668:38:1", "nodes": [], "absolutePath": "lib/forge-std/src/Base.sol", "file": "./Base.sol", "nameLocation": "-1:-1:-1", "scope": 101, "sourceUnit": 60, "symbolAliases": [{"foreign": {"id": 87, "name": "ScriptBase", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 59, "src": "676:10:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 100, "nodeType": "ContractDefinition", "src": "725:155:1", "nodes": [{"id": 99, "nodeType": "VariableDeclaration", "src": "849:28:1", "nodes": [], "constant": false, "functionSelector": "f8ccbf47", "mutability": "mutable", "name": "IS_SCRIPT", "nameLocation": "861:9:1", "scope": 100, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 97, "name": "bool", "nodeType": "ElementaryTypeName", "src": "849:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": {"hexValue": "74727565", "id": 98, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "873:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "visibility": "public"}], "abstract": true, "baseContracts": [{"baseName": {"id": 89, "name": "ScriptBase", "nameLocations": ["753:10:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 59, "src": "753:10:1"}, "id": 90, "nodeType": "InheritanceSpecifier", "src": "753:10:1"}, {"baseName": {"id": 91, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocations": ["765:9:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 3921, "src": "765:9:1"}, "id": 92, "nodeType": "InheritanceSpecifier", "src": "765:9:1"}, {"baseName": {"id": 93, "name": "StdCheatsSafe", "nameLocations": ["776:13:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 6011, "src": "776:13:1"}, "id": 94, "nodeType": "InheritanceSpecifier", "src": "776:13:1"}, {"baseName": {"id": 95, "name": "StdUtils", "nameLocations": ["791:8:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 13170, "src": "791:8:1"}, "id": 96, "nodeType": "InheritanceSpecifier", "src": "791:8:1"}], "canonicalName": "<PERSON><PERSON><PERSON>", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [100, 13170, 6011, 3921, 59, 47], "name": "<PERSON><PERSON><PERSON>", "nameLocation": "743:6:1", "scope": 101, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 1}