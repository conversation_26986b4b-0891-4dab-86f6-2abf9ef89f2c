{"abi": [], "bytecode": {"object": "0x6055604b600b8282823980515f1a607314603f577f4e487b71000000000000000000000000000000000000000000000000000000005f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea26469706673582212209358d85590e5bf0375b5e58512136d1fe0c97057e0836ffcaa374cd576f88c1964736f6c634300081d0033", "sourceMap": "65:1294:9:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea26469706673582212209358d85590e5bf0375b5e58512136d1fe0c97057e0836ffcaa374cd576f88c1964736f6c634300081d0033", "sourceMap": "65:1294:9:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdMath.sol\":\"stdMath\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdMath.sol": "stdMath"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/forge-std/src/StdMath.sol", "id": 8294, "exportedSymbols": {"stdMath": [8293]}, "nodeType": "SourceUnit", "src": "32:1328:9", "nodes": [{"id": 8153, "nodeType": "PragmaDirective", "src": "32:31:9", "nodes": [], "literals": ["solidity", ">=", "0.6", ".2", "<", "0.9", ".0"]}, {"id": 8293, "nodeType": "ContractDefinition", "src": "65:1294:9", "nodes": [{"id": 8157, "nodeType": "VariableDeclaration", "src": "87:115:9", "nodes": [], "constant": true, "mutability": "constant", "name": "INT256_MIN", "nameLocation": "111:10:9", "scope": 8293, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 8154, "name": "int256", "nodeType": "ElementaryTypeName", "src": "87:6:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "value": {"id": 8156, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "-", "prefix": true, "src": "124:78:9", "subExpression": {"hexValue": "3537383936303434363138363538303937373131373835343932353034333433393533393236363334393932333332383230323832303139373238373932303033393536353634383139393638", "id": 8155, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "125:77:9", "typeDescriptions": {"typeIdentifier": "t_rational_57896044618658097711785492504343953926634992332820282019728792003956564819968_by_1", "typeString": "int_const 5789...(69 digits omitted)...9968"}, "value": "57896044618658097711785492504343953926634992332820282019728792003956564819968"}, "typeDescriptions": {"typeIdentifier": "t_rational_minus_57896044618658097711785492504343953926634992332820282019728792003956564819968_by_1", "typeString": "int_const -578...(70 digits omitted)...9968"}}, "visibility": "private"}, {"id": 8183, "nodeType": "FunctionDefinition", "src": "209:306:9", "nodes": [], "body": {"id": 8182, "nodeType": "Block", "src": "264:251:9", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 8166, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 8164, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8159, "src": "342:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 8165, "name": "INT256_MIN", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8157, "src": "347:10:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "342:15:9", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 8170, "nodeType": "IfStatement", "src": "338:130:9", "trueBody": {"id": 8169, "nodeType": "Block", "src": "359:109:9", "statements": [{"expression": {"hexValue": "3537383936303434363138363538303937373131373835343932353034333433393533393236363334393932333332383230323832303139373238373932303033393536353634383139393638", "id": 8167, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "380:77:9", "typeDescriptions": {"typeIdentifier": "t_rational_57896044618658097711785492504343953926634992332820282019728792003956564819968_by_1", "typeString": "int_const 5789...(69 digits omitted)...9968"}, "value": "57896044618658097711785492504343953926634992332820282019728792003956564819968"}, "functionReturnParameters": 8163, "id": 8168, "nodeType": "Return", "src": "373:84:9"}]}}, {"expression": {"arguments": [{"condition": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 8175, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 8173, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8159, "src": "493:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 8174, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "497:1:9", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "493:5:9", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 8178, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "-", "prefix": true, "src": "505:2:9", "subExpression": {"id": 8177, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8159, "src": "506:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 8179, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "493:14:9", "trueExpression": {"id": 8176, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8159, "src": "501:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 8172, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "485:7:9", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 8171, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "485:7:9", "typeDescriptions": {}}}, "id": 8180, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "485:23:9", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 8163, "id": 8181, "nodeType": "Return", "src": "478:30:9"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "abs", "nameLocation": "218:3:9", "parameters": {"id": 8160, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8159, "mutability": "mutable", "name": "a", "nameLocation": "229:1:9", "nodeType": "VariableDeclaration", "scope": 8183, "src": "222:8:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 8158, "name": "int256", "nodeType": "ElementaryTypeName", "src": "222:6:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "221:10:9"}, "returnParameters": {"id": 8163, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8162, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8183, "src": "255:7:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8161, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "255:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "254:9:9"}, "scope": 8293, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 8204, "nodeType": "FunctionDefinition", "src": "521:114:9", "nodes": [], "body": {"id": 8203, "nodeType": "Block", "src": "590:45:9", "nodes": [], "statements": [{"expression": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 8194, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 8192, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8185, "src": "607:1:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 8193, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8187, "src": "611:1:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "607:5:9", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 8200, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 8198, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8187, "src": "623:1:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 8199, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8185, "src": "627:1:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "623:5:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 8201, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "607:21:9", "trueExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 8197, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 8195, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8185, "src": "615:1:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 8196, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8187, "src": "619:1:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "615:5:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 8191, "id": 8202, "nodeType": "Return", "src": "600:28:9"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "delta", "nameLocation": "530:5:9", "parameters": {"id": 8188, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8185, "mutability": "mutable", "name": "a", "nameLocation": "544:1:9", "nodeType": "VariableDeclaration", "scope": 8204, "src": "536:9:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8184, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "536:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 8187, "mutability": "mutable", "name": "b", "nameLocation": "555:1:9", "nodeType": "VariableDeclaration", "scope": 8204, "src": "547:9:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8186, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "547:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "535:22:9"}, "returnParameters": {"id": 8191, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8190, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8204, "src": "581:7:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8189, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "581:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "580:9:9"}, "scope": 8293, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 8240, "nodeType": "FunctionDefinition", "src": "641:352:9", "nodes": [], "body": {"id": 8239, "nodeType": "Block", "src": "708:285:9", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 8219, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"components": [{"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 8215, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 8213, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8206, "src": "847:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "^", "rightExpression": {"id": 8214, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8208, "src": "851:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "847:5:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "id": 8216, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "846:7:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 8218, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "-", "prefix": true, "src": "856:2:9", "subExpression": {"hexValue": "31", "id": 8217, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "857:1:9", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "typeDescriptions": {"typeIdentifier": "t_rational_minus_1_by_1", "typeString": "int_const -1"}}, "src": "846:12:9", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 8230, "nodeType": "IfStatement", "src": "842:71:9", "trueBody": {"id": 8229, "nodeType": "Block", "src": "860:53:9", "statements": [{"expression": {"arguments": [{"arguments": [{"id": 8222, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8206, "src": "891:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 8221, "name": "abs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8183, "src": "887:3:9", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_int256_$returns$_t_uint256_$", "typeString": "function (int256) pure returns (uint256)"}}, "id": 8223, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "887:6:9", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"arguments": [{"id": 8225, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8208, "src": "899:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 8224, "name": "abs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8183, "src": "895:3:9", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_int256_$returns$_t_uint256_$", "typeString": "function (int256) pure returns (uint256)"}}, "id": 8226, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "895:6:9", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 8220, "name": "delta", "nodeType": "Identifier", "overloadedDeclarations": [8204, 8240], "referencedDeclaration": 8204, "src": "881:5:9", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 8227, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "881:21:9", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 8212, "id": 8228, "nodeType": "Return", "src": "874:28:9"}]}}, {"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 8237, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [{"id": 8232, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8206, "src": "975:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 8231, "name": "abs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8183, "src": "971:3:9", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_int256_$returns$_t_uint256_$", "typeString": "function (int256) pure returns (uint256)"}}, "id": 8233, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "971:6:9", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"arguments": [{"id": 8235, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8208, "src": "984:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 8234, "name": "abs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8183, "src": "980:3:9", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_int256_$returns$_t_uint256_$", "typeString": "function (int256) pure returns (uint256)"}}, "id": 8236, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "980:6:9", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "971:15:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 8212, "id": 8238, "nodeType": "Return", "src": "964:22:9"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "delta", "nameLocation": "650:5:9", "parameters": {"id": 8209, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8206, "mutability": "mutable", "name": "a", "nameLocation": "663:1:9", "nodeType": "VariableDeclaration", "scope": 8240, "src": "656:8:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 8205, "name": "int256", "nodeType": "ElementaryTypeName", "src": "656:6:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}, {"constant": false, "id": 8208, "mutability": "mutable", "name": "b", "nameLocation": "673:1:9", "nodeType": "VariableDeclaration", "scope": 8240, "src": "666:8:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 8207, "name": "int256", "nodeType": "ElementaryTypeName", "src": "666:6:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "655:20:9"}, "returnParameters": {"id": 8212, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8211, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8240, "src": "699:7:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8210, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "699:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "698:9:9"}, "scope": 8293, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 8263, "nodeType": "FunctionDefinition", "src": "999:160:9", "nodes": [], "body": {"id": 8262, "nodeType": "Block", "src": "1075:84:9", "nodes": [], "statements": [{"assignments": [8250], "declarations": [{"constant": false, "id": 8250, "mutability": "mutable", "name": "absD<PERSON><PERSON>", "nameLocation": "1093:8:9", "nodeType": "VariableDeclaration", "scope": 8262, "src": "1085:16:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8249, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1085:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 8255, "initialValue": {"arguments": [{"id": 8252, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8242, "src": "1110:1:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 8253, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8244, "src": "1113:1:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 8251, "name": "delta", "nodeType": "Identifier", "overloadedDeclarations": [8204, 8240], "referencedDeclaration": 8204, "src": "1104:5:9", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 8254, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1104:11:9", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "1085:30:9"}, {"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 8260, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 8258, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 8256, "name": "absD<PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8250, "src": "1133:8:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "*", "rightExpression": {"hexValue": "31653138", "id": 8257, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1144:4:9", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}, "value": "1e18"}, "src": "1133:15:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "/", "rightExpression": {"id": 8259, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8244, "src": "1151:1:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1133:19:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 8248, "id": 8261, "nodeType": "Return", "src": "1126:26:9"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "1008:12:9", "parameters": {"id": 8245, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8242, "mutability": "mutable", "name": "a", "nameLocation": "1029:1:9", "nodeType": "VariableDeclaration", "scope": 8263, "src": "1021:9:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8241, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1021:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 8244, "mutability": "mutable", "name": "b", "nameLocation": "1040:1:9", "nodeType": "VariableDeclaration", "scope": 8263, "src": "1032:9:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8243, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1032:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1020:22:9"}, "returnParameters": {"id": 8248, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8247, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8263, "src": "1066:7:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8246, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1066:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1065:9:9"}, "scope": 8293, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 8292, "nodeType": "FunctionDefinition", "src": "1165:192:9", "nodes": [], "body": {"id": 8291, "nodeType": "Block", "src": "1239:118:9", "nodes": [], "statements": [{"assignments": [8273], "declarations": [{"constant": false, "id": 8273, "mutability": "mutable", "name": "absD<PERSON><PERSON>", "nameLocation": "1257:8:9", "nodeType": "VariableDeclaration", "scope": 8291, "src": "1249:16:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8272, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1249:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 8278, "initialValue": {"arguments": [{"id": 8275, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8265, "src": "1274:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, {"id": 8276, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8267, "src": "1277:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}, {"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 8274, "name": "delta", "nodeType": "Identifier", "overloadedDeclarations": [8204, 8240], "referencedDeclaration": 8240, "src": "1268:5:9", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_int256_$_t_int256_$returns$_t_uint256_$", "typeString": "function (int256,int256) pure returns (uint256)"}}, "id": 8277, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1268:11:9", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "1249:30:9"}, {"assignments": [8280], "declarations": [{"constant": false, "id": 8280, "mutability": "mutable", "name": "absB", "nameLocation": "1297:4:9", "nodeType": "VariableDeclaration", "scope": 8291, "src": "1289:12:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8279, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1289:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 8284, "initialValue": {"arguments": [{"id": 8282, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8267, "src": "1308:1:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 8281, "name": "abs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8183, "src": "1304:3:9", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_int256_$returns$_t_uint256_$", "typeString": "function (int256) pure returns (uint256)"}}, "id": 8283, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1304:6:9", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "1289:21:9"}, {"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 8289, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 8287, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 8285, "name": "absD<PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8273, "src": "1328:8:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "*", "rightExpression": {"hexValue": "31653138", "id": 8286, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1339:4:9", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}, "value": "1e18"}, "src": "1328:15:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "/", "rightExpression": {"id": 8288, "name": "absB", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8280, "src": "1346:4:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1328:22:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 8271, "id": 8290, "nodeType": "Return", "src": "1321:29:9"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "1174:12:9", "parameters": {"id": 8268, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8265, "mutability": "mutable", "name": "a", "nameLocation": "1194:1:9", "nodeType": "VariableDeclaration", "scope": 8292, "src": "1187:8:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 8264, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1187:6:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}, {"constant": false, "id": 8267, "mutability": "mutable", "name": "b", "nameLocation": "1204:1:9", "nodeType": "VariableDeclaration", "scope": 8292, "src": "1197:8:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 8266, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1197:6:9", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "1186:20:9"}, "returnParameters": {"id": 8271, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8270, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8292, "src": "1230:7:9", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8269, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1230:7:9", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1229:9:9"}, "scope": 8293, "stateMutability": "pure", "virtual": false, "visibility": "internal"}], "abstract": false, "baseContracts": [], "canonicalName": "stdMath", "contractDependencies": [], "contractKind": "library", "fullyImplemented": true, "linearizedBaseContracts": [8293], "name": "stdMath", "nameLocation": "73:7:9", "scope": 8294, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 9}