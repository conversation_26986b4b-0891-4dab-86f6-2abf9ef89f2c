{"abi": [], "bytecode": {"object": "0x6055604b600b8282823980515f1a607314603f577f4e487b71000000000000000000000000000000000000000000000000000000005f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220fb42b7a3a68195a5c3cf9c854aa4d51e40912c63b44a2599b0489c2524a542eb64736f6c634300081d0033", "sourceMap": "610:9092:12:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220fb42b7a3a68195a5c3cf9c854aa4d51e40912c63b44a2599b0489c2524a542eb64736f6c634300081d0033", "sourceMap": "610:9092:12:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdToml.sol\":\"stdToml\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdToml.sol": "stdToml"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/forge-std/src/StdToml.sol", "id": 12444, "exportedSymbols": {"VmSafe": [17281], "stdToml": [12443]}, "nodeType": "SourceUnit", "src": "32:9671:12", "nodes": [{"id": 11501, "nodeType": "PragmaDirective", "src": "32:31:12", "nodes": [], "literals": ["solidity", ">=", "0.6", ".0", "<", "0.9", ".0"]}, {"id": 11502, "nodeType": "PragmaDirective", "src": "65:33:12", "nodes": [], "literals": ["experimental", "ABIEncoderV2"]}, {"id": 11504, "nodeType": "ImportDirective", "src": "100:32:12", "nodes": [], "absolutePath": "lib/forge-std/src/Vm.sol", "file": "./Vm.sol", "nameLocation": "-1:-1:-1", "scope": 12444, "sourceUnit": 18353, "symbolAliases": [{"foreign": {"id": 11503, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "108:6:12", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 12443, "nodeType": "ContractDefinition", "src": "610:9092:12", "nodes": [{"id": 11521, "nodeType": "VariableDeclaration", "src": "632:92:12", "nodes": [], "constant": true, "mutability": "constant", "name": "vm", "nameLocation": "656:2:12", "scope": 12443, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}, "typeName": {"id": 11506, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 11505, "name": "VmSafe", "nameLocations": ["632:6:12"], "nodeType": "IdentifierPath", "referencedDeclaration": 17281, "src": "632:6:12"}, "referencedDeclaration": 17281, "src": "632:6:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "value": {"arguments": [{"arguments": [{"arguments": [{"arguments": [{"arguments": [{"hexValue": "6865766d20636865617420636f6465", "id": 11515, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "702:17:12", "typeDescriptions": {"typeIdentifier": "t_stringliteral_885cb69240a935d632d79c317109709ecfa91a80626ff3989d68f67f5b1dd12d", "typeString": "literal_string \"hevm cheat code\""}, "value": "hevm cheat code"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_885cb69240a935d632d79c317109709ecfa91a80626ff3989d68f67f5b1dd12d", "typeString": "literal_string \"hevm cheat code\""}], "id": 11514, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "692:9:12", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 11516, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "692:28:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 11513, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "684:7:12", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 11512, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "684:7:12", "typeDescriptions": {}}}, "id": 11517, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "684:37:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 11511, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "676:7:12", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 11510, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "676:7:12", "typeDescriptions": {}}}, "id": 11518, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "676:46:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 11509, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "668:7:12", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 11508, "name": "address", "nodeType": "ElementaryTypeName", "src": "668:7:12", "typeDescriptions": {}}}, "id": 11519, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "668:55:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 11507, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "661:6:12", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_VmSafe_$17281_$", "typeString": "type(contract VmSafe)"}}, "id": 11520, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "661:63:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "visibility": "private"}, {"id": 11537, "nodeType": "FunctionDefinition", "src": "731:138:12", "nodes": [], "body": {"id": 11536, "nodeType": "Block", "src": "818:51:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11532, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11523, "src": "852:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11533, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11525, "src": "858:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11530, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "835:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11531, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "838:13:12", "memberName": "keyExistsToml", "nodeType": "MemberAccess", "referencedDeclaration": 16790, "src": "835:16:12", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view external returns (bool)"}}, "id": 11534, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "835:27:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 11529, "id": 11535, "nodeType": "Return", "src": "828:34:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "keyExists", "nameLocation": "740:9:12", "parameters": {"id": 11526, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11523, "mutability": "mutable", "name": "toml", "nameLocation": "764:4:12", "nodeType": "VariableDeclaration", "scope": 11537, "src": "750:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11522, "name": "string", "nodeType": "ElementaryTypeName", "src": "750:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11525, "mutability": "mutable", "name": "key", "nameLocation": "784:3:12", "nodeType": "VariableDeclaration", "scope": 11537, "src": "770:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11524, "name": "string", "nodeType": "ElementaryTypeName", "src": "770:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "749:39:12"}, "returnParameters": {"id": 11529, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11528, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11537, "src": "812:4:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 11527, "name": "bool", "nodeType": "ElementaryTypeName", "src": "812:4:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "811:6:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 11553, "nodeType": "FunctionDefinition", "src": "875:141:12", "nodes": [], "body": {"id": 11552, "nodeType": "Block", "src": "969:47:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11548, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11539, "src": "999:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11549, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11541, "src": "1005:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11546, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "986:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11547, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "989:9:12", "memberName": "parseToml", "nodeType": "MemberAccess", "referencedDeclaration": 17000, "src": "986:12:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (bytes memory)"}}, "id": 11550, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "986:23:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "functionReturnParameters": 11545, "id": 11551, "nodeType": "Return", "src": "979:30:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "parseRaw", "nameLocation": "884:8:12", "parameters": {"id": 11542, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11539, "mutability": "mutable", "name": "toml", "nameLocation": "907:4:12", "nodeType": "VariableDeclaration", "scope": 11553, "src": "893:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11538, "name": "string", "nodeType": "ElementaryTypeName", "src": "893:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11541, "mutability": "mutable", "name": "key", "nameLocation": "927:3:12", "nodeType": "VariableDeclaration", "scope": 11553, "src": "913:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11540, "name": "string", "nodeType": "ElementaryTypeName", "src": "913:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "892:39:12"}, "returnParameters": {"id": 11545, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11544, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11553, "src": "955:12:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 11543, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "955:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "954:14:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11569, "nodeType": "FunctionDefinition", "src": "1022:140:12", "nodes": [], "body": {"id": 11568, "nodeType": "Block", "src": "1111:51:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11564, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11555, "src": "1145:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11565, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11557, "src": "1151:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11562, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "1128:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11563, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1131:13:12", "memberName": "parseTomlUint", "nodeType": "MemberAccess", "referencedDeclaration": 16971, "src": "1128:16:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_uint256_$", "typeString": "function (string memory,string memory) pure external returns (uint256)"}}, "id": 11566, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1128:27:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 11561, "id": 11567, "nodeType": "Return", "src": "1121:34:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readUint", "nameLocation": "1031:8:12", "parameters": {"id": 11558, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11555, "mutability": "mutable", "name": "toml", "nameLocation": "1054:4:12", "nodeType": "VariableDeclaration", "scope": 11569, "src": "1040:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11554, "name": "string", "nodeType": "ElementaryTypeName", "src": "1040:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11557, "mutability": "mutable", "name": "key", "nameLocation": "1074:3:12", "nodeType": "VariableDeclaration", "scope": 11569, "src": "1060:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11556, "name": "string", "nodeType": "ElementaryTypeName", "src": "1060:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1039:39:12"}, "returnParameters": {"id": 11561, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11560, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11569, "src": "1102:7:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 11559, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1102:7:12", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1101:9:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11586, "nodeType": "FunctionDefinition", "src": "1168:159:12", "nodes": [], "body": {"id": 11585, "nodeType": "Block", "src": "1271:56:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11581, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11571, "src": "1310:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11582, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11573, "src": "1316:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11579, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "1288:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11580, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1291:18:12", "memberName": "parseTomlUintArray", "nodeType": "MemberAccess", "referencedDeclaration": 16982, "src": "1288:21:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (uint256[] memory)"}}, "id": 11583, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1288:32:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "functionReturnParameters": 11578, "id": 11584, "nodeType": "Return", "src": "1281:39:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readUintArray", "nameLocation": "1177:13:12", "parameters": {"id": 11574, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11571, "mutability": "mutable", "name": "toml", "nameLocation": "1205:4:12", "nodeType": "VariableDeclaration", "scope": 11586, "src": "1191:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11570, "name": "string", "nodeType": "ElementaryTypeName", "src": "1191:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11573, "mutability": "mutable", "name": "key", "nameLocation": "1225:3:12", "nodeType": "VariableDeclaration", "scope": 11586, "src": "1211:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11572, "name": "string", "nodeType": "ElementaryTypeName", "src": "1211:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1190:39:12"}, "returnParameters": {"id": 11578, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11577, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11586, "src": "1253:16:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 11575, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1253:7:12", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 11576, "nodeType": "ArrayTypeName", "src": "1253:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "1252:18:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11602, "nodeType": "FunctionDefinition", "src": "1333:137:12", "nodes": [], "body": {"id": 11601, "nodeType": "Block", "src": "1420:50:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11597, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11588, "src": "1453:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11598, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11590, "src": "1459:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11595, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "1437:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11596, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1440:12:12", "memberName": "parseTomlInt", "nodeType": "MemberAccess", "referencedDeclaration": 16884, "src": "1437:15:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_int256_$", "typeString": "function (string memory,string memory) pure external returns (int256)"}}, "id": 11599, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1437:26:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "functionReturnParameters": 11594, "id": 11600, "nodeType": "Return", "src": "1430:33:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readInt", "nameLocation": "1342:7:12", "parameters": {"id": 11591, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11588, "mutability": "mutable", "name": "toml", "nameLocation": "1364:4:12", "nodeType": "VariableDeclaration", "scope": 11602, "src": "1350:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11587, "name": "string", "nodeType": "ElementaryTypeName", "src": "1350:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11590, "mutability": "mutable", "name": "key", "nameLocation": "1384:3:12", "nodeType": "VariableDeclaration", "scope": 11602, "src": "1370:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11589, "name": "string", "nodeType": "ElementaryTypeName", "src": "1370:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1349:39:12"}, "returnParameters": {"id": 11594, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11593, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11602, "src": "1412:6:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 11592, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1412:6:12", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "1411:8:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11619, "nodeType": "FunctionDefinition", "src": "1476:156:12", "nodes": [], "body": {"id": 11618, "nodeType": "Block", "src": "1577:55:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11614, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11604, "src": "1615:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11615, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11606, "src": "1621:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11612, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "1594:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11613, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1597:17:12", "memberName": "parseTomlIntArray", "nodeType": "MemberAccess", "referencedDeclaration": 16895, "src": "1594:20:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_int256_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (int256[] memory)"}}, "id": 11616, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1594:31:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}}, "functionReturnParameters": 11611, "id": 11617, "nodeType": "Return", "src": "1587:38:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readIntArray", "nameLocation": "1485:12:12", "parameters": {"id": 11607, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11604, "mutability": "mutable", "name": "toml", "nameLocation": "1512:4:12", "nodeType": "VariableDeclaration", "scope": 11619, "src": "1498:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11603, "name": "string", "nodeType": "ElementaryTypeName", "src": "1498:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11606, "mutability": "mutable", "name": "key", "nameLocation": "1532:3:12", "nodeType": "VariableDeclaration", "scope": 11619, "src": "1518:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11605, "name": "string", "nodeType": "ElementaryTypeName", "src": "1518:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1497:39:12"}, "returnParameters": {"id": 11611, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11610, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11619, "src": "1560:15:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[]"}, "typeName": {"baseType": {"id": 11608, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1560:6:12", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 11609, "nodeType": "ArrayTypeName", "src": "1560:8:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_storage_ptr", "typeString": "int256[]"}}, "visibility": "internal"}], "src": "1559:17:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11635, "nodeType": "FunctionDefinition", "src": "1638:146:12", "nodes": [], "body": {"id": 11634, "nodeType": "Block", "src": "1730:54:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11630, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11621, "src": "1767:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11631, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11623, "src": "1773:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11628, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "1747:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11629, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1750:16:12", "memberName": "parseTomlBytes32", "nodeType": "MemberAccess", "referencedDeclaration": 16852, "src": "1747:19:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (string memory,string memory) pure external returns (bytes32)"}}, "id": 11632, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1747:30:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 11627, "id": 11633, "nodeType": "Return", "src": "1740:37:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytes32", "nameLocation": "1647:11:12", "parameters": {"id": 11624, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11621, "mutability": "mutable", "name": "toml", "nameLocation": "1673:4:12", "nodeType": "VariableDeclaration", "scope": 11635, "src": "1659:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11620, "name": "string", "nodeType": "ElementaryTypeName", "src": "1659:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11623, "mutability": "mutable", "name": "key", "nameLocation": "1693:3:12", "nodeType": "VariableDeclaration", "scope": 11635, "src": "1679:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11622, "name": "string", "nodeType": "ElementaryTypeName", "src": "1679:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1658:39:12"}, "returnParameters": {"id": 11627, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11626, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11635, "src": "1721:7:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 11625, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1721:7:12", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "1720:9:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11652, "nodeType": "FunctionDefinition", "src": "1790:165:12", "nodes": [], "body": {"id": 11651, "nodeType": "Block", "src": "1896:59:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11647, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11637, "src": "1938:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11648, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11639, "src": "1944:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11645, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "1913:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11646, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1916:21:12", "memberName": "parseTomlBytes32Array", "nodeType": "MemberAccess", "referencedDeclaration": 16863, "src": "1913:24:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bytes32_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (bytes32[] memory)"}}, "id": 11649, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1913:35:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "functionReturnParameters": 11644, "id": 11650, "nodeType": "Return", "src": "1906:42:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytes32Array", "nameLocation": "1799:16:12", "parameters": {"id": 11640, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11637, "mutability": "mutable", "name": "toml", "nameLocation": "1830:4:12", "nodeType": "VariableDeclaration", "scope": 11652, "src": "1816:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11636, "name": "string", "nodeType": "ElementaryTypeName", "src": "1816:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11639, "mutability": "mutable", "name": "key", "nameLocation": "1850:3:12", "nodeType": "VariableDeclaration", "scope": 11652, "src": "1836:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11638, "name": "string", "nodeType": "ElementaryTypeName", "src": "1836:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1815:39:12"}, "returnParameters": {"id": 11644, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11643, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11652, "src": "1878:16:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 11641, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1878:7:12", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 11642, "nodeType": "ArrayTypeName", "src": "1878:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "1877:18:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11668, "nodeType": "FunctionDefinition", "src": "1961:150:12", "nodes": [], "body": {"id": 11667, "nodeType": "Block", "src": "2058:53:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11663, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11654, "src": "2094:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11664, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11656, "src": "2100:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11661, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "2075:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11662, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2078:15:12", "memberName": "parseTomlString", "nodeType": "MemberAccess", "referencedDeclaration": 16916, "src": "2075:18:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (string memory)"}}, "id": 11665, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2075:29:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11660, "id": 11666, "nodeType": "Return", "src": "2068:36:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readString", "nameLocation": "1970:10:12", "parameters": {"id": 11657, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11654, "mutability": "mutable", "name": "toml", "nameLocation": "1995:4:12", "nodeType": "VariableDeclaration", "scope": 11668, "src": "1981:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11653, "name": "string", "nodeType": "ElementaryTypeName", "src": "1981:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11656, "mutability": "mutable", "name": "key", "nameLocation": "2015:3:12", "nodeType": "VariableDeclaration", "scope": 11668, "src": "2001:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11655, "name": "string", "nodeType": "ElementaryTypeName", "src": "2001:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1980:39:12"}, "returnParameters": {"id": 11660, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11659, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11668, "src": "2043:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11658, "name": "string", "nodeType": "ElementaryTypeName", "src": "2043:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2042:15:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11685, "nodeType": "FunctionDefinition", "src": "2117:162:12", "nodes": [], "body": {"id": 11684, "nodeType": "Block", "src": "2221:58:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11680, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11670, "src": "2262:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11681, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11672, "src": "2268:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11678, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "2238:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11679, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2241:20:12", "memberName": "parseTomlStringArray", "nodeType": "MemberAccess", "referencedDeclaration": 16927, "src": "2238:23:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_string_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (string memory[] memory)"}}, "id": 11682, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2238:34:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "functionReturnParameters": 11677, "id": 11683, "nodeType": "Return", "src": "2231:41:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readStringArray", "nameLocation": "2126:15:12", "parameters": {"id": 11673, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11670, "mutability": "mutable", "name": "toml", "nameLocation": "2156:4:12", "nodeType": "VariableDeclaration", "scope": 11685, "src": "2142:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11669, "name": "string", "nodeType": "ElementaryTypeName", "src": "2142:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11672, "mutability": "mutable", "name": "key", "nameLocation": "2176:3:12", "nodeType": "VariableDeclaration", "scope": 11685, "src": "2162:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11671, "name": "string", "nodeType": "ElementaryTypeName", "src": "2162:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2141:39:12"}, "returnParameters": {"id": 11677, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11676, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11685, "src": "2204:15:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 11674, "name": "string", "nodeType": "ElementaryTypeName", "src": "2204:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 11675, "nodeType": "ArrayTypeName", "src": "2204:8:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "2203:17:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11701, "nodeType": "FunctionDefinition", "src": "2285:146:12", "nodes": [], "body": {"id": 11700, "nodeType": "Block", "src": "2377:54:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11696, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11687, "src": "2414:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11697, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11689, "src": "2420:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11694, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "2394:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11695, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2397:16:12", "memberName": "parseTomlAddress", "nodeType": "MemberAccess", "referencedDeclaration": 16800, "src": "2394:19:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_address_$", "typeString": "function (string memory,string memory) pure external returns (address)"}}, "id": 11698, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2394:30:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 11693, "id": 11699, "nodeType": "Return", "src": "2387:37:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readAddress", "nameLocation": "2294:11:12", "parameters": {"id": 11690, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11687, "mutability": "mutable", "name": "toml", "nameLocation": "2320:4:12", "nodeType": "VariableDeclaration", "scope": 11701, "src": "2306:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11686, "name": "string", "nodeType": "ElementaryTypeName", "src": "2306:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11689, "mutability": "mutable", "name": "key", "nameLocation": "2340:3:12", "nodeType": "VariableDeclaration", "scope": 11701, "src": "2326:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11688, "name": "string", "nodeType": "ElementaryTypeName", "src": "2326:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2305:39:12"}, "returnParameters": {"id": 11693, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11692, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11701, "src": "2368:7:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 11691, "name": "address", "nodeType": "ElementaryTypeName", "src": "2368:7:12", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2367:9:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11718, "nodeType": "FunctionDefinition", "src": "2437:165:12", "nodes": [], "body": {"id": 11717, "nodeType": "Block", "src": "2543:59:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11713, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11703, "src": "2585:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11714, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11705, "src": "2591:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11711, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "2560:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11712, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2563:21:12", "memberName": "parseTomlAddressArray", "nodeType": "MemberAccess", "referencedDeclaration": 16811, "src": "2560:24:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_address_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (address[] memory)"}}, "id": 11715, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2560:35:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "functionReturnParameters": 11710, "id": 11716, "nodeType": "Return", "src": "2553:42:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readAddressArray", "nameLocation": "2446:16:12", "parameters": {"id": 11706, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11703, "mutability": "mutable", "name": "toml", "nameLocation": "2477:4:12", "nodeType": "VariableDeclaration", "scope": 11718, "src": "2463:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11702, "name": "string", "nodeType": "ElementaryTypeName", "src": "2463:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11705, "mutability": "mutable", "name": "key", "nameLocation": "2497:3:12", "nodeType": "VariableDeclaration", "scope": 11718, "src": "2483:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11704, "name": "string", "nodeType": "ElementaryTypeName", "src": "2483:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2462:39:12"}, "returnParameters": {"id": 11710, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11709, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11718, "src": "2525:16:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 11707, "name": "address", "nodeType": "ElementaryTypeName", "src": "2525:7:12", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 11708, "nodeType": "ArrayTypeName", "src": "2525:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "2524:18:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11734, "nodeType": "FunctionDefinition", "src": "2608:137:12", "nodes": [], "body": {"id": 11733, "nodeType": "Block", "src": "2694:51:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11729, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11720, "src": "2728:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11730, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11722, "src": "2734:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11727, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "2711:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11728, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2714:13:12", "memberName": "parseTomlBool", "nodeType": "MemberAccess", "referencedDeclaration": 16821, "src": "2711:16:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) pure external returns (bool)"}}, "id": 11731, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2711:27:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 11726, "id": 11732, "nodeType": "Return", "src": "2704:34:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBool", "nameLocation": "2617:8:12", "parameters": {"id": 11723, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11720, "mutability": "mutable", "name": "toml", "nameLocation": "2640:4:12", "nodeType": "VariableDeclaration", "scope": 11734, "src": "2626:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11719, "name": "string", "nodeType": "ElementaryTypeName", "src": "2626:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11722, "mutability": "mutable", "name": "key", "nameLocation": "2660:3:12", "nodeType": "VariableDeclaration", "scope": 11734, "src": "2646:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11721, "name": "string", "nodeType": "ElementaryTypeName", "src": "2646:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2625:39:12"}, "returnParameters": {"id": 11726, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11725, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11734, "src": "2688:4:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 11724, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2688:4:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2687:6:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11751, "nodeType": "FunctionDefinition", "src": "2751:156:12", "nodes": [], "body": {"id": 11750, "nodeType": "Block", "src": "2851:56:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11746, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11736, "src": "2890:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11747, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11738, "src": "2896:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11744, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "2868:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11745, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2871:18:12", "memberName": "parseTomlBoolArray", "nodeType": "MemberAccess", "referencedDeclaration": 16832, "src": "2868:21:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bool_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (bool[] memory)"}}, "id": 11748, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2868:32:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}}, "functionReturnParameters": 11743, "id": 11749, "nodeType": "Return", "src": "2861:39:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBoolArray", "nameLocation": "2760:13:12", "parameters": {"id": 11739, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11736, "mutability": "mutable", "name": "toml", "nameLocation": "2788:4:12", "nodeType": "VariableDeclaration", "scope": 11751, "src": "2774:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11735, "name": "string", "nodeType": "ElementaryTypeName", "src": "2774:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11738, "mutability": "mutable", "name": "key", "nameLocation": "2808:3:12", "nodeType": "VariableDeclaration", "scope": 11751, "src": "2794:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11737, "name": "string", "nodeType": "ElementaryTypeName", "src": "2794:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2773:39:12"}, "returnParameters": {"id": 11743, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11742, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11751, "src": "2836:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[]"}, "typeName": {"baseType": {"id": 11740, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2836:4:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 11741, "nodeType": "ArrayTypeName", "src": "2836:6:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_storage_ptr", "typeString": "bool[]"}}, "visibility": "internal"}], "src": "2835:15:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11767, "nodeType": "FunctionDefinition", "src": "2913:147:12", "nodes": [], "body": {"id": 11766, "nodeType": "Block", "src": "3008:52:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11762, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11753, "src": "3043:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11763, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11755, "src": "3049:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11760, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "3025:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11761, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3028:14:12", "memberName": "parseTomlBytes", "nodeType": "MemberAccess", "referencedDeclaration": 16842, "src": "3025:17:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (bytes memory)"}}, "id": 11764, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3025:28:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "functionReturnParameters": 11759, "id": 11765, "nodeType": "Return", "src": "3018:35:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytes", "nameLocation": "2922:9:12", "parameters": {"id": 11756, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11753, "mutability": "mutable", "name": "toml", "nameLocation": "2946:4:12", "nodeType": "VariableDeclaration", "scope": 11767, "src": "2932:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11752, "name": "string", "nodeType": "ElementaryTypeName", "src": "2932:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11755, "mutability": "mutable", "name": "key", "nameLocation": "2966:3:12", "nodeType": "VariableDeclaration", "scope": 11767, "src": "2952:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11754, "name": "string", "nodeType": "ElementaryTypeName", "src": "2952:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2931:39:12"}, "returnParameters": {"id": 11759, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11758, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11767, "src": "2994:12:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 11757, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "2994:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "2993:14:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11784, "nodeType": "FunctionDefinition", "src": "3066:159:12", "nodes": [], "body": {"id": 11783, "nodeType": "Block", "src": "3168:57:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11779, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11769, "src": "3208:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11780, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11771, "src": "3214:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 11777, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "3185:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11778, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3188:19:12", "memberName": "parseTomlBytesArray", "nodeType": "MemberAccess", "referencedDeclaration": 16874, "src": "3185:22:12", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bytes_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (bytes memory[] memory)"}}, "id": 11781, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3185:33:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}, "functionReturnParameters": 11776, "id": 11782, "nodeType": "Return", "src": "3178:40:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytesArray", "nameLocation": "3075:14:12", "parameters": {"id": 11772, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11769, "mutability": "mutable", "name": "toml", "nameLocation": "3104:4:12", "nodeType": "VariableDeclaration", "scope": 11784, "src": "3090:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11768, "name": "string", "nodeType": "ElementaryTypeName", "src": "3090:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11771, "mutability": "mutable", "name": "key", "nameLocation": "3124:3:12", "nodeType": "VariableDeclaration", "scope": 11784, "src": "3110:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11770, "name": "string", "nodeType": "ElementaryTypeName", "src": "3110:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3089:39:12"}, "returnParameters": {"id": 11776, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11775, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11784, "src": "3152:14:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 11773, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "3152:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 11774, "nodeType": "ArrayTypeName", "src": "3152:7:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "src": "3151:16:12"}, "scope": 12443, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11807, "nodeType": "FunctionDefinition", "src": "3231:194:12", "nodes": [], "body": {"id": 11806, "nodeType": "Block", "src": "3344:81:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 11796, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11786, "src": "3371:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11797, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11788, "src": "3377:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11795, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "3361:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 11798, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3361:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 11803, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11790, "src": "3406:12:12", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 11804, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "3361:57:12", "trueExpression": {"arguments": [{"id": 11800, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11786, "src": "3393:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11801, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11788, "src": "3399:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11799, "name": "readUint", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11569, "src": "3384:8:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_uint256_$", "typeString": "function (string memory,string memory) pure returns (uint256)"}}, "id": 11802, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3384:19:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 11794, "id": 11805, "nodeType": "Return", "src": "3354:64:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readUintOr", "nameLocation": "3240:10:12", "parameters": {"id": 11791, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11786, "mutability": "mutable", "name": "toml", "nameLocation": "3265:4:12", "nodeType": "VariableDeclaration", "scope": 11807, "src": "3251:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11785, "name": "string", "nodeType": "ElementaryTypeName", "src": "3251:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11788, "mutability": "mutable", "name": "key", "nameLocation": "3285:3:12", "nodeType": "VariableDeclaration", "scope": 11807, "src": "3271:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11787, "name": "string", "nodeType": "ElementaryTypeName", "src": "3271:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11790, "mutability": "mutable", "name": "defaultValue", "nameLocation": "3298:12:12", "nodeType": "VariableDeclaration", "scope": 11807, "src": "3290:20:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 11789, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3290:7:12", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3250:61:12"}, "returnParameters": {"id": 11794, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11793, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11807, "src": "3335:7:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 11792, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3335:7:12", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3334:9:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 11832, "nodeType": "FunctionDefinition", "src": "3431:250:12", "nodes": [], "body": {"id": 11831, "nodeType": "Block", "src": "3595:86:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 11821, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11809, "src": "3622:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11822, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11811, "src": "3628:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11820, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "3612:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 11823, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3612:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 11828, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11814, "src": "3662:12:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "id": 11829, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "3612:62:12", "trueExpression": {"arguments": [{"id": 11825, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11809, "src": "3649:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11826, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11811, "src": "3655:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11824, "name": "readUintArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11586, "src": "3635:13:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (uint256[] memory)"}}, "id": 11827, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3635:24:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "functionReturnParameters": 11819, "id": 11830, "nodeType": "Return", "src": "3605:69:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readUintArrayOr", "nameLocation": "3440:15:12", "parameters": {"id": 11815, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11809, "mutability": "mutable", "name": "toml", "nameLocation": "3470:4:12", "nodeType": "VariableDeclaration", "scope": 11832, "src": "3456:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11808, "name": "string", "nodeType": "ElementaryTypeName", "src": "3456:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11811, "mutability": "mutable", "name": "key", "nameLocation": "3490:3:12", "nodeType": "VariableDeclaration", "scope": 11832, "src": "3476:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11810, "name": "string", "nodeType": "ElementaryTypeName", "src": "3476:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11814, "mutability": "mutable", "name": "defaultValue", "nameLocation": "3512:12:12", "nodeType": "VariableDeclaration", "scope": 11832, "src": "3495:29:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 11812, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3495:7:12", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 11813, "nodeType": "ArrayTypeName", "src": "3495:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "3455:70:12"}, "returnParameters": {"id": 11819, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11818, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11832, "src": "3573:16:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 11816, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3573:7:12", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 11817, "nodeType": "ArrayTypeName", "src": "3573:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "3572:18:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 11855, "nodeType": "FunctionDefinition", "src": "3687:190:12", "nodes": [], "body": {"id": 11854, "nodeType": "Block", "src": "3797:80:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 11844, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11834, "src": "3824:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11845, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11836, "src": "3830:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11843, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "3814:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 11846, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3814:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 11851, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11838, "src": "3858:12:12", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 11852, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "3814:56:12", "trueExpression": {"arguments": [{"id": 11848, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11834, "src": "3845:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11849, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11836, "src": "3851:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11847, "name": "readInt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11602, "src": "3837:7:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_int256_$", "typeString": "function (string memory,string memory) pure returns (int256)"}}, "id": 11850, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3837:18:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "functionReturnParameters": 11842, "id": 11853, "nodeType": "Return", "src": "3807:63:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readIntOr", "nameLocation": "3696:9:12", "parameters": {"id": 11839, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11834, "mutability": "mutable", "name": "toml", "nameLocation": "3720:4:12", "nodeType": "VariableDeclaration", "scope": 11855, "src": "3706:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11833, "name": "string", "nodeType": "ElementaryTypeName", "src": "3706:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11836, "mutability": "mutable", "name": "key", "nameLocation": "3740:3:12", "nodeType": "VariableDeclaration", "scope": 11855, "src": "3726:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11835, "name": "string", "nodeType": "ElementaryTypeName", "src": "3726:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11838, "mutability": "mutable", "name": "defaultValue", "nameLocation": "3752:12:12", "nodeType": "VariableDeclaration", "scope": 11855, "src": "3745:19:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 11837, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3745:6:12", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "3705:60:12"}, "returnParameters": {"id": 11842, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11841, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11855, "src": "3789:6:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 11840, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3789:6:12", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "3788:8:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 11880, "nodeType": "FunctionDefinition", "src": "3883:246:12", "nodes": [], "body": {"id": 11879, "nodeType": "Block", "src": "4044:85:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 11869, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11857, "src": "4071:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11870, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11859, "src": "4077:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11868, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "4061:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 11871, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4061:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 11876, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11862, "src": "4110:12:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}}, "id": 11877, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "4061:61:12", "trueExpression": {"arguments": [{"id": 11873, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11857, "src": "4097:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11874, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11859, "src": "4103:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11872, "name": "readIntArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11619, "src": "4084:12:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_int256_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (int256[] memory)"}}, "id": 11875, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4084:23:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}}, "functionReturnParameters": 11867, "id": 11878, "nodeType": "Return", "src": "4054:68:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readIntArrayOr", "nameLocation": "3892:14:12", "parameters": {"id": 11863, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11857, "mutability": "mutable", "name": "toml", "nameLocation": "3921:4:12", "nodeType": "VariableDeclaration", "scope": 11880, "src": "3907:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11856, "name": "string", "nodeType": "ElementaryTypeName", "src": "3907:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11859, "mutability": "mutable", "name": "key", "nameLocation": "3941:3:12", "nodeType": "VariableDeclaration", "scope": 11880, "src": "3927:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11858, "name": "string", "nodeType": "ElementaryTypeName", "src": "3927:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11862, "mutability": "mutable", "name": "defaultValue", "nameLocation": "3962:12:12", "nodeType": "VariableDeclaration", "scope": 11880, "src": "3946:28:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[]"}, "typeName": {"baseType": {"id": 11860, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3946:6:12", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 11861, "nodeType": "ArrayTypeName", "src": "3946:8:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_storage_ptr", "typeString": "int256[]"}}, "visibility": "internal"}], "src": "3906:69:12"}, "returnParameters": {"id": 11867, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11866, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11880, "src": "4023:15:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[]"}, "typeName": {"baseType": {"id": 11864, "name": "int256", "nodeType": "ElementaryTypeName", "src": "4023:6:12", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 11865, "nodeType": "ArrayTypeName", "src": "4023:8:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_storage_ptr", "typeString": "int256[]"}}, "visibility": "internal"}], "src": "4022:17:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 11903, "nodeType": "FunctionDefinition", "src": "4135:228:12", "nodes": [], "body": {"id": 11902, "nodeType": "Block", "src": "4279:84:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 11892, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11882, "src": "4306:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11893, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11884, "src": "4312:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11891, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "4296:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 11894, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4296:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 11899, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11886, "src": "4344:12:12", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 11900, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "4296:60:12", "trueExpression": {"arguments": [{"id": 11896, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11882, "src": "4331:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11897, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11884, "src": "4337:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11895, "name": "readBytes32", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11635, "src": "4319:11:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (string memory,string memory) pure returns (bytes32)"}}, "id": 11898, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4319:22:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 11890, "id": 11901, "nodeType": "Return", "src": "4289:67:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytes32Or", "nameLocation": "4144:13:12", "parameters": {"id": 11887, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11882, "mutability": "mutable", "name": "toml", "nameLocation": "4172:4:12", "nodeType": "VariableDeclaration", "scope": 11903, "src": "4158:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11881, "name": "string", "nodeType": "ElementaryTypeName", "src": "4158:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11884, "mutability": "mutable", "name": "key", "nameLocation": "4192:3:12", "nodeType": "VariableDeclaration", "scope": 11903, "src": "4178:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11883, "name": "string", "nodeType": "ElementaryTypeName", "src": "4178:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11886, "mutability": "mutable", "name": "defaultValue", "nameLocation": "4205:12:12", "nodeType": "VariableDeclaration", "scope": 11903, "src": "4197:20:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 11885, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4197:7:12", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "4157:61:12"}, "returnParameters": {"id": 11890, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11889, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11903, "src": "4266:7:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 11888, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4266:7:12", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "4265:9:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 11928, "nodeType": "FunctionDefinition", "src": "4369:256:12", "nodes": [], "body": {"id": 11927, "nodeType": "Block", "src": "4536:89:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 11917, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11905, "src": "4563:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11918, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11907, "src": "4569:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11916, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "4553:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 11919, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4553:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 11924, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11910, "src": "4606:12:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "id": 11925, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "4553:65:12", "trueExpression": {"arguments": [{"id": 11921, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11905, "src": "4593:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11922, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11907, "src": "4599:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11920, "name": "readBytes32Array", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11652, "src": "4576:16:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bytes32_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (bytes32[] memory)"}}, "id": 11923, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4576:27:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "functionReturnParameters": 11915, "id": 11926, "nodeType": "Return", "src": "4546:72:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytes32ArrayOr", "nameLocation": "4378:18:12", "parameters": {"id": 11911, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11905, "mutability": "mutable", "name": "toml", "nameLocation": "4411:4:12", "nodeType": "VariableDeclaration", "scope": 11928, "src": "4397:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11904, "name": "string", "nodeType": "ElementaryTypeName", "src": "4397:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11907, "mutability": "mutable", "name": "key", "nameLocation": "4431:3:12", "nodeType": "VariableDeclaration", "scope": 11928, "src": "4417:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11906, "name": "string", "nodeType": "ElementaryTypeName", "src": "4417:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11910, "mutability": "mutable", "name": "defaultValue", "nameLocation": "4453:12:12", "nodeType": "VariableDeclaration", "scope": 11928, "src": "4436:29:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 11908, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4436:7:12", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 11909, "nodeType": "ArrayTypeName", "src": "4436:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "4396:70:12"}, "returnParameters": {"id": 11915, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11914, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11928, "src": "4514:16:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 11912, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4514:7:12", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 11913, "nodeType": "ArrayTypeName", "src": "4514:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "4513:18:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 11951, "nodeType": "FunctionDefinition", "src": "4631:238:12", "nodes": [], "body": {"id": 11950, "nodeType": "Block", "src": "4786:83:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 11940, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11930, "src": "4813:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11941, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11932, "src": "4819:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11939, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "4803:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 11942, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4803:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 11947, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11934, "src": "4850:12:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 11948, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "4803:59:12", "trueExpression": {"arguments": [{"id": 11944, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11930, "src": "4837:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11945, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11932, "src": "4843:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11943, "name": "readString", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11668, "src": "4826:10:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 11946, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4826:21:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11938, "id": 11949, "nodeType": "Return", "src": "4796:66:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readStringOr", "nameLocation": "4640:12:12", "parameters": {"id": 11935, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11930, "mutability": "mutable", "name": "toml", "nameLocation": "4667:4:12", "nodeType": "VariableDeclaration", "scope": 11951, "src": "4653:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11929, "name": "string", "nodeType": "ElementaryTypeName", "src": "4653:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11932, "mutability": "mutable", "name": "key", "nameLocation": "4687:3:12", "nodeType": "VariableDeclaration", "scope": 11951, "src": "4673:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11931, "name": "string", "nodeType": "ElementaryTypeName", "src": "4673:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11934, "mutability": "mutable", "name": "defaultValue", "nameLocation": "4706:12:12", "nodeType": "VariableDeclaration", "scope": 11951, "src": "4692:26:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11933, "name": "string", "nodeType": "ElementaryTypeName", "src": "4692:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4652:67:12"}, "returnParameters": {"id": 11938, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11937, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11951, "src": "4767:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11936, "name": "string", "nodeType": "ElementaryTypeName", "src": "4767:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4766:15:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 11976, "nodeType": "FunctionDefinition", "src": "4875:252:12", "nodes": [], "body": {"id": 11975, "nodeType": "Block", "src": "5039:88:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 11965, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11953, "src": "5066:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11966, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11955, "src": "5072:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11964, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "5056:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 11967, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5056:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 11972, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11958, "src": "5108:12:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 11973, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "5056:64:12", "trueExpression": {"arguments": [{"id": 11969, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11953, "src": "5095:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11970, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11955, "src": "5101:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11968, "name": "readStringArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11685, "src": "5079:15:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_string_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory[] memory)"}}, "id": 11971, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5079:26:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "functionReturnParameters": 11963, "id": 11974, "nodeType": "Return", "src": "5049:71:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readStringArrayOr", "nameLocation": "4884:17:12", "parameters": {"id": 11959, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11953, "mutability": "mutable", "name": "toml", "nameLocation": "4916:4:12", "nodeType": "VariableDeclaration", "scope": 11976, "src": "4902:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11952, "name": "string", "nodeType": "ElementaryTypeName", "src": "4902:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11955, "mutability": "mutable", "name": "key", "nameLocation": "4936:3:12", "nodeType": "VariableDeclaration", "scope": 11976, "src": "4922:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11954, "name": "string", "nodeType": "ElementaryTypeName", "src": "4922:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11958, "mutability": "mutable", "name": "defaultValue", "nameLocation": "4957:12:12", "nodeType": "VariableDeclaration", "scope": 11976, "src": "4941:28:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 11956, "name": "string", "nodeType": "ElementaryTypeName", "src": "4941:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 11957, "nodeType": "ArrayTypeName", "src": "4941:8:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "4901:69:12"}, "returnParameters": {"id": 11963, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11962, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11976, "src": "5018:15:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 11960, "name": "string", "nodeType": "ElementaryTypeName", "src": "5018:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 11961, "nodeType": "ArrayTypeName", "src": "5018:8:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "5017:17:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 11999, "nodeType": "FunctionDefinition", "src": "5133:228:12", "nodes": [], "body": {"id": 11998, "nodeType": "Block", "src": "5277:84:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 11988, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11978, "src": "5304:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11989, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11980, "src": "5310:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11987, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "5294:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 11990, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5294:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 11995, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11982, "src": "5342:12:12", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 11996, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "5294:60:12", "trueExpression": {"arguments": [{"id": 11992, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11978, "src": "5329:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11993, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11980, "src": "5335:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11991, "name": "readAddress", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11701, "src": "5317:11:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_address_$", "typeString": "function (string memory,string memory) pure returns (address)"}}, "id": 11994, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5317:22:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 11986, "id": 11997, "nodeType": "Return", "src": "5287:67:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readAddressOr", "nameLocation": "5142:13:12", "parameters": {"id": 11983, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11978, "mutability": "mutable", "name": "toml", "nameLocation": "5170:4:12", "nodeType": "VariableDeclaration", "scope": 11999, "src": "5156:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11977, "name": "string", "nodeType": "ElementaryTypeName", "src": "5156:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11980, "mutability": "mutable", "name": "key", "nameLocation": "5190:3:12", "nodeType": "VariableDeclaration", "scope": 11999, "src": "5176:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11979, "name": "string", "nodeType": "ElementaryTypeName", "src": "5176:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 11982, "mutability": "mutable", "name": "defaultValue", "nameLocation": "5203:12:12", "nodeType": "VariableDeclaration", "scope": 11999, "src": "5195:20:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 11981, "name": "address", "nodeType": "ElementaryTypeName", "src": "5195:7:12", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5155:61:12"}, "returnParameters": {"id": 11986, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11985, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11999, "src": "5264:7:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 11984, "name": "address", "nodeType": "ElementaryTypeName", "src": "5264:7:12", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5263:9:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 12024, "nodeType": "FunctionDefinition", "src": "5367:256:12", "nodes": [], "body": {"id": 12023, "nodeType": "Block", "src": "5534:89:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 12013, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12001, "src": "5561:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12014, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12003, "src": "5567:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12012, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "5551:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 12015, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5551:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 12020, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12006, "src": "5604:12:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 12021, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "5551:65:12", "trueExpression": {"arguments": [{"id": 12017, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12001, "src": "5591:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12018, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12003, "src": "5597:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12016, "name": "readAddressArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11718, "src": "5574:16:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_address_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (address[] memory)"}}, "id": 12019, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5574:27:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "functionReturnParameters": 12011, "id": 12022, "nodeType": "Return", "src": "5544:72:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readAddressArrayOr", "nameLocation": "5376:18:12", "parameters": {"id": 12007, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12001, "mutability": "mutable", "name": "toml", "nameLocation": "5409:4:12", "nodeType": "VariableDeclaration", "scope": 12024, "src": "5395:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12000, "name": "string", "nodeType": "ElementaryTypeName", "src": "5395:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12003, "mutability": "mutable", "name": "key", "nameLocation": "5429:3:12", "nodeType": "VariableDeclaration", "scope": 12024, "src": "5415:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12002, "name": "string", "nodeType": "ElementaryTypeName", "src": "5415:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12006, "mutability": "mutable", "name": "defaultValue", "nameLocation": "5451:12:12", "nodeType": "VariableDeclaration", "scope": 12024, "src": "5434:29:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 12004, "name": "address", "nodeType": "ElementaryTypeName", "src": "5434:7:12", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 12005, "nodeType": "ArrayTypeName", "src": "5434:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "5394:70:12"}, "returnParameters": {"id": 12011, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12010, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12024, "src": "5512:16:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 12008, "name": "address", "nodeType": "ElementaryTypeName", "src": "5512:7:12", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 12009, "nodeType": "ArrayTypeName", "src": "5512:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "5511:18:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 12047, "nodeType": "FunctionDefinition", "src": "5629:188:12", "nodes": [], "body": {"id": 12046, "nodeType": "Block", "src": "5736:81:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 12036, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12026, "src": "5763:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12037, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12028, "src": "5769:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12035, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "5753:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 12038, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5753:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 12043, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12030, "src": "5798:12:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 12044, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "5753:57:12", "trueExpression": {"arguments": [{"id": 12040, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12026, "src": "5785:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12041, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12028, "src": "5791:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12039, "name": "readBool", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11734, "src": "5776:8:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) pure returns (bool)"}}, "id": 12042, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5776:19:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 12034, "id": 12045, "nodeType": "Return", "src": "5746:64:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBoolOr", "nameLocation": "5638:10:12", "parameters": {"id": 12031, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12026, "mutability": "mutable", "name": "toml", "nameLocation": "5663:4:12", "nodeType": "VariableDeclaration", "scope": 12047, "src": "5649:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12025, "name": "string", "nodeType": "ElementaryTypeName", "src": "5649:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12028, "mutability": "mutable", "name": "key", "nameLocation": "5683:3:12", "nodeType": "VariableDeclaration", "scope": 12047, "src": "5669:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12027, "name": "string", "nodeType": "ElementaryTypeName", "src": "5669:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12030, "mutability": "mutable", "name": "defaultValue", "nameLocation": "5693:12:12", "nodeType": "VariableDeclaration", "scope": 12047, "src": "5688:17:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 12029, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5688:4:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "5648:58:12"}, "returnParameters": {"id": 12034, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12033, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12047, "src": "5730:4:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 12032, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5730:4:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "5729:6:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 12072, "nodeType": "FunctionDefinition", "src": "5823:244:12", "nodes": [], "body": {"id": 12071, "nodeType": "Block", "src": "5981:86:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 12061, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12049, "src": "6008:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12062, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12051, "src": "6014:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12060, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "5998:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 12063, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5998:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 12068, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12054, "src": "6048:12:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}}, "id": 12069, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "5998:62:12", "trueExpression": {"arguments": [{"id": 12065, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12049, "src": "6035:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12066, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12051, "src": "6041:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12064, "name": "readBoolArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11751, "src": "6021:13:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bool_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (bool[] memory)"}}, "id": 12067, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6021:24:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}}, "functionReturnParameters": 12059, "id": 12070, "nodeType": "Return", "src": "5991:69:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBoolArrayOr", "nameLocation": "5832:15:12", "parameters": {"id": 12055, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12049, "mutability": "mutable", "name": "toml", "nameLocation": "5862:4:12", "nodeType": "VariableDeclaration", "scope": 12072, "src": "5848:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12048, "name": "string", "nodeType": "ElementaryTypeName", "src": "5848:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12051, "mutability": "mutable", "name": "key", "nameLocation": "5882:3:12", "nodeType": "VariableDeclaration", "scope": 12072, "src": "5868:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12050, "name": "string", "nodeType": "ElementaryTypeName", "src": "5868:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12054, "mutability": "mutable", "name": "defaultValue", "nameLocation": "5901:12:12", "nodeType": "VariableDeclaration", "scope": 12072, "src": "5887:26:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[]"}, "typeName": {"baseType": {"id": 12052, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5887:4:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 12053, "nodeType": "ArrayTypeName", "src": "5887:6:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_storage_ptr", "typeString": "bool[]"}}, "visibility": "internal"}], "src": "5847:67:12"}, "returnParameters": {"id": 12059, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12058, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12072, "src": "5962:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[]"}, "typeName": {"baseType": {"id": 12056, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5962:4:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 12057, "nodeType": "ArrayTypeName", "src": "5962:6:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_storage_ptr", "typeString": "bool[]"}}, "visibility": "internal"}], "src": "5961:15:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 12095, "nodeType": "FunctionDefinition", "src": "6073:234:12", "nodes": [], "body": {"id": 12094, "nodeType": "Block", "src": "6225:82:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 12084, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12074, "src": "6252:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12085, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12076, "src": "6258:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12083, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "6242:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 12086, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6242:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 12091, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12078, "src": "6288:12:12", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 12092, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "6242:58:12", "trueExpression": {"arguments": [{"id": 12088, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12074, "src": "6275:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12089, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12076, "src": "6281:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12087, "name": "readBytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11767, "src": "6265:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (bytes memory)"}}, "id": 12090, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6265:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "functionReturnParameters": 12082, "id": 12093, "nodeType": "Return", "src": "6235:65:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytesOr", "nameLocation": "6082:11:12", "parameters": {"id": 12079, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12074, "mutability": "mutable", "name": "toml", "nameLocation": "6108:4:12", "nodeType": "VariableDeclaration", "scope": 12095, "src": "6094:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12073, "name": "string", "nodeType": "ElementaryTypeName", "src": "6094:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12076, "mutability": "mutable", "name": "key", "nameLocation": "6128:3:12", "nodeType": "VariableDeclaration", "scope": 12095, "src": "6114:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12075, "name": "string", "nodeType": "ElementaryTypeName", "src": "6114:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12078, "mutability": "mutable", "name": "defaultValue", "nameLocation": "6146:12:12", "nodeType": "VariableDeclaration", "scope": 12095, "src": "6133:25:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 12077, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6133:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "6093:66:12"}, "returnParameters": {"id": 12082, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12081, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12095, "src": "6207:12:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 12080, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6207:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "6206:14:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 12120, "nodeType": "FunctionDefinition", "src": "6313:248:12", "nodes": [], "body": {"id": 12119, "nodeType": "Block", "src": "6474:87:12", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 12109, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12097, "src": "6501:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12110, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12099, "src": "6507:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12108, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11537, "src": "6491:9:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 12111, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6491:20:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 12116, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12102, "src": "6542:12:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}, "id": 12117, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "6491:63:12", "trueExpression": {"arguments": [{"id": 12113, "name": "toml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12097, "src": "6529:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12114, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12099, "src": "6535:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12112, "name": "readBytesArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11784, "src": "6514:14:12", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bytes_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (bytes memory[] memory)"}}, "id": 12115, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6514:25:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}, "functionReturnParameters": 12107, "id": 12118, "nodeType": "Return", "src": "6484:70:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytesArrayOr", "nameLocation": "6322:16:12", "parameters": {"id": 12103, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12097, "mutability": "mutable", "name": "toml", "nameLocation": "6353:4:12", "nodeType": "VariableDeclaration", "scope": 12120, "src": "6339:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12096, "name": "string", "nodeType": "ElementaryTypeName", "src": "6339:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12099, "mutability": "mutable", "name": "key", "nameLocation": "6373:3:12", "nodeType": "VariableDeclaration", "scope": 12120, "src": "6359:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12098, "name": "string", "nodeType": "ElementaryTypeName", "src": "6359:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12102, "mutability": "mutable", "name": "defaultValue", "nameLocation": "6393:12:12", "nodeType": "VariableDeclaration", "scope": 12120, "src": "6378:27:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 12100, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6378:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 12101, "nodeType": "ArrayTypeName", "src": "6378:7:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "src": "6338:68:12"}, "returnParameters": {"id": 12107, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12106, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12120, "src": "6454:14:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 12104, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6454:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 12105, "nodeType": "ArrayTypeName", "src": "6454:7:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "src": "6453:16:12"}, "scope": 12443, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 12136, "nodeType": "FunctionDefinition", "src": "6567:162:12", "nodes": [], "body": {"id": 12135, "nodeType": "Block", "src": "6668:61:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12131, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12122, "src": "6702:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12132, "name": "rootObject", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12124, "src": "6711:10:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 12129, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "6685:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12130, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6688:13:12", "memberName": "serializeJ<PERSON>", "nodeType": "MemberAccess", "referencedDeclaration": 15060, "src": "6685:16:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) external returns (string memory)"}}, "id": 12133, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6685:37:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12128, "id": 12134, "nodeType": "Return", "src": "6678:44:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "6576:9:12", "parameters": {"id": 12125, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12122, "mutability": "mutable", "name": "jsonKey", "nameLocation": "6600:7:12", "nodeType": "VariableDeclaration", "scope": 12136, "src": "6586:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12121, "name": "string", "nodeType": "ElementaryTypeName", "src": "6586:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12124, "mutability": "mutable", "name": "rootObject", "nameLocation": "6623:10:12", "nodeType": "VariableDeclaration", "scope": 12136, "src": "6609:24:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12123, "name": "string", "nodeType": "ElementaryTypeName", "src": "6609:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6585:49:12"}, "returnParameters": {"id": 12128, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12127, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12136, "src": "6653:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12126, "name": "string", "nodeType": "ElementaryTypeName", "src": "6653:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6652:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12155, "nodeType": "FunctionDefinition", "src": "6735:167:12", "nodes": [], "body": {"id": 12154, "nodeType": "Block", "src": "6841:61:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12149, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12138, "src": "6875:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12150, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12140, "src": "6884:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12151, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12142, "src": "6889:5:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 12147, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "6858:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12148, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6861:13:12", "memberName": "serializeBool", "nodeType": "MemberAccess", "referencedDeclaration": 14962, "src": "6858:16:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bool) external returns (string memory)"}}, "id": 12152, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6858:37:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12146, "id": 12153, "nodeType": "Return", "src": "6851:44:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "6744:9:12", "parameters": {"id": 12143, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12138, "mutability": "mutable", "name": "jsonKey", "nameLocation": "6768:7:12", "nodeType": "VariableDeclaration", "scope": 12155, "src": "6754:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12137, "name": "string", "nodeType": "ElementaryTypeName", "src": "6754:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12140, "mutability": "mutable", "name": "key", "nameLocation": "6791:3:12", "nodeType": "VariableDeclaration", "scope": 12155, "src": "6777:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12139, "name": "string", "nodeType": "ElementaryTypeName", "src": "6777:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12142, "mutability": "mutable", "name": "value", "nameLocation": "6801:5:12", "nodeType": "VariableDeclaration", "scope": 12155, "src": "6796:10:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 12141, "name": "bool", "nodeType": "ElementaryTypeName", "src": "6796:4:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "6753:54:12"}, "returnParameters": {"id": 12146, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12145, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12155, "src": "6826:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12144, "name": "string", "nodeType": "ElementaryTypeName", "src": "6826:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6825:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12175, "nodeType": "FunctionDefinition", "src": "6908:196:12", "nodes": [], "body": {"id": 12174, "nodeType": "Block", "src": "7043:61:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12169, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12157, "src": "7077:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12170, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12159, "src": "7086:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12171, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12162, "src": "7091:5:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}], "expression": {"id": 12167, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "7060:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12168, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7063:13:12", "memberName": "serializeBool", "nodeType": "MemberAccess", "referencedDeclaration": 14975, "src": "7060:16:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_bool_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bool[] memory) external returns (string memory)"}}, "id": 12172, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7060:37:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12166, "id": 12173, "nodeType": "Return", "src": "7053:44:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "6917:9:12", "parameters": {"id": 12163, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12157, "mutability": "mutable", "name": "jsonKey", "nameLocation": "6941:7:12", "nodeType": "VariableDeclaration", "scope": 12175, "src": "6927:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12156, "name": "string", "nodeType": "ElementaryTypeName", "src": "6927:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12159, "mutability": "mutable", "name": "key", "nameLocation": "6964:3:12", "nodeType": "VariableDeclaration", "scope": 12175, "src": "6950:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12158, "name": "string", "nodeType": "ElementaryTypeName", "src": "6950:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12162, "mutability": "mutable", "name": "value", "nameLocation": "6983:5:12", "nodeType": "VariableDeclaration", "scope": 12175, "src": "6969:19:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[]"}, "typeName": {"baseType": {"id": 12160, "name": "bool", "nodeType": "ElementaryTypeName", "src": "6969:4:12", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 12161, "nodeType": "ArrayTypeName", "src": "6969:6:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_storage_ptr", "typeString": "bool[]"}}, "visibility": "internal"}], "src": "6926:63:12"}, "returnParameters": {"id": 12166, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12165, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12175, "src": "7024:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12164, "name": "string", "nodeType": "ElementaryTypeName", "src": "7024:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7023:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12194, "nodeType": "FunctionDefinition", "src": "7110:170:12", "nodes": [], "body": {"id": 12193, "nodeType": "Block", "src": "7219:61:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12188, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12177, "src": "7253:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12189, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12179, "src": "7262:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12190, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12181, "src": "7267:5:12", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 12186, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "7236:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12187, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7239:13:12", "memberName": "serializeUint", "nodeType": "MemberAccess", "referencedDeclaration": 15133, "src": "7236:16:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,uint256) external returns (string memory)"}}, "id": 12191, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7236:37:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12185, "id": 12192, "nodeType": "Return", "src": "7229:44:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "7119:9:12", "parameters": {"id": 12182, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12177, "mutability": "mutable", "name": "jsonKey", "nameLocation": "7143:7:12", "nodeType": "VariableDeclaration", "scope": 12194, "src": "7129:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12176, "name": "string", "nodeType": "ElementaryTypeName", "src": "7129:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12179, "mutability": "mutable", "name": "key", "nameLocation": "7166:3:12", "nodeType": "VariableDeclaration", "scope": 12194, "src": "7152:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12178, "name": "string", "nodeType": "ElementaryTypeName", "src": "7152:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12181, "mutability": "mutable", "name": "value", "nameLocation": "7179:5:12", "nodeType": "VariableDeclaration", "scope": 12194, "src": "7171:13:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12180, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7171:7:12", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7128:57:12"}, "returnParameters": {"id": 12185, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12184, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12194, "src": "7204:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12183, "name": "string", "nodeType": "ElementaryTypeName", "src": "7204:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7203:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12214, "nodeType": "FunctionDefinition", "src": "7286:199:12", "nodes": [], "body": {"id": 12213, "nodeType": "Block", "src": "7424:61:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12208, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12196, "src": "7458:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12209, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12198, "src": "7467:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12210, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12201, "src": "7472:5:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}], "expression": {"id": 12206, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "7441:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12207, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7444:13:12", "memberName": "serializeUint", "nodeType": "MemberAccess", "referencedDeclaration": 15146, "src": "7441:16:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_uint256_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,uint256[] memory) external returns (string memory)"}}, "id": 12211, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7441:37:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12205, "id": 12212, "nodeType": "Return", "src": "7434:44:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "7295:9:12", "parameters": {"id": 12202, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12196, "mutability": "mutable", "name": "jsonKey", "nameLocation": "7319:7:12", "nodeType": "VariableDeclaration", "scope": 12214, "src": "7305:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12195, "name": "string", "nodeType": "ElementaryTypeName", "src": "7305:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12198, "mutability": "mutable", "name": "key", "nameLocation": "7342:3:12", "nodeType": "VariableDeclaration", "scope": 12214, "src": "7328:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12197, "name": "string", "nodeType": "ElementaryTypeName", "src": "7328:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12201, "mutability": "mutable", "name": "value", "nameLocation": "7364:5:12", "nodeType": "VariableDeclaration", "scope": 12214, "src": "7347:22:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 12199, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7347:7:12", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 12200, "nodeType": "ArrayTypeName", "src": "7347:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "7304:66:12"}, "returnParameters": {"id": 12205, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12204, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12214, "src": "7405:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12203, "name": "string", "nodeType": "ElementaryTypeName", "src": "7405:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7404:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12233, "nodeType": "FunctionDefinition", "src": "7491:168:12", "nodes": [], "body": {"id": 12232, "nodeType": "Block", "src": "7599:60:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12227, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12216, "src": "7632:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12228, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12218, "src": "7641:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12229, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12220, "src": "7646:5:12", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 12225, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "7616:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12226, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7619:12:12", "memberName": "serializeInt", "nodeType": "MemberAccess", "referencedDeclaration": 15037, "src": "7616:15:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,int256) external returns (string memory)"}}, "id": 12230, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7616:36:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12224, "id": 12231, "nodeType": "Return", "src": "7609:43:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "7500:9:12", "parameters": {"id": 12221, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12216, "mutability": "mutable", "name": "jsonKey", "nameLocation": "7524:7:12", "nodeType": "VariableDeclaration", "scope": 12233, "src": "7510:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12215, "name": "string", "nodeType": "ElementaryTypeName", "src": "7510:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12218, "mutability": "mutable", "name": "key", "nameLocation": "7547:3:12", "nodeType": "VariableDeclaration", "scope": 12233, "src": "7533:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12217, "name": "string", "nodeType": "ElementaryTypeName", "src": "7533:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12220, "mutability": "mutable", "name": "value", "nameLocation": "7559:5:12", "nodeType": "VariableDeclaration", "scope": 12233, "src": "7552:12:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 12219, "name": "int256", "nodeType": "ElementaryTypeName", "src": "7552:6:12", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "7509:56:12"}, "returnParameters": {"id": 12224, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12223, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12233, "src": "7584:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12222, "name": "string", "nodeType": "ElementaryTypeName", "src": "7584:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7583:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12253, "nodeType": "FunctionDefinition", "src": "7665:197:12", "nodes": [], "body": {"id": 12252, "nodeType": "Block", "src": "7802:60:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12247, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12235, "src": "7835:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12248, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12237, "src": "7844:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12249, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12240, "src": "7849:5:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}], "expression": {"id": 12245, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "7819:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12246, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7822:12:12", "memberName": "serializeInt", "nodeType": "MemberAccess", "referencedDeclaration": 15050, "src": "7819:15:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_int256_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,int256[] memory) external returns (string memory)"}}, "id": 12250, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7819:36:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12244, "id": 12251, "nodeType": "Return", "src": "7812:43:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "7674:9:12", "parameters": {"id": 12241, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12235, "mutability": "mutable", "name": "jsonKey", "nameLocation": "7698:7:12", "nodeType": "VariableDeclaration", "scope": 12253, "src": "7684:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12234, "name": "string", "nodeType": "ElementaryTypeName", "src": "7684:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12237, "mutability": "mutable", "name": "key", "nameLocation": "7721:3:12", "nodeType": "VariableDeclaration", "scope": 12253, "src": "7707:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12236, "name": "string", "nodeType": "ElementaryTypeName", "src": "7707:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12240, "mutability": "mutable", "name": "value", "nameLocation": "7742:5:12", "nodeType": "VariableDeclaration", "scope": 12253, "src": "7726:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[]"}, "typeName": {"baseType": {"id": 12238, "name": "int256", "nodeType": "ElementaryTypeName", "src": "7726:6:12", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 12239, "nodeType": "ArrayTypeName", "src": "7726:8:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_storage_ptr", "typeString": "int256[]"}}, "visibility": "internal"}], "src": "7683:65:12"}, "returnParameters": {"id": 12244, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12243, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12253, "src": "7783:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12242, "name": "string", "nodeType": "ElementaryTypeName", "src": "7783:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7782:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12272, "nodeType": "FunctionDefinition", "src": "7868:173:12", "nodes": [], "body": {"id": 12271, "nodeType": "Block", "src": "7977:64:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12266, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12255, "src": "8014:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12267, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12257, "src": "8023:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12268, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12259, "src": "8028:5:12", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 12264, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "7994:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12265, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7997:16:12", "memberName": "serializeAddress", "nodeType": "MemberAccess", "referencedDeclaration": 14937, "src": "7994:19:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,address) external returns (string memory)"}}, "id": 12269, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7994:40:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12263, "id": 12270, "nodeType": "Return", "src": "7987:47:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "7877:9:12", "parameters": {"id": 12260, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12255, "mutability": "mutable", "name": "jsonKey", "nameLocation": "7901:7:12", "nodeType": "VariableDeclaration", "scope": 12272, "src": "7887:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12254, "name": "string", "nodeType": "ElementaryTypeName", "src": "7887:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12257, "mutability": "mutable", "name": "key", "nameLocation": "7924:3:12", "nodeType": "VariableDeclaration", "scope": 12272, "src": "7910:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12256, "name": "string", "nodeType": "ElementaryTypeName", "src": "7910:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12259, "mutability": "mutable", "name": "value", "nameLocation": "7937:5:12", "nodeType": "VariableDeclaration", "scope": 12272, "src": "7929:13:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12258, "name": "address", "nodeType": "ElementaryTypeName", "src": "7929:7:12", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "7886:57:12"}, "returnParameters": {"id": 12263, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12262, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12272, "src": "7962:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12261, "name": "string", "nodeType": "ElementaryTypeName", "src": "7962:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7961:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12292, "nodeType": "FunctionDefinition", "src": "8047:202:12", "nodes": [], "body": {"id": 12291, "nodeType": "Block", "src": "8185:64:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12286, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12274, "src": "8222:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12287, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12276, "src": "8231:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12288, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12279, "src": "8236:5:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}], "expression": {"id": 12284, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "8202:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12285, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8205:16:12", "memberName": "serializeAddress", "nodeType": "MemberAccess", "referencedDeclaration": 14950, "src": "8202:19:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_address_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,address[] memory) external returns (string memory)"}}, "id": 12289, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8202:40:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12283, "id": 12290, "nodeType": "Return", "src": "8195:47:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "8056:9:12", "parameters": {"id": 12280, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12274, "mutability": "mutable", "name": "jsonKey", "nameLocation": "8080:7:12", "nodeType": "VariableDeclaration", "scope": 12292, "src": "8066:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12273, "name": "string", "nodeType": "ElementaryTypeName", "src": "8066:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12276, "mutability": "mutable", "name": "key", "nameLocation": "8103:3:12", "nodeType": "VariableDeclaration", "scope": 12292, "src": "8089:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12275, "name": "string", "nodeType": "ElementaryTypeName", "src": "8089:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12279, "mutability": "mutable", "name": "value", "nameLocation": "8125:5:12", "nodeType": "VariableDeclaration", "scope": 12292, "src": "8108:22:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 12277, "name": "address", "nodeType": "ElementaryTypeName", "src": "8108:7:12", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 12278, "nodeType": "ArrayTypeName", "src": "8108:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "8065:66:12"}, "returnParameters": {"id": 12283, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12282, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12292, "src": "8166:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12281, "name": "string", "nodeType": "ElementaryTypeName", "src": "8166:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8165:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12311, "nodeType": "FunctionDefinition", "src": "8255:173:12", "nodes": [], "body": {"id": 12310, "nodeType": "Block", "src": "8364:64:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12305, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12294, "src": "8401:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12306, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12296, "src": "8410:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12307, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12298, "src": "8415:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 12303, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "8381:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12304, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8384:16:12", "memberName": "serializeBytes32", "nodeType": "MemberAccess", "referencedDeclaration": 14987, "src": "8381:19:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bytes32) external returns (string memory)"}}, "id": 12308, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8381:40:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12302, "id": 12309, "nodeType": "Return", "src": "8374:47:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "8264:9:12", "parameters": {"id": 12299, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12294, "mutability": "mutable", "name": "jsonKey", "nameLocation": "8288:7:12", "nodeType": "VariableDeclaration", "scope": 12311, "src": "8274:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12293, "name": "string", "nodeType": "ElementaryTypeName", "src": "8274:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12296, "mutability": "mutable", "name": "key", "nameLocation": "8311:3:12", "nodeType": "VariableDeclaration", "scope": 12311, "src": "8297:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12295, "name": "string", "nodeType": "ElementaryTypeName", "src": "8297:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12298, "mutability": "mutable", "name": "value", "nameLocation": "8324:5:12", "nodeType": "VariableDeclaration", "scope": 12311, "src": "8316:13:12", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 12297, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8316:7:12", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "8273:57:12"}, "returnParameters": {"id": 12302, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12301, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12311, "src": "8349:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12300, "name": "string", "nodeType": "ElementaryTypeName", "src": "8349:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8348:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12331, "nodeType": "FunctionDefinition", "src": "8434:202:12", "nodes": [], "body": {"id": 12330, "nodeType": "Block", "src": "8572:64:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12325, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12313, "src": "8609:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12326, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12315, "src": "8618:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12327, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12318, "src": "8623:5:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}], "expression": {"id": 12323, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "8589:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12324, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8592:16:12", "memberName": "serializeBytes32", "nodeType": "MemberAccess", "referencedDeclaration": 15000, "src": "8589:19:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_bytes32_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bytes32[] memory) external returns (string memory)"}}, "id": 12328, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8589:40:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12322, "id": 12329, "nodeType": "Return", "src": "8582:47:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "8443:9:12", "parameters": {"id": 12319, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12313, "mutability": "mutable", "name": "jsonKey", "nameLocation": "8467:7:12", "nodeType": "VariableDeclaration", "scope": 12331, "src": "8453:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12312, "name": "string", "nodeType": "ElementaryTypeName", "src": "8453:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12315, "mutability": "mutable", "name": "key", "nameLocation": "8490:3:12", "nodeType": "VariableDeclaration", "scope": 12331, "src": "8476:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12314, "name": "string", "nodeType": "ElementaryTypeName", "src": "8476:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12318, "mutability": "mutable", "name": "value", "nameLocation": "8512:5:12", "nodeType": "VariableDeclaration", "scope": 12331, "src": "8495:22:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 12316, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8495:7:12", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 12317, "nodeType": "ArrayTypeName", "src": "8495:9:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "8452:66:12"}, "returnParameters": {"id": 12322, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12321, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12331, "src": "8553:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12320, "name": "string", "nodeType": "ElementaryTypeName", "src": "8553:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8552:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12350, "nodeType": "FunctionDefinition", "src": "8642:176:12", "nodes": [], "body": {"id": 12349, "nodeType": "Block", "src": "8756:62:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12344, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12333, "src": "8791:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12345, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12335, "src": "8800:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12346, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12337, "src": "8805:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 12342, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "8773:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12343, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8776:14:12", "memberName": "serializeBytes", "nodeType": "MemberAccess", "referencedDeclaration": 15012, "src": "8773:17:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bytes memory) external returns (string memory)"}}, "id": 12347, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8773:38:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12341, "id": 12348, "nodeType": "Return", "src": "8766:45:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "8651:9:12", "parameters": {"id": 12338, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12333, "mutability": "mutable", "name": "jsonKey", "nameLocation": "8675:7:12", "nodeType": "VariableDeclaration", "scope": 12350, "src": "8661:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12332, "name": "string", "nodeType": "ElementaryTypeName", "src": "8661:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12335, "mutability": "mutable", "name": "key", "nameLocation": "8698:3:12", "nodeType": "VariableDeclaration", "scope": 12350, "src": "8684:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12334, "name": "string", "nodeType": "ElementaryTypeName", "src": "8684:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12337, "mutability": "mutable", "name": "value", "nameLocation": "8716:5:12", "nodeType": "VariableDeclaration", "scope": 12350, "src": "8703:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 12336, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "8703:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "8660:62:12"}, "returnParameters": {"id": 12341, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12340, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12350, "src": "8741:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12339, "name": "string", "nodeType": "ElementaryTypeName", "src": "8741:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8740:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12370, "nodeType": "FunctionDefinition", "src": "8824:198:12", "nodes": [], "body": {"id": 12369, "nodeType": "Block", "src": "8960:62:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12364, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12352, "src": "8995:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12365, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12354, "src": "9004:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12366, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12357, "src": "9009:5:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}], "expression": {"id": 12362, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "8977:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12363, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8980:14:12", "memberName": "serializeBytes", "nodeType": "MemberAccess", "referencedDeclaration": 15025, "src": "8977:17:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_bytes_memory_ptr_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bytes memory[] memory) external returns (string memory)"}}, "id": 12367, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8977:38:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12361, "id": 12368, "nodeType": "Return", "src": "8970:45:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "8833:9:12", "parameters": {"id": 12358, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12352, "mutability": "mutable", "name": "jsonKey", "nameLocation": "8857:7:12", "nodeType": "VariableDeclaration", "scope": 12370, "src": "8843:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12351, "name": "string", "nodeType": "ElementaryTypeName", "src": "8843:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12354, "mutability": "mutable", "name": "key", "nameLocation": "8880:3:12", "nodeType": "VariableDeclaration", "scope": 12370, "src": "8866:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12353, "name": "string", "nodeType": "ElementaryTypeName", "src": "8866:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12357, "mutability": "mutable", "name": "value", "nameLocation": "8900:5:12", "nodeType": "VariableDeclaration", "scope": 12370, "src": "8885:20:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 12355, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "8885:5:12", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 12356, "nodeType": "ArrayTypeName", "src": "8885:7:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "src": "8842:64:12"}, "returnParameters": {"id": 12361, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12360, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12370, "src": "8941:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12359, "name": "string", "nodeType": "ElementaryTypeName", "src": "8941:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8940:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12389, "nodeType": "FunctionDefinition", "src": "9028:198:12", "nodes": [], "body": {"id": 12388, "nodeType": "Block", "src": "9163:63:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12383, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12372, "src": "9199:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12384, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12374, "src": "9208:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12385, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12376, "src": "9213:5:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 12381, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "9180:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12382, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9183:15:12", "memberName": "serializeString", "nodeType": "MemberAccess", "referencedDeclaration": 15096, "src": "9180:18:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,string memory) external returns (string memory)"}}, "id": 12386, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9180:39:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12380, "id": 12387, "nodeType": "Return", "src": "9173:46:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "9037:9:12", "parameters": {"id": 12377, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12372, "mutability": "mutable", "name": "jsonKey", "nameLocation": "9061:7:12", "nodeType": "VariableDeclaration", "scope": 12389, "src": "9047:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12371, "name": "string", "nodeType": "ElementaryTypeName", "src": "9047:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12374, "mutability": "mutable", "name": "key", "nameLocation": "9084:3:12", "nodeType": "VariableDeclaration", "scope": 12389, "src": "9070:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12373, "name": "string", "nodeType": "ElementaryTypeName", "src": "9070:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12376, "mutability": "mutable", "name": "value", "nameLocation": "9103:5:12", "nodeType": "VariableDeclaration", "scope": 12389, "src": "9089:19:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12375, "name": "string", "nodeType": "ElementaryTypeName", "src": "9089:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9046:63:12"}, "returnParameters": {"id": 12380, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12379, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12389, "src": "9144:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12378, "name": "string", "nodeType": "ElementaryTypeName", "src": "9144:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9143:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12409, "nodeType": "FunctionDefinition", "src": "9232:200:12", "nodes": [], "body": {"id": 12408, "nodeType": "Block", "src": "9369:63:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12403, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12391, "src": "9405:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12404, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12393, "src": "9414:3:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12405, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12396, "src": "9419:5:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}], "expression": {"id": 12401, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "9386:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12402, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9389:15:12", "memberName": "serializeString", "nodeType": "MemberAccess", "referencedDeclaration": 15109, "src": "9386:18:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_string_memory_ptr_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,string memory[] memory) external returns (string memory)"}}, "id": 12406, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9386:39:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 12400, "id": 12407, "nodeType": "Return", "src": "9379:46:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "9241:9:12", "parameters": {"id": 12397, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12391, "mutability": "mutable", "name": "jsonKey", "nameLocation": "9265:7:12", "nodeType": "VariableDeclaration", "scope": 12409, "src": "9251:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12390, "name": "string", "nodeType": "ElementaryTypeName", "src": "9251:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12393, "mutability": "mutable", "name": "key", "nameLocation": "9288:3:12", "nodeType": "VariableDeclaration", "scope": 12409, "src": "9274:17:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12392, "name": "string", "nodeType": "ElementaryTypeName", "src": "9274:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12396, "mutability": "mutable", "name": "value", "nameLocation": "9309:5:12", "nodeType": "VariableDeclaration", "scope": 12409, "src": "9293:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 12394, "name": "string", "nodeType": "ElementaryTypeName", "src": "9293:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 12395, "nodeType": "ArrayTypeName", "src": "9293:8:12", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "9250:65:12"}, "returnParameters": {"id": 12400, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12399, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12409, "src": "9350:13:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12398, "name": "string", "nodeType": "ElementaryTypeName", "src": "9350:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9349:15:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12424, "nodeType": "FunctionDefinition", "src": "9438:111:12", "nodes": [], "body": {"id": 12423, "nodeType": "Block", "src": "9505:44:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12419, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12411, "src": "9528:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12420, "name": "path", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12413, "src": "9537:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 12416, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "9515:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12418, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9518:9:12", "memberName": "writeToml", "nodeType": "MemberAccess", "referencedDeclaration": 17008, "src": "9515:12:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory,string memory) external"}}, "id": 12421, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9515:27:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12422, "nodeType": "ExpressionStatement", "src": "9515:27:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "write", "nameLocation": "9447:5:12", "parameters": {"id": 12414, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12411, "mutability": "mutable", "name": "jsonKey", "nameLocation": "9467:7:12", "nodeType": "VariableDeclaration", "scope": 12424, "src": "9453:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12410, "name": "string", "nodeType": "ElementaryTypeName", "src": "9453:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12413, "mutability": "mutable", "name": "path", "nameLocation": "9490:4:12", "nodeType": "VariableDeclaration", "scope": 12424, "src": "9476:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12412, "name": "string", "nodeType": "ElementaryTypeName", "src": "9476:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9452:43:12"}, "returnParameters": {"id": 12415, "nodeType": "ParameterList", "parameters": [], "src": "9505:0:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 12442, "nodeType": "FunctionDefinition", "src": "9555:145:12", "nodes": [], "body": {"id": 12441, "nodeType": "Block", "src": "9646:54:12", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12436, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12426, "src": "9669:7:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12437, "name": "path", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12428, "src": "9678:4:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 12438, "name": "valueKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12430, "src": "9684:8:12", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 12433, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11521, "src": "9656:2:12", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12435, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9659:9:12", "memberName": "writeToml", "nodeType": "MemberAccess", "referencedDeclaration": 17018, "src": "9656:12:12", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory,string memory,string memory) external"}}, "id": 12439, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9656:37:12", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12440, "nodeType": "ExpressionStatement", "src": "9656:37:12"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "write", "nameLocation": "9564:5:12", "parameters": {"id": 12431, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12426, "mutability": "mutable", "name": "jsonKey", "nameLocation": "9584:7:12", "nodeType": "VariableDeclaration", "scope": 12442, "src": "9570:21:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12425, "name": "string", "nodeType": "ElementaryTypeName", "src": "9570:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12428, "mutability": "mutable", "name": "path", "nameLocation": "9607:4:12", "nodeType": "VariableDeclaration", "scope": 12442, "src": "9593:18:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12427, "name": "string", "nodeType": "ElementaryTypeName", "src": "9593:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 12430, "mutability": "mutable", "name": "valueKey", "nameLocation": "9627:8:12", "nodeType": "VariableDeclaration", "scope": 12442, "src": "9613:22:12", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 12429, "name": "string", "nodeType": "ElementaryTypeName", "src": "9613:6:12", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9569:67:12"}, "returnParameters": {"id": 12432, "nodeType": "ParameterList", "parameters": [], "src": "9646:0:12"}, "scope": 12443, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}], "abstract": false, "baseContracts": [], "canonicalName": "stdToml", "contractDependencies": [], "contractKind": "library", "fullyImplemented": true, "linearizedBaseContracts": [12443], "name": "stdToml", "nameLocation": "618:7:12", "scope": 12444, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 12}