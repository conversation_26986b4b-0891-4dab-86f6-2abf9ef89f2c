{"abi": [], "bytecode": {"object": "0x6055604b600b8282823980515f1a607314603f577f4e487b71000000000000000000000000000000000000000000000000000000005f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220cdb4ce1cf9a478adb073dc3763e638c818fbfbb13f34ec528e5fa551b9b00b7364736f6c634300081d0033", "sourceMap": "610:9092:8:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220cdb4ce1cf9a478adb073dc3763e638c818fbfbb13f34ec528e5fa551b9b00b7364736f6c634300081d0033", "sourceMap": "610:9092:8:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdJson.sol\":\"stdJson\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdJson.sol": "stdJson"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/forge-std/src/StdJson.sol", "id": 8152, "exportedSymbols": {"VmSafe": [17281], "stdJson": [8151]}, "nodeType": "SourceUnit", "src": "32:9671:8", "nodes": [{"id": 7209, "nodeType": "PragmaDirective", "src": "32:31:8", "nodes": [], "literals": ["solidity", ">=", "0.6", ".0", "<", "0.9", ".0"]}, {"id": 7210, "nodeType": "PragmaDirective", "src": "65:33:8", "nodes": [], "literals": ["experimental", "ABIEncoderV2"]}, {"id": 7212, "nodeType": "ImportDirective", "src": "100:32:8", "nodes": [], "absolutePath": "lib/forge-std/src/Vm.sol", "file": "./Vm.sol", "nameLocation": "-1:-1:-1", "scope": 8152, "sourceUnit": 18353, "symbolAliases": [{"foreign": {"id": 7211, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "108:6:8", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 8151, "nodeType": "ContractDefinition", "src": "610:9092:8", "nodes": [{"id": 7229, "nodeType": "VariableDeclaration", "src": "632:92:8", "nodes": [], "constant": true, "mutability": "constant", "name": "vm", "nameLocation": "656:2:8", "scope": 8151, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}, "typeName": {"id": 7214, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 7213, "name": "VmSafe", "nameLocations": ["632:6:8"], "nodeType": "IdentifierPath", "referencedDeclaration": 17281, "src": "632:6:8"}, "referencedDeclaration": 17281, "src": "632:6:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "value": {"arguments": [{"arguments": [{"arguments": [{"arguments": [{"arguments": [{"hexValue": "6865766d20636865617420636f6465", "id": 7223, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "702:17:8", "typeDescriptions": {"typeIdentifier": "t_stringliteral_885cb69240a935d632d79c317109709ecfa91a80626ff3989d68f67f5b1dd12d", "typeString": "literal_string \"hevm cheat code\""}, "value": "hevm cheat code"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_885cb69240a935d632d79c317109709ecfa91a80626ff3989d68f67f5b1dd12d", "typeString": "literal_string \"hevm cheat code\""}], "id": 7222, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "692:9:8", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 7224, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "692:28:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 7221, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "684:7:8", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 7220, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "684:7:8", "typeDescriptions": {}}}, "id": 7225, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "684:37:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 7219, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "676:7:8", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 7218, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "676:7:8", "typeDescriptions": {}}}, "id": 7226, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "676:46:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 7217, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "668:7:8", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 7216, "name": "address", "nodeType": "ElementaryTypeName", "src": "668:7:8", "typeDescriptions": {}}}, "id": 7227, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "668:55:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 7215, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "661:6:8", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_VmSafe_$17281_$", "typeString": "type(contract VmSafe)"}}, "id": 7228, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "661:63:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "visibility": "private"}, {"id": 7245, "nodeType": "FunctionDefinition", "src": "731:138:8", "nodes": [], "body": {"id": 7244, "nodeType": "Block", "src": "818:51:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7240, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7231, "src": "852:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7241, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7233, "src": "858:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7238, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "835:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7239, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "838:13:8", "memberName": "keyExists<PERSON>son", "nodeType": "MemberAccess", "referencedDeclaration": 14715, "src": "835:16:8", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view external returns (bool)"}}, "id": 7242, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "835:27:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 7237, "id": 7243, "nodeType": "Return", "src": "828:34:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "keyExists", "nameLocation": "740:9:8", "parameters": {"id": 7234, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7231, "mutability": "mutable", "name": "json", "nameLocation": "764:4:8", "nodeType": "VariableDeclaration", "scope": 7245, "src": "750:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7230, "name": "string", "nodeType": "ElementaryTypeName", "src": "750:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7233, "mutability": "mutable", "name": "key", "nameLocation": "784:3:8", "nodeType": "VariableDeclaration", "scope": 7245, "src": "770:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7232, "name": "string", "nodeType": "ElementaryTypeName", "src": "770:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "749:39:8"}, "returnParameters": {"id": 7237, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7236, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7245, "src": "812:4:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 7235, "name": "bool", "nodeType": "ElementaryTypeName", "src": "812:4:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "811:6:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7261, "nodeType": "FunctionDefinition", "src": "875:141:8", "nodes": [], "body": {"id": 7260, "nodeType": "Block", "src": "969:47:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7256, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7247, "src": "999:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7257, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7249, "src": "1005:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7254, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "986:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7255, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "989:9:8", "memberName": "parseJson", "nodeType": "MemberAccess", "referencedDeclaration": 14925, "src": "986:12:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (bytes memory)"}}, "id": 7258, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "986:23:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "functionReturnParameters": 7253, "id": 7259, "nodeType": "Return", "src": "979:30:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "parseRaw", "nameLocation": "884:8:8", "parameters": {"id": 7250, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7247, "mutability": "mutable", "name": "json", "nameLocation": "907:4:8", "nodeType": "VariableDeclaration", "scope": 7261, "src": "893:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7246, "name": "string", "nodeType": "ElementaryTypeName", "src": "893:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7249, "mutability": "mutable", "name": "key", "nameLocation": "927:3:8", "nodeType": "VariableDeclaration", "scope": 7261, "src": "913:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7248, "name": "string", "nodeType": "ElementaryTypeName", "src": "913:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "892:39:8"}, "returnParameters": {"id": 7253, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7252, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7261, "src": "955:12:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 7251, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "955:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "954:14:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7277, "nodeType": "FunctionDefinition", "src": "1022:140:8", "nodes": [], "body": {"id": 7276, "nodeType": "Block", "src": "1111:51:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7272, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7263, "src": "1145:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7273, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7265, "src": "1151:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7270, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "1128:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7271, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1131:13:8", "memberName": "parseJsonUint", "nodeType": "MemberAccess", "referencedDeclaration": 14896, "src": "1128:16:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_uint256_$", "typeString": "function (string memory,string memory) pure external returns (uint256)"}}, "id": 7274, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1128:27:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 7269, "id": 7275, "nodeType": "Return", "src": "1121:34:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readUint", "nameLocation": "1031:8:8", "parameters": {"id": 7266, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7263, "mutability": "mutable", "name": "json", "nameLocation": "1054:4:8", "nodeType": "VariableDeclaration", "scope": 7277, "src": "1040:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7262, "name": "string", "nodeType": "ElementaryTypeName", "src": "1040:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7265, "mutability": "mutable", "name": "key", "nameLocation": "1074:3:8", "nodeType": "VariableDeclaration", "scope": 7277, "src": "1060:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7264, "name": "string", "nodeType": "ElementaryTypeName", "src": "1060:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1039:39:8"}, "returnParameters": {"id": 7269, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7268, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7277, "src": "1102:7:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 7267, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1102:7:8", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1101:9:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7294, "nodeType": "FunctionDefinition", "src": "1168:159:8", "nodes": [], "body": {"id": 7293, "nodeType": "Block", "src": "1271:56:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7289, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7279, "src": "1310:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7290, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7281, "src": "1316:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7287, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "1288:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7288, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1291:18:8", "memberName": "parseJsonUintArray", "nodeType": "MemberAccess", "referencedDeclaration": 14907, "src": "1288:21:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (uint256[] memory)"}}, "id": 7291, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1288:32:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "functionReturnParameters": 7286, "id": 7292, "nodeType": "Return", "src": "1281:39:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readUintArray", "nameLocation": "1177:13:8", "parameters": {"id": 7282, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7279, "mutability": "mutable", "name": "json", "nameLocation": "1205:4:8", "nodeType": "VariableDeclaration", "scope": 7294, "src": "1191:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7278, "name": "string", "nodeType": "ElementaryTypeName", "src": "1191:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7281, "mutability": "mutable", "name": "key", "nameLocation": "1225:3:8", "nodeType": "VariableDeclaration", "scope": 7294, "src": "1211:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7280, "name": "string", "nodeType": "ElementaryTypeName", "src": "1211:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1190:39:8"}, "returnParameters": {"id": 7286, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7285, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7294, "src": "1253:16:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 7283, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1253:7:8", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 7284, "nodeType": "ArrayTypeName", "src": "1253:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "1252:18:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7310, "nodeType": "FunctionDefinition", "src": "1333:137:8", "nodes": [], "body": {"id": 7309, "nodeType": "Block", "src": "1420:50:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7305, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7296, "src": "1453:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7306, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7298, "src": "1459:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7303, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "1437:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7304, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1440:12:8", "memberName": "parseJsonInt", "nodeType": "MemberAccess", "referencedDeclaration": 14809, "src": "1437:15:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_int256_$", "typeString": "function (string memory,string memory) pure external returns (int256)"}}, "id": 7307, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1437:26:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "functionReturnParameters": 7302, "id": 7308, "nodeType": "Return", "src": "1430:33:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readInt", "nameLocation": "1342:7:8", "parameters": {"id": 7299, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7296, "mutability": "mutable", "name": "json", "nameLocation": "1364:4:8", "nodeType": "VariableDeclaration", "scope": 7310, "src": "1350:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7295, "name": "string", "nodeType": "ElementaryTypeName", "src": "1350:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7298, "mutability": "mutable", "name": "key", "nameLocation": "1384:3:8", "nodeType": "VariableDeclaration", "scope": 7310, "src": "1370:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7297, "name": "string", "nodeType": "ElementaryTypeName", "src": "1370:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1349:39:8"}, "returnParameters": {"id": 7302, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7301, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7310, "src": "1412:6:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 7300, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1412:6:8", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "1411:8:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7327, "nodeType": "FunctionDefinition", "src": "1476:156:8", "nodes": [], "body": {"id": 7326, "nodeType": "Block", "src": "1577:55:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7322, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7312, "src": "1615:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7323, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7314, "src": "1621:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7320, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "1594:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7321, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1597:17:8", "memberName": "parseJsonIntArray", "nodeType": "MemberAccess", "referencedDeclaration": 14820, "src": "1594:20:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_int256_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (int256[] memory)"}}, "id": 7324, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1594:31:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}}, "functionReturnParameters": 7319, "id": 7325, "nodeType": "Return", "src": "1587:38:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readIntArray", "nameLocation": "1485:12:8", "parameters": {"id": 7315, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7312, "mutability": "mutable", "name": "json", "nameLocation": "1512:4:8", "nodeType": "VariableDeclaration", "scope": 7327, "src": "1498:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7311, "name": "string", "nodeType": "ElementaryTypeName", "src": "1498:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7314, "mutability": "mutable", "name": "key", "nameLocation": "1532:3:8", "nodeType": "VariableDeclaration", "scope": 7327, "src": "1518:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7313, "name": "string", "nodeType": "ElementaryTypeName", "src": "1518:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1497:39:8"}, "returnParameters": {"id": 7319, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7318, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7327, "src": "1560:15:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[]"}, "typeName": {"baseType": {"id": 7316, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1560:6:8", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 7317, "nodeType": "ArrayTypeName", "src": "1560:8:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_storage_ptr", "typeString": "int256[]"}}, "visibility": "internal"}], "src": "1559:17:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7343, "nodeType": "FunctionDefinition", "src": "1638:146:8", "nodes": [], "body": {"id": 7342, "nodeType": "Block", "src": "1730:54:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7338, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7329, "src": "1767:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7339, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7331, "src": "1773:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7336, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "1747:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7337, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1750:16:8", "memberName": "parseJsonBytes32", "nodeType": "MemberAccess", "referencedDeclaration": 14777, "src": "1747:19:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (string memory,string memory) pure external returns (bytes32)"}}, "id": 7340, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1747:30:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 7335, "id": 7341, "nodeType": "Return", "src": "1740:37:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytes32", "nameLocation": "1647:11:8", "parameters": {"id": 7332, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7329, "mutability": "mutable", "name": "json", "nameLocation": "1673:4:8", "nodeType": "VariableDeclaration", "scope": 7343, "src": "1659:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7328, "name": "string", "nodeType": "ElementaryTypeName", "src": "1659:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7331, "mutability": "mutable", "name": "key", "nameLocation": "1693:3:8", "nodeType": "VariableDeclaration", "scope": 7343, "src": "1679:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7330, "name": "string", "nodeType": "ElementaryTypeName", "src": "1679:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1658:39:8"}, "returnParameters": {"id": 7335, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7334, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7343, "src": "1721:7:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 7333, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1721:7:8", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "1720:9:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7360, "nodeType": "FunctionDefinition", "src": "1790:165:8", "nodes": [], "body": {"id": 7359, "nodeType": "Block", "src": "1896:59:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7355, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7345, "src": "1938:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7356, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7347, "src": "1944:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7353, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "1913:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7354, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1916:21:8", "memberName": "parseJsonBytes32Array", "nodeType": "MemberAccess", "referencedDeclaration": 14788, "src": "1913:24:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bytes32_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (bytes32[] memory)"}}, "id": 7357, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1913:35:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "functionReturnParameters": 7352, "id": 7358, "nodeType": "Return", "src": "1906:42:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytes32Array", "nameLocation": "1799:16:8", "parameters": {"id": 7348, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7345, "mutability": "mutable", "name": "json", "nameLocation": "1830:4:8", "nodeType": "VariableDeclaration", "scope": 7360, "src": "1816:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7344, "name": "string", "nodeType": "ElementaryTypeName", "src": "1816:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7347, "mutability": "mutable", "name": "key", "nameLocation": "1850:3:8", "nodeType": "VariableDeclaration", "scope": 7360, "src": "1836:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7346, "name": "string", "nodeType": "ElementaryTypeName", "src": "1836:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1815:39:8"}, "returnParameters": {"id": 7352, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7351, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7360, "src": "1878:16:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 7349, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1878:7:8", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 7350, "nodeType": "ArrayTypeName", "src": "1878:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "1877:18:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7376, "nodeType": "FunctionDefinition", "src": "1961:150:8", "nodes": [], "body": {"id": 7375, "nodeType": "Block", "src": "2058:53:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7371, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7362, "src": "2094:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7372, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7364, "src": "2100:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7369, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "2075:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7370, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2078:15:8", "memberName": "parseJsonString", "nodeType": "MemberAccess", "referencedDeclaration": 14841, "src": "2075:18:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (string memory)"}}, "id": 7373, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2075:29:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7368, "id": 7374, "nodeType": "Return", "src": "2068:36:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readString", "nameLocation": "1970:10:8", "parameters": {"id": 7365, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7362, "mutability": "mutable", "name": "json", "nameLocation": "1995:4:8", "nodeType": "VariableDeclaration", "scope": 7376, "src": "1981:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7361, "name": "string", "nodeType": "ElementaryTypeName", "src": "1981:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7364, "mutability": "mutable", "name": "key", "nameLocation": "2015:3:8", "nodeType": "VariableDeclaration", "scope": 7376, "src": "2001:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7363, "name": "string", "nodeType": "ElementaryTypeName", "src": "2001:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1980:39:8"}, "returnParameters": {"id": 7368, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7367, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7376, "src": "2043:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7366, "name": "string", "nodeType": "ElementaryTypeName", "src": "2043:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2042:15:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7393, "nodeType": "FunctionDefinition", "src": "2117:162:8", "nodes": [], "body": {"id": 7392, "nodeType": "Block", "src": "2221:58:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7388, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7378, "src": "2262:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7389, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7380, "src": "2268:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7386, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "2238:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7387, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2241:20:8", "memberName": "parseJsonStringArray", "nodeType": "MemberAccess", "referencedDeclaration": 14852, "src": "2238:23:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_string_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (string memory[] memory)"}}, "id": 7390, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2238:34:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "functionReturnParameters": 7385, "id": 7391, "nodeType": "Return", "src": "2231:41:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readStringArray", "nameLocation": "2126:15:8", "parameters": {"id": 7381, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7378, "mutability": "mutable", "name": "json", "nameLocation": "2156:4:8", "nodeType": "VariableDeclaration", "scope": 7393, "src": "2142:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7377, "name": "string", "nodeType": "ElementaryTypeName", "src": "2142:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7380, "mutability": "mutable", "name": "key", "nameLocation": "2176:3:8", "nodeType": "VariableDeclaration", "scope": 7393, "src": "2162:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7379, "name": "string", "nodeType": "ElementaryTypeName", "src": "2162:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2141:39:8"}, "returnParameters": {"id": 7385, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7384, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7393, "src": "2204:15:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 7382, "name": "string", "nodeType": "ElementaryTypeName", "src": "2204:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 7383, "nodeType": "ArrayTypeName", "src": "2204:8:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "2203:17:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7409, "nodeType": "FunctionDefinition", "src": "2285:146:8", "nodes": [], "body": {"id": 7408, "nodeType": "Block", "src": "2377:54:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7404, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7395, "src": "2414:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7405, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7397, "src": "2420:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7402, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "2394:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7403, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2397:16:8", "memberName": "parseJson<PERSON>dd<PERSON>", "nodeType": "MemberAccess", "referencedDeclaration": 14725, "src": "2394:19:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_address_$", "typeString": "function (string memory,string memory) pure external returns (address)"}}, "id": 7406, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2394:30:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 7401, "id": 7407, "nodeType": "Return", "src": "2387:37:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readAddress", "nameLocation": "2294:11:8", "parameters": {"id": 7398, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7395, "mutability": "mutable", "name": "json", "nameLocation": "2320:4:8", "nodeType": "VariableDeclaration", "scope": 7409, "src": "2306:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7394, "name": "string", "nodeType": "ElementaryTypeName", "src": "2306:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7397, "mutability": "mutable", "name": "key", "nameLocation": "2340:3:8", "nodeType": "VariableDeclaration", "scope": 7409, "src": "2326:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7396, "name": "string", "nodeType": "ElementaryTypeName", "src": "2326:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2305:39:8"}, "returnParameters": {"id": 7401, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7400, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7409, "src": "2368:7:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 7399, "name": "address", "nodeType": "ElementaryTypeName", "src": "2368:7:8", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2367:9:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7426, "nodeType": "FunctionDefinition", "src": "2437:165:8", "nodes": [], "body": {"id": 7425, "nodeType": "Block", "src": "2543:59:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7421, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7411, "src": "2585:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7422, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7413, "src": "2591:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7419, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "2560:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7420, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2563:21:8", "memberName": "parseJsonAddressArray", "nodeType": "MemberAccess", "referencedDeclaration": 14736, "src": "2560:24:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_address_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (address[] memory)"}}, "id": 7423, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2560:35:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "functionReturnParameters": 7418, "id": 7424, "nodeType": "Return", "src": "2553:42:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readAddressArray", "nameLocation": "2446:16:8", "parameters": {"id": 7414, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7411, "mutability": "mutable", "name": "json", "nameLocation": "2477:4:8", "nodeType": "VariableDeclaration", "scope": 7426, "src": "2463:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7410, "name": "string", "nodeType": "ElementaryTypeName", "src": "2463:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7413, "mutability": "mutable", "name": "key", "nameLocation": "2497:3:8", "nodeType": "VariableDeclaration", "scope": 7426, "src": "2483:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7412, "name": "string", "nodeType": "ElementaryTypeName", "src": "2483:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2462:39:8"}, "returnParameters": {"id": 7418, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7417, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7426, "src": "2525:16:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 7415, "name": "address", "nodeType": "ElementaryTypeName", "src": "2525:7:8", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 7416, "nodeType": "ArrayTypeName", "src": "2525:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "2524:18:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7442, "nodeType": "FunctionDefinition", "src": "2608:137:8", "nodes": [], "body": {"id": 7441, "nodeType": "Block", "src": "2694:51:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7437, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7428, "src": "2728:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7438, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7430, "src": "2734:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7435, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "2711:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7436, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2714:13:8", "memberName": "parseJsonBool", "nodeType": "MemberAccess", "referencedDeclaration": 14746, "src": "2711:16:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) pure external returns (bool)"}}, "id": 7439, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2711:27:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 7434, "id": 7440, "nodeType": "Return", "src": "2704:34:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBool", "nameLocation": "2617:8:8", "parameters": {"id": 7431, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7428, "mutability": "mutable", "name": "json", "nameLocation": "2640:4:8", "nodeType": "VariableDeclaration", "scope": 7442, "src": "2626:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7427, "name": "string", "nodeType": "ElementaryTypeName", "src": "2626:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7430, "mutability": "mutable", "name": "key", "nameLocation": "2660:3:8", "nodeType": "VariableDeclaration", "scope": 7442, "src": "2646:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7429, "name": "string", "nodeType": "ElementaryTypeName", "src": "2646:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2625:39:8"}, "returnParameters": {"id": 7434, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7433, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7442, "src": "2688:4:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 7432, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2688:4:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2687:6:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7459, "nodeType": "FunctionDefinition", "src": "2751:156:8", "nodes": [], "body": {"id": 7458, "nodeType": "Block", "src": "2851:56:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7454, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7444, "src": "2890:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7455, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7446, "src": "2896:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7452, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "2868:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7453, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2871:18:8", "memberName": "parseJsonBoolArray", "nodeType": "MemberAccess", "referencedDeclaration": 14757, "src": "2868:21:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bool_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (bool[] memory)"}}, "id": 7456, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2868:32:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}}, "functionReturnParameters": 7451, "id": 7457, "nodeType": "Return", "src": "2861:39:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBoolArray", "nameLocation": "2760:13:8", "parameters": {"id": 7447, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7444, "mutability": "mutable", "name": "json", "nameLocation": "2788:4:8", "nodeType": "VariableDeclaration", "scope": 7459, "src": "2774:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7443, "name": "string", "nodeType": "ElementaryTypeName", "src": "2774:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7446, "mutability": "mutable", "name": "key", "nameLocation": "2808:3:8", "nodeType": "VariableDeclaration", "scope": 7459, "src": "2794:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7445, "name": "string", "nodeType": "ElementaryTypeName", "src": "2794:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2773:39:8"}, "returnParameters": {"id": 7451, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7450, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7459, "src": "2836:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[]"}, "typeName": {"baseType": {"id": 7448, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2836:4:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 7449, "nodeType": "ArrayTypeName", "src": "2836:6:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_storage_ptr", "typeString": "bool[]"}}, "visibility": "internal"}], "src": "2835:15:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7475, "nodeType": "FunctionDefinition", "src": "2913:147:8", "nodes": [], "body": {"id": 7474, "nodeType": "Block", "src": "3008:52:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7470, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7461, "src": "3043:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7471, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7463, "src": "3049:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7468, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "3025:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7469, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3028:14:8", "memberName": "parseJsonBytes", "nodeType": "MemberAccess", "referencedDeclaration": 14767, "src": "3025:17:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (bytes memory)"}}, "id": 7472, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3025:28:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "functionReturnParameters": 7467, "id": 7473, "nodeType": "Return", "src": "3018:35:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytes", "nameLocation": "2922:9:8", "parameters": {"id": 7464, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7461, "mutability": "mutable", "name": "json", "nameLocation": "2946:4:8", "nodeType": "VariableDeclaration", "scope": 7475, "src": "2932:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7460, "name": "string", "nodeType": "ElementaryTypeName", "src": "2932:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7463, "mutability": "mutable", "name": "key", "nameLocation": "2966:3:8", "nodeType": "VariableDeclaration", "scope": 7475, "src": "2952:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7462, "name": "string", "nodeType": "ElementaryTypeName", "src": "2952:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2931:39:8"}, "returnParameters": {"id": 7467, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7466, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7475, "src": "2994:12:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 7465, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "2994:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "2993:14:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7492, "nodeType": "FunctionDefinition", "src": "3066:159:8", "nodes": [], "body": {"id": 7491, "nodeType": "Block", "src": "3168:57:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7487, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7477, "src": "3208:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7488, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7479, "src": "3214:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7485, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "3185:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7486, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3188:19:8", "memberName": "parseJsonBytesArray", "nodeType": "MemberAccess", "referencedDeclaration": 14799, "src": "3185:22:8", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bytes_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure external returns (bytes memory[] memory)"}}, "id": 7489, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3185:33:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}, "functionReturnParameters": 7484, "id": 7490, "nodeType": "Return", "src": "3178:40:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytesArray", "nameLocation": "3075:14:8", "parameters": {"id": 7480, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7477, "mutability": "mutable", "name": "json", "nameLocation": "3104:4:8", "nodeType": "VariableDeclaration", "scope": 7492, "src": "3090:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7476, "name": "string", "nodeType": "ElementaryTypeName", "src": "3090:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7479, "mutability": "mutable", "name": "key", "nameLocation": "3124:3:8", "nodeType": "VariableDeclaration", "scope": 7492, "src": "3110:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7478, "name": "string", "nodeType": "ElementaryTypeName", "src": "3110:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3089:39:8"}, "returnParameters": {"id": 7484, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7483, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7492, "src": "3152:14:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 7481, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "3152:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 7482, "nodeType": "ArrayTypeName", "src": "3152:7:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "src": "3151:16:8"}, "scope": 8151, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 7515, "nodeType": "FunctionDefinition", "src": "3231:194:8", "nodes": [], "body": {"id": 7514, "nodeType": "Block", "src": "3344:81:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7504, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7494, "src": "3371:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7505, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7496, "src": "3377:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7503, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "3361:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7506, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3361:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7511, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7498, "src": "3406:12:8", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 7512, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "3361:57:8", "trueExpression": {"arguments": [{"id": 7508, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7494, "src": "3393:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7509, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7496, "src": "3399:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7507, "name": "readUint", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7277, "src": "3384:8:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_uint256_$", "typeString": "function (string memory,string memory) pure returns (uint256)"}}, "id": 7510, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3384:19:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 7502, "id": 7513, "nodeType": "Return", "src": "3354:64:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readUintOr", "nameLocation": "3240:10:8", "parameters": {"id": 7499, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7494, "mutability": "mutable", "name": "json", "nameLocation": "3265:4:8", "nodeType": "VariableDeclaration", "scope": 7515, "src": "3251:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7493, "name": "string", "nodeType": "ElementaryTypeName", "src": "3251:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7496, "mutability": "mutable", "name": "key", "nameLocation": "3285:3:8", "nodeType": "VariableDeclaration", "scope": 7515, "src": "3271:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7495, "name": "string", "nodeType": "ElementaryTypeName", "src": "3271:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7498, "mutability": "mutable", "name": "defaultValue", "nameLocation": "3298:12:8", "nodeType": "VariableDeclaration", "scope": 7515, "src": "3290:20:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 7497, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3290:7:8", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3250:61:8"}, "returnParameters": {"id": 7502, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7501, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7515, "src": "3335:7:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 7500, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3335:7:8", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3334:9:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7540, "nodeType": "FunctionDefinition", "src": "3431:250:8", "nodes": [], "body": {"id": 7539, "nodeType": "Block", "src": "3595:86:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7529, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7517, "src": "3622:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7530, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7519, "src": "3628:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7528, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "3612:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7531, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3612:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7536, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7522, "src": "3662:12:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "id": 7537, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "3612:62:8", "trueExpression": {"arguments": [{"id": 7533, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7517, "src": "3649:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7534, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7519, "src": "3655:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7532, "name": "readUintArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7294, "src": "3635:13:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (uint256[] memory)"}}, "id": 7535, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3635:24:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "functionReturnParameters": 7527, "id": 7538, "nodeType": "Return", "src": "3605:69:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readUintArrayOr", "nameLocation": "3440:15:8", "parameters": {"id": 7523, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7517, "mutability": "mutable", "name": "json", "nameLocation": "3470:4:8", "nodeType": "VariableDeclaration", "scope": 7540, "src": "3456:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7516, "name": "string", "nodeType": "ElementaryTypeName", "src": "3456:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7519, "mutability": "mutable", "name": "key", "nameLocation": "3490:3:8", "nodeType": "VariableDeclaration", "scope": 7540, "src": "3476:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7518, "name": "string", "nodeType": "ElementaryTypeName", "src": "3476:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7522, "mutability": "mutable", "name": "defaultValue", "nameLocation": "3512:12:8", "nodeType": "VariableDeclaration", "scope": 7540, "src": "3495:29:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 7520, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3495:7:8", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 7521, "nodeType": "ArrayTypeName", "src": "3495:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "3455:70:8"}, "returnParameters": {"id": 7527, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7526, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7540, "src": "3573:16:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 7524, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3573:7:8", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 7525, "nodeType": "ArrayTypeName", "src": "3573:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "3572:18:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7563, "nodeType": "FunctionDefinition", "src": "3687:190:8", "nodes": [], "body": {"id": 7562, "nodeType": "Block", "src": "3797:80:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7552, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7542, "src": "3824:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7553, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7544, "src": "3830:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7551, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "3814:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7554, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3814:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7559, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7546, "src": "3858:12:8", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 7560, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "3814:56:8", "trueExpression": {"arguments": [{"id": 7556, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7542, "src": "3845:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7557, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7544, "src": "3851:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7555, "name": "readInt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7310, "src": "3837:7:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_int256_$", "typeString": "function (string memory,string memory) pure returns (int256)"}}, "id": 7558, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3837:18:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "functionReturnParameters": 7550, "id": 7561, "nodeType": "Return", "src": "3807:63:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readIntOr", "nameLocation": "3696:9:8", "parameters": {"id": 7547, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7542, "mutability": "mutable", "name": "json", "nameLocation": "3720:4:8", "nodeType": "VariableDeclaration", "scope": 7563, "src": "3706:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7541, "name": "string", "nodeType": "ElementaryTypeName", "src": "3706:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7544, "mutability": "mutable", "name": "key", "nameLocation": "3740:3:8", "nodeType": "VariableDeclaration", "scope": 7563, "src": "3726:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7543, "name": "string", "nodeType": "ElementaryTypeName", "src": "3726:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7546, "mutability": "mutable", "name": "defaultValue", "nameLocation": "3752:12:8", "nodeType": "VariableDeclaration", "scope": 7563, "src": "3745:19:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 7545, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3745:6:8", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "3705:60:8"}, "returnParameters": {"id": 7550, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7549, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7563, "src": "3789:6:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 7548, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3789:6:8", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "3788:8:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7588, "nodeType": "FunctionDefinition", "src": "3883:246:8", "nodes": [], "body": {"id": 7587, "nodeType": "Block", "src": "4044:85:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7577, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7565, "src": "4071:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7578, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7567, "src": "4077:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7576, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "4061:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7579, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4061:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7584, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7570, "src": "4110:12:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}}, "id": 7585, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "4061:61:8", "trueExpression": {"arguments": [{"id": 7581, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7565, "src": "4097:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7582, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7567, "src": "4103:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7580, "name": "readIntArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7327, "src": "4084:12:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_int256_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (int256[] memory)"}}, "id": 7583, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4084:23:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}}, "functionReturnParameters": 7575, "id": 7586, "nodeType": "Return", "src": "4054:68:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readIntArrayOr", "nameLocation": "3892:14:8", "parameters": {"id": 7571, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7565, "mutability": "mutable", "name": "json", "nameLocation": "3921:4:8", "nodeType": "VariableDeclaration", "scope": 7588, "src": "3907:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7564, "name": "string", "nodeType": "ElementaryTypeName", "src": "3907:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7567, "mutability": "mutable", "name": "key", "nameLocation": "3941:3:8", "nodeType": "VariableDeclaration", "scope": 7588, "src": "3927:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7566, "name": "string", "nodeType": "ElementaryTypeName", "src": "3927:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7570, "mutability": "mutable", "name": "defaultValue", "nameLocation": "3962:12:8", "nodeType": "VariableDeclaration", "scope": 7588, "src": "3946:28:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[]"}, "typeName": {"baseType": {"id": 7568, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3946:6:8", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 7569, "nodeType": "ArrayTypeName", "src": "3946:8:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_storage_ptr", "typeString": "int256[]"}}, "visibility": "internal"}], "src": "3906:69:8"}, "returnParameters": {"id": 7575, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7574, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7588, "src": "4023:15:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[]"}, "typeName": {"baseType": {"id": 7572, "name": "int256", "nodeType": "ElementaryTypeName", "src": "4023:6:8", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 7573, "nodeType": "ArrayTypeName", "src": "4023:8:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_storage_ptr", "typeString": "int256[]"}}, "visibility": "internal"}], "src": "4022:17:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7611, "nodeType": "FunctionDefinition", "src": "4135:228:8", "nodes": [], "body": {"id": 7610, "nodeType": "Block", "src": "4279:84:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7600, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7590, "src": "4306:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7601, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7592, "src": "4312:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7599, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "4296:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7602, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4296:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7607, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7594, "src": "4344:12:8", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 7608, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "4296:60:8", "trueExpression": {"arguments": [{"id": 7604, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7590, "src": "4331:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7605, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7592, "src": "4337:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7603, "name": "readBytes32", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7343, "src": "4319:11:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (string memory,string memory) pure returns (bytes32)"}}, "id": 7606, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4319:22:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 7598, "id": 7609, "nodeType": "Return", "src": "4289:67:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytes32Or", "nameLocation": "4144:13:8", "parameters": {"id": 7595, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7590, "mutability": "mutable", "name": "json", "nameLocation": "4172:4:8", "nodeType": "VariableDeclaration", "scope": 7611, "src": "4158:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7589, "name": "string", "nodeType": "ElementaryTypeName", "src": "4158:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7592, "mutability": "mutable", "name": "key", "nameLocation": "4192:3:8", "nodeType": "VariableDeclaration", "scope": 7611, "src": "4178:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7591, "name": "string", "nodeType": "ElementaryTypeName", "src": "4178:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7594, "mutability": "mutable", "name": "defaultValue", "nameLocation": "4205:12:8", "nodeType": "VariableDeclaration", "scope": 7611, "src": "4197:20:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 7593, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4197:7:8", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "4157:61:8"}, "returnParameters": {"id": 7598, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7597, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7611, "src": "4266:7:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 7596, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4266:7:8", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "4265:9:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7636, "nodeType": "FunctionDefinition", "src": "4369:256:8", "nodes": [], "body": {"id": 7635, "nodeType": "Block", "src": "4536:89:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7625, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7613, "src": "4563:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7626, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7615, "src": "4569:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7624, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "4553:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7627, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4553:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7632, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7618, "src": "4606:12:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "id": 7633, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "4553:65:8", "trueExpression": {"arguments": [{"id": 7629, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7613, "src": "4593:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7630, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7615, "src": "4599:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7628, "name": "readBytes32Array", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7360, "src": "4576:16:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bytes32_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (bytes32[] memory)"}}, "id": 7631, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4576:27:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "functionReturnParameters": 7623, "id": 7634, "nodeType": "Return", "src": "4546:72:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytes32ArrayOr", "nameLocation": "4378:18:8", "parameters": {"id": 7619, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7613, "mutability": "mutable", "name": "json", "nameLocation": "4411:4:8", "nodeType": "VariableDeclaration", "scope": 7636, "src": "4397:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7612, "name": "string", "nodeType": "ElementaryTypeName", "src": "4397:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7615, "mutability": "mutable", "name": "key", "nameLocation": "4431:3:8", "nodeType": "VariableDeclaration", "scope": 7636, "src": "4417:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7614, "name": "string", "nodeType": "ElementaryTypeName", "src": "4417:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7618, "mutability": "mutable", "name": "defaultValue", "nameLocation": "4453:12:8", "nodeType": "VariableDeclaration", "scope": 7636, "src": "4436:29:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 7616, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4436:7:8", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 7617, "nodeType": "ArrayTypeName", "src": "4436:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "4396:70:8"}, "returnParameters": {"id": 7623, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7622, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7636, "src": "4514:16:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 7620, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4514:7:8", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 7621, "nodeType": "ArrayTypeName", "src": "4514:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "4513:18:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7659, "nodeType": "FunctionDefinition", "src": "4631:238:8", "nodes": [], "body": {"id": 7658, "nodeType": "Block", "src": "4786:83:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7648, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7638, "src": "4813:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7649, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7640, "src": "4819:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7647, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "4803:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7650, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4803:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7655, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7642, "src": "4850:12:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 7656, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "4803:59:8", "trueExpression": {"arguments": [{"id": 7652, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7638, "src": "4837:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7653, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7640, "src": "4843:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7651, "name": "readString", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7376, "src": "4826:10:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 7654, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4826:21:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7646, "id": 7657, "nodeType": "Return", "src": "4796:66:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readStringOr", "nameLocation": "4640:12:8", "parameters": {"id": 7643, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7638, "mutability": "mutable", "name": "json", "nameLocation": "4667:4:8", "nodeType": "VariableDeclaration", "scope": 7659, "src": "4653:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7637, "name": "string", "nodeType": "ElementaryTypeName", "src": "4653:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7640, "mutability": "mutable", "name": "key", "nameLocation": "4687:3:8", "nodeType": "VariableDeclaration", "scope": 7659, "src": "4673:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7639, "name": "string", "nodeType": "ElementaryTypeName", "src": "4673:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7642, "mutability": "mutable", "name": "defaultValue", "nameLocation": "4706:12:8", "nodeType": "VariableDeclaration", "scope": 7659, "src": "4692:26:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7641, "name": "string", "nodeType": "ElementaryTypeName", "src": "4692:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4652:67:8"}, "returnParameters": {"id": 7646, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7645, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7659, "src": "4767:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7644, "name": "string", "nodeType": "ElementaryTypeName", "src": "4767:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4766:15:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7684, "nodeType": "FunctionDefinition", "src": "4875:252:8", "nodes": [], "body": {"id": 7683, "nodeType": "Block", "src": "5039:88:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7673, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7661, "src": "5066:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7674, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7663, "src": "5072:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7672, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "5056:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7675, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5056:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7680, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7666, "src": "5108:12:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 7681, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "5056:64:8", "trueExpression": {"arguments": [{"id": 7677, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7661, "src": "5095:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7678, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7663, "src": "5101:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7676, "name": "readStringArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7393, "src": "5079:15:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_string_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory[] memory)"}}, "id": 7679, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5079:26:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "functionReturnParameters": 7671, "id": 7682, "nodeType": "Return", "src": "5049:71:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readStringArrayOr", "nameLocation": "4884:17:8", "parameters": {"id": 7667, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7661, "mutability": "mutable", "name": "json", "nameLocation": "4916:4:8", "nodeType": "VariableDeclaration", "scope": 7684, "src": "4902:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7660, "name": "string", "nodeType": "ElementaryTypeName", "src": "4902:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7663, "mutability": "mutable", "name": "key", "nameLocation": "4936:3:8", "nodeType": "VariableDeclaration", "scope": 7684, "src": "4922:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7662, "name": "string", "nodeType": "ElementaryTypeName", "src": "4922:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7666, "mutability": "mutable", "name": "defaultValue", "nameLocation": "4957:12:8", "nodeType": "VariableDeclaration", "scope": 7684, "src": "4941:28:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 7664, "name": "string", "nodeType": "ElementaryTypeName", "src": "4941:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 7665, "nodeType": "ArrayTypeName", "src": "4941:8:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "4901:69:8"}, "returnParameters": {"id": 7671, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7670, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7684, "src": "5018:15:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 7668, "name": "string", "nodeType": "ElementaryTypeName", "src": "5018:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 7669, "nodeType": "ArrayTypeName", "src": "5018:8:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "5017:17:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7707, "nodeType": "FunctionDefinition", "src": "5133:228:8", "nodes": [], "body": {"id": 7706, "nodeType": "Block", "src": "5277:84:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7696, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7686, "src": "5304:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7697, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7688, "src": "5310:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7695, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "5294:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7698, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5294:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7703, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7690, "src": "5342:12:8", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 7704, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "5294:60:8", "trueExpression": {"arguments": [{"id": 7700, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7686, "src": "5329:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7701, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7688, "src": "5335:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7699, "name": "readAddress", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7409, "src": "5317:11:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_address_$", "typeString": "function (string memory,string memory) pure returns (address)"}}, "id": 7702, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5317:22:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 7694, "id": 7705, "nodeType": "Return", "src": "5287:67:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readAddressOr", "nameLocation": "5142:13:8", "parameters": {"id": 7691, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7686, "mutability": "mutable", "name": "json", "nameLocation": "5170:4:8", "nodeType": "VariableDeclaration", "scope": 7707, "src": "5156:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7685, "name": "string", "nodeType": "ElementaryTypeName", "src": "5156:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7688, "mutability": "mutable", "name": "key", "nameLocation": "5190:3:8", "nodeType": "VariableDeclaration", "scope": 7707, "src": "5176:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7687, "name": "string", "nodeType": "ElementaryTypeName", "src": "5176:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7690, "mutability": "mutable", "name": "defaultValue", "nameLocation": "5203:12:8", "nodeType": "VariableDeclaration", "scope": 7707, "src": "5195:20:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 7689, "name": "address", "nodeType": "ElementaryTypeName", "src": "5195:7:8", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5155:61:8"}, "returnParameters": {"id": 7694, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7693, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7707, "src": "5264:7:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 7692, "name": "address", "nodeType": "ElementaryTypeName", "src": "5264:7:8", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5263:9:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7732, "nodeType": "FunctionDefinition", "src": "5367:256:8", "nodes": [], "body": {"id": 7731, "nodeType": "Block", "src": "5534:89:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7721, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7709, "src": "5561:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7722, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7711, "src": "5567:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7720, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "5551:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7723, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5551:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7728, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7714, "src": "5604:12:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 7729, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "5551:65:8", "trueExpression": {"arguments": [{"id": 7725, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7709, "src": "5591:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7726, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7711, "src": "5597:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7724, "name": "readAddressArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7426, "src": "5574:16:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_address_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (address[] memory)"}}, "id": 7727, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5574:27:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "functionReturnParameters": 7719, "id": 7730, "nodeType": "Return", "src": "5544:72:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readAddressArrayOr", "nameLocation": "5376:18:8", "parameters": {"id": 7715, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7709, "mutability": "mutable", "name": "json", "nameLocation": "5409:4:8", "nodeType": "VariableDeclaration", "scope": 7732, "src": "5395:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7708, "name": "string", "nodeType": "ElementaryTypeName", "src": "5395:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7711, "mutability": "mutable", "name": "key", "nameLocation": "5429:3:8", "nodeType": "VariableDeclaration", "scope": 7732, "src": "5415:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7710, "name": "string", "nodeType": "ElementaryTypeName", "src": "5415:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7714, "mutability": "mutable", "name": "defaultValue", "nameLocation": "5451:12:8", "nodeType": "VariableDeclaration", "scope": 7732, "src": "5434:29:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 7712, "name": "address", "nodeType": "ElementaryTypeName", "src": "5434:7:8", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 7713, "nodeType": "ArrayTypeName", "src": "5434:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "5394:70:8"}, "returnParameters": {"id": 7719, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7718, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7732, "src": "5512:16:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 7716, "name": "address", "nodeType": "ElementaryTypeName", "src": "5512:7:8", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 7717, "nodeType": "ArrayTypeName", "src": "5512:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "5511:18:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7755, "nodeType": "FunctionDefinition", "src": "5629:188:8", "nodes": [], "body": {"id": 7754, "nodeType": "Block", "src": "5736:81:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7744, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7734, "src": "5763:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7745, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7736, "src": "5769:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7743, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "5753:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7746, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5753:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7751, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7738, "src": "5798:12:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 7752, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "5753:57:8", "trueExpression": {"arguments": [{"id": 7748, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7734, "src": "5785:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7749, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7736, "src": "5791:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7747, "name": "readBool", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7442, "src": "5776:8:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) pure returns (bool)"}}, "id": 7750, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5776:19:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 7742, "id": 7753, "nodeType": "Return", "src": "5746:64:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBoolOr", "nameLocation": "5638:10:8", "parameters": {"id": 7739, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7734, "mutability": "mutable", "name": "json", "nameLocation": "5663:4:8", "nodeType": "VariableDeclaration", "scope": 7755, "src": "5649:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7733, "name": "string", "nodeType": "ElementaryTypeName", "src": "5649:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7736, "mutability": "mutable", "name": "key", "nameLocation": "5683:3:8", "nodeType": "VariableDeclaration", "scope": 7755, "src": "5669:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7735, "name": "string", "nodeType": "ElementaryTypeName", "src": "5669:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7738, "mutability": "mutable", "name": "defaultValue", "nameLocation": "5693:12:8", "nodeType": "VariableDeclaration", "scope": 7755, "src": "5688:17:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 7737, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5688:4:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "5648:58:8"}, "returnParameters": {"id": 7742, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7741, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7755, "src": "5730:4:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 7740, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5730:4:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "5729:6:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7780, "nodeType": "FunctionDefinition", "src": "5823:244:8", "nodes": [], "body": {"id": 7779, "nodeType": "Block", "src": "5981:86:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7769, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7757, "src": "6008:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7770, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7759, "src": "6014:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7768, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "5998:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7771, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5998:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7776, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7762, "src": "6048:12:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}}, "id": 7777, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "5998:62:8", "trueExpression": {"arguments": [{"id": 7773, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7757, "src": "6035:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7774, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7759, "src": "6041:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7772, "name": "readBoolArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7459, "src": "6021:13:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bool_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (bool[] memory)"}}, "id": 7775, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6021:24:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}}, "functionReturnParameters": 7767, "id": 7778, "nodeType": "Return", "src": "5991:69:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBoolArrayOr", "nameLocation": "5832:15:8", "parameters": {"id": 7763, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7757, "mutability": "mutable", "name": "json", "nameLocation": "5862:4:8", "nodeType": "VariableDeclaration", "scope": 7780, "src": "5848:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7756, "name": "string", "nodeType": "ElementaryTypeName", "src": "5848:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7759, "mutability": "mutable", "name": "key", "nameLocation": "5882:3:8", "nodeType": "VariableDeclaration", "scope": 7780, "src": "5868:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7758, "name": "string", "nodeType": "ElementaryTypeName", "src": "5868:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7762, "mutability": "mutable", "name": "defaultValue", "nameLocation": "5901:12:8", "nodeType": "VariableDeclaration", "scope": 7780, "src": "5887:26:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[]"}, "typeName": {"baseType": {"id": 7760, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5887:4:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 7761, "nodeType": "ArrayTypeName", "src": "5887:6:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_storage_ptr", "typeString": "bool[]"}}, "visibility": "internal"}], "src": "5847:67:8"}, "returnParameters": {"id": 7767, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7766, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7780, "src": "5962:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[]"}, "typeName": {"baseType": {"id": 7764, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5962:4:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 7765, "nodeType": "ArrayTypeName", "src": "5962:6:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_storage_ptr", "typeString": "bool[]"}}, "visibility": "internal"}], "src": "5961:15:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7803, "nodeType": "FunctionDefinition", "src": "6073:234:8", "nodes": [], "body": {"id": 7802, "nodeType": "Block", "src": "6225:82:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7792, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7782, "src": "6252:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7793, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7784, "src": "6258:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7791, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "6242:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7794, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6242:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7799, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7786, "src": "6288:12:8", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 7800, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "6242:58:8", "trueExpression": {"arguments": [{"id": 7796, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7782, "src": "6275:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7797, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7784, "src": "6281:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7795, "name": "readBytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7475, "src": "6265:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (bytes memory)"}}, "id": 7798, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6265:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "functionReturnParameters": 7790, "id": 7801, "nodeType": "Return", "src": "6235:65:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytesOr", "nameLocation": "6082:11:8", "parameters": {"id": 7787, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7782, "mutability": "mutable", "name": "json", "nameLocation": "6108:4:8", "nodeType": "VariableDeclaration", "scope": 7803, "src": "6094:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7781, "name": "string", "nodeType": "ElementaryTypeName", "src": "6094:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7784, "mutability": "mutable", "name": "key", "nameLocation": "6128:3:8", "nodeType": "VariableDeclaration", "scope": 7803, "src": "6114:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7783, "name": "string", "nodeType": "ElementaryTypeName", "src": "6114:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7786, "mutability": "mutable", "name": "defaultValue", "nameLocation": "6146:12:8", "nodeType": "VariableDeclaration", "scope": 7803, "src": "6133:25:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 7785, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6133:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "6093:66:8"}, "returnParameters": {"id": 7790, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7789, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7803, "src": "6207:12:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 7788, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6207:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "6206:14:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7828, "nodeType": "FunctionDefinition", "src": "6313:248:8", "nodes": [], "body": {"id": 7827, "nodeType": "Block", "src": "6474:87:8", "nodes": [], "statements": [{"expression": {"condition": {"arguments": [{"id": 7817, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7805, "src": "6501:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7818, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7807, "src": "6507:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7816, "name": "keyExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7245, "src": "6491:9:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_bool_$", "typeString": "function (string memory,string memory) view returns (bool)"}}, "id": 7819, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6491:20:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 7824, "name": "defaultValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7810, "src": "6542:12:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}, "id": 7825, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "6491:63:8", "trueExpression": {"arguments": [{"id": 7821, "name": "json", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7805, "src": "6529:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7822, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7807, "src": "6535:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 7820, "name": "readBytesArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7492, "src": "6514:14:8", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_array$_t_bytes_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (bytes memory[] memory)"}}, "id": 7823, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6514:25:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}, "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}, "functionReturnParameters": 7815, "id": 7826, "nodeType": "Return", "src": "6484:70:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "readBytesArrayOr", "nameLocation": "6322:16:8", "parameters": {"id": 7811, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7805, "mutability": "mutable", "name": "json", "nameLocation": "6353:4:8", "nodeType": "VariableDeclaration", "scope": 7828, "src": "6339:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7804, "name": "string", "nodeType": "ElementaryTypeName", "src": "6339:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7807, "mutability": "mutable", "name": "key", "nameLocation": "6373:3:8", "nodeType": "VariableDeclaration", "scope": 7828, "src": "6359:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7806, "name": "string", "nodeType": "ElementaryTypeName", "src": "6359:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7810, "mutability": "mutable", "name": "defaultValue", "nameLocation": "6393:12:8", "nodeType": "VariableDeclaration", "scope": 7828, "src": "6378:27:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 7808, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6378:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 7809, "nodeType": "ArrayTypeName", "src": "6378:7:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "src": "6338:68:8"}, "returnParameters": {"id": 7815, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7814, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7828, "src": "6454:14:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 7812, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6454:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 7813, "nodeType": "ArrayTypeName", "src": "6454:7:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "src": "6453:16:8"}, "scope": 8151, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 7844, "nodeType": "FunctionDefinition", "src": "6567:162:8", "nodes": [], "body": {"id": 7843, "nodeType": "Block", "src": "6668:61:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7839, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7830, "src": "6702:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7840, "name": "rootObject", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7832, "src": "6711:10:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7837, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "6685:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7838, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6688:13:8", "memberName": "serializeJ<PERSON>", "nodeType": "MemberAccess", "referencedDeclaration": 15060, "src": "6685:16:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) external returns (string memory)"}}, "id": 7841, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6685:37:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7836, "id": 7842, "nodeType": "Return", "src": "6678:44:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "6576:9:8", "parameters": {"id": 7833, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7830, "mutability": "mutable", "name": "jsonKey", "nameLocation": "6600:7:8", "nodeType": "VariableDeclaration", "scope": 7844, "src": "6586:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7829, "name": "string", "nodeType": "ElementaryTypeName", "src": "6586:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7832, "mutability": "mutable", "name": "rootObject", "nameLocation": "6623:10:8", "nodeType": "VariableDeclaration", "scope": 7844, "src": "6609:24:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7831, "name": "string", "nodeType": "ElementaryTypeName", "src": "6609:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6585:49:8"}, "returnParameters": {"id": 7836, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7835, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7844, "src": "6653:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7834, "name": "string", "nodeType": "ElementaryTypeName", "src": "6653:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6652:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7863, "nodeType": "FunctionDefinition", "src": "6735:167:8", "nodes": [], "body": {"id": 7862, "nodeType": "Block", "src": "6841:61:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7857, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7846, "src": "6875:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7858, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7848, "src": "6884:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7859, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7850, "src": "6889:5:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 7855, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "6858:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7856, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6861:13:8", "memberName": "serializeBool", "nodeType": "MemberAccess", "referencedDeclaration": 14962, "src": "6858:16:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bool) external returns (string memory)"}}, "id": 7860, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6858:37:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7854, "id": 7861, "nodeType": "Return", "src": "6851:44:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "6744:9:8", "parameters": {"id": 7851, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7846, "mutability": "mutable", "name": "jsonKey", "nameLocation": "6768:7:8", "nodeType": "VariableDeclaration", "scope": 7863, "src": "6754:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7845, "name": "string", "nodeType": "ElementaryTypeName", "src": "6754:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7848, "mutability": "mutable", "name": "key", "nameLocation": "6791:3:8", "nodeType": "VariableDeclaration", "scope": 7863, "src": "6777:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7847, "name": "string", "nodeType": "ElementaryTypeName", "src": "6777:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7850, "mutability": "mutable", "name": "value", "nameLocation": "6801:5:8", "nodeType": "VariableDeclaration", "scope": 7863, "src": "6796:10:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 7849, "name": "bool", "nodeType": "ElementaryTypeName", "src": "6796:4:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "6753:54:8"}, "returnParameters": {"id": 7854, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7853, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7863, "src": "6826:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7852, "name": "string", "nodeType": "ElementaryTypeName", "src": "6826:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6825:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7883, "nodeType": "FunctionDefinition", "src": "6908:196:8", "nodes": [], "body": {"id": 7882, "nodeType": "Block", "src": "7043:61:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7877, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7865, "src": "7077:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7878, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7867, "src": "7086:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7879, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7870, "src": "7091:5:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[] memory"}], "expression": {"id": 7875, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "7060:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7876, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7063:13:8", "memberName": "serializeBool", "nodeType": "MemberAccess", "referencedDeclaration": 14975, "src": "7060:16:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_bool_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bool[] memory) external returns (string memory)"}}, "id": 7880, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7060:37:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7874, "id": 7881, "nodeType": "Return", "src": "7053:44:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "6917:9:8", "parameters": {"id": 7871, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7865, "mutability": "mutable", "name": "jsonKey", "nameLocation": "6941:7:8", "nodeType": "VariableDeclaration", "scope": 7883, "src": "6927:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7864, "name": "string", "nodeType": "ElementaryTypeName", "src": "6927:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7867, "mutability": "mutable", "name": "key", "nameLocation": "6964:3:8", "nodeType": "VariableDeclaration", "scope": 7883, "src": "6950:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7866, "name": "string", "nodeType": "ElementaryTypeName", "src": "6950:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7870, "mutability": "mutable", "name": "value", "nameLocation": "6983:5:8", "nodeType": "VariableDeclaration", "scope": 7883, "src": "6969:19:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_memory_ptr", "typeString": "bool[]"}, "typeName": {"baseType": {"id": 7868, "name": "bool", "nodeType": "ElementaryTypeName", "src": "6969:4:8", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 7869, "nodeType": "ArrayTypeName", "src": "6969:6:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bool_$dyn_storage_ptr", "typeString": "bool[]"}}, "visibility": "internal"}], "src": "6926:63:8"}, "returnParameters": {"id": 7874, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7873, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7883, "src": "7024:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7872, "name": "string", "nodeType": "ElementaryTypeName", "src": "7024:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7023:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7902, "nodeType": "FunctionDefinition", "src": "7110:170:8", "nodes": [], "body": {"id": 7901, "nodeType": "Block", "src": "7219:61:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7896, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7885, "src": "7253:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7897, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7887, "src": "7262:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7898, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7889, "src": "7267:5:8", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 7894, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "7236:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7895, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7239:13:8", "memberName": "serializeUint", "nodeType": "MemberAccess", "referencedDeclaration": 15133, "src": "7236:16:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,uint256) external returns (string memory)"}}, "id": 7899, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7236:37:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7893, "id": 7900, "nodeType": "Return", "src": "7229:44:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "7119:9:8", "parameters": {"id": 7890, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7885, "mutability": "mutable", "name": "jsonKey", "nameLocation": "7143:7:8", "nodeType": "VariableDeclaration", "scope": 7902, "src": "7129:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7884, "name": "string", "nodeType": "ElementaryTypeName", "src": "7129:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7887, "mutability": "mutable", "name": "key", "nameLocation": "7166:3:8", "nodeType": "VariableDeclaration", "scope": 7902, "src": "7152:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7886, "name": "string", "nodeType": "ElementaryTypeName", "src": "7152:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7889, "mutability": "mutable", "name": "value", "nameLocation": "7179:5:8", "nodeType": "VariableDeclaration", "scope": 7902, "src": "7171:13:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 7888, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7171:7:8", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7128:57:8"}, "returnParameters": {"id": 7893, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7892, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7902, "src": "7204:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7891, "name": "string", "nodeType": "ElementaryTypeName", "src": "7204:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7203:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7922, "nodeType": "FunctionDefinition", "src": "7286:199:8", "nodes": [], "body": {"id": 7921, "nodeType": "Block", "src": "7424:61:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7916, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7904, "src": "7458:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7917, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7906, "src": "7467:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7918, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7909, "src": "7472:5:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}], "expression": {"id": 7914, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "7441:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7915, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7444:13:8", "memberName": "serializeUint", "nodeType": "MemberAccess", "referencedDeclaration": 15146, "src": "7441:16:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_uint256_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,uint256[] memory) external returns (string memory)"}}, "id": 7919, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7441:37:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7913, "id": 7920, "nodeType": "Return", "src": "7434:44:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "7295:9:8", "parameters": {"id": 7910, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7904, "mutability": "mutable", "name": "jsonKey", "nameLocation": "7319:7:8", "nodeType": "VariableDeclaration", "scope": 7922, "src": "7305:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7903, "name": "string", "nodeType": "ElementaryTypeName", "src": "7305:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7906, "mutability": "mutable", "name": "key", "nameLocation": "7342:3:8", "nodeType": "VariableDeclaration", "scope": 7922, "src": "7328:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7905, "name": "string", "nodeType": "ElementaryTypeName", "src": "7328:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7909, "mutability": "mutable", "name": "value", "nameLocation": "7364:5:8", "nodeType": "VariableDeclaration", "scope": 7922, "src": "7347:22:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 7907, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7347:7:8", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 7908, "nodeType": "ArrayTypeName", "src": "7347:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "7304:66:8"}, "returnParameters": {"id": 7913, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7912, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7922, "src": "7405:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7911, "name": "string", "nodeType": "ElementaryTypeName", "src": "7405:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7404:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7941, "nodeType": "FunctionDefinition", "src": "7491:168:8", "nodes": [], "body": {"id": 7940, "nodeType": "Block", "src": "7599:60:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7935, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7924, "src": "7632:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7936, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7926, "src": "7641:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7937, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7928, "src": "7646:5:8", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 7933, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "7616:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7934, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7619:12:8", "memberName": "serializeInt", "nodeType": "MemberAccess", "referencedDeclaration": 15037, "src": "7616:15:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,int256) external returns (string memory)"}}, "id": 7938, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7616:36:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7932, "id": 7939, "nodeType": "Return", "src": "7609:43:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "7500:9:8", "parameters": {"id": 7929, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7924, "mutability": "mutable", "name": "jsonKey", "nameLocation": "7524:7:8", "nodeType": "VariableDeclaration", "scope": 7941, "src": "7510:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7923, "name": "string", "nodeType": "ElementaryTypeName", "src": "7510:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7926, "mutability": "mutable", "name": "key", "nameLocation": "7547:3:8", "nodeType": "VariableDeclaration", "scope": 7941, "src": "7533:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7925, "name": "string", "nodeType": "ElementaryTypeName", "src": "7533:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7928, "mutability": "mutable", "name": "value", "nameLocation": "7559:5:8", "nodeType": "VariableDeclaration", "scope": 7941, "src": "7552:12:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 7927, "name": "int256", "nodeType": "ElementaryTypeName", "src": "7552:6:8", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "7509:56:8"}, "returnParameters": {"id": 7932, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7931, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7941, "src": "7584:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7930, "name": "string", "nodeType": "ElementaryTypeName", "src": "7584:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7583:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7961, "nodeType": "FunctionDefinition", "src": "7665:197:8", "nodes": [], "body": {"id": 7960, "nodeType": "Block", "src": "7802:60:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7955, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7943, "src": "7835:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7956, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7945, "src": "7844:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7957, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7948, "src": "7849:5:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[] memory"}], "expression": {"id": 7953, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "7819:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7954, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7822:12:8", "memberName": "serializeInt", "nodeType": "MemberAccess", "referencedDeclaration": 15050, "src": "7819:15:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_int256_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,int256[] memory) external returns (string memory)"}}, "id": 7958, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7819:36:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7952, "id": 7959, "nodeType": "Return", "src": "7812:43:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "7674:9:8", "parameters": {"id": 7949, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7943, "mutability": "mutable", "name": "jsonKey", "nameLocation": "7698:7:8", "nodeType": "VariableDeclaration", "scope": 7961, "src": "7684:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7942, "name": "string", "nodeType": "ElementaryTypeName", "src": "7684:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7945, "mutability": "mutable", "name": "key", "nameLocation": "7721:3:8", "nodeType": "VariableDeclaration", "scope": 7961, "src": "7707:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7944, "name": "string", "nodeType": "ElementaryTypeName", "src": "7707:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7948, "mutability": "mutable", "name": "value", "nameLocation": "7742:5:8", "nodeType": "VariableDeclaration", "scope": 7961, "src": "7726:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_memory_ptr", "typeString": "int256[]"}, "typeName": {"baseType": {"id": 7946, "name": "int256", "nodeType": "ElementaryTypeName", "src": "7726:6:8", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 7947, "nodeType": "ArrayTypeName", "src": "7726:8:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_int256_$dyn_storage_ptr", "typeString": "int256[]"}}, "visibility": "internal"}], "src": "7683:65:8"}, "returnParameters": {"id": 7952, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7951, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7961, "src": "7783:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7950, "name": "string", "nodeType": "ElementaryTypeName", "src": "7783:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7782:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7980, "nodeType": "FunctionDefinition", "src": "7868:173:8", "nodes": [], "body": {"id": 7979, "nodeType": "Block", "src": "7977:64:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7974, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7963, "src": "8014:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7975, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7965, "src": "8023:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7976, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7967, "src": "8028:5:8", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 7972, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "7994:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7973, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7997:16:8", "memberName": "serializeAddress", "nodeType": "MemberAccess", "referencedDeclaration": 14937, "src": "7994:19:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,address) external returns (string memory)"}}, "id": 7977, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7994:40:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7971, "id": 7978, "nodeType": "Return", "src": "7987:47:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "7877:9:8", "parameters": {"id": 7968, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7963, "mutability": "mutable", "name": "jsonKey", "nameLocation": "7901:7:8", "nodeType": "VariableDeclaration", "scope": 7980, "src": "7887:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7962, "name": "string", "nodeType": "ElementaryTypeName", "src": "7887:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7965, "mutability": "mutable", "name": "key", "nameLocation": "7924:3:8", "nodeType": "VariableDeclaration", "scope": 7980, "src": "7910:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7964, "name": "string", "nodeType": "ElementaryTypeName", "src": "7910:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7967, "mutability": "mutable", "name": "value", "nameLocation": "7937:5:8", "nodeType": "VariableDeclaration", "scope": 7980, "src": "7929:13:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 7966, "name": "address", "nodeType": "ElementaryTypeName", "src": "7929:7:8", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "7886:57:8"}, "returnParameters": {"id": 7971, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7970, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 7980, "src": "7962:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7969, "name": "string", "nodeType": "ElementaryTypeName", "src": "7962:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7961:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 8000, "nodeType": "FunctionDefinition", "src": "8047:202:8", "nodes": [], "body": {"id": 7999, "nodeType": "Block", "src": "8185:64:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7994, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7982, "src": "8222:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7995, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7984, "src": "8231:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 7996, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7987, "src": "8236:5:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}], "expression": {"id": 7992, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "8202:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 7993, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8205:16:8", "memberName": "serializeAddress", "nodeType": "MemberAccess", "referencedDeclaration": 14950, "src": "8202:19:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_address_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,address[] memory) external returns (string memory)"}}, "id": 7997, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8202:40:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 7991, "id": 7998, "nodeType": "Return", "src": "8195:47:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "8056:9:8", "parameters": {"id": 7988, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7982, "mutability": "mutable", "name": "jsonKey", "nameLocation": "8080:7:8", "nodeType": "VariableDeclaration", "scope": 8000, "src": "8066:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7981, "name": "string", "nodeType": "ElementaryTypeName", "src": "8066:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7984, "mutability": "mutable", "name": "key", "nameLocation": "8103:3:8", "nodeType": "VariableDeclaration", "scope": 8000, "src": "8089:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7983, "name": "string", "nodeType": "ElementaryTypeName", "src": "8089:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 7987, "mutability": "mutable", "name": "value", "nameLocation": "8125:5:8", "nodeType": "VariableDeclaration", "scope": 8000, "src": "8108:22:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 7985, "name": "address", "nodeType": "ElementaryTypeName", "src": "8108:7:8", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 7986, "nodeType": "ArrayTypeName", "src": "8108:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "8065:66:8"}, "returnParameters": {"id": 7991, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7990, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8000, "src": "8166:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7989, "name": "string", "nodeType": "ElementaryTypeName", "src": "8166:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8165:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 8019, "nodeType": "FunctionDefinition", "src": "8255:173:8", "nodes": [], "body": {"id": 8018, "nodeType": "Block", "src": "8364:64:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 8013, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8002, "src": "8401:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8014, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8004, "src": "8410:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8015, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8006, "src": "8415:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 8011, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "8381:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 8012, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8384:16:8", "memberName": "serializeBytes32", "nodeType": "MemberAccess", "referencedDeclaration": 14987, "src": "8381:19:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bytes32) external returns (string memory)"}}, "id": 8016, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8381:40:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 8010, "id": 8017, "nodeType": "Return", "src": "8374:47:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "8264:9:8", "parameters": {"id": 8007, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8002, "mutability": "mutable", "name": "jsonKey", "nameLocation": "8288:7:8", "nodeType": "VariableDeclaration", "scope": 8019, "src": "8274:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8001, "name": "string", "nodeType": "ElementaryTypeName", "src": "8274:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8004, "mutability": "mutable", "name": "key", "nameLocation": "8311:3:8", "nodeType": "VariableDeclaration", "scope": 8019, "src": "8297:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8003, "name": "string", "nodeType": "ElementaryTypeName", "src": "8297:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8006, "mutability": "mutable", "name": "value", "nameLocation": "8324:5:8", "nodeType": "VariableDeclaration", "scope": 8019, "src": "8316:13:8", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 8005, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8316:7:8", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "8273:57:8"}, "returnParameters": {"id": 8010, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8009, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8019, "src": "8349:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8008, "name": "string", "nodeType": "ElementaryTypeName", "src": "8349:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8348:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 8039, "nodeType": "FunctionDefinition", "src": "8434:202:8", "nodes": [], "body": {"id": 8038, "nodeType": "Block", "src": "8572:64:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 8033, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8021, "src": "8609:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8034, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8023, "src": "8618:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8035, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8026, "src": "8623:5:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}], "expression": {"id": 8031, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "8589:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 8032, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8592:16:8", "memberName": "serializeBytes32", "nodeType": "MemberAccess", "referencedDeclaration": 15000, "src": "8589:19:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_bytes32_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bytes32[] memory) external returns (string memory)"}}, "id": 8036, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8589:40:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 8030, "id": 8037, "nodeType": "Return", "src": "8582:47:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "8443:9:8", "parameters": {"id": 8027, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8021, "mutability": "mutable", "name": "jsonKey", "nameLocation": "8467:7:8", "nodeType": "VariableDeclaration", "scope": 8039, "src": "8453:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8020, "name": "string", "nodeType": "ElementaryTypeName", "src": "8453:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8023, "mutability": "mutable", "name": "key", "nameLocation": "8490:3:8", "nodeType": "VariableDeclaration", "scope": 8039, "src": "8476:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8022, "name": "string", "nodeType": "ElementaryTypeName", "src": "8476:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8026, "mutability": "mutable", "name": "value", "nameLocation": "8512:5:8", "nodeType": "VariableDeclaration", "scope": 8039, "src": "8495:22:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 8024, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8495:7:8", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 8025, "nodeType": "ArrayTypeName", "src": "8495:9:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "8452:66:8"}, "returnParameters": {"id": 8030, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8029, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8039, "src": "8553:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8028, "name": "string", "nodeType": "ElementaryTypeName", "src": "8553:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8552:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 8058, "nodeType": "FunctionDefinition", "src": "8642:176:8", "nodes": [], "body": {"id": 8057, "nodeType": "Block", "src": "8756:62:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 8052, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8041, "src": "8791:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8053, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8043, "src": "8800:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8054, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8045, "src": "8805:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 8050, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "8773:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 8051, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8776:14:8", "memberName": "serializeBytes", "nodeType": "MemberAccess", "referencedDeclaration": 15012, "src": "8773:17:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bytes memory) external returns (string memory)"}}, "id": 8055, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8773:38:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 8049, "id": 8056, "nodeType": "Return", "src": "8766:45:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "8651:9:8", "parameters": {"id": 8046, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8041, "mutability": "mutable", "name": "jsonKey", "nameLocation": "8675:7:8", "nodeType": "VariableDeclaration", "scope": 8058, "src": "8661:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8040, "name": "string", "nodeType": "ElementaryTypeName", "src": "8661:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8043, "mutability": "mutable", "name": "key", "nameLocation": "8698:3:8", "nodeType": "VariableDeclaration", "scope": 8058, "src": "8684:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8042, "name": "string", "nodeType": "ElementaryTypeName", "src": "8684:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8045, "mutability": "mutable", "name": "value", "nameLocation": "8716:5:8", "nodeType": "VariableDeclaration", "scope": 8058, "src": "8703:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 8044, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "8703:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "8660:62:8"}, "returnParameters": {"id": 8049, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8048, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8058, "src": "8741:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8047, "name": "string", "nodeType": "ElementaryTypeName", "src": "8741:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8740:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 8078, "nodeType": "FunctionDefinition", "src": "8824:198:8", "nodes": [], "body": {"id": 8077, "nodeType": "Block", "src": "8960:62:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 8072, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8060, "src": "8995:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8073, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8062, "src": "9004:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8074, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8065, "src": "9009:5:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}], "expression": {"id": 8070, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "8977:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 8071, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8980:14:8", "memberName": "serializeBytes", "nodeType": "MemberAccess", "referencedDeclaration": 15025, "src": "8977:17:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_bytes_memory_ptr_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,bytes memory[] memory) external returns (string memory)"}}, "id": 8075, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8977:38:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 8069, "id": 8076, "nodeType": "Return", "src": "8970:45:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "8833:9:8", "parameters": {"id": 8066, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8060, "mutability": "mutable", "name": "jsonKey", "nameLocation": "8857:7:8", "nodeType": "VariableDeclaration", "scope": 8078, "src": "8843:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8059, "name": "string", "nodeType": "ElementaryTypeName", "src": "8843:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8062, "mutability": "mutable", "name": "key", "nameLocation": "8880:3:8", "nodeType": "VariableDeclaration", "scope": 8078, "src": "8866:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8061, "name": "string", "nodeType": "ElementaryTypeName", "src": "8866:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8065, "mutability": "mutable", "name": "value", "nameLocation": "8900:5:8", "nodeType": "VariableDeclaration", "scope": 8078, "src": "8885:20:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 8063, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "8885:5:8", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 8064, "nodeType": "ArrayTypeName", "src": "8885:7:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "src": "8842:64:8"}, "returnParameters": {"id": 8069, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8068, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8078, "src": "8941:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8067, "name": "string", "nodeType": "ElementaryTypeName", "src": "8941:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8940:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 8097, "nodeType": "FunctionDefinition", "src": "9028:198:8", "nodes": [], "body": {"id": 8096, "nodeType": "Block", "src": "9163:63:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 8091, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8080, "src": "9199:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8092, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8082, "src": "9208:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8093, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8084, "src": "9213:5:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 8089, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "9180:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 8090, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9183:15:8", "memberName": "serializeString", "nodeType": "MemberAccess", "referencedDeclaration": 15096, "src": "9180:18:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,string memory) external returns (string memory)"}}, "id": 8094, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9180:39:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 8088, "id": 8095, "nodeType": "Return", "src": "9173:46:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "9037:9:8", "parameters": {"id": 8085, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8080, "mutability": "mutable", "name": "jsonKey", "nameLocation": "9061:7:8", "nodeType": "VariableDeclaration", "scope": 8097, "src": "9047:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8079, "name": "string", "nodeType": "ElementaryTypeName", "src": "9047:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8082, "mutability": "mutable", "name": "key", "nameLocation": "9084:3:8", "nodeType": "VariableDeclaration", "scope": 8097, "src": "9070:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8081, "name": "string", "nodeType": "ElementaryTypeName", "src": "9070:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8084, "mutability": "mutable", "name": "value", "nameLocation": "9103:5:8", "nodeType": "VariableDeclaration", "scope": 8097, "src": "9089:19:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8083, "name": "string", "nodeType": "ElementaryTypeName", "src": "9089:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9046:63:8"}, "returnParameters": {"id": 8088, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8087, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8097, "src": "9144:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8086, "name": "string", "nodeType": "ElementaryTypeName", "src": "9144:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9143:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 8117, "nodeType": "FunctionDefinition", "src": "9232:200:8", "nodes": [], "body": {"id": 8116, "nodeType": "Block", "src": "9369:63:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 8111, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8099, "src": "9405:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8112, "name": "key", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8101, "src": "9414:3:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8113, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8104, "src": "9419:5:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}], "expression": {"id": 8109, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "9386:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 8110, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9389:15:8", "memberName": "serializeString", "nodeType": "MemberAccess", "referencedDeclaration": 15109, "src": "9386:18:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_array$_t_string_memory_ptr_$dyn_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory,string memory[] memory) external returns (string memory)"}}, "id": 8114, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9386:39:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 8108, "id": 8115, "nodeType": "Return", "src": "9379:46:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "serialize", "nameLocation": "9241:9:8", "parameters": {"id": 8105, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8099, "mutability": "mutable", "name": "jsonKey", "nameLocation": "9265:7:8", "nodeType": "VariableDeclaration", "scope": 8117, "src": "9251:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8098, "name": "string", "nodeType": "ElementaryTypeName", "src": "9251:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8101, "mutability": "mutable", "name": "key", "nameLocation": "9288:3:8", "nodeType": "VariableDeclaration", "scope": 8117, "src": "9274:17:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8100, "name": "string", "nodeType": "ElementaryTypeName", "src": "9274:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8104, "mutability": "mutable", "name": "value", "nameLocation": "9309:5:8", "nodeType": "VariableDeclaration", "scope": 8117, "src": "9293:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 8102, "name": "string", "nodeType": "ElementaryTypeName", "src": "9293:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 8103, "nodeType": "ArrayTypeName", "src": "9293:8:8", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "9250:65:8"}, "returnParameters": {"id": 8108, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8107, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8117, "src": "9350:13:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8106, "name": "string", "nodeType": "ElementaryTypeName", "src": "9350:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9349:15:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 8132, "nodeType": "FunctionDefinition", "src": "9438:111:8", "nodes": [], "body": {"id": 8131, "nodeType": "Block", "src": "9505:44:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 8127, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8119, "src": "9528:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8128, "name": "path", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8121, "src": "9537:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 8124, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "9515:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 8126, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9518:9:8", "memberName": "writeJson", "nodeType": "MemberAccess", "referencedDeclaration": 15154, "src": "9515:12:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory,string memory) external"}}, "id": 8129, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9515:27:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 8130, "nodeType": "ExpressionStatement", "src": "9515:27:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "write", "nameLocation": "9447:5:8", "parameters": {"id": 8122, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8119, "mutability": "mutable", "name": "jsonKey", "nameLocation": "9467:7:8", "nodeType": "VariableDeclaration", "scope": 8132, "src": "9453:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8118, "name": "string", "nodeType": "ElementaryTypeName", "src": "9453:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8121, "mutability": "mutable", "name": "path", "nameLocation": "9490:4:8", "nodeType": "VariableDeclaration", "scope": 8132, "src": "9476:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8120, "name": "string", "nodeType": "ElementaryTypeName", "src": "9476:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9452:43:8"}, "returnParameters": {"id": 8123, "nodeType": "ParameterList", "parameters": [], "src": "9505:0:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 8150, "nodeType": "FunctionDefinition", "src": "9555:145:8", "nodes": [], "body": {"id": 8149, "nodeType": "Block", "src": "9646:54:8", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 8144, "name": "jsonKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8134, "src": "9669:7:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8145, "name": "path", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8136, "src": "9678:4:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 8146, "name": "valueKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8138, "src": "9684:8:8", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 8141, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7229, "src": "9656:2:8", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 8143, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9659:9:8", "memberName": "writeJson", "nodeType": "MemberAccess", "referencedDeclaration": 15164, "src": "9656:12:8", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory,string memory,string memory) external"}}, "id": 8147, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9656:37:8", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 8148, "nodeType": "ExpressionStatement", "src": "9656:37:8"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "write", "nameLocation": "9564:5:8", "parameters": {"id": 8139, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 8134, "mutability": "mutable", "name": "jsonKey", "nameLocation": "9584:7:8", "nodeType": "VariableDeclaration", "scope": 8150, "src": "9570:21:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8133, "name": "string", "nodeType": "ElementaryTypeName", "src": "9570:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8136, "mutability": "mutable", "name": "path", "nameLocation": "9607:4:8", "nodeType": "VariableDeclaration", "scope": 8150, "src": "9593:18:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8135, "name": "string", "nodeType": "ElementaryTypeName", "src": "9593:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 8138, "mutability": "mutable", "name": "valueKey", "nameLocation": "9627:8:8", "nodeType": "VariableDeclaration", "scope": 8150, "src": "9613:22:8", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 8137, "name": "string", "nodeType": "ElementaryTypeName", "src": "9613:6:8", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9569:67:8"}, "returnParameters": {"id": 8140, "nodeType": "ParameterList", "parameters": [], "src": "9646:0:8"}, "scope": 8151, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}], "abstract": false, "baseContracts": [], "canonicalName": "stdJson", "contractDependencies": [], "contractKind": "library", "fullyImplemented": true, "linearizedBaseContracts": [8151], "name": "stdJson", "nameLocation": "618:7:8", "scope": 8152, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 8}