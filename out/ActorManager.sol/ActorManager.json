{"abi": [{"type": "error", "name": "ActorExists", "inputs": []}, {"type": "error", "name": "ActorNotAdded", "inputs": []}, {"type": "error", "name": "ActorNotSetup", "inputs": []}, {"type": "error", "name": "DefaultActor", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ActorExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotSetup\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Default<PERSON><PERSON>\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"This is the source of truth for the actors being used in the test\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"constructor\":{\"notice\":\"address(this) is the default actor\"}},\"notice\":\"No actors should be used in the suite without being added here first\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/setup-helpers/src/ActorManager.sol\":\"ActorManager\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/BaseSetup.sol\":{\"keccak256\":\"0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe\",\"dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1\"]},\"lib/chimera/src/Hevm.sol\":{\"keccak256\":\"0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b\",\"dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr\"]},\"lib/setup-helpers/src/ActorManager.sol\":{\"keccak256\":\"0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c\",\"dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB\"]},\"lib/setup-helpers/src/EnumerableSet.sol\":{\"keccak256\":\"0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df\",\"dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ActorExists"}, {"inputs": [], "type": "error", "name": "ActorNotAdded"}, {"inputs": [], "type": "error", "name": "ActorNotSetup"}, {"inputs": [], "type": "error", "name": "DefaultActor"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {"constructor": {"notice": "address(this) is the default actor"}}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/setup-helpers/src/ActorManager.sol": "<PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/BaseSetup.sol": {"keccak256": "0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107", "urls": ["bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe", "dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1"], "license": "MIT"}, "lib/chimera/src/Hevm.sol": {"keccak256": "0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe", "urls": ["bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b", "dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr"], "license": "MIT"}, "lib/setup-helpers/src/ActorManager.sol": {"keccak256": "0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06", "urls": ["bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c", "dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB"], "license": "GPL-2.0"}, "lib/setup-helpers/src/EnumerableSet.sol": {"keccak256": "0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514", "urls": ["bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df", "dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 40405, "contract": "lib/setup-helpers/src/ActorManager.sol:Actor<PERSON>ana<PERSON>", "label": "_actor", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 40409, "contract": "lib/setup-helpers/src/ActorManager.sol:Actor<PERSON>ana<PERSON>", "label": "_actors", "offset": 0, "slot": "1", "type": "t_struct(AddressSet)41192_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(AddressSet)41192_storage": {"encoding": "inplace", "label": "struct EnumerableSet.AddressSet", "numberOfBytes": "64", "members": [{"astId": 41191, "contract": "lib/setup-helpers/src/ActorManager.sol:Actor<PERSON>ana<PERSON>", "label": "_inner", "offset": 0, "slot": "0", "type": "t_struct(Set)40877_storage"}]}, "t_struct(Set)40877_storage": {"encoding": "inplace", "label": "struct EnumerableSet.Set", "numberOfBytes": "64", "members": [{"astId": 40872, "contract": "lib/setup-helpers/src/ActorManager.sol:Actor<PERSON>ana<PERSON>", "label": "_values", "offset": 0, "slot": "0", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 40876, "contract": "lib/setup-helpers/src/ActorManager.sol:Actor<PERSON>ana<PERSON>", "label": "_indexes", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_uint256)"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "lib/setup-helpers/src/ActorManager.sol", "id": 40548, "exportedSymbols": {"ActorManager": [40547], "BaseSetup": [94], "EnumerableSet": [41479], "vm": [719]}, "nodeType": "SourceUnit", "src": "36:2657:26", "nodes": [{"id": 40391, "nodeType": "PragmaDirective", "src": "36:23:26", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 40393, "nodeType": "ImportDirective", "src": "61:49:26", "nodes": [], "absolutePath": "lib/chimera/src/BaseSetup.sol", "file": "@chimera/BaseSetup.sol", "nameLocation": "-1:-1:-1", "scope": 40548, "sourceUnit": 95, "symbolAliases": [{"foreign": {"id": 40392, "name": "BaseSetup", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 94, "src": "69:9:26", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 40395, "nodeType": "ImportDirective", "src": "111:37:26", "nodes": [], "absolutePath": "lib/chimera/src/Hevm.sol", "file": "@chimera/Hevm.sol", "nameLocation": "-1:-1:-1", "scope": 40548, "sourceUnit": 720, "symbolAliases": [{"foreign": {"id": 40394, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "119:2:26", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 40397, "nodeType": "ImportDirective", "src": "149:50:26", "nodes": [], "absolutePath": "lib/setup-helpers/src/EnumerableSet.sol", "file": "./EnumerableSet.sol", "nameLocation": "-1:-1:-1", "scope": 40548, "sourceUnit": 41480, "symbolAliases": [{"foreign": {"id": 40396, "name": "EnumerableSet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41479, "src": "157:13:26", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 40547, "nodeType": "ContractDefinition", "src": "357:2335:26", "nodes": [{"id": 40402, "nodeType": "UsingForDirective", "src": "394:49:26", "nodes": [], "global": false, "libraryName": {"id": 40399, "name": "EnumerableSet", "nameLocations": ["400:13:26"], "nodeType": "IdentifierPath", "referencedDeclaration": 41479, "src": "400:13:26"}, "typeName": {"id": 40401, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 40400, "name": "EnumerableSet.AddressSet", "nameLocations": ["418:13:26", "432:10:26"], "nodeType": "IdentifierPath", "referencedDeclaration": 41192, "src": "418:24:26"}, "referencedDeclaration": 41192, "src": "418:24:26", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}}}, {"id": 40405, "nodeType": "VariableDeclaration", "src": "493:22:26", "nodes": [], "constant": false, "documentation": {"id": 40403, "nodeType": "StructuredDocumentation", "src": "449:39:26", "text": "@notice The current actor being used"}, "mutability": "mutable", "name": "_actor", "nameLocation": "509:6:26", "scope": 40547, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40404, "name": "address", "nodeType": "ElementaryTypeName", "src": "493:7:26", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "private"}, {"id": 40409, "nodeType": "VariableDeclaration", "src": "571:40:26", "nodes": [], "constant": false, "documentation": {"id": 40406, "nodeType": "StructuredDocumentation", "src": "522:44:26", "text": "@notice The list of all actors being used"}, "mutability": "mutable", "name": "_actors", "nameLocation": "604:7:26", "scope": 40547, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet"}, "typeName": {"id": 40408, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 40407, "name": "EnumerableSet.AddressSet", "nameLocations": ["571:13:26", "585:10:26"], "nodeType": "IdentifierPath", "referencedDeclaration": 41192, "src": "571:24:26"}, "referencedDeclaration": 41192, "src": "571:24:26", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}}, "visibility": "private"}, {"id": 40411, "nodeType": "ErrorDefinition", "src": "710:22:26", "nodes": [], "errorSelector": "e1365923", "name": "ActorNotSetup", "nameLocation": "716:13:26", "parameters": {"id": 40410, "nodeType": "ParameterList", "parameters": [], "src": "729:2:26"}}, {"id": 40413, "nodeType": "ErrorDefinition", "src": "768:20:26", "nodes": [], "errorSelector": "3ecac8cf", "name": "ActorExists", "nameLocation": "774:11:26", "parameters": {"id": 40412, "nodeType": "ParameterList", "parameters": [], "src": "785:2:26"}}, {"id": 40415, "nodeType": "ErrorDefinition", "src": "828:22:26", "nodes": [], "errorSelector": "bca14754", "name": "ActorNotAdded", "nameLocation": "834:13:26", "parameters": {"id": 40414, "nodeType": "ParameterList", "parameters": [], "src": "847:2:26"}}, {"id": 40417, "nodeType": "ErrorDefinition", "src": "893:21:26", "nodes": [], "errorSelector": "accf4a23", "name": "DefaultActor", "nameLocation": "899:12:26", "parameters": {"id": 40416, "nodeType": "ParameterList", "parameters": [], "src": "911:2:26"}}, {"id": 40438, "nodeType": "FunctionDefinition", "src": "971:89:26", "nodes": [], "body": {"id": 40437, "nodeType": "Block", "src": "985:75:26", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 40426, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "1015:4:26", "typeDescriptions": {"typeIdentifier": "t_contract$_ActorManager_$40547", "typeString": "contract Actor<PERSON><PERSON><PERSON>"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_ActorManager_$40547", "typeString": "contract Actor<PERSON><PERSON><PERSON>"}], "id": 40425, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1007:7:26", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 40424, "name": "address", "nodeType": "ElementaryTypeName", "src": "1007:7:26", "typeDescriptions": {}}}, "id": 40427, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1007:13:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40421, "name": "_actors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40409, "src": "995:7:26", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40423, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1003:3:26", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 41219, "src": "995:11:26", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_AddressSet_$41192_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,address) returns (bool)"}}, "id": 40428, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "995:26:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40429, "nodeType": "ExpressionStatement", "src": "995:26:26"}, {"expression": {"id": 40435, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 40430, "name": "_actor", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40405, "src": "1031:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 40433, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "1048:4:26", "typeDescriptions": {"typeIdentifier": "t_contract$_ActorManager_$40547", "typeString": "contract Actor<PERSON><PERSON><PERSON>"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_ActorManager_$40547", "typeString": "contract Actor<PERSON><PERSON><PERSON>"}], "id": 40432, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1040:7:26", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 40431, "name": "address", "nodeType": "ElementaryTypeName", "src": "1040:7:26", "typeDescriptions": {}}}, "id": 40434, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1040:13:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1031:22:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40436, "nodeType": "ExpressionStatement", "src": "1031:22:26"}]}, "documentation": {"id": 40418, "nodeType": "StructuredDocumentation", "src": "920:46:26", "text": "@notice address(this) is the default actor"}, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 40419, "nodeType": "ParameterList", "parameters": [], "src": "982:2:26"}, "returnParameters": {"id": 40420, "nodeType": "ParameterList", "parameters": [], "src": "985:0:26"}, "scope": 40547, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 40447, "nodeType": "FunctionDefinition", "src": "1115:83:26", "nodes": [], "body": {"id": 40446, "nodeType": "Block", "src": "1168:30:26", "nodes": [], "statements": [{"expression": {"id": 40444, "name": "_actor", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40405, "src": "1185:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 40443, "id": 40445, "nodeType": "Return", "src": "1178:13:26"}]}, "documentation": {"id": 40439, "nodeType": "StructuredDocumentation", "src": "1066:44:26", "text": "@notice Returns the current active actor"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_getActor", "nameLocation": "1124:9:26", "parameters": {"id": 40440, "nodeType": "ParameterList", "parameters": [], "src": "1133:2:26"}, "returnParameters": {"id": 40443, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40442, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 40447, "src": "1159:7:26", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40441, "name": "address", "nodeType": "ElementaryTypeName", "src": "1159:7:26", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1158:9:26"}, "scope": 40547, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 40459, "nodeType": "FunctionDefinition", "src": "1250:103:26", "nodes": [], "body": {"id": 40458, "nodeType": "Block", "src": "1313:40:26", "nodes": [], "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40454, "name": "_actors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40409, "src": "1330:7:26", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40455, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1338:6:26", "memberName": "values", "nodeType": "MemberAccess", "referencedDeclaration": 41345, "src": "1330:14:26", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_AddressSet_$41192_storage_ptr_$returns$_t_array$_t_address_$dyn_memory_ptr_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer) view returns (address[] memory)"}}, "id": 40456, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1330:16:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "functionReturnParameters": 40453, "id": 40457, "nodeType": "Return", "src": "1323:23:26"}]}, "documentation": {"id": 40448, "nodeType": "StructuredDocumentation", "src": "1204:41:26", "text": "@notice Returns all actors being used"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_getActors", "nameLocation": "1259:10:26", "parameters": {"id": 40449, "nodeType": "ParameterList", "parameters": [], "src": "1269:2:26"}, "returnParameters": {"id": 40453, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40452, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 40459, "src": "1295:16:26", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 40450, "name": "address", "nodeType": "ElementaryTypeName", "src": "1295:7:26", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40451, "nodeType": "ArrayTypeName", "src": "1295:9:26", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "1294:18:26"}, "scope": 40547, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 40492, "nodeType": "FunctionDefinition", "src": "1411:250:26", "nodes": [], "body": {"id": 40491, "nodeType": "Block", "src": "1455:206:26", "nodes": [], "statements": [{"condition": {"arguments": [{"id": 40467, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40462, "src": "1486:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40465, "name": "_actors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40409, "src": "1469:7:26", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40466, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1477:8:26", "memberName": "contains", "nodeType": "MemberAccess", "referencedDeclaration": 41273, "src": "1469:16:26", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_AddressSet_$41192_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,address) view returns (bool)"}}, "id": 40468, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1469:24:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40473, "nodeType": "IfStatement", "src": "1465:75:26", "trueBody": {"id": 40472, "nodeType": "Block", "src": "1495:45:26", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 40469, "name": "ActorExists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40413, "src": "1516:11:26", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$_t_error_$", "typeString": "function () pure returns (error)"}}, "id": 40470, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1516:13:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 40471, "nodeType": "RevertStatement", "src": "1509:20:26"}]}}, {"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 40479, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40474, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40462, "src": "1554:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"id": 40477, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "1572:4:26", "typeDescriptions": {"typeIdentifier": "t_contract$_ActorManager_$40547", "typeString": "contract Actor<PERSON><PERSON><PERSON>"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_ActorManager_$40547", "typeString": "contract Actor<PERSON><PERSON><PERSON>"}], "id": 40476, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1564:7:26", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 40475, "name": "address", "nodeType": "ElementaryTypeName", "src": "1564:7:26", "typeDescriptions": {}}}, "id": 40478, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1564:13:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1554:23:26", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40484, "nodeType": "IfStatement", "src": "1550:75:26", "trueBody": {"id": 40483, "nodeType": "Block", "src": "1579:46:26", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 40480, "name": "DefaultActor", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40417, "src": "1600:12:26", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$_t_error_$", "typeString": "function () pure returns (error)"}}, "id": 40481, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1600:14:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 40482, "nodeType": "RevertStatement", "src": "1593:21:26"}]}}, {"expression": {"arguments": [{"id": 40488, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40462, "src": "1647:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40485, "name": "_actors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40409, "src": "1635:7:26", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40487, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1643:3:26", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 41219, "src": "1635:11:26", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_AddressSet_$41192_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,address) returns (bool)"}}, "id": 40489, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1635:19:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40490, "nodeType": "ExpressionStatement", "src": "1635:19:26"}]}, "documentation": {"id": 40460, "nodeType": "StructuredDocumentation", "src": "1359:47:26", "text": "@notice Adds an actor to the list of actors"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_addActor", "nameLocation": "1420:9:26", "parameters": {"id": 40463, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40462, "mutability": "mutable", "name": "target", "nameLocation": "1438:6:26", "nodeType": "VariableDeclaration", "scope": 40492, "src": "1430:14:26", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40461, "name": "address", "nodeType": "ElementaryTypeName", "src": "1430:7:26", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1429:16:26"}, "returnParameters": {"id": 40464, "nodeType": "ParameterList", "parameters": [], "src": "1455:0:26"}, "scope": 40547, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 40526, "nodeType": "FunctionDefinition", "src": "1724:259:26", "nodes": [], "body": {"id": 40525, "nodeType": "Block", "src": "1771:212:26", "nodes": [], "statements": [{"condition": {"id": 40502, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "1785:25:26", "subExpression": {"arguments": [{"id": 40500, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40495, "src": "1803:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40498, "name": "_actors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40409, "src": "1786:7:26", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40499, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1794:8:26", "memberName": "contains", "nodeType": "MemberAccess", "referencedDeclaration": 41273, "src": "1786:16:26", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_AddressSet_$41192_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,address) view returns (bool)"}}, "id": 40501, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1786:24:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40507, "nodeType": "IfStatement", "src": "1781:78:26", "trueBody": {"id": 40506, "nodeType": "Block", "src": "1812:47:26", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 40503, "name": "ActorNotAdded", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40415, "src": "1833:13:26", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$_t_error_$", "typeString": "function () pure returns (error)"}}, "id": 40504, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1833:15:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 40505, "nodeType": "RevertStatement", "src": "1826:22:26"}]}}, {"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 40513, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40508, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40495, "src": "1873:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"id": 40511, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "1891:4:26", "typeDescriptions": {"typeIdentifier": "t_contract$_ActorManager_$40547", "typeString": "contract Actor<PERSON><PERSON><PERSON>"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_ActorManager_$40547", "typeString": "contract Actor<PERSON><PERSON><PERSON>"}], "id": 40510, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1883:7:26", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 40509, "name": "address", "nodeType": "ElementaryTypeName", "src": "1883:7:26", "typeDescriptions": {}}}, "id": 40512, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1883:13:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1873:23:26", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40518, "nodeType": "IfStatement", "src": "1869:75:26", "trueBody": {"id": 40517, "nodeType": "Block", "src": "1898:46:26", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 40514, "name": "DefaultActor", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40417, "src": "1919:12:26", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$_t_error_$", "typeString": "function () pure returns (error)"}}, "id": 40515, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1919:14:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 40516, "nodeType": "RevertStatement", "src": "1912:21:26"}]}}, {"expression": {"arguments": [{"id": 40522, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40495, "src": "1969:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40519, "name": "_actors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40409, "src": "1954:7:26", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40521, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1962:6:26", "memberName": "remove", "nodeType": "MemberAccess", "referencedDeclaration": 41246, "src": "1954:14:26", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_AddressSet_$41192_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,address) returns (bool)"}}, "id": 40523, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1954:22:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40524, "nodeType": "ExpressionStatement", "src": "1954:22:26"}]}, "documentation": {"id": 40493, "nodeType": "StructuredDocumentation", "src": "1667:52:26", "text": "@notice Removes an actor from the list of actors"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_removeActor", "nameLocation": "1733:12:26", "parameters": {"id": 40496, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40495, "mutability": "mutable", "name": "target", "nameLocation": "1754:6:26", "nodeType": "VariableDeclaration", "scope": 40526, "src": "1746:14:26", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40494, "name": "address", "nodeType": "ElementaryTypeName", "src": "1746:7:26", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1745:16:26"}, "returnParameters": {"id": 40497, "nodeType": "ParameterList", "parameters": [], "src": "1771:0:26"}, "scope": 40547, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 40546, "nodeType": "FunctionDefinition", "src": "2547:143:26", "nodes": [], "body": {"id": 40545, "nodeType": "Block", "src": "2620:70:26", "nodes": [], "statements": [{"expression": {"id": 40539, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 40534, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40532, "src": "2630:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 40537, "name": "entropy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40529, "src": "2650:7:26", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40535, "name": "_actors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40409, "src": "2639:7:26", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40536, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2647:2:26", "memberName": "at", "nodeType": "MemberAccess", "referencedDeclaration": 41315, "src": "2639:10:26", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_AddressSet_$41192_storage_ptr_$_t_uint256_$returns$_t_address_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,uint256) view returns (address)"}}, "id": 40538, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2639:19:26", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2630:28:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40540, "nodeType": "ExpressionStatement", "src": "2630:28:26"}, {"expression": {"id": 40543, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 40541, "name": "_actor", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40405, "src": "2668:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 40542, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40532, "src": "2677:6:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2668:15:26", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40544, "nodeType": "ExpressionStatement", "src": "2668:15:26"}]}, "documentation": {"id": 40527, "nodeType": "StructuredDocumentation", "src": "1989:553:26", "text": "@dev Expose this in the `TargetFunctions` contract to let the fuzzer switch actors\n   NOTE: We revert if the entropy is greater than the number of actors, for Halmos compatibility\n @dev This may reduce fuzzing performance if using multiple actors, if so add explicitly clamped handlers to ManagersTargets using the index of all added actors\n @notice Switches the current actor based on the entropy\n @param entropy The entropy to choose a random actor in the array for switching\n @return target The new active actor"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_switchActor", "nameLocation": "2556:12:26", "parameters": {"id": 40530, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40529, "mutability": "mutable", "name": "entropy", "nameLocation": "2577:7:26", "nodeType": "VariableDeclaration", "scope": 40546, "src": "2569:15:26", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40528, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2569:7:26", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2568:17:26"}, "returnParameters": {"id": 40533, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40532, "mutability": "mutable", "name": "target", "nameLocation": "2612:6:26", "nodeType": "VariableDeclaration", "scope": 40546, "src": "2604:14:26", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40531, "name": "address", "nodeType": "ElementaryTypeName", "src": "2604:7:26", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2603:16:26"}, "scope": 40547, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}], "abstract": true, "baseContracts": [], "canonicalName": "<PERSON><PERSON><PERSON><PERSON>", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 40398, "nodeType": "StructuredDocumentation", "src": "201:156:26", "text": "@dev This is the source of truth for the actors being used in the test\n @notice No actors should be used in the suite without being added here first"}, "fullyImplemented": true, "linearizedBaseContracts": [40547], "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "375:12:26", "scope": 40548, "usedErrors": [40411, 40413, 40415, 40417], "usedEvents": []}], "license": "GPL-2.0"}, "id": 26}