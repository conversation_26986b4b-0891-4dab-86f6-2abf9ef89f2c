{"abi": [], "bytecode": {"object": "0x6055604b600b8282823980515f1a607314603f577f4e487b71000000000000000000000000000000000000000000000000000000005f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea26469706673582212201e41234fdc4f5d8725951b58aed1552f5ab3b940f0eb0c435c9e476e3f6bc80264736f6c634300081d0033", "sourceMap": "61:479:30:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea26469706673582212201e41234fdc4f5d8725951b58aed1552f5ab3b940f0eb0c435c9e476e3f6bc80264736f6c634300081d0033", "sourceMap": "61:479:30:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/setup-helpers/src/Panic.sol\":\"Panic\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/setup-helpers/src/Panic.sol\":{\"keccak256\":\"0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325\",\"dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/setup-helpers/src/Panic.sol": "Panic"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/setup-helpers/src/Panic.sol": {"keccak256": "0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92", "urls": ["bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325", "dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd"], "license": "GPL-2.0"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/setup-helpers/src/Panic.sol", "id": 42053, "exportedSymbols": {"Panic": [42052]}, "nodeType": "SourceUnit", "src": "36:505:30", "nodes": [{"id": 42024, "nodeType": "PragmaDirective", "src": "36:23:30", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 42052, "nodeType": "ContractDefinition", "src": "61:479:30", "nodes": [{"id": 42027, "nodeType": "VariableDeclaration", "src": "104:43:30", "nodes": [], "constant": true, "mutability": "constant", "name": "assertionPanic", "nameLocation": "120:14:30", "scope": 42052, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42025, "name": "string", "nodeType": "ElementaryTypeName", "src": "104:6:30", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "50616e6963283129", "id": 42026, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "137:10:30", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8a01f2a748dda97d26cfd36ab66c5dd4d102ec23dda0be1f52a7e39b9cef595c", "typeString": "literal_string \"Panic(1)\""}, "value": "Panic(1)"}, "visibility": "internal"}, {"id": 42030, "nodeType": "VariableDeclaration", "src": "153:45:30", "nodes": [], "constant": true, "mutability": "constant", "name": "arithmeticPanic", "nameLocation": "169:15:30", "scope": 42052, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42028, "name": "string", "nodeType": "ElementaryTypeName", "src": "153:6:30", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "50616e696328313729", "id": 42029, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "187:11:30", "typeDescriptions": {"typeIdentifier": "t_stringliteral_94411bfceaa2818bbe8a48025ab2917260b95eff9a2d9f053d7449275dc6c169", "typeString": "literal_string \"Panic(17)\""}, "value": "Panic(17)"}, "visibility": "internal"}, {"id": 42033, "nodeType": "VariableDeclaration", "src": "204:43:30", "nodes": [], "constant": true, "mutability": "constant", "name": "divisionPanic", "nameLocation": "220:13:30", "scope": 42052, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42031, "name": "string", "nodeType": "ElementaryTypeName", "src": "204:6:30", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "50616e696328313829", "id": 42032, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "236:11:30", "typeDescriptions": {"typeIdentifier": "t_stringliteral_82509fc5a4769ab09b33a2c3f395b2e2f0d087bcc4c2a174e24f6a83022c3ca2", "typeString": "literal_string \"Panic(18)\""}, "value": "Panic(18)"}, "visibility": "internal"}, {"id": 42036, "nodeType": "VariableDeclaration", "src": "253:39:30", "nodes": [], "constant": true, "mutability": "constant", "name": "enumPanic", "nameLocation": "269:9:30", "scope": 42052, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42034, "name": "string", "nodeType": "ElementaryTypeName", "src": "253:6:30", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "50616e696328333329", "id": 42035, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "281:11:30", "typeDescriptions": {"typeIdentifier": "t_stringliteral_de9171edfe3497d553e3e463f0ce578845eee02f7568a20e5a31bb29e2f3c9ce", "typeString": "literal_string \"Panic(33)\""}, "value": "Panic(33)"}, "visibility": "internal"}, {"id": 42039, "nodeType": "VariableDeclaration", "src": "298:40:30", "nodes": [], "constant": true, "mutability": "constant", "name": "arrayPanic", "nameLocation": "314:10:30", "scope": 42052, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42037, "name": "string", "nodeType": "ElementaryTypeName", "src": "298:6:30", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "50616e696328333429", "id": 42038, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "327:11:30", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d61eeb7830127a09d293e9566d845895b55f447b98db0bbb366b87ca77dcfdd3", "typeString": "literal_string \"Panic(34)\""}, "value": "Panic(34)"}, "visibility": "internal"}, {"id": 42042, "nodeType": "VariableDeclaration", "src": "344:45:30", "nodes": [], "constant": true, "mutability": "constant", "name": "emptyArrayPanic", "nameLocation": "360:15:30", "scope": 42052, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42040, "name": "string", "nodeType": "ElementaryTypeName", "src": "344:6:30", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "50616e696328343929", "id": 42041, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "378:11:30", "typeDescriptions": {"typeIdentifier": "t_stringliteral_dca911d1cffbe057ae0500641d51f54af3b642fb4f767b288e57fd121446c1b4", "typeString": "literal_string \"Panic(49)\""}, "value": "Panic(49)"}, "visibility": "internal"}, {"id": 42045, "nodeType": "VariableDeclaration", "src": "395:46:30", "nodes": [], "constant": true, "mutability": "constant", "name": "outOfBoundsPanic", "nameLocation": "411:16:30", "scope": 42052, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42043, "name": "string", "nodeType": "ElementaryTypeName", "src": "395:6:30", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "50616e696328353029", "id": 42044, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "430:11:30", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ed185a6af490181725d7ba3ef218f34b448dd414f662a5793621fa0740baeccb", "typeString": "literal_string \"Panic(50)\""}, "value": "Panic(50)"}, "visibility": "internal"}, {"id": 42048, "nodeType": "VariableDeclaration", "src": "447:41:30", "nodes": [], "constant": true, "mutability": "constant", "name": "memoryPanic", "nameLocation": "463:11:30", "scope": 42052, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42046, "name": "string", "nodeType": "ElementaryTypeName", "src": "447:6:30", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "50616e696328363529", "id": 42047, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "477:11:30", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0676dbbb5cea0ed689e5822180f05b5dfbd254a37a5b96ac6c9a1cd69ff6afd0", "typeString": "literal_string \"Panic(65)\""}, "value": "Panic(65)"}, "visibility": "internal"}, {"id": 42051, "nodeType": "VariableDeclaration", "src": "494:43:30", "nodes": [], "constant": true, "mutability": "constant", "name": "functionPanic", "nameLocation": "510:13:30", "scope": 42052, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 42049, "name": "string", "nodeType": "ElementaryTypeName", "src": "494:6:30", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "50616e696328383129", "id": 42050, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "526:11:30", "typeDescriptions": {"typeIdentifier": "t_stringliteral_9c850c8b757404bba1c70ee642edd21ec0d1ba46c6004f311d96ef8c9e63a507", "typeString": "literal_string \"Panic(81)\""}, "value": "Panic(81)"}, "visibility": "internal"}], "abstract": false, "baseContracts": [], "canonicalName": "Panic", "contractDependencies": [], "contractKind": "library", "fullyImplemented": true, "linearizedBaseContracts": [42052], "name": "Panic", "nameLocation": "69:5:30", "scope": 42053, "usedErrors": [], "usedEvents": []}], "license": "GPL-2.0"}, "id": 30}