{"abi": [{"type": "function", "name": "MAX_VALUE", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "add", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "decrement", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "increment", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "number", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "reset", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setNumber", "inputs": [{"name": "newNumber", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "subtract", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "CounterReset", "inputs": [], "anonymous": false}, {"type": "event", "name": "NumberChanged", "inputs": [{"name": "oldValue", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newValue", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "65:1467:21:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "65:1467:21:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;115:40;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1073:233;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;1312:218;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;711:194;;;:::i;:::-;;250:237;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;88:21;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;493:212;;;:::i;:::-;;911:156;;;:::i;:::-;;115:40;151:4;115:40;:::o;1073:233::-;151:4;1135:5;1126:6;;:14;;;;:::i;:::-;:27;;1118:75;;;;;;;;;;;;:::i;:::-;;;;;;;;;1203:16;1222:6;;1203:25;;1248:5;1238:6;;:15;;;;;;;:::i;:::-;;;;;;;;1268:31;1282:8;1292:6;;1268:31;;;;;;;:::i;:::-;;;;;;;;1108:198;1073:233;:::o;1312:218::-;1380:5;1370:6;;:15;;1362:55;;;;;;;;;;;;:::i;:::-;;;;;;;;;1427:16;1446:6;;1427:25;;1472:5;1462:6;;:15;;;;;;;:::i;:::-;;;;;;;;1492:31;1506:8;1516:6;;1492:31;;;;;;;:::i;:::-;;;;;;;;1352:178;1312:218;:::o;711:194::-;766:1;757:6;;:10;749:50;;;;;;;;;;;;:::i;:::-;;;;;;;;;809:16;828:6;;809:25;;844:6;;:8;;;;;;;;;:::i;:::-;;;;;;867:31;881:8;891:6;;867:31;;;;;;;:::i;:::-;;;;;;;;739:166;711:194::o;250:237::-;151:4;313:9;:22;;305:63;;;;;;;;;;;;:::i;:::-;;;;;;;;;378:16;397:6;;378:25;;422:9;413:6;:18;;;;446:34;460:8;470:9;446:34;;;;;;;:::i;:::-;;;;;;;;295:192;250:237;:::o;88:21::-;;;;:::o;493:212::-;151:4;539:6;;:18;531:68;;;;;;;;;;;;:::i;:::-;;;;;;;;;609:16;628:6;;609:25;;644:6;;:8;;;;;;;;;:::i;:::-;;;;;;667:31;681:8;691:6;;667:31;;;;;;;:::i;:::-;;;;;;;;521:184;493:212::o;911:156::-;945:16;964:6;;945:25;;989:1;980:6;:10;;;;1005:26;1019:8;1029:1;1005:26;;;;;;;:::i;:::-;;;;;;;;1046:14;;;;;;;;;;935:132;911:156::o;7:77:24:-;44:7;73:5;62:16;;7:77;;;:::o;90:118::-;177:24;195:5;177:24;:::i;:::-;172:3;165:37;90:118;;:::o;214:222::-;307:4;345:2;334:9;330:18;322:26;;358:71;426:1;415:9;411:17;402:6;358:71;:::i;:::-;214:222;;;;:::o;523:117::-;632:1;629;622:12;769:122;842:24;860:5;842:24;:::i;:::-;835:5;832:35;822:63;;881:1;878;871:12;822:63;769:122;:::o;897:139::-;943:5;981:6;968:20;959:29;;997:33;1024:5;997:33;:::i;:::-;897:139;;;;:::o;1042:329::-;1101:6;1150:2;1138:9;1129:7;1125:23;1121:32;1118:119;;;1156:79;;:::i;:::-;1118:119;1276:1;1301:53;1346:7;1337:6;1326:9;1322:22;1301:53;:::i;:::-;1291:63;;1247:117;1042:329;;;;:::o;1377:180::-;1425:77;1422:1;1415:88;1522:4;1519:1;1512:15;1546:4;1543:1;1536:15;1563:191;1603:3;1622:20;1640:1;1622:20;:::i;:::-;1617:25;;1656:20;1674:1;1656:20;:::i;:::-;1651:25;;1699:1;1696;1692:9;1685:16;;1720:3;1717:1;1714:10;1711:36;;;1727:18;;:::i;:::-;1711:36;1563:191;;;;:::o;1760:169::-;1844:11;1878:6;1873:3;1866:19;1918:4;1913:3;1909:14;1894:29;;1760:169;;;;:::o;1935:222::-;2075:34;2071:1;2063:6;2059:14;2052:58;2144:5;2139:2;2131:6;2127:15;2120:30;1935:222;:::o;2163:366::-;2305:3;2326:67;2390:2;2385:3;2326:67;:::i;:::-;2319:74;;2402:93;2491:3;2402:93;:::i;:::-;2520:2;2515:3;2511:12;2504:19;;2163:366;;;:::o;2535:419::-;2701:4;2739:2;2728:9;2724:18;2716:26;;2788:9;2782:4;2778:20;2774:1;2763:9;2759:17;2752:47;2816:131;2942:4;2816:131;:::i;:::-;2808:139;;2535:419;;;:::o;2960:332::-;3081:4;3119:2;3108:9;3104:18;3096:26;;3132:71;3200:1;3189:9;3185:17;3176:6;3132:71;:::i;:::-;3213:72;3281:2;3270:9;3266:18;3257:6;3213:72;:::i;:::-;2960:332;;;;;:::o;3298:177::-;3438:29;3434:1;3426:6;3422:14;3415:53;3298:177;:::o;3481:366::-;3623:3;3644:67;3708:2;3703:3;3644:67;:::i;:::-;3637:74;;3720:93;3809:3;3720:93;:::i;:::-;3838:2;3833:3;3829:12;3822:19;;3481:366;;;:::o;3853:419::-;4019:4;4057:2;4046:9;4042:18;4034:26;;4106:9;4100:4;4096:20;4092:1;4081:9;4077:17;4070:47;4134:131;4260:4;4134:131;:::i;:::-;4126:139;;3853:419;;;:::o;4278:194::-;4318:4;4338:20;4356:1;4338:20;:::i;:::-;4333:25;;4372:20;4390:1;4372:20;:::i;:::-;4367:25;;4416:1;4413;4409:9;4401:17;;4440:1;4434:4;4431:11;4428:37;;;4445:18;;:::i;:::-;4428:37;4278:194;;;;:::o;4478:177::-;4618:29;4614:1;4606:6;4602:14;4595:53;4478:177;:::o;4661:366::-;4803:3;4824:67;4888:2;4883:3;4824:67;:::i;:::-;4817:74;;4900:93;4989:3;4900:93;:::i;:::-;5018:2;5013:3;5009:12;5002:19;;4661:366;;;:::o;5033:419::-;5199:4;5237:2;5226:9;5222:18;5214:26;;5286:9;5280:4;5276:20;5272:1;5261:9;5257:17;5250:47;5314:131;5440:4;5314:131;:::i;:::-;5306:139;;5033:419;;;:::o;5458:171::-;5497:3;5520:24;5538:5;5520:24;:::i;:::-;5511:33;;5566:4;5559:5;5556:15;5553:41;;5574:18;;:::i;:::-;5553:41;5621:1;5614:5;5610:13;5603:20;;5458:171;;;:::o;5635:178::-;5775:30;5771:1;5763:6;5759:14;5752:54;5635:178;:::o;5819:366::-;5961:3;5982:67;6046:2;6041:3;5982:67;:::i;:::-;5975:74;;6058:93;6147:3;6058:93;:::i;:::-;6176:2;6171:3;6167:12;6160:19;;5819:366;;;:::o;6191:419::-;6357:4;6395:2;6384:9;6380:18;6372:26;;6444:9;6438:4;6434:20;6430:1;6419:9;6415:17;6408:47;6472:131;6598:4;6472:131;:::i;:::-;6464:139;;6191:419;;;:::o;6616:224::-;6756:34;6752:1;6744:6;6740:14;6733:58;6825:7;6820:2;6812:6;6808:15;6801:32;6616:224;:::o;6846:366::-;6988:3;7009:67;7073:2;7068:3;7009:67;:::i;:::-;7002:74;;7085:93;7174:3;7085:93;:::i;:::-;7203:2;7198:3;7194:12;7187:19;;6846:366;;;:::o;7218:419::-;7384:4;7422:2;7411:9;7407:18;7399:26;;7471:9;7465:4;7461:20;7457:1;7446:9;7442:17;7435:47;7499:131;7625:4;7499:131;:::i;:::-;7491:139;;7218:419;;;:::o;7643:233::-;7682:3;7705:24;7723:5;7705:24;:::i;:::-;7696:33;;7751:66;7744:5;7741:77;7738:103;;7821:18;;:::i;:::-;7738:103;7868:1;7861:5;7857:13;7850:20;;7643:233;;;:::o;7882:85::-;7927:7;7956:5;7945:16;;7882:85;;;:::o;7973:60::-;8001:3;8022:5;8015:12;;7973:60;;;:::o;8039:158::-;8097:9;8130:61;8148:42;8157:32;8183:5;8157:32;:::i;:::-;8148:42;:::i;:::-;8130:61;:::i;:::-;8117:74;;8039:158;;;:::o;8203:147::-;8298:45;8337:5;8298:45;:::i;:::-;8293:3;8286:58;8203:147;;:::o;8356:348::-;8485:4;8523:2;8512:9;8508:18;8500:26;;8536:71;8604:1;8593:9;8589:17;8580:6;8536:71;:::i;:::-;8617:80;8693:2;8682:9;8678:18;8669:6;8617:80;:::i;:::-;8356:348;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"MAX_VALUE()": "063bde24", "add(uint256)": "1003e2d2", "decrement()": "2baeceb7", "increment()": "d09de08a", "number()": "8381f58a", "reset()": "d826f88f", "setNumber(uint256)": "3fb5c1cb", "subtract(uint256)": "1dc05f17"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[],\"name\":\"CounterReset\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldValue\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newValue\",\"type\":\"uint256\"}],\"name\":\"NumberChanged\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"MAX_VALUE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"add\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decrement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"increment\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"number\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"reset\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newNumber\",\"type\":\"uint256\"}],\"name\":\"setNumber\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"subtract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/Counter.sol\":\"Counter\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"src/Counter.sol\":{\"keccak256\":\"0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e\",\"dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "event", "name": "CounterReset", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "oldValue", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "newValue", "type": "uint256", "indexed": false}], "type": "event", "name": "NumberChanged", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MAX_VALUE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "add"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "decrement"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "increment"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "number", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "reset"}, {"inputs": [{"internalType": "uint256", "name": "newNumber", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setNumber"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "subtract"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/Counter.sol": "Counter"}, "evmVersion": "prague", "libraries": {}}, "sources": {"src/Counter.sol": {"keccak256": "0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c", "urls": ["bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e", "dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2"], "license": "UNLICENSED"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 39751, "contract": "src/Counter.sol:Counter", "label": "number", "offset": 0, "slot": "0", "type": "t_uint256"}], "types": {"t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "src/Counter.sol", "id": 39910, "exportedSymbols": {"Counter": [39909]}, "nodeType": "SourceUnit", "src": "39:1494:21", "nodes": [{"id": 39749, "nodeType": "PragmaDirective", "src": "39:24:21", "nodes": [], "literals": ["solidity", "^", "0.8", ".13"]}, {"id": 39909, "nodeType": "ContractDefinition", "src": "65:1467:21", "nodes": [{"id": 39751, "nodeType": "VariableDeclaration", "src": "88:21:21", "nodes": [], "constant": false, "functionSelector": "8381f58a", "mutability": "mutable", "name": "number", "nameLocation": "103:6:21", "scope": 39909, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39750, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "88:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"id": 39754, "nodeType": "VariableDeclaration", "src": "115:40:21", "nodes": [], "constant": true, "functionSelector": "063bde24", "mutability": "constant", "name": "MAX_VALUE", "nameLocation": "139:9:21", "scope": 39909, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39752, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "115:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "31303030", "id": 39753, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "151:4:21", "typeDescriptions": {"typeIdentifier": "t_rational_1000_by_1", "typeString": "int_const 1000"}, "value": "1000"}, "visibility": "public"}, {"id": 39760, "nodeType": "EventDefinition", "src": "162:56:21", "nodes": [], "anonymous": false, "eventSelector": "31431e8e0193815c649ffbfb9013954926640a5c67ada972108cdb5a47a0d728", "name": "NumberChanged", "nameLocation": "168:13:21", "parameters": {"id": 39759, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 39756, "indexed": false, "mutability": "mutable", "name": "oldValue", "nameLocation": "190:8:21", "nodeType": "VariableDeclaration", "scope": 39760, "src": "182:16:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39755, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "182:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 39758, "indexed": false, "mutability": "mutable", "name": "newValue", "nameLocation": "208:8:21", "nodeType": "VariableDeclaration", "scope": 39760, "src": "200:16:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39757, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "200:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "181:36:21"}}, {"id": 39762, "nodeType": "EventDefinition", "src": "223:21:21", "nodes": [], "anonymous": false, "eventSelector": "79e13020b0c9b840ddf270793ab373525f9456e10bbba08dbe94217994736ec8", "name": "CounterReset", "nameLocation": "229:12:21", "parameters": {"id": 39761, "nodeType": "ParameterList", "parameters": [], "src": "241:2:21"}}, {"id": 39788, "nodeType": "FunctionDefinition", "src": "250:237:21", "nodes": [], "body": {"id": 39787, "nodeType": "Block", "src": "295:192:21", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39770, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39768, "name": "newNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39764, "src": "313:9:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 39769, "name": "MAX_VALUE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39754, "src": "326:9:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "313:22:21", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4e756d6265722065786365656473206d6178696d756d2076616c7565", "id": 39771, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "337:30:21", "typeDescriptions": {"typeIdentifier": "t_stringliteral_1f7057b68f544209f2678bbc67b5d3111fa214ef9c8eb8c8097330ba8340bd48", "typeString": "literal_string \"Number exceeds maximum value\""}, "value": "Number exceeds maximum value"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_1f7057b68f544209f2678bbc67b5d3111fa214ef9c8eb8c8097330ba8340bd48", "typeString": "literal_string \"Number exceeds maximum value\""}], "id": 39767, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "305:7:21", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 39772, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "305:63:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39773, "nodeType": "ExpressionStatement", "src": "305:63:21"}, {"assignments": [39775], "declarations": [{"constant": false, "id": 39775, "mutability": "mutable", "name": "oldValue", "nameLocation": "386:8:21", "nodeType": "VariableDeclaration", "scope": 39787, "src": "378:16:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39774, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "378:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 39777, "initialValue": {"id": 39776, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "397:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "378:25:21"}, {"expression": {"id": 39780, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 39778, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "413:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 39779, "name": "newNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39764, "src": "422:9:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "413:18:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 39781, "nodeType": "ExpressionStatement", "src": "413:18:21"}, {"eventCall": {"arguments": [{"id": 39783, "name": "oldValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39775, "src": "460:8:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 39784, "name": "newNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39764, "src": "470:9:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 39782, "name": "NumberChanged", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39760, "src": "446:13:21", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256)"}}, "id": 39785, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "446:34:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39786, "nodeType": "EmitStatement", "src": "441:39:21"}]}, "functionSelector": "3fb5c1cb", "implemented": true, "kind": "function", "modifiers": [], "name": "setNumber", "nameLocation": "259:9:21", "parameters": {"id": 39765, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 39764, "mutability": "mutable", "name": "newNumber", "nameLocation": "277:9:21", "nodeType": "VariableDeclaration", "scope": 39788, "src": "269:17:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39763, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "269:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "268:19:21"}, "returnParameters": {"id": 39766, "nodeType": "ParameterList", "parameters": [], "src": "295:0:21"}, "scope": 39909, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 39811, "nodeType": "FunctionDefinition", "src": "493:212:21", "nodes": [], "body": {"id": 39810, "nodeType": "Block", "src": "521:184:21", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39794, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39792, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "539:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 39793, "name": "MAX_VALUE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39754, "src": "548:9:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "539:18:21", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "43616e6e6f7420696e6372656d656e74206265796f6e64206d6178696d756d2076616c7565", "id": 39795, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "559:39:21", "typeDescriptions": {"typeIdentifier": "t_stringliteral_dc4ad0c2847e925214d60d7358aab774a9f3fd9a370fbde50cf6cd0455cea68b", "typeString": "literal_string \"Cannot increment beyond maximum value\""}, "value": "Cannot increment beyond maximum value"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_dc4ad0c2847e925214d60d7358aab774a9f3fd9a370fbde50cf6cd0455cea68b", "typeString": "literal_string \"Cannot increment beyond maximum value\""}], "id": 39791, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "531:7:21", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 39796, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "531:68:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39797, "nodeType": "ExpressionStatement", "src": "531:68:21"}, {"assignments": [39799], "declarations": [{"constant": false, "id": 39799, "mutability": "mutable", "name": "oldValue", "nameLocation": "617:8:21", "nodeType": "VariableDeclaration", "scope": 39810, "src": "609:16:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39798, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "609:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 39801, "initialValue": {"id": 39800, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "628:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "609:25:21"}, {"expression": {"id": 39803, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "644:8:21", "subExpression": {"id": 39802, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "644:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 39804, "nodeType": "ExpressionStatement", "src": "644:8:21"}, {"eventCall": {"arguments": [{"id": 39806, "name": "oldValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39799, "src": "681:8:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 39807, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "691:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 39805, "name": "NumberChanged", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39760, "src": "667:13:21", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256)"}}, "id": 39808, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "667:31:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39809, "nodeType": "EmitStatement", "src": "662:36:21"}]}, "functionSelector": "d09de08a", "implemented": true, "kind": "function", "modifiers": [], "name": "increment", "nameLocation": "502:9:21", "parameters": {"id": 39789, "nodeType": "ParameterList", "parameters": [], "src": "511:2:21"}, "returnParameters": {"id": 39790, "nodeType": "ParameterList", "parameters": [], "src": "521:0:21"}, "scope": 39909, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 39834, "nodeType": "FunctionDefinition", "src": "711:194:21", "nodes": [], "body": {"id": 39833, "nodeType": "Block", "src": "739:166:21", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39817, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39815, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "757:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 39816, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "766:1:21", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "757:10:21", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "43616e6e6f742064656372656d656e742062656c6f77207a65726f", "id": 39818, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "769:29:21", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d1a36f333738d56db25706f3ee0d170f6f047b262c854995a688cb11b3df3828", "typeString": "literal_string \"Cannot decrement below zero\""}, "value": "Cannot decrement below zero"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_d1a36f333738d56db25706f3ee0d170f6f047b262c854995a688cb11b3df3828", "typeString": "literal_string \"Cannot decrement below zero\""}], "id": 39814, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "749:7:21", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 39819, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "749:50:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39820, "nodeType": "ExpressionStatement", "src": "749:50:21"}, {"assignments": [39822], "declarations": [{"constant": false, "id": 39822, "mutability": "mutable", "name": "oldValue", "nameLocation": "817:8:21", "nodeType": "VariableDeclaration", "scope": 39833, "src": "809:16:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39821, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "809:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 39824, "initialValue": {"id": 39823, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "828:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "809:25:21"}, {"expression": {"id": 39826, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "--", "prefix": false, "src": "844:8:21", "subExpression": {"id": 39825, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "844:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 39827, "nodeType": "ExpressionStatement", "src": "844:8:21"}, {"eventCall": {"arguments": [{"id": 39829, "name": "oldValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39822, "src": "881:8:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 39830, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "891:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 39828, "name": "NumberChanged", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39760, "src": "867:13:21", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256)"}}, "id": 39831, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "867:31:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39832, "nodeType": "EmitStatement", "src": "862:36:21"}]}, "functionSelector": "2baeceb7", "implemented": true, "kind": "function", "modifiers": [], "name": "decrement", "nameLocation": "720:9:21", "parameters": {"id": 39812, "nodeType": "ParameterList", "parameters": [], "src": "729:2:21"}, "returnParameters": {"id": 39813, "nodeType": "ParameterList", "parameters": [], "src": "739:0:21"}, "scope": 39909, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 39854, "nodeType": "FunctionDefinition", "src": "911:156:21", "nodes": [], "body": {"id": 39853, "nodeType": "Block", "src": "935:132:21", "nodes": [], "statements": [{"assignments": [39838], "declarations": [{"constant": false, "id": 39838, "mutability": "mutable", "name": "oldValue", "nameLocation": "953:8:21", "nodeType": "VariableDeclaration", "scope": 39853, "src": "945:16:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39837, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "945:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 39840, "initialValue": {"id": 39839, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "964:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "945:25:21"}, {"expression": {"id": 39843, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 39841, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "980:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "30", "id": 39842, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "989:1:21", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "980:10:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 39844, "nodeType": "ExpressionStatement", "src": "980:10:21"}, {"eventCall": {"arguments": [{"id": 39846, "name": "oldValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39838, "src": "1019:8:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "30", "id": 39847, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1029:1:21", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 39845, "name": "NumberChanged", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39760, "src": "1005:13:21", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256)"}}, "id": 39848, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1005:26:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39849, "nodeType": "EmitStatement", "src": "1000:31:21"}, {"eventCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 39850, "name": "CounterReset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39762, "src": "1046:12:21", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 39851, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1046:14:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39852, "nodeType": "EmitStatement", "src": "1041:19:21"}]}, "functionSelector": "d826f88f", "implemented": true, "kind": "function", "modifiers": [], "name": "reset", "nameLocation": "920:5:21", "parameters": {"id": 39835, "nodeType": "ParameterList", "parameters": [], "src": "925:2:21"}, "returnParameters": {"id": 39836, "nodeType": "ParameterList", "parameters": [], "src": "935:0:21"}, "scope": 39909, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 39882, "nodeType": "FunctionDefinition", "src": "1073:233:21", "nodes": [], "body": {"id": 39881, "nodeType": "Block", "src": "1108:198:21", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39864, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39862, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39860, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "1126:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 39861, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39856, "src": "1135:5:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1126:14:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 39863, "name": "MAX_VALUE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39754, "src": "1144:9:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1126:27:21", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4164646974696f6e20776f756c6420657863656564206d6178696d756d2076616c7565", "id": 39865, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1155:37:21", "typeDescriptions": {"typeIdentifier": "t_stringliteral_5d6edfb1d28183084ca1af74059bf1cb57af67efcb1c81f7d881b49775afa072", "typeString": "literal_string \"Addition would exceed maximum value\""}, "value": "Addition would exceed maximum value"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_5d6edfb1d28183084ca1af74059bf1cb57af67efcb1c81f7d881b49775afa072", "typeString": "literal_string \"Addition would exceed maximum value\""}], "id": 39859, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "1118:7:21", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 39866, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1118:75:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39867, "nodeType": "ExpressionStatement", "src": "1118:75:21"}, {"assignments": [39869], "declarations": [{"constant": false, "id": 39869, "mutability": "mutable", "name": "oldValue", "nameLocation": "1211:8:21", "nodeType": "VariableDeclaration", "scope": 39881, "src": "1203:16:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39868, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1203:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 39871, "initialValue": {"id": 39870, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "1222:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "1203:25:21"}, {"expression": {"id": 39874, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 39872, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "1238:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"id": 39873, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39856, "src": "1248:5:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1238:15:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 39875, "nodeType": "ExpressionStatement", "src": "1238:15:21"}, {"eventCall": {"arguments": [{"id": 39877, "name": "oldValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39869, "src": "1282:8:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 39878, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "1292:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 39876, "name": "NumberChanged", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39760, "src": "1268:13:21", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256)"}}, "id": 39879, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1268:31:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39880, "nodeType": "EmitStatement", "src": "1263:36:21"}]}, "functionSelector": "1003e2d2", "implemented": true, "kind": "function", "modifiers": [], "name": "add", "nameLocation": "1082:3:21", "parameters": {"id": 39857, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 39856, "mutability": "mutable", "name": "value", "nameLocation": "1094:5:21", "nodeType": "VariableDeclaration", "scope": 39882, "src": "1086:13:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39855, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1086:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1085:15:21"}, "returnParameters": {"id": 39858, "nodeType": "ParameterList", "parameters": [], "src": "1108:0:21"}, "scope": 39909, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 39908, "nodeType": "FunctionDefinition", "src": "1312:218:21", "nodes": [], "body": {"id": 39907, "nodeType": "Block", "src": "1352:178:21", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 39890, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 39888, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "1370:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 39889, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39884, "src": "1380:5:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1370:15:21", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5375627472616374696f6e20776f756c6420756e646572666c6f77", "id": 39891, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1387:29:21", "typeDescriptions": {"typeIdentifier": "t_stringliteral_bafc5048e29e1b5de6b86e7151b8bdcfb1fcf05c231391025069de3655c7401f", "typeString": "literal_string \"Subtraction would underflow\""}, "value": "Subtraction would underflow"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_bafc5048e29e1b5de6b86e7151b8bdcfb1fcf05c231391025069de3655c7401f", "typeString": "literal_string \"Subtraction would underflow\""}], "id": 39887, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "1362:7:21", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 39892, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1362:55:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39893, "nodeType": "ExpressionStatement", "src": "1362:55:21"}, {"assignments": [39895], "declarations": [{"constant": false, "id": 39895, "mutability": "mutable", "name": "oldValue", "nameLocation": "1435:8:21", "nodeType": "VariableDeclaration", "scope": 39907, "src": "1427:16:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39894, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1427:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 39897, "initialValue": {"id": 39896, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "1446:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "1427:25:21"}, {"expression": {"id": 39900, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 39898, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "1462:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "-=", "rightHandSide": {"id": 39899, "name": "value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39884, "src": "1472:5:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1462:15:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 39901, "nodeType": "ExpressionStatement", "src": "1462:15:21"}, {"eventCall": {"arguments": [{"id": 39903, "name": "oldValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39895, "src": "1506:8:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 39904, "name": "number", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39751, "src": "1516:6:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 39902, "name": "NumberChanged", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39760, "src": "1492:13:21", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256)"}}, "id": 39905, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1492:31:21", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 39906, "nodeType": "EmitStatement", "src": "1487:36:21"}]}, "functionSelector": "1dc05f17", "implemented": true, "kind": "function", "modifiers": [], "name": "subtract", "nameLocation": "1321:8:21", "parameters": {"id": 39885, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 39884, "mutability": "mutable", "name": "value", "nameLocation": "1338:5:21", "nodeType": "VariableDeclaration", "scope": 39908, "src": "1330:13:21", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 39883, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1330:7:21", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1329:15:21"}, "returnParameters": {"id": 39886, "nodeType": "ParameterList", "parameters": [], "src": "1352:0:21"}, "scope": 39909, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "abstract": false, "baseContracts": [], "canonicalName": "Counter", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [39909], "name": "Counter", "nameLocation": "74:7:21", "scope": 39910, "usedErrors": [], "usedEvents": [39760, 39762]}], "license": "UNLICENSED"}, "id": 21}