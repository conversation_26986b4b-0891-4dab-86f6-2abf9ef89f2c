{"abi": [], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/chimera/src/BaseProperties.sol\":\"BaseProperties\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/BaseProperties.sol\":{\"keccak256\":\"0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848\",\"dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w\"]},\"lib/chimera/src/BaseSetup.sol\":{\"keccak256\":\"0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe\",\"dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/chimera/src/BaseProperties.sol": "BaseProperties"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/BaseProperties.sol": {"keccak256": "0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a", "urls": ["bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848", "dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w"], "license": "MIT"}, "lib/chimera/src/BaseSetup.sol": {"keccak256": "0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107", "urls": ["bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe", "dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/chimera/src/BaseProperties.sol", "id": 89, "exportedSymbols": {"BaseProperties": [88], "BaseSetup": [94]}, "nodeType": "SourceUnit", "src": "32:118:1", "nodes": [{"id": 83, "nodeType": "PragmaDirective", "src": "32:23:1", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 85, "nodeType": "ImportDirective", "src": "57:42:1", "nodes": [], "absolutePath": "lib/chimera/src/BaseSetup.sol", "file": "./BaseSetup.sol", "nameLocation": "-1:-1:-1", "scope": 89, "sourceUnit": 95, "symbolAliases": [{"foreign": {"id": 84, "name": "BaseSetup", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 94, "src": "65:9:1", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 88, "nodeType": "ContractDefinition", "src": "101:48:1", "nodes": [], "abstract": true, "baseContracts": [{"baseName": {"id": 86, "name": "BaseSetup", "nameLocations": ["137:9:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 94, "src": "137:9:1"}, "id": 87, "nodeType": "InheritanceSpecifier", "src": "137:9:1"}], "canonicalName": "BaseProperties", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": false, "linearizedBaseContracts": [88, 94], "name": "BaseProperties", "nameLocation": "119:14:1", "scope": 89, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 1}