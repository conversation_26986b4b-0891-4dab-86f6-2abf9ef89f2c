{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/Test.sol\":\"Test\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3\",\"dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/Test.sol": "Test"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43", "urls": ["bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3", "dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 46, "contract": "lib/forge-std/src/Test.sol:Test", "label": "stdstore", "offset": 0, "slot": "0", "type": "t_struct(StdStorage)8331_storage"}, {"astId": 250, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_failed", "offset": 0, "slot": "8", "type": "t_bool"}, {"astId": 2983, "contract": "lib/forge-std/src/Test.sol:Test", "label": "stdChainsInitialized", "offset": 1, "slot": "8", "type": "t_bool"}, {"astId": 3004, "contract": "lib/forge-std/src/Test.sol:Test", "label": "chains", "offset": 0, "slot": "9", "type": "t_mapping(t_string_memory_ptr,t_struct(Chain)2999_storage)"}, {"astId": 3008, "contract": "lib/forge-std/src/Test.sol:Test", "label": "defaultRpcUrls", "offset": 0, "slot": "10", "type": "t_mapping(t_string_memory_ptr,t_string_storage)"}, {"astId": 3012, "contract": "lib/forge-std/src/Test.sol:Test", "label": "idToAlias", "offset": 0, "slot": "11", "type": "t_mapping(t_uint256,t_string_storage)"}, {"astId": 3015, "contract": "lib/forge-std/src/Test.sol:Test", "label": "fallbackToDefaultRpcUrls", "offset": 0, "slot": "12", "type": "t_bool"}, {"astId": 3953, "contract": "lib/forge-std/src/Test.sol:Test", "label": "gasMeteringOff", "offset": 1, "slot": "12", "type": "t_bool"}, {"astId": 6020, "contract": "lib/forge-std/src/Test.sol:Test", "label": "stdstore", "offset": 0, "slot": "13", "type": "t_struct(StdStorage)8331_storage"}, {"astId": 6937, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_excludedContracts", "offset": 0, "slot": "21", "type": "t_array(t_address)dyn_storage"}, {"astId": 6940, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_excludedSenders", "offset": 0, "slot": "22", "type": "t_array(t_address)dyn_storage"}, {"astId": 6943, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_targetedContracts", "offset": 0, "slot": "23", "type": "t_array(t_address)dyn_storage"}, {"astId": 6946, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_targetedSenders", "offset": 0, "slot": "24", "type": "t_array(t_address)dyn_storage"}, {"astId": 6949, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_excludedArtifacts", "offset": 0, "slot": "25", "type": "t_array(t_string_storage)dyn_storage"}, {"astId": 6952, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_targetedArtifacts", "offset": 0, "slot": "26", "type": "t_array(t_string_storage)dyn_storage"}, {"astId": 6956, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_targetedArtifactSelectors", "offset": 0, "slot": "27", "type": "t_array(t_struct(FuzzArtifactSelector)6928_storage)dyn_storage"}, {"astId": 6960, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_excludedSelectors", "offset": 0, "slot": "28", "type": "t_array(t_struct(FuzzSelector)6922_storage)dyn_storage"}, {"astId": 6964, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_targetedSelectors", "offset": 0, "slot": "29", "type": "t_array(t_struct(FuzzSelector)6922_storage)dyn_storage"}, {"astId": 6968, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_targetedInterfaces", "offset": 0, "slot": "30", "type": "t_array(t_struct(FuzzInterface)6934_storage)dyn_storage"}, {"astId": 13223, "contract": "lib/forge-std/src/Test.sol:Test", "label": "IS_TEST", "offset": 0, "slot": "31", "type": "t_bool"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"encoding": "dynamic_array", "label": "address[]", "numberOfBytes": "32", "base": "t_address"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_array(t_bytes4)dyn_storage": {"encoding": "dynamic_array", "label": "bytes4[]", "numberOfBytes": "32", "base": "t_bytes4"}, "t_array(t_string_storage)dyn_storage": {"encoding": "dynamic_array", "label": "string[]", "numberOfBytes": "32", "base": "t_string_storage"}, "t_array(t_struct(FuzzArtifactSelector)6928_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzArtifactSelector[]", "numberOfBytes": "32", "base": "t_struct(FuzzArtifactSelector)6928_storage"}, "t_array(t_struct(FuzzInterface)6934_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzInterface[]", "numberOfBytes": "32", "base": "t_struct(FuzzInterface)6934_storage"}, "t_array(t_struct(FuzzSelector)6922_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzSelector[]", "numberOfBytes": "32", "base": "t_struct(FuzzSelector)6922_storage"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_bytes4": {"encoding": "inplace", "label": "bytes4", "numberOfBytes": "4"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage)))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(bytes4 => mapping(bytes32 => struct FindData)))", "numberOfBytes": "32", "value": "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage))"}, "t_mapping(t_bytes32,t_struct(FindData)8306_storage)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => struct FindData)", "numberOfBytes": "32", "value": "t_struct(FindData)8306_storage"}, "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage))": {"encoding": "mapping", "key": "t_bytes4", "label": "mapping(bytes4 => mapping(bytes32 => struct FindData))", "numberOfBytes": "32", "value": "t_mapping(t_bytes32,t_struct(FindData)8306_storage)"}, "t_mapping(t_string_memory_ptr,t_string_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_mapping(t_string_memory_ptr,t_struct(Chain)2999_storage)": {"encoding": "mapping", "key": "t_string_memory_ptr", "label": "mapping(string => struct StdChains.Chain)", "numberOfBytes": "32", "value": "t_struct(Chain)2999_storage"}, "t_mapping(t_uint256,t_string_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_string_memory_ptr": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Chain)2999_storage": {"encoding": "inplace", "label": "struct StdChains.Chain", "numberOfBytes": "128", "members": [{"astId": 2992, "contract": "lib/forge-std/src/Test.sol:Test", "label": "name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 2994, "contract": "lib/forge-std/src/Test.sol:Test", "label": "chainId", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 2996, "contract": "lib/forge-std/src/Test.sol:Test", "label": "chainAlias", "offset": 0, "slot": "2", "type": "t_string_storage"}, {"astId": 2998, "contract": "lib/forge-std/src/Test.sol:Test", "label": "rpcUrl", "offset": 0, "slot": "3", "type": "t_string_storage"}]}, "t_struct(FindData)8306_storage": {"encoding": "inplace", "label": "struct FindData", "numberOfBytes": "128", "members": [{"astId": 8299, "contract": "lib/forge-std/src/Test.sol:Test", "label": "slot", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 8301, "contract": "lib/forge-std/src/Test.sol:Test", "label": "offsetLeft", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 8303, "contract": "lib/forge-std/src/Test.sol:Test", "label": "offsetRight", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 8305, "contract": "lib/forge-std/src/Test.sol:Test", "label": "found", "offset": 0, "slot": "3", "type": "t_bool"}]}, "t_struct(FuzzArtifactSelector)6928_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzArtifactSelector", "numberOfBytes": "64", "members": [{"astId": 6924, "contract": "lib/forge-std/src/Test.sol:Test", "label": "artifact", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 6927, "contract": "lib/forge-std/src/Test.sol:Test", "label": "selectors", "offset": 0, "slot": "1", "type": "t_array(t_bytes4)dyn_storage"}]}, "t_struct(FuzzInterface)6934_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzInterface", "numberOfBytes": "64", "members": [{"astId": 6930, "contract": "lib/forge-std/src/Test.sol:Test", "label": "addr", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 6933, "contract": "lib/forge-std/src/Test.sol:Test", "label": "artifacts", "offset": 0, "slot": "1", "type": "t_array(t_string_storage)dyn_storage"}]}, "t_struct(FuzzSelector)6922_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzSelector", "numberOfBytes": "64", "members": [{"astId": 6918, "contract": "lib/forge-std/src/Test.sol:Test", "label": "addr", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 6921, "contract": "lib/forge-std/src/Test.sol:Test", "label": "selectors", "offset": 0, "slot": "1", "type": "t_array(t_bytes4)dyn_storage"}]}, "t_struct(StdStorage)8331_storage": {"encoding": "inplace", "label": "struct StdStorage", "numberOfBytes": "256", "members": [{"astId": 8315, "contract": "lib/forge-std/src/Test.sol:Test", "label": "finds", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage)))"}, {"astId": 8318, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_keys", "offset": 0, "slot": "1", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 8320, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_sig", "offset": 0, "slot": "2", "type": "t_bytes4"}, {"astId": 8322, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_depth", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 8324, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_target", "offset": 0, "slot": "4", "type": "t_address"}, {"astId": 8326, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_set", "offset": 0, "slot": "5", "type": "t_bytes32"}, {"astId": 8328, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_enable_packed_slots", "offset": 0, "slot": "6", "type": "t_bool"}, {"astId": 8330, "contract": "lib/forge-std/src/Test.sol:Test", "label": "_calldata", "offset": 0, "slot": "7", "type": "t_bytes_storage"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "lib/forge-std/src/Test.sol", "id": 13225, "exportedSymbols": {"StdAssertions": [2959], "StdChains": [3921], "StdCheats": [6806], "StdConstants": [6847], "StdInvariant": [7207], "StdStorage": [8331], "StdStyle": [11499], "StdUtils": [13170], "Test": [13224], "TestBase": [50], "Vm": [18352], "console": [26468], "console2": [26468], "safeconsole": [39710], "stdError": [6913], "stdJson": [8151], "stdMath": [8293], "stdStorage": [10288], "stdToml": [12443]}, "nodeType": "SourceUnit", "src": "32:1063:14", "nodes": [{"id": 13172, "nodeType": "PragmaDirective", "src": "32:31:14", "nodes": [], "literals": ["solidity", ">=", "0.6", ".2", "<", "0.9", ".0"]}, {"id": 13173, "nodeType": "PragmaDirective", "src": "65:33:14", "nodes": [], "literals": ["experimental", "ABIEncoderV2"]}, {"id": 13175, "nodeType": "ImportDirective", "src": "160:38:14", "nodes": [], "absolutePath": "lib/forge-std/src/console.sol", "file": "./console.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 26469, "symbolAliases": [{"foreign": {"id": 13174, "name": "console", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26468, "src": "168:7:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13177, "nodeType": "ImportDirective", "src": "199:40:14", "nodes": [], "absolutePath": "lib/forge-std/src/console2.sol", "file": "./console2.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 26473, "symbolAliases": [{"foreign": {"id": 13176, "name": "console2", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26468, "src": "207:8:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13179, "nodeType": "ImportDirective", "src": "240:46:14", "nodes": [], "absolutePath": "lib/forge-std/src/safeconsole.sol", "file": "./safeconsole.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 39711, "symbolAliases": [{"foreign": {"id": 13178, "name": "safeconsole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 39710, "src": "248:11:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13181, "nodeType": "ImportDirective", "src": "287:50:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdAssertions.sol", "file": "./StdAssertions.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 2960, "symbolAliases": [{"foreign": {"id": 13180, "name": "StdAssertions", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2959, "src": "295:13:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13183, "nodeType": "ImportDirective", "src": "338:42:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdChains.sol", "file": "./StdChains.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 3922, "symbolAliases": [{"foreign": {"id": 13182, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3921, "src": "346:9:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13185, "nodeType": "ImportDirective", "src": "381:42:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdCheats.sol", "file": "./StdCheats.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 6807, "symbolAliases": [{"foreign": {"id": 13184, "name": "StdCheats", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6806, "src": "389:9:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13187, "nodeType": "ImportDirective", "src": "424:48:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdConstants.sol", "file": "./StdConstants.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 6848, "symbolAliases": [{"foreign": {"id": 13186, "name": "StdConstants", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6847, "src": "432:12:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13189, "nodeType": "ImportDirective", "src": "473:40:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdError.sol", "file": "./StdError.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 6914, "symbolAliases": [{"foreign": {"id": 13188, "name": "stdError", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6913, "src": "481:8:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13191, "nodeType": "ImportDirective", "src": "514:48:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdInvariant.sol", "file": "./StdInvariant.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 7208, "symbolAliases": [{"foreign": {"id": 13190, "name": "StdInvariant", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7207, "src": "522:12:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13193, "nodeType": "ImportDirective", "src": "563:38:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdJson.sol", "file": "./StdJson.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 8152, "symbolAliases": [{"foreign": {"id": 13192, "name": "stdJson", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8151, "src": "571:7:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13195, "nodeType": "ImportDirective", "src": "602:38:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdMath.sol", "file": "./StdMath.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 8294, "symbolAliases": [{"foreign": {"id": 13194, "name": "stdMath", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8293, "src": "610:7:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13198, "nodeType": "ImportDirective", "src": "641:56:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdStorage.sol", "file": "./StdStorage.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 10289, "symbolAliases": [{"foreign": {"id": 13196, "name": "StdStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8331, "src": "649:10:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}, {"foreign": {"id": 13197, "name": "stdStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10288, "src": "661:10:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13200, "nodeType": "ImportDirective", "src": "698:40:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdStyle.sol", "file": "./StdStyle.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 11500, "symbolAliases": [{"foreign": {"id": 13199, "name": "StdStyle", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11499, "src": "706:8:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13202, "nodeType": "ImportDirective", "src": "739:38:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdToml.sol", "file": "./StdToml.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 12444, "symbolAliases": [{"foreign": {"id": 13201, "name": "stdToml", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12443, "src": "747:7:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13204, "nodeType": "ImportDirective", "src": "778:40:14", "nodes": [], "absolutePath": "lib/forge-std/src/StdUtils.sol", "file": "./StdUtils.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 13171, "symbolAliases": [{"foreign": {"id": 13203, "name": "StdUtils", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13170, "src": "786:8:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13206, "nodeType": "ImportDirective", "src": "819:28:14", "nodes": [], "absolutePath": "lib/forge-std/src/Vm.sol", "file": "./Vm.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 18353, "symbolAliases": [{"foreign": {"id": 13205, "name": "Vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18352, "src": "827:2:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13208, "nodeType": "ImportDirective", "src": "869:36:14", "nodes": [], "absolutePath": "lib/forge-std/src/Base.sol", "file": "./Base.sol", "nameLocation": "-1:-1:-1", "scope": 13225, "sourceUnit": 60, "symbolAliases": [{"foreign": {"id": 13207, "name": "TestBase", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 50, "src": "877:8:14", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13224, "nodeType": "ContractDefinition", "src": "922:172:14", "nodes": [{"id": 13223, "nodeType": "VariableDeclaration", "src": "1065:26:14", "nodes": [], "constant": false, "functionSelector": "fa7626d4", "mutability": "mutable", "name": "IS_TEST", "nameLocation": "1077:7:14", "scope": 13224, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 13221, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1065:4:14", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": {"hexValue": "74727565", "id": 13222, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1087:4:14", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "visibility": "public"}], "abstract": true, "baseContracts": [{"baseName": {"id": 13209, "name": "TestBase", "nameLocations": ["948:8:14"], "nodeType": "IdentifierPath", "referencedDeclaration": 50, "src": "948:8:14"}, "id": 13210, "nodeType": "InheritanceSpecifier", "src": "948:8:14"}, {"baseName": {"id": 13211, "name": "StdAssertions", "nameLocations": ["958:13:14"], "nodeType": "IdentifierPath", "referencedDeclaration": 2959, "src": "958:13:14"}, "id": 13212, "nodeType": "InheritanceSpecifier", "src": "958:13:14"}, {"baseName": {"id": 13213, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocations": ["973:9:14"], "nodeType": "IdentifierPath", "referencedDeclaration": 3921, "src": "973:9:14"}, "id": 13214, "nodeType": "InheritanceSpecifier", "src": "973:9:14"}, {"baseName": {"id": 13215, "name": "StdCheats", "nameLocations": ["984:9:14"], "nodeType": "IdentifierPath", "referencedDeclaration": 6806, "src": "984:9:14"}, "id": 13216, "nodeType": "InheritanceSpecifier", "src": "984:9:14"}, {"baseName": {"id": 13217, "name": "StdInvariant", "nameLocations": ["995:12:14"], "nodeType": "IdentifierPath", "referencedDeclaration": 7207, "src": "995:12:14"}, "id": 13218, "nodeType": "InheritanceSpecifier", "src": "995:12:14"}, {"baseName": {"id": 13219, "name": "StdUtils", "nameLocations": ["1009:8:14"], "nodeType": "IdentifierPath", "referencedDeclaration": 13170, "src": "1009:8:14"}, "id": 13220, "nodeType": "InheritanceSpecifier", "src": "1009:8:14"}], "canonicalName": "Test", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [13224, 13170, 7207, 6806, 6011, 3921, 2959, 50, 47], "name": "Test", "nameLocation": "940:4:14", "scope": 13225, "usedErrors": [], "usedEvents": [126, 130, 134, 138, 142, 146, 150, 154, 160, 166, 174, 182, 188, 194, 200, 206, 211, 216, 221, 228, 235, 242]}], "license": "MIT"}, "id": 14}