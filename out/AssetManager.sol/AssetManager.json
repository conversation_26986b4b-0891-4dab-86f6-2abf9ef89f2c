{"abi": [{"type": "error", "name": "Exists", "inputs": []}, {"type": "error", "name": "NotAdded", "inputs": []}, {"type": "error", "name": "NotSetup", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"Exists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotSetup\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"Source of truth for the assets being used in the test\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"No assets should be used in the suite without being added here first\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/setup-helpers/src/AssetManager.sol\":\"AssetManager\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/BaseSetup.sol\":{\"keccak256\":\"0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe\",\"dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1\"]},\"lib/chimera/src/Hevm.sol\":{\"keccak256\":\"0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b\",\"dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr\"]},\"lib/setup-helpers/src/AssetManager.sol\":{\"keccak256\":\"0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa\",\"dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq\"]},\"lib/setup-helpers/src/EnumerableSet.sol\":{\"keccak256\":\"0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df\",\"dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW\"]},\"lib/setup-helpers/src/MockERC20.sol\":{\"keccak256\":\"0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578\",\"dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "Exists"}, {"inputs": [], "type": "error", "name": "NotAdded"}, {"inputs": [], "type": "error", "name": "NotSetup"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/setup-helpers/src/AssetManager.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/BaseSetup.sol": {"keccak256": "0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107", "urls": ["bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe", "dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1"], "license": "MIT"}, "lib/chimera/src/Hevm.sol": {"keccak256": "0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe", "urls": ["bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b", "dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr"], "license": "MIT"}, "lib/setup-helpers/src/AssetManager.sol": {"keccak256": "0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc", "urls": ["bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa", "dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq"], "license": "GPL-2.0"}, "lib/setup-helpers/src/EnumerableSet.sol": {"keccak256": "0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514", "urls": ["bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df", "dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW"], "license": "MIT"}, "lib/setup-helpers/src/MockERC20.sol": {"keccak256": "0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f", "urls": ["bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578", "dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE"], "license": "AGPL-3.0-only"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 40565, "contract": "lib/setup-helpers/src/AssetManager.sol:AssetManager", "label": "__asset", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 40569, "contract": "lib/setup-helpers/src/AssetManager.sol:AssetManager", "label": "_assets", "offset": 0, "slot": "1", "type": "t_struct(AddressSet)41192_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(AddressSet)41192_storage": {"encoding": "inplace", "label": "struct EnumerableSet.AddressSet", "numberOfBytes": "64", "members": [{"astId": 41191, "contract": "lib/setup-helpers/src/AssetManager.sol:AssetManager", "label": "_inner", "offset": 0, "slot": "0", "type": "t_struct(Set)40877_storage"}]}, "t_struct(Set)40877_storage": {"encoding": "inplace", "label": "struct EnumerableSet.Set", "numberOfBytes": "64", "members": [{"astId": 40872, "contract": "lib/setup-helpers/src/AssetManager.sol:AssetManager", "label": "_values", "offset": 0, "slot": "0", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 40876, "contract": "lib/setup-helpers/src/AssetManager.sol:AssetManager", "label": "_indexes", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_uint256)"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "lib/setup-helpers/src/AssetManager.sol", "id": 40867, "exportedSymbols": {"AssetManager": [40866], "BaseSetup": [94], "EnumerableSet": [41479], "MockERC20": [42022], "vm": [719]}, "nodeType": "SourceUnit", "src": "36:4578:27", "nodes": [{"id": 40549, "nodeType": "PragmaDirective", "src": "36:23:27", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 40551, "nodeType": "ImportDirective", "src": "61:49:27", "nodes": [], "absolutePath": "lib/chimera/src/BaseSetup.sol", "file": "@chimera/BaseSetup.sol", "nameLocation": "-1:-1:-1", "scope": 40867, "sourceUnit": 95, "symbolAliases": [{"foreign": {"id": 40550, "name": "BaseSetup", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 94, "src": "69:9:27", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 40553, "nodeType": "ImportDirective", "src": "111:37:27", "nodes": [], "absolutePath": "lib/chimera/src/Hevm.sol", "file": "@chimera/Hevm.sol", "nameLocation": "-1:-1:-1", "scope": 40867, "sourceUnit": 720, "symbolAliases": [{"foreign": {"id": 40552, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "119:2:27", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 40555, "nodeType": "ImportDirective", "src": "150:50:27", "nodes": [], "absolutePath": "lib/setup-helpers/src/EnumerableSet.sol", "file": "./EnumerableSet.sol", "nameLocation": "-1:-1:-1", "scope": 40867, "sourceUnit": 41480, "symbolAliases": [{"foreign": {"id": 40554, "name": "EnumerableSet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41479, "src": "158:13:27", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 40557, "nodeType": "ImportDirective", "src": "201:42:27", "nodes": [], "absolutePath": "lib/setup-helpers/src/MockERC20.sol", "file": "./MockERC20.sol", "nameLocation": "-1:-1:-1", "scope": 40867, "sourceUnit": 42023, "symbolAliases": [{"foreign": {"id": 40556, "name": "MockERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42022, "src": "209:9:27", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 40866, "nodeType": "ContractDefinition", "src": "389:4224:27", "nodes": [{"id": 40562, "nodeType": "UsingForDirective", "src": "426:49:27", "nodes": [], "global": false, "libraryName": {"id": 40559, "name": "EnumerableSet", "nameLocations": ["432:13:27"], "nodeType": "IdentifierPath", "referencedDeclaration": 41479, "src": "432:13:27"}, "typeName": {"id": 40561, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 40560, "name": "EnumerableSet.AddressSet", "nameLocations": ["450:13:27", "464:10:27"], "nodeType": "IdentifierPath", "referencedDeclaration": 41192, "src": "450:24:27"}, "referencedDeclaration": 41192, "src": "450:24:27", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}}}, {"id": 40565, "nodeType": "VariableDeclaration", "src": "542:23:27", "nodes": [], "constant": false, "documentation": {"id": 40563, "nodeType": "StructuredDocumentation", "src": "481:56:27", "text": "@notice The current target for this set of variables"}, "mutability": "mutable", "name": "__asset", "nameLocation": "558:7:27", "scope": 40866, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40564, "name": "address", "nodeType": "ElementaryTypeName", "src": "542:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "private"}, {"id": 40569, "nodeType": "VariableDeclaration", "src": "622:40:27", "nodes": [], "constant": false, "documentation": {"id": 40566, "nodeType": "StructuredDocumentation", "src": "572:45:27", "text": "@notice The list of all assets being used"}, "mutability": "mutable", "name": "_assets", "nameLocation": "655:7:27", "scope": 40866, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet"}, "typeName": {"id": 40568, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 40567, "name": "EnumerableSet.AddressSet", "nameLocations": ["622:13:27", "636:10:27"], "nodeType": "IdentifierPath", "referencedDeclaration": 41192, "src": "622:24:27"}, "referencedDeclaration": 41192, "src": "622:24:27", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage_ptr", "typeString": "struct EnumerableSet.AddressSet"}}, "visibility": "private"}, {"id": 40571, "nodeType": "ErrorDefinition", "src": "761:17:27", "nodes": [], "errorSelector": "b09c99c0", "name": "NotSetup", "nameLocation": "767:8:27", "parameters": {"id": 40570, "nodeType": "ParameterList", "parameters": [], "src": "775:2:27"}}, {"id": 40573, "nodeType": "ErrorDefinition", "src": "814:15:27", "nodes": [], "errorSelector": "846ec056", "name": "Exists", "nameLocation": "820:6:27", "parameters": {"id": 40572, "nodeType": "ParameterList", "parameters": [], "src": "826:2:27"}}, {"id": 40575, "nodeType": "ErrorDefinition", "src": "866:17:27", "nodes": [], "errorSelector": "4414c633", "name": "NotAdded", "nameLocation": "872:8:27", "parameters": {"id": 40574, "nodeType": "ParameterList", "parameters": [], "src": "880:2:27"}}, {"id": 40595, "nodeType": "FunctionDefinition", "src": "938:163:27", "nodes": [], "body": {"id": 40594, "nodeType": "Block", "src": "991:110:27", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 40586, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40581, "name": "__asset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40565, "src": "1005:7:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 40584, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1024:1:27", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 40583, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1016:7:27", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 40582, "name": "address", "nodeType": "ElementaryTypeName", "src": "1016:7:27", "typeDescriptions": {}}}, "id": 40585, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1016:10:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1005:21:27", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40591, "nodeType": "IfStatement", "src": "1001:69:27", "trueBody": {"id": 40590, "nodeType": "Block", "src": "1028:42:27", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 40587, "name": "NotSetup", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40571, "src": "1049:8:27", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$_t_error_$", "typeString": "function () pure returns (error)"}}, "id": 40588, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1049:10:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 40589, "nodeType": "RevertStatement", "src": "1042:17:27"}]}}, {"expression": {"id": 40592, "name": "__asset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40565, "src": "1087:7:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 40580, "id": 40593, "nodeType": "Return", "src": "1080:14:27"}]}, "documentation": {"id": 40576, "nodeType": "StructuredDocumentation", "src": "889:44:27", "text": "@notice Returns the current active asset"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_getAsset", "nameLocation": "947:9:27", "parameters": {"id": 40577, "nodeType": "ParameterList", "parameters": [], "src": "956:2:27"}, "returnParameters": {"id": 40580, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40579, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 40595, "src": "982:7:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40578, "name": "address", "nodeType": "ElementaryTypeName", "src": "982:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "981:9:27"}, "scope": 40866, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 40607, "nodeType": "FunctionDefinition", "src": "1153:103:27", "nodes": [], "body": {"id": 40606, "nodeType": "Block", "src": "1216:40:27", "nodes": [], "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 40602, "name": "_assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40569, "src": "1233:7:27", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40603, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1241:6:27", "memberName": "values", "nodeType": "MemberAccess", "referencedDeclaration": 41345, "src": "1233:14:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_AddressSet_$41192_storage_ptr_$returns$_t_array$_t_address_$dyn_memory_ptr_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer) view returns (address[] memory)"}}, "id": 40604, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1233:16:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "functionReturnParameters": 40601, "id": 40605, "nodeType": "Return", "src": "1226:23:27"}]}, "documentation": {"id": 40596, "nodeType": "StructuredDocumentation", "src": "1107:41:27", "text": "@notice Returns all assets being used"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_getAssets", "nameLocation": "1162:10:27", "parameters": {"id": 40597, "nodeType": "ParameterList", "parameters": [], "src": "1172:2:27"}, "returnParameters": {"id": 40601, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40600, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 40607, "src": "1198:16:27", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 40598, "name": "address", "nodeType": "ElementaryTypeName", "src": "1198:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40599, "nodeType": "ArrayTypeName", "src": "1198:9:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "1197:18:27"}, "scope": 40866, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 40639, "nodeType": "FunctionDefinition", "src": "1438:328:27", "nodes": [], "body": {"id": 40638, "nodeType": "Block", "src": "1500:266:27", "nodes": [], "statements": [{"assignments": [40616], "declarations": [{"constant": false, "id": 40616, "mutability": "mutable", "name": "asset_", "nameLocation": "1518:6:27", "nodeType": "VariableDeclaration", "scope": 40638, "src": "1510:14:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40615, "name": "address", "nodeType": "ElementaryTypeName", "src": "1510:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 40627, "initialValue": {"arguments": [{"arguments": [{"hexValue": "5465737420546f6b656e", "id": 40622, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1549:12:27", "typeDescriptions": {"typeIdentifier": "t_stringliteral_81e8ad438398ab0d4a2a7ecb159490c091bb27c67ace5181f70084e30fadaea2", "typeString": "literal_string \"Test Token\""}, "value": "Test Token"}, {"hexValue": "545354", "id": 40623, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1563:5:27", "typeDescriptions": {"typeIdentifier": "t_stringliteral_20a94e575762ae205efd2d939940bb203cefdf1c455cc39ef343e287f43b70f4", "typeString": "literal_string \"TST\""}, "value": "TST"}, {"id": 40624, "name": "decimals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40610, "src": "1570:8:27", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_81e8ad438398ab0d4a2a7ecb159490c091bb27c67ace5181f70084e30fadaea2", "typeString": "literal_string \"Test Token\""}, {"typeIdentifier": "t_stringliteral_20a94e575762ae205efd2d939940bb203cefdf1c455cc39ef343e287f43b70f4", "typeString": "literal_string \"TST\""}, {"typeIdentifier": "t_uint8", "typeString": "uint8"}], "id": 40621, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "NewExpression", "src": "1535:13:27", "typeDescriptions": {"typeIdentifier": "t_function_creation_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_uint8_$returns$_t_contract$_MockERC20_$42022_$", "typeString": "function (string memory,string memory,uint8) returns (contract MockERC20)"}, "typeName": {"id": 40620, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 40619, "name": "MockERC20", "nameLocations": ["1539:9:27"], "nodeType": "IdentifierPath", "referencedDeclaration": 42022, "src": "1539:9:27"}, "referencedDeclaration": 42022, "src": "1539:9:27", "typeDescriptions": {"typeIdentifier": "t_contract$_MockERC20_$42022", "typeString": "contract MockERC20"}}}, "id": 40625, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1535:44:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_MockERC20_$42022", "typeString": "contract MockERC20"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_MockERC20_$42022", "typeString": "contract MockERC20"}], "id": 40618, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1527:7:27", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 40617, "name": "address", "nodeType": "ElementaryTypeName", "src": "1527:7:27", "typeDescriptions": {}}}, "id": 40626, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1527:53:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "1510:70:27"}, {"expression": {"arguments": [{"id": 40629, "name": "asset_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40616, "src": "1664:6:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 40628, "name": "_addAsset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40661, "src": "1654:9:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 40630, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1654:17:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40631, "nodeType": "ExpressionStatement", "src": "1654:17:27"}, {"expression": {"id": 40634, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 40632, "name": "__asset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40565, "src": "1681:7:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 40633, "name": "asset_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40616, "src": "1691:6:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1681:16:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40635, "nodeType": "ExpressionStatement", "src": "1681:16:27"}, {"expression": {"id": 40636, "name": "asset_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40616, "src": "1753:6:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 40614, "id": 40637, "nodeType": "Return", "src": "1746:13:27"}]}, "documentation": {"id": 40608, "nodeType": "StructuredDocumentation", "src": "1262:171:27", "text": "@notice Creates a new asset and adds it to the list of assets\n @param decimals The number of decimals for the asset\n @return The address of the new asset"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_newAsset", "nameLocation": "1447:9:27", "parameters": {"id": 40611, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40610, "mutability": "mutable", "name": "decimals", "nameLocation": "1463:8:27", "nodeType": "VariableDeclaration", "scope": 40639, "src": "1457:14:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 40609, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "1457:5:27", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "1456:16:27"}, "returnParameters": {"id": 40614, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40613, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 40639, "src": "1491:7:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40612, "name": "address", "nodeType": "ElementaryTypeName", "src": "1491:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1490:9:27"}, "scope": 40866, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 40661, "nodeType": "FunctionDefinition", "src": "1878:160:27", "nodes": [], "body": {"id": 40660, "nodeType": "Block", "src": "1922:116:27", "nodes": [], "statements": [{"condition": {"arguments": [{"id": 40647, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40642, "src": "1953:6:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40645, "name": "_assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40569, "src": "1936:7:27", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40646, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1944:8:27", "memberName": "contains", "nodeType": "MemberAccess", "referencedDeclaration": 41273, "src": "1936:16:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_AddressSet_$41192_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,address) view returns (bool)"}}, "id": 40648, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1936:24:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40653, "nodeType": "IfStatement", "src": "1932:70:27", "trueBody": {"id": 40652, "nodeType": "Block", "src": "1962:40:27", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 40649, "name": "Exists", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40573, "src": "1983:6:27", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$_t_error_$", "typeString": "function () pure returns (error)"}}, "id": 40650, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1983:8:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 40651, "nodeType": "RevertStatement", "src": "1976:15:27"}]}}, {"expression": {"arguments": [{"id": 40657, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40642, "src": "2024:6:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40654, "name": "_assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40569, "src": "2012:7:27", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40656, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2020:3:27", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 41219, "src": "2012:11:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_AddressSet_$41192_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,address) returns (bool)"}}, "id": 40658, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2012:19:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40659, "nodeType": "ExpressionStatement", "src": "2012:19:27"}]}, "documentation": {"id": 40640, "nodeType": "StructuredDocumentation", "src": "1772:101:27", "text": "@notice Adds an asset to the list of assets\n @param target The address of the asset to add"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_addAsset", "nameLocation": "1887:9:27", "parameters": {"id": 40643, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40642, "mutability": "mutable", "name": "target", "nameLocation": "1905:6:27", "nodeType": "VariableDeclaration", "scope": 40661, "src": "1897:14:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40641, "name": "address", "nodeType": "ElementaryTypeName", "src": "1897:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1896:16:27"}, "returnParameters": {"id": 40644, "nodeType": "ParameterList", "parameters": [], "src": "1922:0:27"}, "scope": 40866, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 40684, "nodeType": "FunctionDefinition", "src": "2158:169:27", "nodes": [], "body": {"id": 40683, "nodeType": "Block", "src": "2205:122:27", "nodes": [], "statements": [{"condition": {"id": 40671, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "2219:25:27", "subExpression": {"arguments": [{"id": 40669, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40664, "src": "2237:6:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40667, "name": "_assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40569, "src": "2220:7:27", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40668, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2228:8:27", "memberName": "contains", "nodeType": "MemberAccess", "referencedDeclaration": 41273, "src": "2220:16:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_AddressSet_$41192_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,address) view returns (bool)"}}, "id": 40670, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2220:24:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40676, "nodeType": "IfStatement", "src": "2215:73:27", "trueBody": {"id": 40675, "nodeType": "Block", "src": "2246:42:27", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 40672, "name": "NotAdded", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40575, "src": "2267:8:27", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$_t_error_$", "typeString": "function () pure returns (error)"}}, "id": 40673, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2267:10:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_error", "typeString": "error"}}, "id": 40674, "nodeType": "RevertStatement", "src": "2260:17:27"}]}}, {"expression": {"arguments": [{"id": 40680, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40664, "src": "2313:6:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40677, "name": "_assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40569, "src": "2298:7:27", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40679, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2306:6:27", "memberName": "remove", "nodeType": "MemberAccess", "referencedDeclaration": 41246, "src": "2298:14:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_AddressSet_$41192_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,address) returns (bool)"}}, "id": 40681, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2298:22:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40682, "nodeType": "ExpressionStatement", "src": "2298:22:27"}]}, "documentation": {"id": 40662, "nodeType": "StructuredDocumentation", "src": "2044:109:27", "text": "@notice Removes an asset from the list of assets\n @param target The address of the asset to remove"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_removeAsset", "nameLocation": "2167:12:27", "parameters": {"id": 40665, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40664, "mutability": "mutable", "name": "target", "nameLocation": "2188:6:27", "nodeType": "VariableDeclaration", "scope": 40684, "src": "2180:14:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40663, "name": "address", "nodeType": "ElementaryTypeName", "src": "2180:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2179:16:27"}, "returnParameters": {"id": 40666, "nodeType": "ParameterList", "parameters": [], "src": "2205:0:27"}, "scope": 40866, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 40702, "nodeType": "FunctionDefinition", "src": "2588:127:27", "nodes": [], "body": {"id": 40701, "nodeType": "Block", "src": "2636:79:27", "nodes": [], "statements": [{"assignments": [40691], "declarations": [{"constant": false, "id": 40691, "mutability": "mutable", "name": "target", "nameLocation": "2654:6:27", "nodeType": "VariableDeclaration", "scope": 40701, "src": "2646:14:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40690, "name": "address", "nodeType": "ElementaryTypeName", "src": "2646:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 40696, "initialValue": {"arguments": [{"id": 40694, "name": "entropy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40687, "src": "2674:7:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 40692, "name": "_assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40569, "src": "2663:7:27", "typeDescriptions": {"typeIdentifier": "t_struct$_AddressSet_$41192_storage", "typeString": "struct EnumerableSet.AddressSet storage ref"}}, "id": 40693, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2671:2:27", "memberName": "at", "nodeType": "MemberAccess", "referencedDeclaration": 41315, "src": "2663:10:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_AddressSet_$41192_storage_ptr_$_t_uint256_$returns$_t_address_$attached_to$_t_struct$_AddressSet_$41192_storage_ptr_$", "typeString": "function (struct EnumerableSet.AddressSet storage pointer,uint256) view returns (address)"}}, "id": 40695, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2663:19:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "2646:36:27"}, {"expression": {"id": 40699, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 40697, "name": "__asset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40565, "src": "2692:7:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 40698, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40691, "src": "2702:6:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2692:16:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40700, "nodeType": "ExpressionStatement", "src": "2692:16:27"}]}, "documentation": {"id": 40685, "nodeType": "StructuredDocumentation", "src": "2333:250:27", "text": "@notice Switches the current asset based on the entropy\n   NOTE: We revert if the entropy is greater than the number of actors, for Halmos compatibility\n @param entropy The entropy to choose a random asset in the array for switching"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_switchAsset", "nameLocation": "2597:12:27", "parameters": {"id": 40688, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40687, "mutability": "mutable", "name": "entropy", "nameLocation": "2618:7:27", "nodeType": "VariableDeclaration", "scope": 40702, "src": "2610:15:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40686, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2610:7:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2609:17:27"}, "returnParameters": {"id": 40689, "nodeType": "ParameterList", "parameters": [], "src": "2636:0:27"}, "scope": 40866, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 40739, "nodeType": "FunctionDefinition", "src": "3042:338:27", "nodes": [], "body": {"id": 40738, "nodeType": "Block", "src": "3175:205:27", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 40715, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40706, "src": "3207:11:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, {"id": 40716, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40711, "src": "3220:6:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 40714, "name": "_mintAssetToAllActors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40801, "src": "3185:21:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_array$_t_address_$dyn_memory_ptr_$_t_uint256_$returns$__$", "typeString": "function (address[] memory,uint256)"}}, "id": 40717, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3185:42:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40718, "nodeType": "ExpressionStatement", "src": "3185:42:27"}, {"body": {"id": 40736, "nodeType": "Block", "src": "3284:90:27", "statements": [{"expression": {"arguments": [{"id": 40730, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40706, "src": "3333:11:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, {"baseExpression": {"id": 40731, "name": "approvalArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40709, "src": "3346:13:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40733, "indexExpression": {"id": 40732, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40720, "src": "3360:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3346:16:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 40729, "name": "_approveAssetToAddressForAllActors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40865, "src": "3298:34:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_array$_t_address_$dyn_memory_ptr_$_t_address_$returns$__$", "typeString": "function (address[] memory,address)"}}, "id": 40734, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3298:65:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40735, "nodeType": "ExpressionStatement", "src": "3298:65:27"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40725, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40722, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40720, "src": "3253:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 40723, "name": "approvalArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40709, "src": "3257:13:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40724, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3271:6:27", "memberName": "length", "nodeType": "MemberAccess", "src": "3257:20:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3253:24:27", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40737, "initializationExpression": {"assignments": [40720], "declarations": [{"constant": false, "id": 40720, "mutability": "mutable", "name": "i", "nameLocation": "3250:1:27", "nodeType": "VariableDeclaration", "scope": 40737, "src": "3242:9:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40719, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3242:7:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40721, "nodeType": "VariableDeclarationStatement", "src": "3242:9:27"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 40727, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "3279:3:27", "subExpression": {"id": 40726, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40720, "src": "3279:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 40728, "nodeType": "ExpressionStatement", "src": "3279:3:27"}, "nodeType": "ForStatement", "src": "3237:137:27"}]}, "documentation": {"id": 40703, "nodeType": "StructuredDocumentation", "src": "2763:274:27", "text": "@notice Mint initial balance and approve allowances for the active asset\n @param actorsArray The array of actors to mint the asset to\n @param approvalArray The array of addresses to approve the asset to\n @param amount The amount of the asset to mint"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_finalizeAssetDeployment", "nameLocation": "3051:24:27", "parameters": {"id": 40712, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40706, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "3093:11:27", "nodeType": "VariableDeclaration", "scope": 40739, "src": "3076:28:27", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 40704, "name": "address", "nodeType": "ElementaryTypeName", "src": "3076:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40705, "nodeType": "ArrayTypeName", "src": "3076:9:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}, {"constant": false, "id": 40709, "mutability": "mutable", "name": "approvalArray", "nameLocation": "3123:13:27", "nodeType": "VariableDeclaration", "scope": 40739, "src": "3106:30:27", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 40707, "name": "address", "nodeType": "ElementaryTypeName", "src": "3106:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40708, "nodeType": "ArrayTypeName", "src": "3106:9:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}, {"constant": false, "id": 40711, "mutability": "mutable", "name": "amount", "nameLocation": "3146:6:27", "nodeType": "VariableDeclaration", "scope": 40739, "src": "3138:14:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40710, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3138:7:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3075:78:27"}, "returnParameters": {"id": 40713, "nodeType": "ParameterList", "parameters": [], "src": "3175:0:27"}, "scope": 40866, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 40801, "nodeType": "FunctionDefinition", "src": "3553:409:27", "nodes": [], "body": {"id": 40800, "nodeType": "Block", "src": "3638:324:27", "nodes": [], "statements": [{"assignments": [40752], "declarations": [{"constant": false, "id": 40752, "mutability": "mutable", "name": "assets", "nameLocation": "3692:6:27", "nodeType": "VariableDeclaration", "scope": 40800, "src": "3675:23:27", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 40750, "name": "address", "nodeType": "ElementaryTypeName", "src": "3675:7:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40751, "nodeType": "ArrayTypeName", "src": "3675:9:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "id": 40755, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 40753, "name": "_getAssets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40607, "src": "3701:10:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_array$_t_address_$dyn_memory_ptr_$", "typeString": "function () view returns (address[] memory)"}}, "id": 40754, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3701:12:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "3675:38:27"}, {"body": {"id": 40798, "nodeType": "Block", "src": "3763:193:27", "statements": [{"body": {"id": 40796, "nodeType": "Block", "src": "3822:124:27", "statements": [{"expression": {"arguments": [{"baseExpression": {"id": 40779, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40743, "src": "3849:11:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40781, "indexExpression": {"id": 40780, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40767, "src": "3861:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3849:14:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40776, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "3840:2:27", "typeDescriptions": {"typeIdentifier": "t_contract$_IHevm_$713", "typeString": "contract IHevm"}}, "id": 40778, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3843:5:27", "memberName": "prank", "nodeType": "MemberAccess", "referencedDeclaration": 672, "src": "3840:8:27", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$returns$__$", "typeString": "function (address) external"}}, "id": 40782, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3840:24:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40783, "nodeType": "ExpressionStatement", "src": "3840:24:27"}, {"expression": {"arguments": [{"baseExpression": {"id": 40790, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40743, "src": "3908:11:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40792, "indexExpression": {"id": 40791, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40767, "src": "3920:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3908:14:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 40793, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40745, "src": "3924:6:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"baseExpression": {"id": 40785, "name": "assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40752, "src": "3892:6:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40787, "indexExpression": {"id": 40786, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40757, "src": "3899:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3892:9:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 40784, "name": "MockERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42022, "src": "3882:9:27", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_MockERC20_$42022_$", "typeString": "type(contract MockERC20)"}}, "id": 40788, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3882:20:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_MockERC20_$42022", "typeString": "contract MockERC20"}}, "id": 40789, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3903:4:27", "memberName": "mint", "nodeType": "MemberAccess", "referencedDeclaration": 42008, "src": "3882:25:27", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256) external"}}, "id": 40794, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3882:49:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40795, "nodeType": "ExpressionStatement", "src": "3882:49:27"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40772, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40769, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40767, "src": "3793:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 40770, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40743, "src": "3797:11:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40771, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3809:6:27", "memberName": "length", "nodeType": "MemberAccess", "src": "3797:18:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3793:22:27", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40797, "initializationExpression": {"assignments": [40767], "declarations": [{"constant": false, "id": 40767, "mutability": "mutable", "name": "j", "nameLocation": "3790:1:27", "nodeType": "VariableDeclaration", "scope": 40797, "src": "3782:9:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40766, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3782:7:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40768, "nodeType": "VariableDeclarationStatement", "src": "3782:9:27"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 40774, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "3817:3:27", "subExpression": {"id": 40773, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40767, "src": "3817:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 40775, "nodeType": "ExpressionStatement", "src": "3817:3:27"}, "nodeType": "ForStatement", "src": "3777:169:27"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40762, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40759, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40757, "src": "3739:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 40760, "name": "assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40752, "src": "3743:6:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40761, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3750:6:27", "memberName": "length", "nodeType": "MemberAccess", "src": "3743:13:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3739:17:27", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40799, "initializationExpression": {"assignments": [40757], "declarations": [{"constant": false, "id": 40757, "mutability": "mutable", "name": "i", "nameLocation": "3736:1:27", "nodeType": "VariableDeclaration", "scope": 40799, "src": "3728:9:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40756, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3728:7:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40758, "nodeType": "VariableDeclarationStatement", "src": "3728:9:27"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 40764, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "3758:3:27", "subExpression": {"id": 40763, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40757, "src": "3758:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 40765, "nodeType": "ExpressionStatement", "src": "3758:3:27"}, "nodeType": "ForStatement", "src": "3723:233:27"}]}, "documentation": {"id": 40740, "nodeType": "StructuredDocumentation", "src": "3386:162:27", "text": "@notice Mint the asset to all actors\n @param actorsArray The array of actors to mint the asset to\n @param amount The amount of the asset to mint"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_mintAssetToAllActors", "nameLocation": "3562:21:27", "parameters": {"id": 40746, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40743, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "3601:11:27", "nodeType": "VariableDeclaration", "scope": 40801, "src": "3584:28:27", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 40741, "name": "address", "nodeType": "ElementaryTypeName", "src": "3584:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40742, "nodeType": "ArrayTypeName", "src": "3584:9:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}, {"constant": false, "id": 40745, "mutability": "mutable", "name": "amount", "nameLocation": "3622:6:27", "nodeType": "VariableDeclaration", "scope": 40801, "src": "3614:14:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40744, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3614:7:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3583:46:27"}, "returnParameters": {"id": 40747, "nodeType": "ParameterList", "parameters": [], "src": "3638:0:27"}, "scope": 40866, "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"id": 40865, "nodeType": "FunctionDefinition", "src": "4157:454:27", "nodes": [], "body": {"id": 40864, "nodeType": "Block", "src": "4265:346:27", "nodes": [], "statements": [{"assignments": [40814], "declarations": [{"constant": false, "id": 40814, "mutability": "mutable", "name": "assets", "nameLocation": "4325:6:27", "nodeType": "VariableDeclaration", "scope": 40864, "src": "4308:23:27", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 40812, "name": "address", "nodeType": "ElementaryTypeName", "src": "4308:7:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40813, "nodeType": "ArrayTypeName", "src": "4308:9:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "id": 40817, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 40815, "name": "_getAssets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40607, "src": "4334:10:27", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_array$_t_address_$dyn_memory_ptr_$", "typeString": "function () view returns (address[] memory)"}}, "id": 40816, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4334:12:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "4308:38:27"}, {"body": {"id": 40862, "nodeType": "Block", "src": "4396:209:27", "statements": [{"body": {"id": 40860, "nodeType": "Block", "src": "4455:140:27", "statements": [{"expression": {"arguments": [{"baseExpression": {"id": 40841, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40805, "src": "4482:11:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40843, "indexExpression": {"id": 40842, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40829, "src": "4494:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4482:14:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 40838, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "4473:2:27", "typeDescriptions": {"typeIdentifier": "t_contract$_IHevm_$713", "typeString": "contract IHevm"}}, "id": 40840, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4476:5:27", "memberName": "prank", "nodeType": "MemberAccess", "referencedDeclaration": 672, "src": "4473:8:27", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$returns$__$", "typeString": "function (address) external"}}, "id": 40844, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4473:24:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40845, "nodeType": "ExpressionStatement", "src": "4473:24:27"}, {"expression": {"arguments": [{"id": 40852, "name": "addressToApprove", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40807, "src": "4544:16:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"arguments": [{"id": 40855, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4567:7:27", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 40854, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4567:7:27", "typeDescriptions": {}}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}], "id": 40853, "name": "type", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -27, "src": "4562:4:27", "typeDescriptions": {"typeIdentifier": "t_function_metatype_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 40856, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4562:13:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_magic_meta_type_t_uint256", "typeString": "type(uint256)"}}, "id": 40857, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4576:3:27", "memberName": "max", "nodeType": "MemberAccess", "src": "4562:17:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"baseExpression": {"id": 40847, "name": "assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40814, "src": "4525:6:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40849, "indexExpression": {"id": 40848, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40819, "src": "4532:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4525:9:27", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 40846, "name": "MockERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42022, "src": "4515:9:27", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_MockERC20_$42022_$", "typeString": "type(contract MockERC20)"}}, "id": 40850, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4515:20:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_MockERC20_$42022", "typeString": "contract MockERC20"}}, "id": 40851, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4536:7:27", "memberName": "approve", "nodeType": "MemberAccess", "referencedDeclaration": 41611, "src": "4515:28:27", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,uint256) external returns (bool)"}}, "id": 40858, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4515:65:27", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40859, "nodeType": "ExpressionStatement", "src": "4515:65:27"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40834, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40831, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40829, "src": "4426:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 40832, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40805, "src": "4430:11:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40833, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4442:6:27", "memberName": "length", "nodeType": "MemberAccess", "src": "4430:18:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4426:22:27", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40861, "initializationExpression": {"assignments": [40829], "declarations": [{"constant": false, "id": 40829, "mutability": "mutable", "name": "j", "nameLocation": "4423:1:27", "nodeType": "VariableDeclaration", "scope": 40861, "src": "4415:9:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40828, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4415:7:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40830, "nodeType": "VariableDeclarationStatement", "src": "4415:9:27"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 40836, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "4450:3:27", "subExpression": {"id": 40835, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40829, "src": "4450:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 40837, "nodeType": "ExpressionStatement", "src": "4450:3:27"}, "nodeType": "ForStatement", "src": "4410:185:27"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40824, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40821, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40819, "src": "4372:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 40822, "name": "assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40814, "src": "4376:6:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 40823, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4383:6:27", "memberName": "length", "nodeType": "MemberAccess", "src": "4376:13:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4372:17:27", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 40863, "initializationExpression": {"assignments": [40819], "declarations": [{"constant": false, "id": 40819, "mutability": "mutable", "name": "i", "nameLocation": "4369:1:27", "nodeType": "VariableDeclaration", "scope": 40863, "src": "4361:9:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 40818, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4361:7:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 40820, "nodeType": "VariableDeclarationStatement", "src": "4361:9:27"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 40826, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "4391:3:27", "subExpression": {"id": 40825, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40819, "src": "4391:1:27", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 40827, "nodeType": "ExpressionStatement", "src": "4391:3:27"}, "nodeType": "ForStatement", "src": "4356:249:27"}]}, "documentation": {"id": 40802, "nodeType": "StructuredDocumentation", "src": "3968:184:27", "text": "@notice Approve the asset to all actors\n @param actorsArray The array of actors to approve the asset from\n @param addressToApprove The address to approve the asset to"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_approveAssetToAddressForAllActors", "nameLocation": "4166:34:27", "parameters": {"id": 40808, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 40805, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "4218:11:27", "nodeType": "VariableDeclaration", "scope": 40865, "src": "4201:28:27", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 40803, "name": "address", "nodeType": "ElementaryTypeName", "src": "4201:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 40804, "nodeType": "ArrayTypeName", "src": "4201:9:27", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}, {"constant": false, "id": 40807, "mutability": "mutable", "name": "addressToApprove", "nameLocation": "4239:16:27", "nodeType": "VariableDeclaration", "scope": 40865, "src": "4231:24:27", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40806, "name": "address", "nodeType": "ElementaryTypeName", "src": "4231:7:27", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "4200:56:27"}, "returnParameters": {"id": 40809, "nodeType": "ParameterList", "parameters": [], "src": "4265:0:27"}, "scope": 40866, "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}], "abstract": true, "baseContracts": [], "canonicalName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 40558, "nodeType": "StructuredDocumentation", "src": "245:144:27", "text": "@dev Source of truth for the assets being used in the test\n @notice No assets should be used in the suite without being added here first"}, "fullyImplemented": true, "linearizedBaseContracts": [40866], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocation": "407:12:27", "scope": 40867, "usedErrors": [40571, 40573, 40575], "usedEvents": []}], "license": "GPL-2.0"}, "id": 27}