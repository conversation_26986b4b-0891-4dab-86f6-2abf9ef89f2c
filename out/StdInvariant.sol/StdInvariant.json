{"abi": [{"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdInvariant.sol\":\"StdInvariant\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdInvariant.sol": "StdInvariant"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 6937, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "_excludedContracts", "offset": 0, "slot": "0", "type": "t_array(t_address)dyn_storage"}, {"astId": 6940, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "_excludedSenders", "offset": 0, "slot": "1", "type": "t_array(t_address)dyn_storage"}, {"astId": 6943, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "_targetedContracts", "offset": 0, "slot": "2", "type": "t_array(t_address)dyn_storage"}, {"astId": 6946, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "_targetedSenders", "offset": 0, "slot": "3", "type": "t_array(t_address)dyn_storage"}, {"astId": 6949, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "_excludedArtifacts", "offset": 0, "slot": "4", "type": "t_array(t_string_storage)dyn_storage"}, {"astId": 6952, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "_targetedArtifacts", "offset": 0, "slot": "5", "type": "t_array(t_string_storage)dyn_storage"}, {"astId": 6956, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "_targetedArtifactSelectors", "offset": 0, "slot": "6", "type": "t_array(t_struct(FuzzArtifactSelector)6928_storage)dyn_storage"}, {"astId": 6960, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "_excludedSelectors", "offset": 0, "slot": "7", "type": "t_array(t_struct(FuzzSelector)6922_storage)dyn_storage"}, {"astId": 6964, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "_targetedSelectors", "offset": 0, "slot": "8", "type": "t_array(t_struct(FuzzSelector)6922_storage)dyn_storage"}, {"astId": 6968, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "_targetedInterfaces", "offset": 0, "slot": "9", "type": "t_array(t_struct(FuzzInterface)6934_storage)dyn_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"encoding": "dynamic_array", "label": "address[]", "numberOfBytes": "32", "base": "t_address"}, "t_array(t_bytes4)dyn_storage": {"encoding": "dynamic_array", "label": "bytes4[]", "numberOfBytes": "32", "base": "t_bytes4"}, "t_array(t_string_storage)dyn_storage": {"encoding": "dynamic_array", "label": "string[]", "numberOfBytes": "32", "base": "t_string_storage"}, "t_array(t_struct(FuzzArtifactSelector)6928_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzArtifactSelector[]", "numberOfBytes": "32", "base": "t_struct(FuzzArtifactSelector)6928_storage"}, "t_array(t_struct(FuzzInterface)6934_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzInterface[]", "numberOfBytes": "32", "base": "t_struct(FuzzInterface)6934_storage"}, "t_array(t_struct(FuzzSelector)6922_storage)dyn_storage": {"encoding": "dynamic_array", "label": "struct StdInvariant.FuzzSelector[]", "numberOfBytes": "32", "base": "t_struct(FuzzSelector)6922_storage"}, "t_bytes4": {"encoding": "inplace", "label": "bytes4", "numberOfBytes": "4"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(FuzzArtifactSelector)6928_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzArtifactSelector", "numberOfBytes": "64", "members": [{"astId": 6924, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "artifact", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 6927, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "selectors", "offset": 0, "slot": "1", "type": "t_array(t_bytes4)dyn_storage"}]}, "t_struct(FuzzInterface)6934_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzInterface", "numberOfBytes": "64", "members": [{"astId": 6930, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "addr", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 6933, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "artifacts", "offset": 0, "slot": "1", "type": "t_array(t_string_storage)dyn_storage"}]}, "t_struct(FuzzSelector)6922_storage": {"encoding": "inplace", "label": "struct StdInvariant.FuzzSelector", "numberOfBytes": "64", "members": [{"astId": 6918, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "addr", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 6921, "contract": "lib/forge-std/src/StdInvariant.sol:StdInvariant", "label": "selectors", "offset": 0, "slot": "1", "type": "t_array(t_bytes4)dyn_storage"}]}}}, "ast": {"absolutePath": "lib/forge-std/src/StdInvariant.sol", "id": 7208, "exportedSymbols": {"StdInvariant": [7207]}, "nodeType": "SourceUnit", "src": "32:3945:7", "nodes": [{"id": 6915, "nodeType": "PragmaDirective", "src": "32:31:7", "nodes": [], "literals": ["solidity", ">=", "0.6", ".2", "<", "0.9", ".0"]}, {"id": 6916, "nodeType": "PragmaDirective", "src": "65:33:7", "nodes": [], "literals": ["experimental", "ABIEncoderV2"]}, {"id": 7207, "nodeType": "ContractDefinition", "src": "100:3876:7", "nodes": [{"id": 6922, "nodeType": "StructDefinition", "src": "137:77:7", "nodes": [], "canonicalName": "StdInvariant.FuzzSelector", "members": [{"constant": false, "id": 6918, "mutability": "mutable", "name": "addr", "nameLocation": "175:4:7", "nodeType": "VariableDeclaration", "scope": 6922, "src": "167:12:7", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 6917, "name": "address", "nodeType": "ElementaryTypeName", "src": "167:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 6921, "mutability": "mutable", "name": "selectors", "nameLocation": "198:9:7", "nodeType": "VariableDeclaration", "scope": 6922, "src": "189:18:7", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes4_$dyn_storage_ptr", "typeString": "bytes4[]"}, "typeName": {"baseType": {"id": 6919, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "189:6:7", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "id": 6920, "nodeType": "ArrayTypeName", "src": "189:8:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes4_$dyn_storage_ptr", "typeString": "bytes4[]"}}, "visibility": "internal"}], "name": "FuzzSelector", "nameLocation": "144:12:7", "scope": 7207, "visibility": "public"}, {"id": 6928, "nodeType": "StructDefinition", "src": "220:88:7", "nodes": [], "canonicalName": "StdInvariant.FuzzArtifactSelector", "members": [{"constant": false, "id": 6924, "mutability": "mutable", "name": "artifact", "nameLocation": "265:8:7", "nodeType": "VariableDeclaration", "scope": 6928, "src": "258:15:7", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 6923, "name": "string", "nodeType": "ElementaryTypeName", "src": "258:6:7", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 6927, "mutability": "mutable", "name": "selectors", "nameLocation": "292:9:7", "nodeType": "VariableDeclaration", "scope": 6928, "src": "283:18:7", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes4_$dyn_storage_ptr", "typeString": "bytes4[]"}, "typeName": {"baseType": {"id": 6925, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "283:6:7", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "id": 6926, "nodeType": "ArrayTypeName", "src": "283:8:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes4_$dyn_storage_ptr", "typeString": "bytes4[]"}}, "visibility": "internal"}], "name": "FuzzArtifactSelector", "nameLocation": "227:20:7", "scope": 7207, "visibility": "public"}, {"id": 6934, "nodeType": "StructDefinition", "src": "314:78:7", "nodes": [], "canonicalName": "StdInvariant.FuzzInterface", "members": [{"constant": false, "id": 6930, "mutability": "mutable", "name": "addr", "nameLocation": "353:4:7", "nodeType": "VariableDeclaration", "scope": 6934, "src": "345:12:7", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 6929, "name": "address", "nodeType": "ElementaryTypeName", "src": "345:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 6933, "mutability": "mutable", "name": "artifacts", "nameLocation": "376:9:7", "nodeType": "VariableDeclaration", "scope": 6934, "src": "367:18:7", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 6931, "name": "string", "nodeType": "ElementaryTypeName", "src": "367:6:7", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 6932, "nodeType": "ArrayTypeName", "src": "367:8:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "name": "FuzzInterface", "nameLocation": "321:13:7", "scope": 7207, "visibility": "public"}, {"id": 6937, "nodeType": "VariableDeclaration", "src": "398:36:7", "nodes": [], "constant": false, "mutability": "mutable", "name": "_excludedContracts", "nameLocation": "416:18:7", "scope": 7207, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[]"}, "typeName": {"baseType": {"id": 6935, "name": "address", "nodeType": "ElementaryTypeName", "src": "398:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 6936, "nodeType": "ArrayTypeName", "src": "398:9:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "private"}, {"id": 6940, "nodeType": "VariableDeclaration", "src": "440:34:7", "nodes": [], "constant": false, "mutability": "mutable", "name": "_excludedSenders", "nameLocation": "458:16:7", "scope": 7207, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[]"}, "typeName": {"baseType": {"id": 6938, "name": "address", "nodeType": "ElementaryTypeName", "src": "440:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 6939, "nodeType": "ArrayTypeName", "src": "440:9:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "private"}, {"id": 6943, "nodeType": "VariableDeclaration", "src": "480:36:7", "nodes": [], "constant": false, "mutability": "mutable", "name": "_targetedContracts", "nameLocation": "498:18:7", "scope": 7207, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[]"}, "typeName": {"baseType": {"id": 6941, "name": "address", "nodeType": "ElementaryTypeName", "src": "480:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 6942, "nodeType": "ArrayTypeName", "src": "480:9:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "private"}, {"id": 6946, "nodeType": "VariableDeclaration", "src": "522:34:7", "nodes": [], "constant": false, "mutability": "mutable", "name": "_targetedSenders", "nameLocation": "540:16:7", "scope": 7207, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[]"}, "typeName": {"baseType": {"id": 6944, "name": "address", "nodeType": "ElementaryTypeName", "src": "522:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 6945, "nodeType": "ArrayTypeName", "src": "522:9:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "private"}, {"id": 6949, "nodeType": "VariableDeclaration", "src": "563:35:7", "nodes": [], "constant": false, "mutability": "mutable", "name": "_excludedArtifacts", "nameLocation": "580:18:7", "scope": 7207, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage", "typeString": "string[]"}, "typeName": {"baseType": {"id": 6947, "name": "string", "nodeType": "ElementaryTypeName", "src": "563:6:7", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 6948, "nodeType": "ArrayTypeName", "src": "563:8:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "private"}, {"id": 6952, "nodeType": "VariableDeclaration", "src": "604:35:7", "nodes": [], "constant": false, "mutability": "mutable", "name": "_targetedArtifacts", "nameLocation": "621:18:7", "scope": 7207, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage", "typeString": "string[]"}, "typeName": {"baseType": {"id": 6950, "name": "string", "nodeType": "ElementaryTypeName", "src": "604:6:7", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 6951, "nodeType": "ArrayTypeName", "src": "604:8:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "private"}, {"id": 6956, "nodeType": "VariableDeclaration", "src": "646:57:7", "nodes": [], "constant": false, "mutability": "mutable", "name": "_targetedArtifactSelectors", "nameLocation": "677:26:7", "scope": 7207, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzArtifactSelector_$6928_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzArtifactSelector[]"}, "typeName": {"baseType": {"id": 6954, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 6953, "name": "FuzzArtifactSelector", "nameLocations": ["646:20:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6928, "src": "646:20:7"}, "referencedDeclaration": 6928, "src": "646:20:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzArtifactSelector_$6928_storage_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector"}}, "id": 6955, "nodeType": "ArrayTypeName", "src": "646:22:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzArtifactSelector_$6928_storage_$dyn_storage_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector[]"}}, "visibility": "private"}, {"id": 6960, "nodeType": "VariableDeclaration", "src": "710:41:7", "nodes": [], "constant": false, "mutability": "mutable", "name": "_excludedSelectors", "nameLocation": "733:18:7", "scope": 7207, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzSelector[]"}, "typeName": {"baseType": {"id": 6958, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 6957, "name": "FuzzSelector", "nameLocations": ["710:12:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6922, "src": "710:12:7"}, "referencedDeclaration": 6922, "src": "710:12:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzSelector_$6922_storage_ptr", "typeString": "struct StdInvariant.FuzzSelector"}}, "id": 6959, "nodeType": "ArrayTypeName", "src": "710:14:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage_ptr", "typeString": "struct StdInvariant.FuzzSelector[]"}}, "visibility": "private"}, {"id": 6964, "nodeType": "VariableDeclaration", "src": "757:41:7", "nodes": [], "constant": false, "mutability": "mutable", "name": "_targetedSelectors", "nameLocation": "780:18:7", "scope": 7207, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzSelector[]"}, "typeName": {"baseType": {"id": 6962, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 6961, "name": "FuzzSelector", "nameLocations": ["757:12:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6922, "src": "757:12:7"}, "referencedDeclaration": 6922, "src": "757:12:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzSelector_$6922_storage_ptr", "typeString": "struct StdInvariant.FuzzSelector"}}, "id": 6963, "nodeType": "ArrayTypeName", "src": "757:14:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage_ptr", "typeString": "struct StdInvariant.FuzzSelector[]"}}, "visibility": "private"}, {"id": 6968, "nodeType": "VariableDeclaration", "src": "805:43:7", "nodes": [], "constant": false, "mutability": "mutable", "name": "_targetedInterfaces", "nameLocation": "829:19:7", "scope": 7207, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzInterface_$6934_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzInterface[]"}, "typeName": {"baseType": {"id": 6966, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 6965, "name": "FuzzInterface", "nameLocations": ["805:13:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6934, "src": "805:13:7"}, "referencedDeclaration": 6934, "src": "805:13:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzInterface_$6934_storage_ptr", "typeString": "struct StdInvariant.FuzzInterface"}}, "id": 6967, "nodeType": "ArrayTypeName", "src": "805:15:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzInterface_$6934_storage_$dyn_storage_ptr", "typeString": "struct StdInvariant.FuzzInterface[]"}}, "visibility": "private"}, {"id": 6980, "nodeType": "FunctionDefinition", "src": "933:126:7", "nodes": [], "body": {"id": 6979, "nodeType": "Block", "src": "997:62:7", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 6976, "name": "newExcludedContract_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6970, "src": "1031:20:7", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 6973, "name": "_excludedContracts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6937, "src": "1007:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[] storage ref"}}, "id": 6975, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1026:4:7", "memberName": "push", "nodeType": "MemberAccess", "src": "1007:23:7", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_address_$dyn_storage_ptr_$_t_address_$returns$__$attached_to$_t_array$_t_address_$dyn_storage_ptr_$", "typeString": "function (address[] storage pointer,address)"}}, "id": 6977, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1007:45:7", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 6978, "nodeType": "ExpressionStatement", "src": "1007:45:7"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "excludeContract", "nameLocation": "942:15:7", "parameters": {"id": 6971, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 6970, "mutability": "mutable", "name": "newExcludedContract_", "nameLocation": "966:20:7", "nodeType": "VariableDeclaration", "scope": 6980, "src": "958:28:7", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 6969, "name": "address", "nodeType": "ElementaryTypeName", "src": "958:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "957:30:7"}, "returnParameters": {"id": 6972, "nodeType": "ParameterList", "parameters": [], "src": "997:0:7"}, "scope": 7207, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 6993, "nodeType": "FunctionDefinition", "src": "1065:138:7", "nodes": [], "body": {"id": 6992, "nodeType": "Block", "src": "1141:62:7", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 6989, "name": "newExcludedSelector_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6983, "src": "1175:20:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzSelector_$6922_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_FuzzSelector_$6922_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector memory"}], "expression": {"id": 6986, "name": "_excludedSelectors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6960, "src": "1151:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzSelector storage ref[] storage ref"}}, "id": 6988, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1170:4:7", "memberName": "push", "nodeType": "MemberAccess", "src": "1151:23:7", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage_ptr_$_t_struct$_FuzzSelector_$6922_storage_$returns$__$attached_to$_t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage_ptr_$", "typeString": "function (struct StdInvariant.FuzzSelector storage ref[] storage pointer,struct StdInvariant.FuzzSelector storage ref)"}}, "id": 6990, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1151:45:7", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 6991, "nodeType": "ExpressionStatement", "src": "1151:45:7"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "excludeSelector", "nameLocation": "1074:15:7", "parameters": {"id": 6984, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 6983, "mutability": "mutable", "name": "newExcludedSelector_", "nameLocation": "1110:20:7", "nodeType": "VariableDeclaration", "scope": 6993, "src": "1090:40:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzSelector_$6922_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector"}, "typeName": {"id": 6982, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 6981, "name": "FuzzSelector", "nameLocations": ["1090:12:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6922, "src": "1090:12:7"}, "referencedDeclaration": 6922, "src": "1090:12:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzSelector_$6922_storage_ptr", "typeString": "struct StdInvariant.FuzzSelector"}}, "visibility": "internal"}], "src": "1089:42:7"}, "returnParameters": {"id": 6985, "nodeType": "ParameterList", "parameters": [], "src": "1141:0:7"}, "scope": 7207, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7005, "nodeType": "FunctionDefinition", "src": "1209:118:7", "nodes": [], "body": {"id": 7004, "nodeType": "Block", "src": "1269:58:7", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7001, "name": "newExcludedSender_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6995, "src": "1301:18:7", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 6998, "name": "_excludedSenders", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6940, "src": "1279:16:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[] storage ref"}}, "id": 7000, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1296:4:7", "memberName": "push", "nodeType": "MemberAccess", "src": "1279:21:7", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_address_$dyn_storage_ptr_$_t_address_$returns$__$attached_to$_t_array$_t_address_$dyn_storage_ptr_$", "typeString": "function (address[] storage pointer,address)"}}, "id": 7002, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1279:41:7", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 7003, "nodeType": "ExpressionStatement", "src": "1279:41:7"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "excludeSender", "nameLocation": "1218:13:7", "parameters": {"id": 6996, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 6995, "mutability": "mutable", "name": "newExcludedSender_", "nameLocation": "1240:18:7", "nodeType": "VariableDeclaration", "scope": 7005, "src": "1232:26:7", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 6994, "name": "address", "nodeType": "ElementaryTypeName", "src": "1232:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1231:28:7"}, "returnParameters": {"id": 6997, "nodeType": "ParameterList", "parameters": [], "src": "1269:0:7"}, "scope": 7207, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7017, "nodeType": "FunctionDefinition", "src": "1333:132:7", "nodes": [], "body": {"id": 7016, "nodeType": "Block", "src": "1403:62:7", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7013, "name": "newExcludedArtifact_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7007, "src": "1437:20:7", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7010, "name": "_excludedArtifacts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6949, "src": "1413:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage", "typeString": "string storage ref[] storage ref"}}, "id": 7012, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1432:4:7", "memberName": "push", "nodeType": "MemberAccess", "src": "1413:23:7", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_string_storage_$dyn_storage_ptr_$_t_string_storage_$returns$__$attached_to$_t_array$_t_string_storage_$dyn_storage_ptr_$", "typeString": "function (string storage ref[] storage pointer,string storage ref)"}}, "id": 7014, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1413:45:7", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 7015, "nodeType": "ExpressionStatement", "src": "1413:45:7"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "excludeArtifact", "nameLocation": "1342:15:7", "parameters": {"id": 7008, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7007, "mutability": "mutable", "name": "newExcludedArtifact_", "nameLocation": "1372:20:7", "nodeType": "VariableDeclaration", "scope": 7017, "src": "1358:34:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7006, "name": "string", "nodeType": "ElementaryTypeName", "src": "1358:6:7", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1357:36:7"}, "returnParameters": {"id": 7009, "nodeType": "ParameterList", "parameters": [], "src": "1403:0:7"}, "scope": 7207, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7029, "nodeType": "FunctionDefinition", "src": "1471:131:7", "nodes": [], "body": {"id": 7028, "nodeType": "Block", "src": "1540:62:7", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7025, "name": "newTargetedArtifact_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7019, "src": "1574:20:7", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 7022, "name": "_targetedArtifacts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6952, "src": "1550:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage", "typeString": "string storage ref[] storage ref"}}, "id": 7024, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1569:4:7", "memberName": "push", "nodeType": "MemberAccess", "src": "1550:23:7", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_string_storage_$dyn_storage_ptr_$_t_string_storage_$returns$__$attached_to$_t_array$_t_string_storage_$dyn_storage_ptr_$", "typeString": "function (string storage ref[] storage pointer,string storage ref)"}}, "id": 7026, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1550:45:7", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 7027, "nodeType": "ExpressionStatement", "src": "1550:45:7"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "targetArtifact", "nameLocation": "1480:14:7", "parameters": {"id": 7020, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7019, "mutability": "mutable", "name": "newTargetedArtifact_", "nameLocation": "1509:20:7", "nodeType": "VariableDeclaration", "scope": 7029, "src": "1495:34:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7018, "name": "string", "nodeType": "ElementaryTypeName", "src": "1495:6:7", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1494:36:7"}, "returnParameters": {"id": 7021, "nodeType": "ParameterList", "parameters": [], "src": "1540:0:7"}, "scope": 7207, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7042, "nodeType": "FunctionDefinition", "src": "1608:177:7", "nodes": [], "body": {"id": 7041, "nodeType": "Block", "src": "1707:78:7", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7038, "name": "newTargetedArtifactSelector_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7032, "src": "1749:28:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzArtifactSelector_$6928_memory_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_FuzzArtifactSelector_$6928_memory_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector memory"}], "expression": {"id": 7035, "name": "_targetedArtifactSelectors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6956, "src": "1717:26:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzArtifactSelector_$6928_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzArtifactSelector storage ref[] storage ref"}}, "id": 7037, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1744:4:7", "memberName": "push", "nodeType": "MemberAccess", "src": "1717:31:7", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_struct$_FuzzArtifactSelector_$6928_storage_$dyn_storage_ptr_$_t_struct$_FuzzArtifactSelector_$6928_storage_$returns$__$attached_to$_t_array$_t_struct$_FuzzArtifactSelector_$6928_storage_$dyn_storage_ptr_$", "typeString": "function (struct StdInvariant.FuzzArtifactSelector storage ref[] storage pointer,struct StdInvariant.FuzzArtifactSelector storage ref)"}}, "id": 7039, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1717:61:7", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 7040, "nodeType": "ExpressionStatement", "src": "1717:61:7"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "targetArtifactSelector", "nameLocation": "1617:22:7", "parameters": {"id": 7033, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7032, "mutability": "mutable", "name": "newTargetedArtifactSelector_", "nameLocation": "1668:28:7", "nodeType": "VariableDeclaration", "scope": 7042, "src": "1640:56:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzArtifactSelector_$6928_memory_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector"}, "typeName": {"id": 7031, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 7030, "name": "FuzzArtifactSelector", "nameLocations": ["1640:20:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6928, "src": "1640:20:7"}, "referencedDeclaration": 6928, "src": "1640:20:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzArtifactSelector_$6928_storage_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector"}}, "visibility": "internal"}], "src": "1639:58:7"}, "returnParameters": {"id": 7034, "nodeType": "ParameterList", "parameters": [], "src": "1707:0:7"}, "scope": 7207, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7054, "nodeType": "FunctionDefinition", "src": "1791:125:7", "nodes": [], "body": {"id": 7053, "nodeType": "Block", "src": "1854:62:7", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7050, "name": "newTargetedContract_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7044, "src": "1888:20:7", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 7047, "name": "_targetedContracts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6943, "src": "1864:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[] storage ref"}}, "id": 7049, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1883:4:7", "memberName": "push", "nodeType": "MemberAccess", "src": "1864:23:7", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_address_$dyn_storage_ptr_$_t_address_$returns$__$attached_to$_t_array$_t_address_$dyn_storage_ptr_$", "typeString": "function (address[] storage pointer,address)"}}, "id": 7051, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1864:45:7", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 7052, "nodeType": "ExpressionStatement", "src": "1864:45:7"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "targetContract", "nameLocation": "1800:14:7", "parameters": {"id": 7045, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7044, "mutability": "mutable", "name": "newTargetedContract_", "nameLocation": "1823:20:7", "nodeType": "VariableDeclaration", "scope": 7054, "src": "1815:28:7", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 7043, "name": "address", "nodeType": "ElementaryTypeName", "src": "1815:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1814:30:7"}, "returnParameters": {"id": 7046, "nodeType": "ParameterList", "parameters": [], "src": "1854:0:7"}, "scope": 7207, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7067, "nodeType": "FunctionDefinition", "src": "1922:137:7", "nodes": [], "body": {"id": 7066, "nodeType": "Block", "src": "1997:62:7", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7063, "name": "newTargetedSelector_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7057, "src": "2031:20:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzSelector_$6922_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_FuzzSelector_$6922_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector memory"}], "expression": {"id": 7060, "name": "_targetedSelectors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6964, "src": "2007:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzSelector storage ref[] storage ref"}}, "id": 7062, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2026:4:7", "memberName": "push", "nodeType": "MemberAccess", "src": "2007:23:7", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage_ptr_$_t_struct$_FuzzSelector_$6922_storage_$returns$__$attached_to$_t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage_ptr_$", "typeString": "function (struct StdInvariant.FuzzSelector storage ref[] storage pointer,struct StdInvariant.FuzzSelector storage ref)"}}, "id": 7064, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2007:45:7", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 7065, "nodeType": "ExpressionStatement", "src": "2007:45:7"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "targetSelector", "nameLocation": "1931:14:7", "parameters": {"id": 7058, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7057, "mutability": "mutable", "name": "newTargetedSelector_", "nameLocation": "1966:20:7", "nodeType": "VariableDeclaration", "scope": 7067, "src": "1946:40:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzSelector_$6922_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector"}, "typeName": {"id": 7056, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 7055, "name": "FuzzSelector", "nameLocations": ["1946:12:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6922, "src": "1946:12:7"}, "referencedDeclaration": 6922, "src": "1946:12:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzSelector_$6922_storage_ptr", "typeString": "struct StdInvariant.FuzzSelector"}}, "visibility": "internal"}], "src": "1945:42:7"}, "returnParameters": {"id": 7059, "nodeType": "ParameterList", "parameters": [], "src": "1997:0:7"}, "scope": 7207, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7079, "nodeType": "FunctionDefinition", "src": "2065:117:7", "nodes": [], "body": {"id": 7078, "nodeType": "Block", "src": "2124:58:7", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7075, "name": "newTargetedSender_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7069, "src": "2156:18:7", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 7072, "name": "_targetedSenders", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6946, "src": "2134:16:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[] storage ref"}}, "id": 7074, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2151:4:7", "memberName": "push", "nodeType": "MemberAccess", "src": "2134:21:7", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_address_$dyn_storage_ptr_$_t_address_$returns$__$attached_to$_t_array$_t_address_$dyn_storage_ptr_$", "typeString": "function (address[] storage pointer,address)"}}, "id": 7076, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2134:41:7", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 7077, "nodeType": "ExpressionStatement", "src": "2134:41:7"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "targetSender", "nameLocation": "2074:12:7", "parameters": {"id": 7070, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7069, "mutability": "mutable", "name": "newTargetedSender_", "nameLocation": "2095:18:7", "nodeType": "VariableDeclaration", "scope": 7079, "src": "2087:26:7", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 7068, "name": "address", "nodeType": "ElementaryTypeName", "src": "2087:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2086:28:7"}, "returnParameters": {"id": 7071, "nodeType": "ParameterList", "parameters": [], "src": "2124:0:7"}, "scope": 7207, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7092, "nodeType": "FunctionDefinition", "src": "2188:142:7", "nodes": [], "body": {"id": 7091, "nodeType": "Block", "src": "2266:64:7", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 7088, "name": "newTargetedInterface_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7082, "src": "2301:21:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzInterface_$6934_memory_ptr", "typeString": "struct StdInvariant.FuzzInterface memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_FuzzInterface_$6934_memory_ptr", "typeString": "struct StdInvariant.FuzzInterface memory"}], "expression": {"id": 7085, "name": "_targetedInterfaces", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6968, "src": "2276:19:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzInterface_$6934_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzInterface storage ref[] storage ref"}}, "id": 7087, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2296:4:7", "memberName": "push", "nodeType": "MemberAccess", "src": "2276:24:7", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_struct$_FuzzInterface_$6934_storage_$dyn_storage_ptr_$_t_struct$_FuzzInterface_$6934_storage_$returns$__$attached_to$_t_array$_t_struct$_FuzzInterface_$6934_storage_$dyn_storage_ptr_$", "typeString": "function (struct StdInvariant.FuzzInterface storage ref[] storage pointer,struct StdInvariant.FuzzInterface storage ref)"}}, "id": 7089, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2276:47:7", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 7090, "nodeType": "ExpressionStatement", "src": "2276:47:7"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "targetInterface", "nameLocation": "2197:15:7", "parameters": {"id": 7083, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7082, "mutability": "mutable", "name": "newTargetedInterface_", "nameLocation": "2234:21:7", "nodeType": "VariableDeclaration", "scope": 7092, "src": "2213:42:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzInterface_$6934_memory_ptr", "typeString": "struct StdInvariant.FuzzInterface"}, "typeName": {"id": 7081, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 7080, "name": "FuzzInterface", "nameLocations": ["2213:13:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6934, "src": "2213:13:7"}, "referencedDeclaration": 6934, "src": "2213:13:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzInterface_$6934_storage_ptr", "typeString": "struct StdInvariant.FuzzInterface"}}, "visibility": "internal"}], "src": "2212:44:7"}, "returnParameters": {"id": 7084, "nodeType": "ParameterList", "parameters": [], "src": "2266:0:7"}, "scope": 7207, "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"id": 7103, "nodeType": "FunctionDefinition", "src": "2459:141:7", "nodes": [], "body": {"id": 7102, "nodeType": "Block", "src": "2544:56:7", "nodes": [], "statements": [{"expression": {"id": 7100, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 7098, "name": "excludedArtifacts_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7096, "src": "2554:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 7099, "name": "_excludedArtifacts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6949, "src": "2575:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage", "typeString": "string storage ref[] storage ref"}}, "src": "2554:39:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 7101, "nodeType": "ExpressionStatement", "src": "2554:39:7"}]}, "functionSelector": "b5508aa9", "implemented": true, "kind": "function", "modifiers": [], "name": "excludeArtifacts", "nameLocation": "2468:16:7", "parameters": {"id": 7093, "nodeType": "ParameterList", "parameters": [], "src": "2484:2:7"}, "returnParameters": {"id": 7097, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7096, "mutability": "mutable", "name": "excludedArtifacts_", "nameLocation": "2524:18:7", "nodeType": "VariableDeclaration", "scope": 7103, "src": "2508:34:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 7094, "name": "string", "nodeType": "ElementaryTypeName", "src": "2508:6:7", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 7095, "nodeType": "ArrayTypeName", "src": "2508:8:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "2507:36:7"}, "scope": 7207, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 7114, "nodeType": "FunctionDefinition", "src": "2606:142:7", "nodes": [], "body": {"id": 7113, "nodeType": "Block", "src": "2692:56:7", "nodes": [], "statements": [{"expression": {"id": 7111, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 7109, "name": "excludedContracts_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7107, "src": "2702:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 7110, "name": "_excludedContracts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6937, "src": "2723:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[] storage ref"}}, "src": "2702:39:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 7112, "nodeType": "ExpressionStatement", "src": "2702:39:7"}]}, "functionSelector": "e20c9f71", "implemented": true, "kind": "function", "modifiers": [], "name": "excludeContracts", "nameLocation": "2615:16:7", "parameters": {"id": 7104, "nodeType": "ParameterList", "parameters": [], "src": "2631:2:7"}, "returnParameters": {"id": 7108, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7107, "mutability": "mutable", "name": "excludedContracts_", "nameLocation": "2672:18:7", "nodeType": "VariableDeclaration", "scope": 7114, "src": "2655:35:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 7105, "name": "address", "nodeType": "ElementaryTypeName", "src": "2655:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 7106, "nodeType": "ArrayTypeName", "src": "2655:9:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "2654:37:7"}, "scope": 7207, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 7126, "nodeType": "FunctionDefinition", "src": "2754:147:7", "nodes": [], "body": {"id": 7125, "nodeType": "Block", "src": "2845:56:7", "nodes": [], "statements": [{"expression": {"id": 7123, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 7121, "name": "excludedSelectors_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7119, "src": "2855:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector memory[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 7122, "name": "_excludedSelectors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6960, "src": "2876:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzSelector storage ref[] storage ref"}}, "src": "2855:39:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector memory[] memory"}}, "id": 7124, "nodeType": "ExpressionStatement", "src": "2855:39:7"}]}, "functionSelector": "b0464fdc", "implemented": true, "kind": "function", "modifiers": [], "name": "excludeSelectors", "nameLocation": "2763:16:7", "parameters": {"id": 7115, "nodeType": "ParameterList", "parameters": [], "src": "2779:2:7"}, "returnParameters": {"id": 7120, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7119, "mutability": "mutable", "name": "excludedSelectors_", "nameLocation": "2825:18:7", "nodeType": "VariableDeclaration", "scope": 7126, "src": "2803:40:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector[]"}, "typeName": {"baseType": {"id": 7117, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 7116, "name": "FuzzSelector", "nameLocations": ["2803:12:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6922, "src": "2803:12:7"}, "referencedDeclaration": 6922, "src": "2803:12:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzSelector_$6922_storage_ptr", "typeString": "struct StdInvariant.FuzzSelector"}}, "id": 7118, "nodeType": "ArrayTypeName", "src": "2803:14:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage_ptr", "typeString": "struct StdInvariant.FuzzSelector[]"}}, "visibility": "internal"}], "src": "2802:42:7"}, "scope": 7207, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 7137, "nodeType": "FunctionDefinition", "src": "2907:134:7", "nodes": [], "body": {"id": 7136, "nodeType": "Block", "src": "2989:52:7", "nodes": [], "statements": [{"expression": {"id": 7134, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 7132, "name": "excludedSenders_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7130, "src": "2999:16:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 7133, "name": "_excludedSenders", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6940, "src": "3018:16:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[] storage ref"}}, "src": "2999:35:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 7135, "nodeType": "ExpressionStatement", "src": "2999:35:7"}]}, "functionSelector": "1ed7831c", "implemented": true, "kind": "function", "modifiers": [], "name": "excludeSenders", "nameLocation": "2916:14:7", "parameters": {"id": 7127, "nodeType": "ParameterList", "parameters": [], "src": "2930:2:7"}, "returnParameters": {"id": 7131, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7130, "mutability": "mutable", "name": "excludedSenders_", "nameLocation": "2971:16:7", "nodeType": "VariableDeclaration", "scope": 7137, "src": "2954:33:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 7128, "name": "address", "nodeType": "ElementaryTypeName", "src": "2954:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 7129, "nodeType": "ArrayTypeName", "src": "2954:9:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "2953:35:7"}, "scope": 7207, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 7148, "nodeType": "FunctionDefinition", "src": "3047:140:7", "nodes": [], "body": {"id": 7147, "nodeType": "Block", "src": "3131:56:7", "nodes": [], "statements": [{"expression": {"id": 7145, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 7143, "name": "targetedArtifacts_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7141, "src": "3141:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 7144, "name": "_targetedArtifacts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6952, "src": "3162:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage", "typeString": "string storage ref[] storage ref"}}, "src": "3141:39:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 7146, "nodeType": "ExpressionStatement", "src": "3141:39:7"}]}, "functionSelector": "85226c81", "implemented": true, "kind": "function", "modifiers": [], "name": "targetArtifacts", "nameLocation": "3056:15:7", "parameters": {"id": 7138, "nodeType": "ParameterList", "parameters": [], "src": "3071:2:7"}, "returnParameters": {"id": 7142, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7141, "mutability": "mutable", "name": "targetedArtifacts_", "nameLocation": "3111:18:7", "nodeType": "VariableDeclaration", "scope": 7148, "src": "3095:34:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 7139, "name": "string", "nodeType": "ElementaryTypeName", "src": "3095:6:7", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 7140, "nodeType": "ArrayTypeName", "src": "3095:8:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}], "src": "3094:36:7"}, "scope": 7207, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 7160, "nodeType": "FunctionDefinition", "src": "3193:186:7", "nodes": [], "body": {"id": 7159, "nodeType": "Block", "src": "3307:72:7", "nodes": [], "statements": [{"expression": {"id": 7157, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 7155, "name": "targetedArtifactSelectors_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7153, "src": "3317:26:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzArtifactSelector_$6928_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector memory[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 7156, "name": "_targetedArtifactSelectors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6956, "src": "3346:26:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzArtifactSelector_$6928_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzArtifactSelector storage ref[] storage ref"}}, "src": "3317:55:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzArtifactSelector_$6928_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector memory[] memory"}}, "id": 7158, "nodeType": "ExpressionStatement", "src": "3317:55:7"}]}, "functionSelector": "66d9a9a0", "implemented": true, "kind": "function", "modifiers": [], "name": "targetArtifactSelectors", "nameLocation": "3202:23:7", "parameters": {"id": 7149, "nodeType": "ParameterList", "parameters": [], "src": "3225:2:7"}, "returnParameters": {"id": 7154, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7153, "mutability": "mutable", "name": "targetedArtifactSelectors_", "nameLocation": "3279:26:7", "nodeType": "VariableDeclaration", "scope": 7160, "src": "3249:56:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzArtifactSelector_$6928_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector[]"}, "typeName": {"baseType": {"id": 7151, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 7150, "name": "FuzzArtifactSelector", "nameLocations": ["3249:20:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6928, "src": "3249:20:7"}, "referencedDeclaration": 6928, "src": "3249:20:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzArtifactSelector_$6928_storage_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector"}}, "id": 7152, "nodeType": "ArrayTypeName", "src": "3249:22:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzArtifactSelector_$6928_storage_$dyn_storage_ptr", "typeString": "struct StdInvariant.FuzzArtifactSelector[]"}}, "visibility": "internal"}], "src": "3248:58:7"}, "scope": 7207, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 7171, "nodeType": "FunctionDefinition", "src": "3385:141:7", "nodes": [], "body": {"id": 7170, "nodeType": "Block", "src": "3470:56:7", "nodes": [], "statements": [{"expression": {"id": 7168, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 7166, "name": "targetedContracts_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7164, "src": "3480:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 7167, "name": "_targetedContracts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6943, "src": "3501:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[] storage ref"}}, "src": "3480:39:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 7169, "nodeType": "ExpressionStatement", "src": "3480:39:7"}]}, "functionSelector": "3f7286f4", "implemented": true, "kind": "function", "modifiers": [], "name": "targetContracts", "nameLocation": "3394:15:7", "parameters": {"id": 7161, "nodeType": "ParameterList", "parameters": [], "src": "3409:2:7"}, "returnParameters": {"id": 7165, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7164, "mutability": "mutable", "name": "targetedContracts_", "nameLocation": "3450:18:7", "nodeType": "VariableDeclaration", "scope": 7171, "src": "3433:35:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 7162, "name": "address", "nodeType": "ElementaryTypeName", "src": "3433:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 7163, "nodeType": "ArrayTypeName", "src": "3433:9:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "3432:37:7"}, "scope": 7207, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 7183, "nodeType": "FunctionDefinition", "src": "3532:146:7", "nodes": [], "body": {"id": 7182, "nodeType": "Block", "src": "3622:56:7", "nodes": [], "statements": [{"expression": {"id": 7180, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 7178, "name": "targetedSelectors_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7176, "src": "3632:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector memory[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 7179, "name": "_targetedSelectors", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6964, "src": "3653:18:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzSelector storage ref[] storage ref"}}, "src": "3632:39:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector memory[] memory"}}, "id": 7181, "nodeType": "ExpressionStatement", "src": "3632:39:7"}]}, "functionSelector": "916a17c6", "implemented": true, "kind": "function", "modifiers": [], "name": "targetSelectors", "nameLocation": "3541:15:7", "parameters": {"id": 7172, "nodeType": "ParameterList", "parameters": [], "src": "3556:2:7"}, "returnParameters": {"id": 7177, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7176, "mutability": "mutable", "name": "targetedSelectors_", "nameLocation": "3602:18:7", "nodeType": "VariableDeclaration", "scope": 7183, "src": "3580:40:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzSelector[]"}, "typeName": {"baseType": {"id": 7174, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 7173, "name": "FuzzSelector", "nameLocations": ["3580:12:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6922, "src": "3580:12:7"}, "referencedDeclaration": 6922, "src": "3580:12:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzSelector_$6922_storage_ptr", "typeString": "struct StdInvariant.FuzzSelector"}}, "id": 7175, "nodeType": "ArrayTypeName", "src": "3580:14:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzSelector_$6922_storage_$dyn_storage_ptr", "typeString": "struct StdInvariant.FuzzSelector[]"}}, "visibility": "internal"}], "src": "3579:42:7"}, "scope": 7207, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 7194, "nodeType": "FunctionDefinition", "src": "3684:133:7", "nodes": [], "body": {"id": 7193, "nodeType": "Block", "src": "3765:52:7", "nodes": [], "statements": [{"expression": {"id": 7191, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 7189, "name": "targetedSenders_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7187, "src": "3775:16:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 7190, "name": "_targetedSenders", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6946, "src": "3794:16:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[] storage ref"}}, "src": "3775:35:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 7192, "nodeType": "ExpressionStatement", "src": "3775:35:7"}]}, "functionSelector": "3e5e3c23", "implemented": true, "kind": "function", "modifiers": [], "name": "targetSenders", "nameLocation": "3693:13:7", "parameters": {"id": 7184, "nodeType": "ParameterList", "parameters": [], "src": "3706:2:7"}, "returnParameters": {"id": 7188, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7187, "mutability": "mutable", "name": "targetedSenders_", "nameLocation": "3747:16:7", "nodeType": "VariableDeclaration", "scope": 7194, "src": "3730:33:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 7185, "name": "address", "nodeType": "ElementaryTypeName", "src": "3730:7:7", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 7186, "nodeType": "ArrayTypeName", "src": "3730:9:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "3729:35:7"}, "scope": 7207, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 7206, "nodeType": "FunctionDefinition", "src": "3823:151:7", "nodes": [], "body": {"id": 7205, "nodeType": "Block", "src": "3916:58:7", "nodes": [], "statements": [{"expression": {"id": 7203, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 7201, "name": "targetedInterfaces_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7199, "src": "3926:19:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzInterface_$6934_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzInterface memory[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 7202, "name": "_targetedInterfaces", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 6968, "src": "3948:19:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzInterface_$6934_storage_$dyn_storage", "typeString": "struct StdInvariant.FuzzInterface storage ref[] storage ref"}}, "src": "3926:41:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzInterface_$6934_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzInterface memory[] memory"}}, "id": 7204, "nodeType": "ExpressionStatement", "src": "3926:41:7"}]}, "functionSelector": "2ade3880", "implemented": true, "kind": "function", "modifiers": [], "name": "targetInterfaces", "nameLocation": "3832:16:7", "parameters": {"id": 7195, "nodeType": "ParameterList", "parameters": [], "src": "3848:2:7"}, "returnParameters": {"id": 7200, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 7199, "mutability": "mutable", "name": "targetedInterfaces_", "nameLocation": "3895:19:7", "nodeType": "VariableDeclaration", "scope": 7206, "src": "3872:42:7", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzInterface_$6934_memory_ptr_$dyn_memory_ptr", "typeString": "struct StdInvariant.FuzzInterface[]"}, "typeName": {"baseType": {"id": 7197, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 7196, "name": "FuzzInterface", "nameLocations": ["3872:13:7"], "nodeType": "IdentifierPath", "referencedDeclaration": 6934, "src": "3872:13:7"}, "referencedDeclaration": 6934, "src": "3872:13:7", "typeDescriptions": {"typeIdentifier": "t_struct$_FuzzInterface_$6934_storage_ptr", "typeString": "struct StdInvariant.FuzzInterface"}}, "id": 7198, "nodeType": "ArrayTypeName", "src": "3872:15:7", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FuzzInterface_$6934_storage_$dyn_storage_ptr", "typeString": "struct StdInvariant.FuzzInterface[]"}}, "visibility": "internal"}], "src": "3871:44:7"}, "scope": 7207, "stateMutability": "view", "virtual": false, "visibility": "public"}], "abstract": true, "baseContracts": [], "canonicalName": "StdInvariant", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [7207], "name": "StdInvariant", "nameLocation": "118:12:7", "scope": 7208, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 7}