{"abi": [{"type": "error", "name": "ActorExists", "inputs": []}, {"type": "error", "name": "ActorNotAdded", "inputs": []}, {"type": "error", "name": "ActorNotSetup", "inputs": []}, {"type": "error", "name": "DefaultActor", "inputs": []}, {"type": "error", "name": "Exists", "inputs": []}, {"type": "error", "name": "NotAdded", "inputs": []}, {"type": "error", "name": "NotSetup", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ActorExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotSetup\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DefaultActor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Exists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotSetup\",\"type\":\"error\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/recon/Properties.sol\":\"Properties\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/Asserts.sol\":{\"keccak256\":\"0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626\",\"dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK\"]},\"lib/chimera/src/BaseSetup.sol\":{\"keccak256\":\"0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe\",\"dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1\"]},\"lib/chimera/src/Hevm.sol\":{\"keccak256\":\"0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b\",\"dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr\"]},\"lib/setup-helpers/src/ActorManager.sol\":{\"keccak256\":\"0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c\",\"dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB\"]},\"lib/setup-helpers/src/AssetManager.sol\":{\"keccak256\":\"0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa\",\"dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq\"]},\"lib/setup-helpers/src/EnumerableSet.sol\":{\"keccak256\":\"0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df\",\"dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW\"]},\"lib/setup-helpers/src/MockERC20.sol\":{\"keccak256\":\"0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578\",\"dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE\"]},\"lib/setup-helpers/src/Panic.sol\":{\"keccak256\":\"0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325\",\"dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd\"]},\"lib/setup-helpers/src/Utils.sol\":{\"keccak256\":\"0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523\",\"dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau\"]},\"src/Counter.sol\":{\"keccak256\":\"0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e\",\"dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2\"]},\"test/recon/BeforeAfter.sol\":{\"keccak256\":\"0x749640c8e4e366e58f610fd34fc71bfb6d0abdc4bb482366ca6a1fff07fcc36e\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://de1f26ec166caba53e9e04b74a5fcfe64cc39a1687a68d357d17b79d65a6bc75\",\"dweb:/ipfs/QmfFjMrfBQ6hGUyEyasvEBknA4SNdokrNCrrWHbPVKGz9k\"]},\"test/recon/Properties.sol\":{\"keccak256\":\"0x5dbc6eba0dc68541aee76cfb2c774eb53912665d44ba9b00e2b4c67ae4b600d9\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://f786cfc9b24abdc05071da9aad64929c9f7b5fd5c6ee317962ad587dff6a1777\",\"dweb:/ipfs/QmWw9B7jLeFgHQeJFcaSgb9SY7oNCLHG12f3owHV4dNDSM\"]},\"test/recon/Setup.sol\":{\"keccak256\":\"0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024\",\"dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ActorExists"}, {"inputs": [], "type": "error", "name": "ActorNotAdded"}, {"inputs": [], "type": "error", "name": "ActorNotSetup"}, {"inputs": [], "type": "error", "name": "DefaultActor"}, {"inputs": [], "type": "error", "name": "Exists"}, {"inputs": [], "type": "error", "name": "NotAdded"}, {"inputs": [], "type": "error", "name": "NotSetup"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/recon/Properties.sol": "Properties"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/Asserts.sol": {"keccak256": "0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9", "urls": ["bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626", "dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK"], "license": "MIT"}, "lib/chimera/src/BaseSetup.sol": {"keccak256": "0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107", "urls": ["bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe", "dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1"], "license": "MIT"}, "lib/chimera/src/Hevm.sol": {"keccak256": "0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe", "urls": ["bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b", "dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr"], "license": "MIT"}, "lib/setup-helpers/src/ActorManager.sol": {"keccak256": "0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06", "urls": ["bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c", "dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB"], "license": "GPL-2.0"}, "lib/setup-helpers/src/AssetManager.sol": {"keccak256": "0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc", "urls": ["bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa", "dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq"], "license": "GPL-2.0"}, "lib/setup-helpers/src/EnumerableSet.sol": {"keccak256": "0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514", "urls": ["bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df", "dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW"], "license": "MIT"}, "lib/setup-helpers/src/MockERC20.sol": {"keccak256": "0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f", "urls": ["bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578", "dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE"], "license": "AGPL-3.0-only"}, "lib/setup-helpers/src/Panic.sol": {"keccak256": "0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92", "urls": ["bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325", "dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd"], "license": "GPL-2.0"}, "lib/setup-helpers/src/Utils.sol": {"keccak256": "0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27", "urls": ["bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523", "dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau"], "license": "GPL-2.0"}, "src/Counter.sol": {"keccak256": "0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c", "urls": ["bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e", "dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2"], "license": "UNLICENSED"}, "test/recon/BeforeAfter.sol": {"keccak256": "0x749640c8e4e366e58f610fd34fc71bfb6d0abdc4bb482366ca6a1fff07fcc36e", "urls": ["bzz-raw://de1f26ec166caba53e9e04b74a5fcfe64cc39a1687a68d357d17b79d65a6bc75", "dweb:/ipfs/QmfFjMrfBQ6hGUyEyasvEBknA4SNdokrNCrrWHbPVKGz9k"], "license": "GPL-2.0"}, "test/recon/Properties.sol": {"keccak256": "0x5dbc6eba0dc68541aee76cfb2c774eb53912665d44ba9b00e2b4c67ae4b600d9", "urls": ["bzz-raw://f786cfc9b24abdc05071da9aad64929c9f7b5fd5c6ee317962ad587dff6a1777", "dweb:/ipfs/QmWw9B7jLeFgHQeJFcaSgb9SY7oNCLHG12f3owHV4dNDSM"], "license": "GPL-2.0"}, "test/recon/Setup.sol": {"keccak256": "0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5", "urls": ["bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024", "dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk"], "license": "GPL-2.0"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 40405, "contract": "test/recon/Properties.sol:Properties", "label": "_actor", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 40409, "contract": "test/recon/Properties.sol:Properties", "label": "_actors", "offset": 0, "slot": "1", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 40565, "contract": "test/recon/Properties.sol:Properties", "label": "__asset", "offset": 0, "slot": "3", "type": "t_address"}, {"astId": 40569, "contract": "test/recon/Properties.sol:Properties", "label": "_assets", "offset": 0, "slot": "4", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 42682, "contract": "test/recon/Properties.sol:Properties", "label": "counter", "offset": 0, "slot": "6", "type": "t_contract(Counter)42568"}, {"astId": 42580, "contract": "test/recon/Properties.sol:Properties", "label": "_before", "offset": 0, "slot": "7", "type": "t_struct(Vars)42577_storage"}, {"astId": 42583, "contract": "test/recon/Properties.sol:Properties", "label": "_after", "offset": 0, "slot": "8", "type": "t_struct(Vars)42577_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_contract(Counter)42568": {"encoding": "inplace", "label": "contract Counter", "numberOfBytes": "20"}, "t_mapping(t_bytes32,t_uint256)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(AddressSet)41192_storage": {"encoding": "inplace", "label": "struct EnumerableSet.AddressSet", "numberOfBytes": "64", "members": [{"astId": 41191, "contract": "test/recon/Properties.sol:Properties", "label": "_inner", "offset": 0, "slot": "0", "type": "t_struct(Set)40877_storage"}]}, "t_struct(Set)40877_storage": {"encoding": "inplace", "label": "struct EnumerableSet.Set", "numberOfBytes": "64", "members": [{"astId": 40872, "contract": "test/recon/Properties.sol:Properties", "label": "_values", "offset": 0, "slot": "0", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 40876, "contract": "test/recon/Properties.sol:Properties", "label": "_indexes", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_uint256)"}]}, "t_struct(Vars)42577_storage": {"encoding": "inplace", "label": "struct BeforeAfter.Vars", "numberOfBytes": "32", "members": [{"astId": 42576, "contract": "test/recon/Properties.sol:Properties", "label": "__ignore__", "offset": 0, "slot": "0", "type": "t_uint256"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "test/recon/Properties.sol", "id": 42659, "exportedSymbols": {"Asserts": [81], "BeforeAfter": [42602], "Properties": [42658]}, "nodeType": "SourceUnit", "src": "36:176:36", "nodes": [{"id": 42649, "nodeType": "PragmaDirective", "src": "36:23:36", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 42651, "nodeType": "ImportDirective", "src": "61:45:36", "nodes": [], "absolutePath": "lib/chimera/src/Asserts.sol", "file": "@chimera/Asserts.sol", "nameLocation": "-1:-1:-1", "scope": 42659, "sourceUnit": 82, "symbolAliases": [{"foreign": {"id": 42650, "name": "Asserts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 81, "src": "69:7:36", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42653, "nodeType": "ImportDirective", "src": "107:46:36", "nodes": [], "absolutePath": "test/recon/BeforeAfter.sol", "file": "./BeforeAfter.sol", "nameLocation": "-1:-1:-1", "scope": 42659, "sourceUnit": 42603, "symbolAliases": [{"foreign": {"id": 42652, "name": "BeforeAfter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42602, "src": "115:11:36", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42658, "nodeType": "ContractDefinition", "src": "155:57:36", "nodes": [], "abstract": true, "baseContracts": [{"baseName": {"id": 42654, "name": "BeforeAfter", "nameLocations": ["187:11:36"], "nodeType": "IdentifierPath", "referencedDeclaration": 42602, "src": "187:11:36"}, "id": 42655, "nodeType": "InheritanceSpecifier", "src": "187:11:36"}, {"baseName": {"id": 42656, "name": "Asserts", "nameLocations": ["200:7:36"], "nodeType": "IdentifierPath", "referencedDeclaration": 81, "src": "200:7:36"}, "id": 42657, "nodeType": "InheritanceSpecifier", "src": "200:7:36"}], "canonicalName": "Properties", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": false, "linearizedBaseContracts": [42658, 81, 42602, 42724, 42406, 40866, 40547, 94], "name": "Properties", "nameLocation": "173:10:36", "scope": 42659, "usedErrors": [40411, 40413, 40415, 40417, 40571, 40573, 40575], "usedEvents": []}], "license": "GPL-2.0"}, "id": 36}