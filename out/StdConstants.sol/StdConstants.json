{"abi": [], "bytecode": {"object": "0x6055604b600b8282823980515f1a607314603f577f4e487b71000000000000000000000000000000000000000000000000000000005f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea26469706673582212206d293b87d7ee939eb0a07ce2c0671cd52e5931e463149d456825a8befb8824ed64736f6c634300081d0033", "sourceMap": "153:1713:5:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea26469706673582212206d293b87d7ee939eb0a07ce2c0671cd52e5931e463149d456825a8befb8824ed64736f6c634300081d0033", "sourceMap": "153:1713:5:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"stateVariables\":{\"CONSOLE\":{\"details\":\"console.sol and console2.sol work by executing a staticcall to this address. Calculated as `address(uint160(uint88(bytes11(\\\"console.log\\\"))))`.\"},\"CREATE2_FACTORY\":{\"details\":\"Used when deploying with create2. Taken from https://github.com/Arachnid/deterministic-deployment-proxy.\"},\"DEFAULT_SENDER\":{\"details\":\"The default address for tx.origin and msg.sender. Calculated as `address(uint160(uint256(keccak256(\\\"foundry default caller\\\"))))`.\"},\"DEFAULT_TEST_CONTRACT\":{\"details\":\"The address of the first contract `CREATE`d by a running test contract. When running tests, each test contract is `CREATE`d by `DEFAULT_SENDER` with nonce 1. Calculated as `VM.computeCreateAddress(VM.computeCreateAddress(DEFAULT_SENDER, 1), 1)`.\"},\"MULTICALL3_ADDRESS\":{\"details\":\"Deterministic deployment address of the Multicall3 contract. Taken from https://www.multicall3.com.\"},\"SECP256K1_ORDER\":{\"details\":\"The order of the secp256k1 curve.\"},\"VM\":{\"details\":\"Cheat code address. Calculated as `address(uint160(uint256(keccak256(\\\"hevm cheat code\\\"))))`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdConstants.sol\":\"StdConstants\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdConstants.sol": "StdConstants"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/forge-std/src/StdConstants.sol", "id": 6848, "exportedSymbols": {"IMulticall3": [26635], "StdConstants": [6847], "Vm": [18352]}, "nodeType": "SourceUnit", "src": "32:1835:5", "nodes": [{"id": 6808, "nodeType": "PragmaDirective", "src": "32:31:5", "nodes": [], "literals": ["solidity", ">=", "0.6", ".2", "<", "0.9", ".0"]}, {"id": 6810, "nodeType": "ImportDirective", "src": "65:57:5", "nodes": [], "absolutePath": "lib/forge-std/src/interfaces/IMulticall3.sol", "file": "./interfaces/IMulticall3.sol", "nameLocation": "-1:-1:-1", "scope": 6848, "sourceUnit": 26636, "symbolAliases": [{"foreign": {"id": 6809, "name": "IMulticall3", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26635, "src": "73:11:5", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 6812, "nodeType": "ImportDirective", "src": "123:28:5", "nodes": [], "absolutePath": "lib/forge-std/src/Vm.sol", "file": "./Vm.sol", "nameLocation": "-1:-1:-1", "scope": 6848, "sourceUnit": 18353, "symbolAliases": [{"foreign": {"id": 6811, "name": "Vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18352, "src": "131:2:5", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 6847, "nodeType": "ContractDefinition", "src": "153:1713:5", "nodes": [{"id": 6819, "nodeType": "VariableDeclaration", "src": "294:72:5", "nodes": [], "constant": true, "documentation": {"id": 6813, "nodeType": "StructuredDocumentation", "src": "180:109:5", "text": "@dev Cheat code address.\n Calculated as `address(uint160(uint256(keccak256(\"hevm cheat code\"))))`."}, "mutability": "constant", "name": "VM", "nameLocation": "315:2:5", "scope": 6847, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}, "typeName": {"id": 6815, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 6814, "name": "Vm", "nameLocations": ["294:2:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 18352, "src": "294:2:5"}, "referencedDeclaration": 18352, "src": "294:2:5", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "value": {"arguments": [{"hexValue": "307837313039373039454366613931613830363236664633393839443638663637463562314444313244", "id": 6817, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "323:42:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x7109709ECfa91a80626fF3989D68f67F5b1DD12D"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 6816, "name": "Vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18352, "src": "320:2:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Vm_$18352_$", "typeString": "type(contract Vm)"}}, "id": 6818, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "320:46:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "visibility": "internal"}, {"id": 6823, "nodeType": "VariableDeclaration", "src": "536:78:5", "nodes": [], "constant": true, "documentation": {"id": 6820, "nodeType": "StructuredDocumentation", "src": "372:159:5", "text": "@dev console.sol and console2.sol work by executing a staticcall to this address.\n Calculated as `address(uint160(uint88(bytes11(\"console.log\"))))`."}, "mutability": "constant", "name": "CONSOLE", "nameLocation": "562:7:5", "scope": 6847, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 6821, "name": "address", "nodeType": "ElementaryTypeName", "src": "536:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307830303030303030303030303030303030303036333646366537333646366336353265366336663637", "id": 6822, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "572:42:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x000000000000000000636F6e736F6c652e6c6f67"}, "visibility": "internal"}, {"id": 6827, "nodeType": "VariableDeclaration", "src": "746:86:5", "nodes": [], "constant": true, "documentation": {"id": 6824, "nodeType": "StructuredDocumentation", "src": "620:121:5", "text": "@dev Used when deploying with create2.\n Taken from https://github.com/Arachnid/deterministic-deployment-proxy."}, "mutability": "constant", "name": "CREATE2_FACTORY", "nameLocation": "772:15:5", "scope": 6847, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 6825, "name": "address", "nodeType": "ElementaryTypeName", "src": "746:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307834653539623434383437623337393537383538383932306341373846624632366330423439353643", "id": 6826, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "790:42:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x4e59b44847b379578588920cA78FbF26c0B4956C"}, "visibility": "internal"}, {"id": 6831, "nodeType": "VariableDeclaration", "src": "989:85:5", "nodes": [], "constant": true, "documentation": {"id": 6828, "nodeType": "StructuredDocumentation", "src": "838:146:5", "text": "@dev The default address for tx.origin and msg.sender.\n Calculated as `address(uint160(uint256(keccak256(\"foundry default caller\"))))`."}, "mutability": "constant", "name": "DEFAULT_SENDER", "nameLocation": "1015:14:5", "scope": 6847, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 6829, "name": "address", "nodeType": "ElementaryTypeName", "src": "989:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307831383034633841423146313245366262663338393464343038336633336530373330396431663338", "id": 6830, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1032:42:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x1804c8AB1F12E6bbf3894d4083f33e07309d1f38"}, "visibility": "internal"}, {"id": 6835, "nodeType": "VariableDeclaration", "src": "1355:92:5", "nodes": [], "constant": true, "documentation": {"id": 6832, "nodeType": "StructuredDocumentation", "src": "1080:270:5", "text": "@dev The address of the first contract `CREATE`d by a running test contract.\n When running tests, each test contract is `CREATE`d by `DEFAULT_SENDER` with nonce 1.\n Calculated as `VM.computeCreateAddress(VM.computeCreateAddress(DEFAULT_SENDER, 1), 1)`."}, "mutability": "constant", "name": "DEFAULT_TEST_CONTRACT", "nameLocation": "1381:21:5", "scope": 6847, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 6833, "name": "address", "nodeType": "ElementaryTypeName", "src": "1355:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307835363135644542373938424233453464466130313339644661316233443433334363323362373266", "id": 6834, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1405:42:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x5615dEB798BB3E4dFa0139dFa1b3D433Cc23b72f"}, "visibility": "internal"}, {"id": 6842, "nodeType": "VariableDeclaration", "src": "1574:106:5", "nodes": [], "constant": true, "documentation": {"id": 6836, "nodeType": "StructuredDocumentation", "src": "1453:116:5", "text": "@dev Deterministic deployment address of the Multicall3 contract.\n Taken from https://www.multicall3.com."}, "mutability": "constant", "name": "MULTICALL3_ADDRESS", "nameLocation": "1604:18:5", "scope": 6847, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IMulticall3_$26635", "typeString": "contract IMulticall3"}, "typeName": {"id": 6838, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 6837, "name": "IMulticall3", "nameLocations": ["1574:11:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 26635, "src": "1574:11:5"}, "referencedDeclaration": 26635, "src": "1574:11:5", "typeDescriptions": {"typeIdentifier": "t_contract$_IMulticall3_$26635", "typeString": "contract IMulticall3"}}, "value": {"arguments": [{"hexValue": "307863413131626465303539373762333633313136373032383836326245326131373339373643413131", "id": 6840, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1637:42:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0xcA11bde05977b3631167028862bE2a173976CA11"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 6839, "name": "IMulticall3", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26635, "src": "1625:11:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IMulticall3_$26635_$", "typeString": "type(contract IMulticall3)"}}, "id": 6841, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1625:55:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IMulticall3_$26635", "typeString": "contract IMulticall3"}}, "visibility": "internal"}, {"id": 6846, "nodeType": "VariableDeclaration", "src": "1733:130:5", "nodes": [], "constant": true, "documentation": {"id": 6843, "nodeType": "StructuredDocumentation", "src": "1686:42:5", "text": "@dev The order of the secp256k1 curve."}, "mutability": "constant", "name": "SECP256K1_ORDER", "nameLocation": "1759:15:5", "scope": 6847, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 6844, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1733:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "313135373932303839323337333136313935343233353730393835303038363837393037383532383337353634323739303734393034333832363035313633313431353138313631343934333337", "id": 6845, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1785:78:5", "typeDescriptions": {"typeIdentifier": "t_rational_115792089237316195423570985008687907852837564279074904382605163141518161494337_by_1", "typeString": "int_const 1157...(70 digits omitted)...4337"}, "value": "115792089237316195423570985008687907852837564279074904382605163141518161494337"}, "visibility": "internal"}], "abstract": false, "baseContracts": [], "canonicalName": "StdConstants", "contractDependencies": [], "contractKind": "library", "fullyImplemented": true, "linearizedBaseContracts": [6847], "name": "StdConstants", "nameLocation": "161:12:5", "scope": 6848, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 5}