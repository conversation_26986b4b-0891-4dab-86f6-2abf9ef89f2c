{"abi": [{"type": "function", "name": "add_new_asset", "inputs": [{"name": "decimals", "type": "uint8", "internalType": "uint8"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "asset_approve", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amt", "type": "uint128", "internalType": "uint128"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "asset_mint", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amt", "type": "uint128", "internalType": "uint128"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "switchActor", "inputs": [{"name": "entropy", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "switch_asset", "inputs": [{"name": "entropy", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "error", "name": "ActorExists", "inputs": []}, {"type": "error", "name": "ActorNotAdded", "inputs": []}, {"type": "error", "name": "ActorNotSetup", "inputs": []}, {"type": "error", "name": "DefaultActor", "inputs": []}, {"type": "error", "name": "Exists", "inputs": []}, {"type": "error", "name": "NotAdded", "inputs": []}, {"type": "error", "name": "NotSetup", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"add_new_asset(uint8)": "7fbba149", "asset_approve(address,uint128)": "2fa9dadc", "asset_mint(address,uint128)": "652e3935", "switchActor(uint256)": "6801b82e", "switch_asset(uint256)": "55ba98ff"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ActorExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotSetup\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DefaultActor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Exists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotSetup\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"decimals\",\"type\":\"uint8\"}],\"name\":\"add_new_asset\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"amt\",\"type\":\"uint128\"}],\"name\":\"asset_approve\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"amt\",\"type\":\"uint128\"}],\"name\":\"asset_mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"entropy\",\"type\":\"uint256\"}],\"name\":\"switchActor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"entropy\",\"type\":\"uint256\"}],\"name\":\"switch_asset\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"add_new_asset(uint8)\":{\"details\":\"Deploy a new token and add it to the list of assets, then set it as the current asset\"},\"asset_approve(address,uint128)\":{\"details\":\"Approve to arbitrary address, uses Actor by default NOTE: You're almost always better off setting approvals in `Setup`\"},\"asset_mint(address,uint128)\":{\"details\":\"Mint to arbitrary address, uses owner by default, even though MockERC20 doesn't check\"},\"switchActor(uint256)\":{\"details\":\"Start acting as another actor\"},\"switch_asset(uint256)\":{\"details\":\"Starts using a new asset\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/recon/targets/ManagersTargets.sol\":\"ManagersTargets\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/Asserts.sol\":{\"keccak256\":\"0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626\",\"dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK\"]},\"lib/chimera/src/BaseProperties.sol\":{\"keccak256\":\"0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848\",\"dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w\"]},\"lib/chimera/src/BaseSetup.sol\":{\"keccak256\":\"0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe\",\"dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1\"]},\"lib/chimera/src/BaseTargetFunctions.sol\":{\"keccak256\":\"0xe3b3de6200ab7039a14bb0a2a7e090402a36bd2c0c31c6d677d766b0f335bd60\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5326abd254a25c5bf8c2219e36641bc1288114271678dac8611d8365fc432851\",\"dweb:/ipfs/QmR4BF3JrTU3XhPVY4vPxGCQFXbUv94Bi48FjvgphroPxJ\"]},\"lib/chimera/src/Hevm.sol\":{\"keccak256\":\"0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b\",\"dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr\"]},\"lib/setup-helpers/src/ActorManager.sol\":{\"keccak256\":\"0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c\",\"dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB\"]},\"lib/setup-helpers/src/AssetManager.sol\":{\"keccak256\":\"0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa\",\"dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq\"]},\"lib/setup-helpers/src/EnumerableSet.sol\":{\"keccak256\":\"0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df\",\"dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW\"]},\"lib/setup-helpers/src/MockERC20.sol\":{\"keccak256\":\"0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578\",\"dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE\"]},\"lib/setup-helpers/src/Panic.sol\":{\"keccak256\":\"0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325\",\"dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd\"]},\"lib/setup-helpers/src/Utils.sol\":{\"keccak256\":\"0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523\",\"dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau\"]},\"src/Counter.sol\":{\"keccak256\":\"0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e\",\"dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2\"]},\"test/recon/BeforeAfter.sol\":{\"keccak256\":\"0x749640c8e4e366e58f610fd34fc71bfb6d0abdc4bb482366ca6a1fff07fcc36e\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://de1f26ec166caba53e9e04b74a5fcfe64cc39a1687a68d357d17b79d65a6bc75\",\"dweb:/ipfs/QmfFjMrfBQ6hGUyEyasvEBknA4SNdokrNCrrWHbPVKGz9k\"]},\"test/recon/Properties.sol\":{\"keccak256\":\"0x5dbc6eba0dc68541aee76cfb2c774eb53912665d44ba9b00e2b4c67ae4b600d9\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://f786cfc9b24abdc05071da9aad64929c9f7b5fd5c6ee317962ad587dff6a1777\",\"dweb:/ipfs/QmWw9B7jLeFgHQeJFcaSgb9SY7oNCLHG12f3owHV4dNDSM\"]},\"test/recon/Setup.sol\":{\"keccak256\":\"0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024\",\"dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk\"]},\"test/recon/targets/ManagersTargets.sol\":{\"keccak256\":\"0x2d2c2ac36388f88d7e53b08c661d5057b0ed84e6b3251ab8d0bfd3e10cd5445a\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://7b7b122d9522ab44b79dc478afd41f1274cffe4aca1933bb7af1dd9dec0b3680\",\"dweb:/ipfs/QmVtJpXLyZvJMouhsssc7ULv6kr6KsALJv3Pq8UQYoTKUT\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ActorExists"}, {"inputs": [], "type": "error", "name": "ActorNotAdded"}, {"inputs": [], "type": "error", "name": "ActorNotSetup"}, {"inputs": [], "type": "error", "name": "DefaultActor"}, {"inputs": [], "type": "error", "name": "Exists"}, {"inputs": [], "type": "error", "name": "NotAdded"}, {"inputs": [], "type": "error", "name": "NotSetup"}, {"inputs": [{"internalType": "uint8", "name": "decimals", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "add_new_asset", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint128", "name": "amt", "type": "uint128"}], "stateMutability": "nonpayable", "type": "function", "name": "asset_approve"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint128", "name": "amt", "type": "uint128"}], "stateMutability": "nonpayable", "type": "function", "name": "asset_mint"}, {"inputs": [{"internalType": "uint256", "name": "entropy", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "switchActor"}, {"inputs": [{"internalType": "uint256", "name": "entropy", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "switch_asset"}], "devdoc": {"kind": "dev", "methods": {"add_new_asset(uint8)": {"details": "Deploy a new token and add it to the list of assets, then set it as the current asset"}, "asset_approve(address,uint128)": {"details": "Approve to arbitrary address, uses Actor by default NOTE: You're almost always better off setting approvals in `Setup`"}, "asset_mint(address,uint128)": {"details": "Mint to arbitrary address, uses owner by default, even though MockERC20 doesn't check"}, "switchActor(uint256)": {"details": "Start acting as another actor"}, "switch_asset(uint256)": {"details": "Starts using a new asset"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/recon/targets/ManagersTargets.sol": "ManagersTargets"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/Asserts.sol": {"keccak256": "0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9", "urls": ["bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626", "dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK"], "license": "MIT"}, "lib/chimera/src/BaseProperties.sol": {"keccak256": "0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a", "urls": ["bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848", "dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w"], "license": "MIT"}, "lib/chimera/src/BaseSetup.sol": {"keccak256": "0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107", "urls": ["bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe", "dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1"], "license": "MIT"}, "lib/chimera/src/BaseTargetFunctions.sol": {"keccak256": "0xe3b3de6200ab7039a14bb0a2a7e090402a36bd2c0c31c6d677d766b0f335bd60", "urls": ["bzz-raw://5326abd254a25c5bf8c2219e36641bc1288114271678dac8611d8365fc432851", "dweb:/ipfs/QmR4BF3JrTU3XhPVY4vPxGCQFXbUv94Bi48FjvgphroPxJ"], "license": "MIT"}, "lib/chimera/src/Hevm.sol": {"keccak256": "0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe", "urls": ["bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b", "dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr"], "license": "MIT"}, "lib/setup-helpers/src/ActorManager.sol": {"keccak256": "0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06", "urls": ["bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c", "dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB"], "license": "GPL-2.0"}, "lib/setup-helpers/src/AssetManager.sol": {"keccak256": "0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc", "urls": ["bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa", "dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq"], "license": "GPL-2.0"}, "lib/setup-helpers/src/EnumerableSet.sol": {"keccak256": "0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514", "urls": ["bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df", "dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW"], "license": "MIT"}, "lib/setup-helpers/src/MockERC20.sol": {"keccak256": "0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f", "urls": ["bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578", "dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE"], "license": "AGPL-3.0-only"}, "lib/setup-helpers/src/Panic.sol": {"keccak256": "0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92", "urls": ["bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325", "dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd"], "license": "GPL-2.0"}, "lib/setup-helpers/src/Utils.sol": {"keccak256": "0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27", "urls": ["bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523", "dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau"], "license": "GPL-2.0"}, "src/Counter.sol": {"keccak256": "0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c", "urls": ["bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e", "dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2"], "license": "UNLICENSED"}, "test/recon/BeforeAfter.sol": {"keccak256": "0x749640c8e4e366e58f610fd34fc71bfb6d0abdc4bb482366ca6a1fff07fcc36e", "urls": ["bzz-raw://de1f26ec166caba53e9e04b74a5fcfe64cc39a1687a68d357d17b79d65a6bc75", "dweb:/ipfs/QmfFjMrfBQ6hGUyEyasvEBknA4SNdokrNCrrWHbPVKGz9k"], "license": "GPL-2.0"}, "test/recon/Properties.sol": {"keccak256": "0x5dbc6eba0dc68541aee76cfb2c774eb53912665d44ba9b00e2b4c67ae4b600d9", "urls": ["bzz-raw://f786cfc9b24abdc05071da9aad64929c9f7b5fd5c6ee317962ad587dff6a1777", "dweb:/ipfs/QmWw9B7jLeFgHQeJFcaSgb9SY7oNCLHG12f3owHV4dNDSM"], "license": "GPL-2.0"}, "test/recon/Setup.sol": {"keccak256": "0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5", "urls": ["bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024", "dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk"], "license": "GPL-2.0"}, "test/recon/targets/ManagersTargets.sol": {"keccak256": "0x2d2c2ac36388f88d7e53b08c661d5057b0ed84e6b3251ab8d0bfd3e10cd5445a", "urls": ["bzz-raw://7b7b122d9522ab44b79dc478afd41f1274cffe4aca1933bb7af1dd9dec0b3680", "dweb:/ipfs/QmVtJpXLyZvJMouhsssc7ULv6kr6KsALJv3Pq8UQYoTKUT"], "license": "GPL-2.0"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 40405, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "_actor", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 40409, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "_actors", "offset": 0, "slot": "1", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 40565, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "__asset", "offset": 0, "slot": "3", "type": "t_address"}, {"astId": 40569, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "_assets", "offset": 0, "slot": "4", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 42682, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "counter", "offset": 0, "slot": "6", "type": "t_contract(Counter)42568"}, {"astId": 42580, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "_before", "offset": 0, "slot": "7", "type": "t_struct(Vars)42577_storage"}, {"astId": 42583, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "_after", "offset": 0, "slot": "8", "type": "t_struct(Vars)42577_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_contract(Counter)42568": {"encoding": "inplace", "label": "contract Counter", "numberOfBytes": "20"}, "t_mapping(t_bytes32,t_uint256)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(AddressSet)41192_storage": {"encoding": "inplace", "label": "struct EnumerableSet.AddressSet", "numberOfBytes": "64", "members": [{"astId": 41191, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "_inner", "offset": 0, "slot": "0", "type": "t_struct(Set)40877_storage"}]}, "t_struct(Set)40877_storage": {"encoding": "inplace", "label": "struct EnumerableSet.Set", "numberOfBytes": "64", "members": [{"astId": 40872, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "_values", "offset": 0, "slot": "0", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 40876, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "_indexes", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_uint256)"}]}, "t_struct(Vars)42577_storage": {"encoding": "inplace", "label": "struct BeforeAfter.Vars", "numberOfBytes": "32", "members": [{"astId": 42576, "contract": "test/recon/targets/ManagersTargets.sol:ManagersTargets", "label": "__ignore__", "offset": 0, "slot": "0", "type": "t_uint256"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "test/recon/targets/ManagersTargets.sol", "id": 42985, "exportedSymbols": {"BaseTargetFunctions": [105], "BeforeAfter": [42602], "ManagersTargets": [42984], "MockERC20": [42022], "Properties": [42658], "vm": [719]}, "nodeType": "SourceUnit", "src": "36:1796:42", "nodes": [{"id": 42886, "nodeType": "PragmaDirective", "src": "36:23:42", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 42888, "nodeType": "ImportDirective", "src": "61:69:42", "nodes": [], "absolutePath": "lib/chimera/src/BaseTargetFunctions.sol", "file": "@chimera/BaseTargetFunctions.sol", "nameLocation": "-1:-1:-1", "scope": 42985, "sourceUnit": 106, "symbolAliases": [{"foreign": {"id": 42887, "name": "BaseTargetFunctions", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "69:19:42", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42890, "nodeType": "ImportDirective", "src": "131:47:42", "nodes": [], "absolutePath": "test/recon/BeforeAfter.sol", "file": "../BeforeAfter.sol", "nameLocation": "-1:-1:-1", "scope": 42985, "sourceUnit": 42603, "symbolAliases": [{"foreign": {"id": 42889, "name": "BeforeAfter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42602, "src": "139:11:42", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42892, "nodeType": "ImportDirective", "src": "179:45:42", "nodes": [], "absolutePath": "test/recon/Properties.sol", "file": "../Properties.sol", "nameLocation": "-1:-1:-1", "scope": 42985, "sourceUnit": 42659, "symbolAliases": [{"foreign": {"id": 42891, "name": "Properties", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42658, "src": "187:10:42", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42894, "nodeType": "ImportDirective", "src": "225:37:42", "nodes": [], "absolutePath": "lib/chimera/src/Hevm.sol", "file": "@chimera/Hevm.sol", "nameLocation": "-1:-1:-1", "scope": 42985, "sourceUnit": 720, "symbolAliases": [{"foreign": {"id": 42893, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "233:2:42", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42896, "nodeType": "ImportDirective", "src": "264:47:42", "nodes": [], "absolutePath": "lib/setup-helpers/src/MockERC20.sol", "file": "@recon/MockERC20.sol", "nameLocation": "-1:-1:-1", "scope": 42985, "sourceUnit": 42023, "symbolAliases": [{"foreign": {"id": 42895, "name": "MockERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42022, "src": "272:9:42", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42984, "nodeType": "ContractDefinition", "src": "518:1314:42", "nodes": [{"id": 42911, "nodeType": "FunctionDefinition", "src": "680:83:42", "nodes": [], "body": {"id": 42910, "nodeType": "Block", "src": "725:38:42", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42907, "name": "entropy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42903, "src": "748:7:42", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 42906, "name": "_switchActor", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40546, "src": "735:12:42", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_uint256_$returns$_t_address_$", "typeString": "function (uint256) returns (address)"}}, "id": 42908, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "735:21:42", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 42909, "nodeType": "ExpressionStatement", "src": "735:21:42"}]}, "documentation": {"id": 42901, "nodeType": "StructuredDocumentation", "src": "637:38:42", "text": "@dev Start acting as another actor"}, "functionSelector": "6801b82e", "implemented": true, "kind": "function", "modifiers": [], "name": "switchActor", "nameLocation": "689:11:42", "parameters": {"id": 42904, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42903, "mutability": "mutable", "name": "entropy", "nameLocation": "709:7:42", "nodeType": "VariableDeclaration", "scope": 42911, "src": "701:15:42", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42902, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "701:7:42", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "700:17:42"}, "returnParameters": {"id": 42905, "nodeType": "ParameterList", "parameters": [], "src": "725:0:42"}, "scope": 42984, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42922, "nodeType": "FunctionDefinition", "src": "808:84:42", "nodes": [], "body": {"id": 42921, "nodeType": "Block", "src": "854:38:42", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42918, "name": "entropy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42914, "src": "877:7:42", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 42917, "name": "_switchAsset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40702, "src": "864:12:42", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 42919, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "864:21:42", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42920, "nodeType": "ExpressionStatement", "src": "864:21:42"}]}, "documentation": {"id": 42912, "nodeType": "StructuredDocumentation", "src": "770:33:42", "text": "@dev Starts using a new asset"}, "functionSelector": "55ba98ff", "implemented": true, "kind": "function", "modifiers": [], "name": "switch_asset", "nameLocation": "817:12:42", "parameters": {"id": 42915, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42914, "mutability": "mutable", "name": "entropy", "nameLocation": "838:7:42", "nodeType": "VariableDeclaration", "scope": 42922, "src": "830:15:42", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42913, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "830:7:42", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "829:17:42"}, "returnParameters": {"id": 42916, "nodeType": "ParameterList", "parameters": [], "src": "854:0:42"}, "scope": 42984, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42939, "nodeType": "FunctionDefinition", "src": "997:144:42", "nodes": [], "body": {"id": 42938, "nodeType": "Block", "src": "1061:80:42", "nodes": [], "statements": [{"assignments": [42931], "declarations": [{"constant": false, "id": 42931, "mutability": "mutable", "name": "newAsset", "nameLocation": "1079:8:42", "nodeType": "VariableDeclaration", "scope": 42938, "src": "1071:16:42", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 42930, "name": "address", "nodeType": "ElementaryTypeName", "src": "1071:7:42", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 42935, "initialValue": {"arguments": [{"id": 42933, "name": "decimals", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42925, "src": "1100:8:42", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint8", "typeString": "uint8"}], "id": 42932, "name": "_newAsset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40639, "src": "1090:9:42", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_uint8_$returns$_t_address_$", "typeString": "function (uint8) returns (address)"}}, "id": 42934, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1090:19:42", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "1071:38:42"}, {"expression": {"id": 42936, "name": "newAsset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42931, "src": "1126:8:42", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 42929, "id": 42937, "nodeType": "Return", "src": "1119:15:42"}]}, "documentation": {"id": 42923, "nodeType": "StructuredDocumentation", "src": "898:94:42", "text": "@dev Deploy a new token and add it to the list of assets, then set it as the current asset"}, "functionSelector": "7fbba149", "implemented": true, "kind": "function", "modifiers": [], "name": "add_new_asset", "nameLocation": "1006:13:42", "parameters": {"id": 42926, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42925, "mutability": "mutable", "name": "decimals", "nameLocation": "1026:8:42", "nodeType": "VariableDeclaration", "scope": 42939, "src": "1020:14:42", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 42924, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "1020:5:42", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "1019:16:42"}, "returnParameters": {"id": 42929, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42928, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 42939, "src": "1052:7:42", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 42927, "name": "address", "nodeType": "ElementaryTypeName", "src": "1052:7:42", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1051:9:42"}, "scope": 42984, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42961, "nodeType": "FunctionDefinition", "src": "1467:132:42", "nodes": [], "body": {"id": 42960, "nodeType": "Block", "src": "1543:56:42", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42956, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42942, "src": "1584:2:42", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 42957, "name": "amt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42944, "src": "1588:3:42", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint128", "typeString": "uint128"}], "expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 42952, "name": "_getAsset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40595, "src": "1563:9:42", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 42953, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1563:11:42", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 42951, "name": "MockERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42022, "src": "1553:9:42", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_MockERC20_$42022_$", "typeString": "type(contract MockERC20)"}}, "id": 42954, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1553:22:42", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_MockERC20_$42022", "typeString": "contract MockERC20"}}, "id": 42955, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1576:7:42", "memberName": "approve", "nodeType": "MemberAccess", "referencedDeclaration": 41611, "src": "1553:30:42", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,uint256) external returns (bool)"}}, "id": 42958, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1553:39:42", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 42959, "nodeType": "ExpressionStatement", "src": "1553:39:42"}]}, "documentation": {"id": 42940, "nodeType": "StructuredDocumentation", "src": "1327:135:42", "text": "@dev Approve to arbitrary address, uses Actor by default\n NOTE: You're almost always better off setting approvals in `Setup`"}, "functionSelector": "2fa9dadc", "implemented": true, "kind": "function", "modifiers": [{"id": 42947, "kind": "modifierInvocation", "modifierName": {"id": 42946, "name": "updateGhosts", "nameLocations": ["1522:12:42"], "nodeType": "IdentifierPath", "referencedDeclaration": 42593, "src": "1522:12:42"}, "nodeType": "ModifierInvocation", "src": "1522:12:42"}, {"id": 42949, "kind": "modifierInvocation", "modifierName": {"id": 42948, "name": "as<PERSON><PERSON>", "nameLocations": ["1535:7:42"], "nodeType": "IdentifierPath", "referencedDeclaration": 42723, "src": "1535:7:42"}, "nodeType": "ModifierInvocation", "src": "1535:7:42"}], "name": "asset_approve", "nameLocation": "1476:13:42", "parameters": {"id": 42945, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42942, "mutability": "mutable", "name": "to", "nameLocation": "1498:2:42", "nodeType": "VariableDeclaration", "scope": 42961, "src": "1490:10:42", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 42941, "name": "address", "nodeType": "ElementaryTypeName", "src": "1490:7:42", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 42944, "mutability": "mutable", "name": "amt", "nameLocation": "1510:3:42", "nodeType": "VariableDeclaration", "scope": 42961, "src": "1502:11:42", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}, "typeName": {"id": 42943, "name": "uint128", "nodeType": "ElementaryTypeName", "src": "1502:7:42", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}, "visibility": "internal"}], "src": "1489:25:42"}, "returnParameters": {"id": 42950, "nodeType": "ParameterList", "parameters": [], "src": "1543:0:42"}, "scope": 42984, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 42983, "nodeType": "FunctionDefinition", "src": "1704:126:42", "nodes": [], "body": {"id": 42982, "nodeType": "Block", "src": "1777:53:42", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 42978, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42964, "src": "1815:2:42", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 42979, "name": "amt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42966, "src": "1819:3:42", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint128", "typeString": "uint128"}], "expression": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 42974, "name": "_getAsset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40595, "src": "1797:9:42", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 42975, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1797:11:42", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 42973, "name": "MockERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42022, "src": "1787:9:42", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_MockERC20_$42022_$", "typeString": "type(contract MockERC20)"}}, "id": 42976, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1787:22:42", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_MockERC20_$42022", "typeString": "contract MockERC20"}}, "id": 42977, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1810:4:42", "memberName": "mint", "nodeType": "MemberAccess", "referencedDeclaration": 42008, "src": "1787:27:42", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256) external"}}, "id": 42980, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1787:36:42", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42981, "nodeType": "ExpressionStatement", "src": "1787:36:42"}]}, "documentation": {"id": 42962, "nodeType": "StructuredDocumentation", "src": "1605:94:42", "text": "@dev Mint to arbitrary address, uses owner by default, even though MockERC20 doesn't check"}, "functionSelector": "652e3935", "implemented": true, "kind": "function", "modifiers": [{"id": 42969, "kind": "modifierInvocation", "modifierName": {"id": 42968, "name": "updateGhosts", "nameLocations": ["1756:12:42"], "nodeType": "IdentifierPath", "referencedDeclaration": 42593, "src": "1756:12:42"}, "nodeType": "ModifierInvocation", "src": "1756:12:42"}, {"id": 42971, "kind": "modifierInvocation", "modifierName": {"id": 42970, "name": "asAdmin", "nameLocations": ["1769:7:42"], "nodeType": "IdentifierPath", "referencedDeclaration": 42709, "src": "1769:7:42"}, "nodeType": "ModifierInvocation", "src": "1769:7:42"}], "name": "asset_mint", "nameLocation": "1713:10:42", "parameters": {"id": 42967, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 42964, "mutability": "mutable", "name": "to", "nameLocation": "1732:2:42", "nodeType": "VariableDeclaration", "scope": 42983, "src": "1724:10:42", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 42963, "name": "address", "nodeType": "ElementaryTypeName", "src": "1724:7:42", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 42966, "mutability": "mutable", "name": "amt", "nameLocation": "1744:3:42", "nodeType": "VariableDeclaration", "scope": 42983, "src": "1736:11:42", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}, "typeName": {"id": 42965, "name": "uint128", "nodeType": "ElementaryTypeName", "src": "1736:7:42", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}, "visibility": "internal"}], "src": "1723:25:42"}, "returnParameters": {"id": 42972, "nodeType": "ParameterList", "parameters": [], "src": "1777:0:42"}, "scope": 42984, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "abstract": true, "baseContracts": [{"baseName": {"id": 42897, "name": "BaseTargetFunctions", "nameLocations": ["559:19:42"], "nodeType": "IdentifierPath", "referencedDeclaration": 105, "src": "559:19:42"}, "id": 42898, "nodeType": "InheritanceSpecifier", "src": "559:19:42"}, {"baseName": {"id": 42899, "name": "Properties", "nameLocations": ["584:10:42"], "nodeType": "IdentifierPath", "referencedDeclaration": 42658, "src": "584:10:42"}, "id": 42900, "nodeType": "InheritanceSpecifier", "src": "584:10:42"}], "canonicalName": "ManagersTargets", "contractDependencies": [42022], "contractKind": "contract", "fullyImplemented": false, "linearizedBaseContracts": [42984, 42658, 105, 81, 42602, 42724, 42406, 40866, 40547, 88, 94], "name": "ManagersTargets", "nameLocation": "536:15:42", "scope": 42985, "usedErrors": [40411, 40413, 40415, 40417, 40571, 40573, 40575], "usedEvents": []}], "license": "GPL-2.0"}, "id": 42}