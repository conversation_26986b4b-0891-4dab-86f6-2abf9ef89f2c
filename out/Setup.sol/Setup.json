{"abi": [{"type": "error", "name": "ActorExists", "inputs": []}, {"type": "error", "name": "ActorNotAdded", "inputs": []}, {"type": "error", "name": "ActorNotSetup", "inputs": []}, {"type": "error", "name": "DefaultActor", "inputs": []}, {"type": "error", "name": "Exists", "inputs": []}, {"type": "error", "name": "NotAdded", "inputs": []}, {"type": "error", "name": "NotSetup", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ActorExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ActorNotSetup\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DefaultActor\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Exists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotSetup\",\"type\":\"error\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/recon/Setup.sol\":\"Setup\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/BaseSetup.sol\":{\"keccak256\":\"0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe\",\"dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1\"]},\"lib/chimera/src/Hevm.sol\":{\"keccak256\":\"0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b\",\"dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr\"]},\"lib/setup-helpers/src/ActorManager.sol\":{\"keccak256\":\"0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c\",\"dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB\"]},\"lib/setup-helpers/src/AssetManager.sol\":{\"keccak256\":\"0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa\",\"dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq\"]},\"lib/setup-helpers/src/EnumerableSet.sol\":{\"keccak256\":\"0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df\",\"dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW\"]},\"lib/setup-helpers/src/MockERC20.sol\":{\"keccak256\":\"0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f\",\"license\":\"AGPL-3.0-only\",\"urls\":[\"bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578\",\"dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE\"]},\"lib/setup-helpers/src/Panic.sol\":{\"keccak256\":\"0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325\",\"dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd\"]},\"lib/setup-helpers/src/Utils.sol\":{\"keccak256\":\"0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523\",\"dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau\"]},\"src/Counter.sol\":{\"keccak256\":\"0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e\",\"dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2\"]},\"test/recon/Setup.sol\":{\"keccak256\":\"0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5\",\"license\":\"GPL-2.0\",\"urls\":[\"bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024\",\"dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ActorExists"}, {"inputs": [], "type": "error", "name": "ActorNotAdded"}, {"inputs": [], "type": "error", "name": "ActorNotSetup"}, {"inputs": [], "type": "error", "name": "DefaultActor"}, {"inputs": [], "type": "error", "name": "Exists"}, {"inputs": [], "type": "error", "name": "NotAdded"}, {"inputs": [], "type": "error", "name": "NotSetup"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/recon/Setup.sol": "Setup"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/BaseSetup.sol": {"keccak256": "0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107", "urls": ["bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe", "dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1"], "license": "MIT"}, "lib/chimera/src/Hevm.sol": {"keccak256": "0xf9bdec9f1c12d323ebfcd19174c1450582815805a1ce6b030cd9327af8b8cafe", "urls": ["bzz-raw://a7f15b22db962e8111b4abe819dab917bbbf6d499be3a03908cf3a3c147dd15b", "dweb:/ipfs/QmSqievB9bd5zbMuZomo4T1rxAv8nKhLdxjPH3qCvZ8dXr"], "license": "MIT"}, "lib/setup-helpers/src/ActorManager.sol": {"keccak256": "0xfe27af93a68d65b0bbb63fc4a3726213628a1c54e07dd60b2578fff7261beb06", "urls": ["bzz-raw://24aded146c23d6d71c987a7af507c0f795b8e0f7bb6393c499b6c3fb6858a83c", "dweb:/ipfs/QmeJAN6xmEVYkAm1xEzrttwqkKw8LiCysmGaY2aRiQn6VB"], "license": "GPL-2.0"}, "lib/setup-helpers/src/AssetManager.sol": {"keccak256": "0xd96028ec5221309048bc0aec4716461c0ae985337cf92c776171d0146a825fbc", "urls": ["bzz-raw://5938c104c25eff6579476ae46af8d150e4e5cf4e2b2cf0ce4dea5471cae2c8fa", "dweb:/ipfs/QmTt6p2GaF7xaXAhXGZYkMUgXAn4ov2wTcYztMpRDgU5iq"], "license": "GPL-2.0"}, "lib/setup-helpers/src/EnumerableSet.sol": {"keccak256": "0x9f4357008a8f7d8c8bf5d48902e789637538d8c016be5766610901b4bba81514", "urls": ["bzz-raw://20bf19b2b851f58a4c24543de80ae70b3e08621f9230eb335dc75e2d4f43f5df", "dweb:/ipfs/QmSYuH1AhvJkPK8hNvoPqtExBcgTB42pPRHgTHkS5c5zYW"], "license": "MIT"}, "lib/setup-helpers/src/MockERC20.sol": {"keccak256": "0xc09f43f3ec348c09f8c1fae8126ae290a81ee73c92712b91a0827f304896384f", "urls": ["bzz-raw://830beab339b6210664fc0bd6df3b77a6e95f782490e9a365503d167c86f91578", "dweb:/ipfs/QmZAi8q8MwCPwtUQnKLfqATPXWYqRBHjvYufWkvEyEUYhE"], "license": "AGPL-3.0-only"}, "lib/setup-helpers/src/Panic.sol": {"keccak256": "0xcb30ff1de74d2e7dbf94d0677ba19a8dde116f6ea8bc89e4adaf176fbdd69e92", "urls": ["bzz-raw://df8616b94a669b43ecff1c80b9af050b600c9d0d9642c50f7b874a767f77c325", "dweb:/ipfs/QmdrgztAw7p4iFuuqyfReEWQNAdSBHpoGX8RULJcxU5jnd"], "license": "GPL-2.0"}, "lib/setup-helpers/src/Utils.sol": {"keccak256": "0xb2cde1a75d2aadfcbcba3626bfcfee938ba84e5cf2ce683b96a744a7f07d5b27", "urls": ["bzz-raw://c69c409f1212d5970eb70bb3ef01dd5d97621eb5034a5892afbc1218ce73f523", "dweb:/ipfs/QmZRy76ACA99dCJsQcCLXzvRLZTmWCCAUp8QjYq87QQUau"], "license": "GPL-2.0"}, "src/Counter.sol": {"keccak256": "0x8905cbcc9329398b712584998f07161f303896421c35c3fb0aa9df83cb988c7c", "urls": ["bzz-raw://9fedfe592aad3c973a0aabf7bb55a483ef610439c60faee6813bceb67acba62e", "dweb:/ipfs/QmUpzw3ZSfoqu4E51fFxWKg3RDH3aicJM9eKy5HGgR1Er2"], "license": "UNLICENSED"}, "test/recon/Setup.sol": {"keccak256": "0xb69f4c29ca764b1e75f3a9ba07517d425e73d45a4e85c20e1c11cb5a51e400f5", "urls": ["bzz-raw://082b06c1a066a3eee11d54001d5c9dad1d881187564c82bdca7f7b29d4a8b024", "dweb:/ipfs/QmakpG2babfqNRwH4UiwYTMfTnQKTTXAsgJDD5HamJtfJk"], "license": "GPL-2.0"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 40405, "contract": "test/recon/Setup.sol:Setup", "label": "_actor", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 40409, "contract": "test/recon/Setup.sol:Setup", "label": "_actors", "offset": 0, "slot": "1", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 40565, "contract": "test/recon/Setup.sol:Setup", "label": "__asset", "offset": 0, "slot": "3", "type": "t_address"}, {"astId": 40569, "contract": "test/recon/Setup.sol:Setup", "label": "_assets", "offset": 0, "slot": "4", "type": "t_struct(AddressSet)41192_storage"}, {"astId": 42682, "contract": "test/recon/Setup.sol:Setup", "label": "counter", "offset": 0, "slot": "6", "type": "t_contract(Counter)42568"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_contract(Counter)42568": {"encoding": "inplace", "label": "contract Counter", "numberOfBytes": "20"}, "t_mapping(t_bytes32,t_uint256)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(AddressSet)41192_storage": {"encoding": "inplace", "label": "struct EnumerableSet.AddressSet", "numberOfBytes": "64", "members": [{"astId": 41191, "contract": "test/recon/Setup.sol:Setup", "label": "_inner", "offset": 0, "slot": "0", "type": "t_struct(Set)40877_storage"}]}, "t_struct(Set)40877_storage": {"encoding": "inplace", "label": "struct EnumerableSet.Set", "numberOfBytes": "64", "members": [{"astId": 40872, "contract": "test/recon/Setup.sol:Setup", "label": "_values", "offset": 0, "slot": "0", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 40876, "contract": "test/recon/Setup.sol:Setup", "label": "_indexes", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_uint256)"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "test/recon/Setup.sol", "id": 42725, "exportedSymbols": {"ActorManager": [40547], "AssetManager": [40866], "BaseSetup": [94], "Counter": [42568], "Setup": [42724], "Utils": [42406], "vm": [719]}, "nodeType": "SourceUnit", "src": "36:912:37", "nodes": [{"id": 42660, "nodeType": "PragmaDirective", "src": "36:23:37", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 42662, "nodeType": "ImportDirective", "src": "77:49:37", "nodes": [], "absolutePath": "lib/chimera/src/BaseSetup.sol", "file": "@chimera/BaseSetup.sol", "nameLocation": "-1:-1:-1", "scope": 42725, "sourceUnit": 95, "symbolAliases": [{"foreign": {"id": 42661, "name": "BaseSetup", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 94, "src": "85:9:37", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42664, "nodeType": "ImportDirective", "src": "127:37:37", "nodes": [], "absolutePath": "lib/chimera/src/Hevm.sol", "file": "@chimera/Hevm.sol", "nameLocation": "-1:-1:-1", "scope": 42725, "sourceUnit": 720, "symbolAliases": [{"foreign": {"id": 42663, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "135:2:37", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42666, "nodeType": "ImportDirective", "src": "178:53:37", "nodes": [], "absolutePath": "lib/setup-helpers/src/ActorManager.sol", "file": "@recon/ActorManager.sol", "nameLocation": "-1:-1:-1", "scope": 42725, "sourceUnit": 40548, "symbolAliases": [{"foreign": {"id": 42665, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40547, "src": "186:12:37", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42668, "nodeType": "ImportDirective", "src": "232:53:37", "nodes": [], "absolutePath": "lib/setup-helpers/src/AssetManager.sol", "file": "@recon/AssetManager.sol", "nameLocation": "-1:-1:-1", "scope": 42725, "sourceUnit": 40867, "symbolAliases": [{"foreign": {"id": 42667, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40866, "src": "240:12:37", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42670, "nodeType": "ImportDirective", "src": "298:39:37", "nodes": [], "absolutePath": "lib/setup-helpers/src/Utils.sol", "file": "@recon/Utils.sol", "nameLocation": "-1:-1:-1", "scope": 42725, "sourceUnit": 42407, "symbolAliases": [{"foreign": {"id": 42669, "name": "Utils", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42406, "src": "306:5:37", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 42671, "nodeType": "ImportDirective", "src": "352:25:37", "nodes": [], "absolutePath": "src/Counter.sol", "file": "src/Counter.sol", "nameLocation": "-1:-1:-1", "scope": 42725, "sourceUnit": 42569, "symbolAliases": [], "unitAlias": ""}, {"id": 42724, "nodeType": "ContractDefinition", "src": "379:568:37", "nodes": [{"id": 42682, "nodeType": "VariableDeclaration", "src": "457:15:37", "nodes": [], "constant": false, "mutability": "mutable", "name": "counter", "nameLocation": "465:7:37", "scope": 42724, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}, "typeName": {"id": 42681, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 42680, "name": "Counter", "nameLocations": ["457:7:37"], "nodeType": "IdentifierPath", "referencedDeclaration": 42568, "src": "457:7:37"}, "referencedDeclaration": 42568, "src": "457:7:37", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}, "visibility": "internal"}, {"id": 42695, "nodeType": "FunctionDefinition", "src": "613:112:37", "nodes": [], "body": {"id": 42694, "nodeType": "Block", "src": "656:69:37", "nodes": [], "statements": [{"expression": {"id": 42692, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 42687, "name": "counter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42682, "src": "666:7:37", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [], "expression": {"argumentTypes": [], "id": 42690, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "NewExpression", "src": "676:11:37", "typeDescriptions": {"typeIdentifier": "t_function_creation_nonpayable$__$returns$_t_contract$_Counter_$42568_$", "typeString": "function () returns (contract Counter)"}, "typeName": {"id": 42689, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 42688, "name": "Counter", "nameLocations": ["680:7:37"], "nodeType": "IdentifierPath", "referencedDeclaration": 42568, "src": "680:7:37"}, "referencedDeclaration": 42568, "src": "680:7:37", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}}, "id": 42691, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "676:13:37", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}, "src": "666:23:37", "typeDescriptions": {"typeIdentifier": "t_contract$_Counter_$42568", "typeString": "contract Counter"}}, "id": 42693, "nodeType": "ExpressionStatement", "src": "666:23:37"}]}, "baseFunctions": [93], "documentation": {"id": 42683, "nodeType": "StructuredDocumentation", "src": "483:125:37", "text": "=== Setup === ///\n This contains all calls to be performed in the tester constructor, both for Echidna and Foundry"}, "implemented": true, "kind": "function", "modifiers": [], "name": "setup", "nameLocation": "622:5:37", "overrides": {"id": 42685, "nodeType": "OverrideSpecifier", "overrides": [], "src": "647:8:37"}, "parameters": {"id": 42684, "nodeType": "ParameterList", "parameters": [], "src": "627:2:37"}, "returnParameters": {"id": 42686, "nodeType": "ParameterList", "parameters": [], "src": "656:0:37"}, "scope": 42724, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 42709, "nodeType": "ModifierDefinition", "src": "796:68:37", "nodes": [], "body": {"id": 42708, "nodeType": "Block", "src": "813:51:37", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 42703, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "840:4:37", "typeDescriptions": {"typeIdentifier": "t_contract$_Setup_$42724", "typeString": "contract Setup"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_Setup_$42724", "typeString": "contract Setup"}], "id": 42702, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "832:7:37", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 42701, "name": "address", "nodeType": "ElementaryTypeName", "src": "832:7:37", "typeDescriptions": {}}}, "id": 42704, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "832:13:37", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 42698, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "823:2:37", "typeDescriptions": {"typeIdentifier": "t_contract$_IHevm_$713", "typeString": "contract IHevm"}}, "id": 42700, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "826:5:37", "memberName": "prank", "nodeType": "MemberAccess", "referencedDeclaration": 672, "src": "823:8:37", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$returns$__$", "typeString": "function (address) external"}}, "id": 42705, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "823:23:37", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42706, "nodeType": "ExpressionStatement", "src": "823:23:37"}, {"id": 42707, "nodeType": "PlaceholderStatement", "src": "856:1:37"}]}, "documentation": {"id": 42696, "nodeType": "StructuredDocumentation", "src": "731:55:37", "text": "=== MODIFIERS === ///\n Prank admin and actor"}, "name": "asAdmin", "nameLocation": "805:7:37", "parameters": {"id": 42697, "nodeType": "ParameterList", "parameters": [], "src": "813:0:37"}, "virtual": false, "visibility": "internal"}, {"id": 42723, "nodeType": "ModifierDefinition", "src": "870:75:37", "nodes": [], "body": {"id": 42722, "nodeType": "Block", "src": "887:58:37", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 42716, "name": "_getActor", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40447, "src": "914:9:37", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 42717, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "914:11:37", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 42715, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "906:7:37", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 42714, "name": "address", "nodeType": "ElementaryTypeName", "src": "906:7:37", "typeDescriptions": {}}}, "id": 42718, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "906:20:37", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 42711, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 719, "src": "897:2:37", "typeDescriptions": {"typeIdentifier": "t_contract$_IHevm_$713", "typeString": "contract IHevm"}}, "id": 42713, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "900:5:37", "memberName": "prank", "nodeType": "MemberAccess", "referencedDeclaration": 672, "src": "897:8:37", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$returns$__$", "typeString": "function (address) external"}}, "id": 42719, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "897:30:37", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42720, "nodeType": "ExpressionStatement", "src": "897:30:37"}, {"id": 42721, "nodeType": "PlaceholderStatement", "src": "937:1:37"}]}, "name": "as<PERSON><PERSON>", "nameLocation": "879:7:37", "parameters": {"id": 42710, "nodeType": "ParameterList", "parameters": [], "src": "887:0:37"}, "virtual": false, "visibility": "internal"}], "abstract": true, "baseContracts": [{"baseName": {"id": 42672, "name": "BaseSetup", "nameLocations": ["406:9:37"], "nodeType": "IdentifierPath", "referencedDeclaration": 94, "src": "406:9:37"}, "id": 42673, "nodeType": "InheritanceSpecifier", "src": "406:9:37"}, {"baseName": {"id": 42674, "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocations": ["417:12:37"], "nodeType": "IdentifierPath", "referencedDeclaration": 40547, "src": "417:12:37"}, "id": 42675, "nodeType": "InheritanceSpecifier", "src": "417:12:37"}, {"baseName": {"id": 42676, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocations": ["431:12:37"], "nodeType": "IdentifierPath", "referencedDeclaration": 40866, "src": "431:12:37"}, "id": 42677, "nodeType": "InheritanceSpecifier", "src": "431:12:37"}, {"baseName": {"id": 42678, "name": "Utils", "nameLocations": ["445:5:37"], "nodeType": "IdentifierPath", "referencedDeclaration": 42406, "src": "445:5:37"}, "id": 42679, "nodeType": "InheritanceSpecifier", "src": "445:5:37"}], "canonicalName": "Setup", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [42724, 42406, 40866, 40547, 94], "name": "Setup", "nameLocation": "397:5:37", "scope": 42725, "usedErrors": [40411, 40413, 40415, 40417, 40571, 40573, 40575], "usedEvents": []}], "license": "GPL-2.0"}, "id": 37}