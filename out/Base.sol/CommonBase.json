{"abi": [], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"stateVariables\":{\"CONSOLE\":{\"details\":\"console.sol and console2.sol work by executing a staticcall to this address. Calculated as `address(uint160(uint88(bytes11(\\\"console.log\\\"))))`.\"},\"CREATE2_FACTORY\":{\"details\":\"Used when deploying with create2. Taken from https://github.com/Arachnid/deterministic-deployment-proxy.\"},\"DEFAULT_SENDER\":{\"details\":\"The default address for tx.origin and msg.sender. Calculated as `address(uint160(uint256(keccak256(\\\"foundry default caller\\\"))))`.\"},\"DEFAULT_TEST_CONTRACT\":{\"details\":\"The address of the first contract `CREATE`d by a running test contract. When running tests, each test contract is `CREATE`d by `DEFAULT_SENDER` with nonce 1. Calculated as `VM.computeCreateAddress(VM.computeCreateAddress(DEFAULT_SENDER, 1), 1)`.\"},\"MULTICALL3_ADDRESS\":{\"details\":\"Deterministic deployment address of the Multicall3 contract. Taken from https://www.multicall3.com.\"},\"SECP256K1_ORDER\":{\"details\":\"The order of the secp256k1 curve.\"},\"VM_ADDRESS\":{\"details\":\"Cheat code address. Calculated as `address(uint160(uint256(keccak256(\\\"hevm cheat code\\\"))))`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/Base.sol\":\"CommonBase\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/Base.sol": "CommonBase"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 46, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "stdstore", "offset": 0, "slot": "0", "type": "t_struct(StdStorage)8331_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32", "base": "t_bytes32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_bytes4": {"encoding": "inplace", "label": "bytes4", "numberOfBytes": "4"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage)))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(bytes4 => mapping(bytes32 => struct FindData)))", "numberOfBytes": "32", "value": "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage))"}, "t_mapping(t_bytes32,t_struct(FindData)8306_storage)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => struct FindData)", "numberOfBytes": "32", "value": "t_struct(FindData)8306_storage"}, "t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage))": {"encoding": "mapping", "key": "t_bytes4", "label": "mapping(bytes4 => mapping(bytes32 => struct FindData))", "numberOfBytes": "32", "value": "t_mapping(t_bytes32,t_struct(FindData)8306_storage)"}, "t_struct(FindData)8306_storage": {"encoding": "inplace", "label": "struct FindData", "numberOfBytes": "128", "members": [{"astId": 8299, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "slot", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 8301, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "offsetLeft", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 8303, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "offsetRight", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 8305, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "found", "offset": 0, "slot": "3", "type": "t_bool"}]}, "t_struct(StdStorage)8331_storage": {"encoding": "inplace", "label": "struct StdStorage", "numberOfBytes": "256", "members": [{"astId": 8315, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "finds", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_mapping(t_bytes4,t_mapping(t_bytes32,t_struct(FindData)8306_storage)))"}, {"astId": 8318, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "_keys", "offset": 0, "slot": "1", "type": "t_array(t_bytes32)dyn_storage"}, {"astId": 8320, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "_sig", "offset": 0, "slot": "2", "type": "t_bytes4"}, {"astId": 8322, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "_depth", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 8324, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "_target", "offset": 0, "slot": "4", "type": "t_address"}, {"astId": 8326, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "_set", "offset": 0, "slot": "5", "type": "t_bytes32"}, {"astId": 8328, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "_enable_packed_slots", "offset": 0, "slot": "6", "type": "t_bool"}, {"astId": 8330, "contract": "lib/forge-std/src/Base.sol:CommonBase", "label": "_calldata", "offset": 0, "slot": "7", "type": "t_bytes_storage"}]}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}, "ast": {"absolutePath": "lib/forge-std/src/Base.sol", "id": 60, "exportedSymbols": {"CommonBase": [47], "ScriptBase": [59], "StdStorage": [8331], "TestBase": [50], "Vm": [18352], "VmSafe": [17281]}, "nodeType": "SourceUnit", "src": "32:2195:0", "nodes": [{"id": 1, "nodeType": "PragmaDirective", "src": "32:31:0", "nodes": [], "literals": ["solidity", ">=", "0.6", ".2", "<", "0.9", ".0"]}, {"id": 3, "nodeType": "ImportDirective", "src": "65:44:0", "nodes": [], "absolutePath": "lib/forge-std/src/StdStorage.sol", "file": "./StdStorage.sol", "nameLocation": "-1:-1:-1", "scope": 60, "sourceUnit": 10289, "symbolAliases": [{"foreign": {"id": 2, "name": "StdStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8331, "src": "73:10:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 6, "nodeType": "ImportDirective", "src": "110:36:0", "nodes": [], "absolutePath": "lib/forge-std/src/Vm.sol", "file": "./Vm.sol", "nameLocation": "-1:-1:-1", "scope": 60, "sourceUnit": 18353, "symbolAliases": [{"foreign": {"id": 4, "name": "Vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18352, "src": "118:2:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}, {"foreign": {"id": 5, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "122:6:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 47, "nodeType": "ContractDefinition", "src": "148:1927:0", "nodes": [{"id": 10, "nodeType": "VariableDeclaration", "src": "297:81:0", "nodes": [], "constant": true, "documentation": {"id": 7, "nodeType": "StructuredDocumentation", "src": "183:109:0", "text": "@dev Cheat code address.\n Calculated as `address(uint160(uint256(keccak256(\"hevm cheat code\"))))`."}, "mutability": "constant", "name": "VM_ADDRESS", "nameLocation": "323:10:0", "scope": 47, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 8, "name": "address", "nodeType": "ElementaryTypeName", "src": "297:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307837313039373039454366613931613830363236664633393839443638663637463562314444313244", "id": 9, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "336:42:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x7109709ECfa91a80626fF3989D68f67F5b1DD12D"}, "visibility": "internal"}, {"id": 14, "nodeType": "VariableDeclaration", "src": "548:78:0", "nodes": [], "constant": true, "documentation": {"id": 11, "nodeType": "StructuredDocumentation", "src": "384:159:0", "text": "@dev console.sol and console2.sol work by executing a staticcall to this address.\n Calculated as `address(uint160(uint88(bytes11(\"console.log\"))))`."}, "mutability": "constant", "name": "CONSOLE", "nameLocation": "574:7:0", "scope": 47, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12, "name": "address", "nodeType": "ElementaryTypeName", "src": "548:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307830303030303030303030303030303030303036333646366537333646366336353265366336663637", "id": 13, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "584:42:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x000000000000000000636F6e736F6c652e6c6f67"}, "visibility": "internal"}, {"id": 18, "nodeType": "VariableDeclaration", "src": "758:86:0", "nodes": [], "constant": true, "documentation": {"id": 15, "nodeType": "StructuredDocumentation", "src": "632:121:0", "text": "@dev Used when deploying with create2.\n Taken from https://github.com/Arachnid/deterministic-deployment-proxy."}, "mutability": "constant", "name": "CREATE2_FACTORY", "nameLocation": "784:15:0", "scope": 47, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 16, "name": "address", "nodeType": "ElementaryTypeName", "src": "758:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307834653539623434383437623337393537383538383932306341373846624632366330423439353643", "id": 17, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "802:42:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x4e59b44847b379578588920cA78FbF26c0B4956C"}, "visibility": "internal"}, {"id": 22, "nodeType": "VariableDeclaration", "src": "1001:85:0", "nodes": [], "constant": true, "documentation": {"id": 19, "nodeType": "StructuredDocumentation", "src": "850:146:0", "text": "@dev The default address for tx.origin and msg.sender.\n Calculated as `address(uint160(uint256(keccak256(\"foundry default caller\"))))`."}, "mutability": "constant", "name": "DEFAULT_SENDER", "nameLocation": "1027:14:0", "scope": 47, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 20, "name": "address", "nodeType": "ElementaryTypeName", "src": "1001:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307831383034633841423146313245366262663338393464343038336633336530373330396431663338", "id": 21, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1044:42:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x1804c8AB1F12E6bbf3894d4083f33e07309d1f38"}, "visibility": "internal"}, {"id": 26, "nodeType": "VariableDeclaration", "src": "1367:92:0", "nodes": [], "constant": true, "documentation": {"id": 23, "nodeType": "StructuredDocumentation", "src": "1092:270:0", "text": "@dev The address of the first contract `CREATE`d by a running test contract.\n When running tests, each test contract is `CREATE`d by `DEFAULT_SENDER` with nonce 1.\n Calculated as `VM.computeCreateAddress(VM.computeCreateAddress(DEFAULT_SENDER, 1), 1)`."}, "mutability": "constant", "name": "DEFAULT_TEST_CONTRACT", "nameLocation": "1393:21:0", "scope": 47, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 24, "name": "address", "nodeType": "ElementaryTypeName", "src": "1367:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307835363135644542373938424233453464466130313339644661316233443433334363323362373266", "id": 25, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1417:42:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x5615dEB798BB3E4dFa0139dFa1b3D433Cc23b72f"}, "visibility": "internal"}, {"id": 30, "nodeType": "VariableDeclaration", "src": "1586:89:0", "nodes": [], "constant": true, "documentation": {"id": 27, "nodeType": "StructuredDocumentation", "src": "1465:116:0", "text": "@dev Deterministic deployment address of the Multicall3 contract.\n Taken from https://www.multicall3.com."}, "mutability": "constant", "name": "MULTICALL3_ADDRESS", "nameLocation": "1612:18:0", "scope": 47, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 28, "name": "address", "nodeType": "ElementaryTypeName", "src": "1586:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307863413131626465303539373762333633313136373032383836326245326131373339373643413131", "id": 29, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1633:42:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0xcA11bde05977b3631167028862bE2a173976CA11"}, "visibility": "internal"}, {"id": 34, "nodeType": "VariableDeclaration", "src": "1728:130:0", "nodes": [], "constant": true, "documentation": {"id": 31, "nodeType": "StructuredDocumentation", "src": "1681:42:0", "text": "@dev The order of the secp256k1 curve."}, "mutability": "constant", "name": "SECP256K1_ORDER", "nameLocation": "1754:15:0", "scope": 47, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 32, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1728:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "313135373932303839323337333136313935343233353730393835303038363837393037383532383337353634323739303734393034333832363035313633313431353138313631343934333337", "id": 33, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1780:78:0", "typeDescriptions": {"typeIdentifier": "t_rational_115792089237316195423570985008687907852837564279074904382605163141518161494337_by_1", "typeString": "int_const 1157...(70 digits omitted)...4337"}, "value": "115792089237316195423570985008687907852837564279074904382605163141518161494337"}, "visibility": "internal"}, {"id": 37, "nodeType": "VariableDeclaration", "src": "1865:126:0", "nodes": [], "constant": true, "mutability": "constant", "name": "UINT256_MAX", "nameLocation": "1891:11:0", "scope": 47, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 35, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1865:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "313135373932303839323337333136313935343233353730393835303038363837393037383533323639393834363635363430353634303339343537353834303037393133313239363339393335", "id": 36, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1913:78:0", "typeDescriptions": {"typeIdentifier": "t_rational_115792089237316195423570985008687907853269984665640564039457584007913129639935_by_1", "typeString": "int_const 1157...(70 digits omitted)...9935"}, "value": "115792089237316195423570985008687907853269984665640564039457584007913129639935"}, "visibility": "internal"}, {"id": 43, "nodeType": "VariableDeclaration", "src": "1998:40:0", "nodes": [], "constant": true, "mutability": "constant", "name": "vm", "nameLocation": "2019:2:0", "scope": 47, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}, "typeName": {"id": 39, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 38, "name": "Vm", "nameLocations": ["1998:2:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 18352, "src": "1998:2:0"}, "referencedDeclaration": 18352, "src": "1998:2:0", "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "value": {"arguments": [{"id": 41, "name": "VM_ADDRESS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10, "src": "2027:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 40, "name": "Vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18352, "src": "2024:2:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Vm_$18352_$", "typeString": "type(contract Vm)"}}, "id": 42, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2024:14:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_Vm_$18352", "typeString": "contract Vm"}}, "visibility": "internal"}, {"id": 46, "nodeType": "VariableDeclaration", "src": "2044:28:0", "nodes": [], "constant": false, "mutability": "mutable", "name": "stdstore", "nameLocation": "2064:8:0", "scope": 47, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_struct$_StdStorage_$8331_storage", "typeString": "struct StdStorage"}, "typeName": {"id": 45, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 44, "name": "StdStorage", "nameLocations": ["2044:10:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 8331, "src": "2044:10:0"}, "referencedDeclaration": 8331, "src": "2044:10:0", "typeDescriptions": {"typeIdentifier": "t_struct$_StdStorage_$8331_storage_ptr", "typeString": "struct StdStorage"}}, "visibility": "internal"}], "abstract": true, "baseContracts": [], "canonicalName": "CommonBase", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [47], "name": "CommonBase", "nameLocation": "166:10:0", "scope": 60, "usedErrors": [], "usedEvents": []}, {"id": 50, "nodeType": "ContractDefinition", "src": "2077:43:0", "nodes": [], "abstract": true, "baseContracts": [{"baseName": {"id": 48, "name": "CommonBase", "nameLocations": ["2107:10:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 47, "src": "2107:10:0"}, "id": 49, "nodeType": "InheritanceSpecifier", "src": "2107:10:0"}], "canonicalName": "TestBase", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [50, 47], "name": "TestBase", "nameLocation": "2095:8:0", "scope": 60, "usedErrors": [], "usedEvents": []}, {"id": 59, "nodeType": "ContractDefinition", "src": "2122:104:0", "nodes": [{"id": 58, "nodeType": "VariableDeclaration", "src": "2171:52:0", "nodes": [], "constant": true, "mutability": "constant", "name": "vmSafe", "nameLocation": "2196:6:0", "scope": 59, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}, "typeName": {"id": 54, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 53, "name": "VmSafe", "nameLocations": ["2171:6:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 17281, "src": "2171:6:0"}, "referencedDeclaration": 17281, "src": "2171:6:0", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "value": {"arguments": [{"id": 56, "name": "VM_ADDRESS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10, "src": "2212:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 55, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "2205:6:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_VmSafe_$17281_$", "typeString": "type(contract VmSafe)"}}, "id": 57, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2205:18:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "visibility": "internal"}], "abstract": true, "baseContracts": [{"baseName": {"id": 51, "name": "CommonBase", "nameLocations": ["2154:10:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 47, "src": "2154:10:0"}, "id": 52, "nodeType": "InheritanceSpecifier", "src": "2154:10:0"}], "canonicalName": "ScriptBase", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [59, 47], "name": "ScriptBase", "nameLocation": "2140:10:0", "scope": 60, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 0}