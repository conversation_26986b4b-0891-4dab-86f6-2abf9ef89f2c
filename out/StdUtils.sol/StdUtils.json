{"abi": [], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdUtils.sol\":\"StdUtils\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdUtils.sol": "StdUtils"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/forge-std/src/StdUtils.sol", "id": 13171, "exportedSymbols": {"IMulticall3": [26635], "StdUtils": [13170], "VmSafe": [17281]}, "nodeType": "SourceUnit", "src": "32:9867:13", "nodes": [{"id": 12445, "nodeType": "PragmaDirective", "src": "32:31:13", "nodes": [], "literals": ["solidity", ">=", "0.6", ".2", "<", "0.9", ".0"]}, {"id": 12446, "nodeType": "PragmaDirective", "src": "65:33:13", "nodes": [], "literals": ["experimental", "ABIEncoderV2"]}, {"id": 12448, "nodeType": "ImportDirective", "src": "100:57:13", "nodes": [], "absolutePath": "lib/forge-std/src/interfaces/IMulticall3.sol", "file": "./interfaces/IMulticall3.sol", "nameLocation": "-1:-1:-1", "scope": 13171, "sourceUnit": 26636, "symbolAliases": [{"foreign": {"id": 12447, "name": "IMulticall3", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26635, "src": "108:11:13", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 12450, "nodeType": "ImportDirective", "src": "158:32:13", "nodes": [], "absolutePath": "lib/forge-std/src/Vm.sol", "file": "./Vm.sol", "nameLocation": "-1:-1:-1", "scope": 13171, "sourceUnit": 18353, "symbolAliases": [{"foreign": {"id": 12449, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "166:6:13", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 13170, "nodeType": "ContractDefinition", "src": "192:9706:13", "nodes": [{"id": 12456, "nodeType": "VariableDeclaration", "src": "435:96:13", "nodes": [], "constant": true, "mutability": "constant", "name": "multicall", "nameLocation": "464:9:13", "scope": 13170, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IMulticall3_$26635", "typeString": "contract IMulticall3"}, "typeName": {"id": 12452, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 12451, "name": "IMulticall3", "nameLocations": ["435:11:13"], "nodeType": "IdentifierPath", "referencedDeclaration": 26635, "src": "435:11:13"}, "referencedDeclaration": 26635, "src": "435:11:13", "typeDescriptions": {"typeIdentifier": "t_contract$_IMulticall3_$26635", "typeString": "contract IMulticall3"}}, "value": {"arguments": [{"hexValue": "307863413131626465303539373762333633313136373032383836326245326131373339373643413131", "id": 12454, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "488:42:13", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0xcA11bde05977b3631167028862bE2a173976CA11"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 12453, "name": "IMulticall3", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26635, "src": "476:11:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IMulticall3_$26635_$", "typeString": "type(contract IMulticall3)"}}, "id": 12455, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "476:55:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IMulticall3_$26635", "typeString": "contract IMulticall3"}}, "visibility": "private"}, {"id": 12473, "nodeType": "VariableDeclaration", "src": "537:92:13", "nodes": [], "constant": true, "mutability": "constant", "name": "vm", "nameLocation": "561:2:13", "scope": 13170, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}, "typeName": {"id": 12458, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 12457, "name": "VmSafe", "nameLocations": ["537:6:13"], "nodeType": "IdentifierPath", "referencedDeclaration": 17281, "src": "537:6:13"}, "referencedDeclaration": 17281, "src": "537:6:13", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "value": {"arguments": [{"arguments": [{"arguments": [{"arguments": [{"arguments": [{"hexValue": "6865766d20636865617420636f6465", "id": 12467, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "607:17:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_885cb69240a935d632d79c317109709ecfa91a80626ff3989d68f67f5b1dd12d", "typeString": "literal_string \"hevm cheat code\""}, "value": "hevm cheat code"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_885cb69240a935d632d79c317109709ecfa91a80626ff3989d68f67f5b1dd12d", "typeString": "literal_string \"hevm cheat code\""}], "id": 12466, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "597:9:13", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 12468, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "597:28:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 12465, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "589:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 12464, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "589:7:13", "typeDescriptions": {}}}, "id": 12469, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "589:37:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 12463, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "581:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 12462, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "581:7:13", "typeDescriptions": {}}}, "id": 12470, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "581:46:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 12461, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "573:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 12460, "name": "address", "nodeType": "ElementaryTypeName", "src": "573:7:13", "typeDescriptions": {}}}, "id": 12471, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "573:55:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 12459, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "566:6:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_VmSafe_$17281_$", "typeString": "type(contract VmSafe)"}}, "id": 12472, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "566:63:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "visibility": "private"}, {"id": 12476, "nodeType": "VariableDeclaration", "src": "635:86:13", "nodes": [], "constant": true, "mutability": "constant", "name": "CONSOLE2_ADDRESS", "nameLocation": "660:16:13", "scope": 13170, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12474, "name": "address", "nodeType": "ElementaryTypeName", "src": "635:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307830303030303030303030303030303030303036333646366537333646366336353265366336663637", "id": 12475, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "679:42:13", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x000000000000000000636F6e736F6c652e6c6f67"}, "visibility": "private"}, {"id": 12479, "nodeType": "VariableDeclaration", "src": "727:127:13", "nodes": [], "constant": true, "mutability": "constant", "name": "INT256_MIN_ABS", "nameLocation": "752:14:13", "scope": 13170, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12477, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "727:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "3537383936303434363138363538303937373131373835343932353034333433393533393236363334393932333332383230323832303139373238373932303033393536353634383139393638", "id": 12478, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "777:77:13", "typeDescriptions": {"typeIdentifier": "t_rational_57896044618658097711785492504343953926634992332820282019728792003956564819968_by_1", "typeString": "int_const 5789...(69 digits omitted)...9968"}, "value": "57896044618658097711785492504343953926634992332820282019728792003956564819968"}, "visibility": "private"}, {"id": 12482, "nodeType": "VariableDeclaration", "src": "860:129:13", "nodes": [], "constant": true, "mutability": "constant", "name": "SECP256K1_ORDER", "nameLocation": "885:15:13", "scope": 13170, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12480, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "860:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "313135373932303839323337333136313935343233353730393835303038363837393037383532383337353634323739303734393034333832363035313633313431353138313631343934333337", "id": 12481, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "911:78:13", "typeDescriptions": {"typeIdentifier": "t_rational_115792089237316195423570985008687907852837564279074904382605163141518161494337_by_1", "typeString": "int_const 1157...(70 digits omitted)...4337"}, "value": "115792089237316195423570985008687907852837564279074904382605163141518161494337"}, "visibility": "private"}, {"id": 12485, "nodeType": "VariableDeclaration", "src": "995:125:13", "nodes": [], "constant": true, "mutability": "constant", "name": "UINT256_MAX", "nameLocation": "1020:11:13", "scope": 13170, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12483, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "995:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "313135373932303839323337333136313935343233353730393835303038363837393037383533323639393834363635363430353634303339343537353834303037393133313239363339393335", "id": 12484, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1042:78:13", "typeDescriptions": {"typeIdentifier": "t_rational_115792089237316195423570985008687907853269984665640564039457584007913129639935_by_1", "typeString": "int_const 1157...(70 digits omitted)...9935"}, "value": "115792089237316195423570985008687907853269984665640564039457584007913129639935"}, "visibility": "private"}, {"id": 12488, "nodeType": "VariableDeclaration", "src": "1239:85:13", "nodes": [], "constant": true, "mutability": "constant", "name": "CREATE2_FACTORY", "nameLocation": "1264:15:13", "scope": 13170, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12486, "name": "address", "nodeType": "ElementaryTypeName", "src": "1239:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"hexValue": "307834653539623434383437623337393537383538383932306341373846624632366330423439353643", "id": 12487, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1282:42:13", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x4e59b44847b379578588920cA78FbF26c0B4956C"}, "visibility": "private"}, {"id": 12618, "nodeType": "FunctionDefinition", "src": "1546:1263:13", "nodes": [], "body": {"id": 12617, "nodeType": "Block", "src": "1646:1163:13", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12502, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12500, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12492, "src": "1664:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 12501, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12494, "src": "1671:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1664:10:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5374645574696c7320626f756e642875696e743235362c75696e743235362c75696e74323536293a204d6178206973206c657373207468616e206d696e2e", "id": 12503, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1676:64:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_16c21f4eccdbbd49e5dc1331f271d929c25cafaf25207892b67e15553a16c5f2", "typeString": "literal_string \"StdUtils bound(uint256,uint256,uint256): Max is less than min.\""}, "value": "StdUtils bound(uint256,uint256,uint256): Max is less than min."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_16c21f4eccdbbd49e5dc1331f271d929c25cafaf25207892b67e15553a16c5f2", "typeString": "literal_string \"StdUtils bound(uint256,uint256,uint256): Max is less than min.\""}], "id": 12499, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "1656:7:13", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 12504, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1656:85:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12505, "nodeType": "ExpressionStatement", "src": "1656:85:13"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 12512, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12508, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12506, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "1970:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 12507, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12492, "src": "1975:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1970:8:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12511, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12509, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "1982:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 12510, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12494, "src": "1987:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1982:8:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "1970:20:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 12515, "nodeType": "IfStatement", "src": "1966:34:13", "trueBody": {"expression": {"id": 12513, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "1999:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 12498, "id": 12514, "nodeType": "Return", "src": "1992:8:13"}}, {"assignments": [12517], "declarations": [{"constant": false, "id": 12517, "mutability": "mutable", "name": "size", "nameLocation": "2019:4:13", "nodeType": "VariableDeclaration", "scope": 12617, "src": "2011:12:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12516, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2011:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12523, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12522, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12520, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12518, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12494, "src": "2026:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12519, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12492, "src": "2032:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2026:9:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"hexValue": "31", "id": 12521, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2038:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "2026:13:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2011:28:13"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 12530, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12526, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12524, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "2229:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "33", "id": 12525, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2234:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_3_by_1", "typeString": "int_const 3"}, "value": "3"}, "src": "2229:6:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12529, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12527, "name": "size", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12517, "src": "2239:4:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 12528, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "2246:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2239:8:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "2229:18:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 12535, "nodeType": "IfStatement", "src": "2225:38:13", "trueBody": {"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12533, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12531, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12492, "src": "2256:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 12532, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "2262:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2256:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 12498, "id": 12534, "nodeType": "Return", "src": "2249:14:13"}}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 12546, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12540, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12536, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "2277:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12539, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"id": 12537, "name": "UINT256_MAX", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12485, "src": "2282:11:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "33", "id": 12538, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2296:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_3_by_1", "typeString": "int_const 3"}, "value": "3"}, "src": "2282:15:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2277:20:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12545, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12541, "name": "size", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12517, "src": "2301:4:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12544, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12542, "name": "UINT256_MAX", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12485, "src": "2308:11:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12543, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "2322:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2308:15:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2301:22:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "2277:46:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 12554, "nodeType": "IfStatement", "src": "2273:82:13", "trueBody": {"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12552, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12547, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12494, "src": "2332:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12550, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12548, "name": "UINT256_MAX", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12485, "src": "2339:11:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12549, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "2353:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2339:15:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 12551, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "2338:17:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2332:23:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 12498, "id": 12553, "nodeType": "Return", "src": "2325:30:13"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12557, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12555, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "2455:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 12556, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12494, "src": "2459:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2455:7:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12587, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12585, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "2634:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 12586, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12492, "src": "2638:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2634:7:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 12615, "nodeType": "IfStatement", "src": "2630:173:13", "trueBody": {"id": 12614, "nodeType": "Block", "src": "2643:160:13", "statements": [{"assignments": [12589], "declarations": [{"constant": false, "id": 12589, "mutability": "mutable", "name": "diff", "nameLocation": "2665:4:13", "nodeType": "VariableDeclaration", "scope": 12614, "src": "2657:12:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12588, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2657:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12593, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12592, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12590, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12492, "src": "2672:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12591, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "2678:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2672:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2657:22:13"}, {"assignments": [12595], "declarations": [{"constant": false, "id": 12595, "mutability": "mutable", "name": "rem", "nameLocation": "2701:3:13", "nodeType": "VariableDeclaration", "scope": 12614, "src": "2693:11:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12594, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2693:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12599, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12598, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12596, "name": "diff", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12589, "src": "2707:4:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "%", "rightExpression": {"id": 12597, "name": "size", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12517, "src": "2714:4:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2707:11:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2693:25:13"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12602, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12600, "name": "rem", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12595, "src": "2736:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 12601, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2743:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "2736:8:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 12605, "nodeType": "IfStatement", "src": "2732:24:13", "trueBody": {"expression": {"id": 12603, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12492, "src": "2753:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 12498, "id": 12604, "nodeType": "Return", "src": "2746:10:13"}}, {"expression": {"id": 12612, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 12606, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12497, "src": "2770:6:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12611, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12609, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12607, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12494, "src": "2779:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12608, "name": "rem", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12595, "src": "2785:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2779:9:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"hexValue": "31", "id": 12610, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2791:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "2779:13:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2770:22:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 12613, "nodeType": "ExpressionStatement", "src": "2770:22:13"}]}}, "id": 12616, "nodeType": "IfStatement", "src": "2451:352:13", "trueBody": {"id": 12584, "nodeType": "Block", "src": "2464:160:13", "statements": [{"assignments": [12559], "declarations": [{"constant": false, "id": 12559, "mutability": "mutable", "name": "diff", "nameLocation": "2486:4:13", "nodeType": "VariableDeclaration", "scope": 12584, "src": "2478:12:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12558, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2478:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12563, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12562, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12560, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12490, "src": "2493:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12561, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12494, "src": "2497:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2493:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2478:22:13"}, {"assignments": [12565], "declarations": [{"constant": false, "id": 12565, "mutability": "mutable", "name": "rem", "nameLocation": "2522:3:13", "nodeType": "VariableDeclaration", "scope": 12584, "src": "2514:11:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12564, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2514:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12569, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12568, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12566, "name": "diff", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12559, "src": "2528:4:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "%", "rightExpression": {"id": 12567, "name": "size", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12517, "src": "2535:4:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2528:11:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2514:25:13"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12572, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12570, "name": "rem", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12565, "src": "2557:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 12571, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2564:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "2557:8:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 12575, "nodeType": "IfStatement", "src": "2553:24:13", "trueBody": {"expression": {"id": 12573, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12494, "src": "2574:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 12498, "id": 12574, "nodeType": "Return", "src": "2567:10:13"}}, {"expression": {"id": 12582, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 12576, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12497, "src": "2591:6:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12581, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12579, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12577, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12492, "src": "2600:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 12578, "name": "rem", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12565, "src": "2606:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2600:9:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "31", "id": 12580, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2612:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "2600:13:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2591:22:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 12583, "nodeType": "ExpressionStatement", "src": "2591:22:13"}]}}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_bound", "nameLocation": "1555:6:13", "parameters": {"id": 12495, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12490, "mutability": "mutable", "name": "x", "nameLocation": "1570:1:13", "nodeType": "VariableDeclaration", "scope": 12618, "src": "1562:9:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12489, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1562:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 12492, "mutability": "mutable", "name": "min", "nameLocation": "1581:3:13", "nodeType": "VariableDeclaration", "scope": 12618, "src": "1573:11:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12491, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1573:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 12494, "mutability": "mutable", "name": "max", "nameLocation": "1594:3:13", "nodeType": "VariableDeclaration", "scope": 12618, "src": "1586:11:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12493, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1586:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1561:37:13"}, "returnParameters": {"id": 12498, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12497, "mutability": "mutable", "name": "result", "nameLocation": "1638:6:13", "nodeType": "VariableDeclaration", "scope": 12618, "src": "1630:14:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12496, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1630:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1629:16:13"}, "scope": 13170, "stateMutability": "pure", "virtual": true, "visibility": "internal"}, {"id": 12643, "nodeType": "FunctionDefinition", "src": "2815:199:13", "nodes": [], "body": {"id": 12642, "nodeType": "Block", "src": "2914:100:13", "nodes": [], "statements": [{"expression": {"id": 12635, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 12629, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12627, "src": "2924:6:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 12631, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12620, "src": "2940:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 12632, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12622, "src": "2943:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 12633, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12624, "src": "2948:3:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 12630, "name": "_bound", "nodeType": "Identifier", "overloadedDeclarations": [12618, 12765], "referencedDeclaration": 12618, "src": "2933:6:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$_t_uint256_$returns$_t_uint256_$", "typeString": "function (uint256,uint256,uint256) pure returns (uint256)"}}, "id": 12634, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2933:19:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2924:28:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 12636, "nodeType": "ExpressionStatement", "src": "2924:28:13"}, {"expression": {"arguments": [{"hexValue": "426f756e6420726573756c74", "id": 12638, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2984:14:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_81387530263afdcc351da6c89e6a10d49583b5beb1fecaddd0371443f1cd026f", "typeString": "literal_string \"Bound result\""}, "value": "Bound result"}, {"id": 12639, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12627, "src": "3000:6:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_81387530263afdcc351da6c89e6a10d49583b5beb1fecaddd0371443f1cd026f", "typeString": "literal_string \"Bound result\""}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 12637, "name": "console2_log_StdUtils", "nodeType": "Identifier", "overloadedDeclarations": [13135, 13152, 13169], "referencedDeclaration": 13152, "src": "2962:21:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_uint256_$returns$__$", "typeString": "function (string memory,uint256) pure"}}, "id": 12640, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2962:45:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12641, "nodeType": "ExpressionStatement", "src": "2962:45:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "bound", "nameLocation": "2824:5:13", "parameters": {"id": 12625, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12620, "mutability": "mutable", "name": "x", "nameLocation": "2838:1:13", "nodeType": "VariableDeclaration", "scope": 12643, "src": "2830:9:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12619, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2830:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 12622, "mutability": "mutable", "name": "min", "nameLocation": "2849:3:13", "nodeType": "VariableDeclaration", "scope": 12643, "src": "2841:11:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12621, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2841:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 12624, "mutability": "mutable", "name": "max", "nameLocation": "2862:3:13", "nodeType": "VariableDeclaration", "scope": 12643, "src": "2854:11:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12623, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2854:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2829:37:13"}, "returnParameters": {"id": 12628, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12627, "mutability": "mutable", "name": "result", "nameLocation": "2906:6:13", "nodeType": "VariableDeclaration", "scope": 12643, "src": "2898:14:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12626, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2898:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2897:16:13"}, "scope": 13170, "stateMutability": "pure", "virtual": true, "visibility": "internal"}, {"id": 12765, "nodeType": "FunctionDefinition", "src": "3020:1145:13", "nodes": [], "body": {"id": 12764, "nodeType": "Block", "src": "3116:1049:13", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 12657, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12655, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12647, "src": "3134:3:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 12656, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12649, "src": "3141:3:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "3134:10:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5374645574696c7320626f756e6428696e743235362c696e743235362c696e74323536293a204d6178206973206c657373207468616e206d696e2e", "id": 12658, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3146:61:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0fd736be0f0596d130ab62399a2ecc4855db1de6a3b01be590df45aa0de73247", "typeString": "literal_string \"StdUtils bound(int256,int256,int256): Max is less than min.\""}, "value": "StdUtils bound(int256,int256,int256): Max is less than min."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_0fd736be0f0596d130ab62399a2ecc4855db1de6a3b01be590df45aa0de73247", "typeString": "literal_string \"StdUtils bound(int256,int256,int256): Max is less than min.\""}], "id": 12654, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "3126:7:13", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 12659, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3126:82:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12660, "nodeType": "ExpressionStatement", "src": "3126:82:13"}, {"assignments": [12662], "declarations": [{"constant": false, "id": 12662, "mutability": "mutable", "name": "_x", "nameLocation": "3644:2:13", "nodeType": "VariableDeclaration", "scope": 12764, "src": "3636:10:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12661, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3636:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12684, "initialValue": {"condition": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 12665, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12663, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12645, "src": "3649:1:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "30", "id": 12664, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3653:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3649:5:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12681, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [{"id": 12678, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12645, "src": "3703:1:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 12677, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3695:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 12676, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3695:7:13", "typeDescriptions": {}}}, "id": 12679, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3695:10:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 12680, "name": "INT256_MIN_ABS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12479, "src": "3708:14:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3695:27:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 12682, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3694:29:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 12683, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "3649:74:13", "trueExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12674, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12672, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12666, "name": "INT256_MIN_ABS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12479, "src": "3658:14:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12671, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "~", "prefix": true, "src": "3675:11:13", "subExpression": {"arguments": [{"id": 12669, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12645, "src": "3684:1:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 12668, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3676:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 12667, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3676:7:13", "typeDescriptions": {}}}, "id": 12670, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3676:10:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3658:28:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "31", "id": 12673, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3689:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "3658:32:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 12675, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3657:34:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3636:87:13"}, {"assignments": [12686], "declarations": [{"constant": false, "id": 12686, "mutability": "mutable", "name": "_min", "nameLocation": "3741:4:13", "nodeType": "VariableDeclaration", "scope": 12764, "src": "3733:12:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12685, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3733:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12708, "initialValue": {"condition": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 12689, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12687, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12647, "src": "3748:3:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "30", "id": 12688, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3754:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3748:7:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12705, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [{"id": 12702, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12647, "src": "3806:3:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 12701, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3798:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 12700, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3798:7:13", "typeDescriptions": {}}}, "id": 12703, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3798:12:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 12704, "name": "INT256_MIN_ABS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12479, "src": "3813:14:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3798:29:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 12706, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3797:31:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 12707, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "3748:80:13", "trueExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12698, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12696, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12690, "name": "INT256_MIN_ABS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12479, "src": "3759:14:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12695, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "~", "prefix": true, "src": "3776:13:13", "subExpression": {"arguments": [{"id": 12693, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12647, "src": "3785:3:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 12692, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3777:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 12691, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3777:7:13", "typeDescriptions": {}}}, "id": 12694, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3777:12:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3759:30:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "31", "id": 12697, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3792:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "3759:34:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 12699, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3758:36:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3733:95:13"}, {"assignments": [12710], "declarations": [{"constant": false, "id": 12710, "mutability": "mutable", "name": "_max", "nameLocation": "3846:4:13", "nodeType": "VariableDeclaration", "scope": 12764, "src": "3838:12:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12709, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3838:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12732, "initialValue": {"condition": {"commonType": {"typeIdentifier": "t_int256", "typeString": "int256"}, "id": 12713, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12711, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12649, "src": "3853:3:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "30", "id": 12712, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3859:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3853:7:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12729, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [{"id": 12726, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12649, "src": "3911:3:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 12725, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3903:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 12724, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3903:7:13", "typeDescriptions": {}}}, "id": 12727, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3903:12:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 12728, "name": "INT256_MIN_ABS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12479, "src": "3918:14:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3903:29:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 12730, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3902:31:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 12731, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "3853:80:13", "trueExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12722, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12720, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12714, "name": "INT256_MIN_ABS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12479, "src": "3864:14:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12719, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "~", "prefix": true, "src": "3881:13:13", "subExpression": {"arguments": [{"id": 12717, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12649, "src": "3890:3:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 12716, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3882:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 12715, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3882:7:13", "typeDescriptions": {}}}, "id": 12718, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3882:12:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3864:30:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "31", "id": 12721, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3897:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "3864:34:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 12723, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3863:36:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3838:95:13"}, {"assignments": [12734], "declarations": [{"constant": false, "id": 12734, "mutability": "mutable", "name": "y", "nameLocation": "3952:1:13", "nodeType": "VariableDeclaration", "scope": 12764, "src": "3944:9:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12733, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3944:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12740, "initialValue": {"arguments": [{"id": 12736, "name": "_x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12662, "src": "3963:2:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 12737, "name": "_min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12686, "src": "3967:4:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 12738, "name": "_max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12710, "src": "3973:4:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 12735, "name": "_bound", "nodeType": "Identifier", "overloadedDeclarations": [12618, 12765], "referencedDeclaration": 12618, "src": "3956:6:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$_t_uint256_$returns$_t_uint256_$", "typeString": "function (uint256,uint256,uint256) pure returns (uint256)"}}, "id": 12739, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3956:22:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3944:34:13"}, {"expression": {"id": 12762, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 12741, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12652, "src": "4066:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12744, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12742, "name": "y", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12734, "src": "4075:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 12743, "name": "INT256_MIN_ABS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12479, "src": "4079:14:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4075:18:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12759, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12757, "name": "y", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12734, "src": "4139:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12758, "name": "INT256_MIN_ABS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12479, "src": "4143:14:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4139:18:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 12756, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4132:6:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_int256_$", "typeString": "type(int256)"}, "typeName": {"id": 12755, "name": "int256", "nodeType": "ElementaryTypeName", "src": "4132:6:13", "typeDescriptions": {}}}, "id": 12760, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4132:26:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 12761, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "4075:83:13", "trueExpression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12753, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12751, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "~", "prefix": true, "src": "4103:21:13", "subExpression": {"components": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12749, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12747, "name": "INT256_MIN_ABS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12479, "src": "4105:14:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 12748, "name": "y", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12734, "src": "4122:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4105:18:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 12750, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4104:20:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"hexValue": "31", "id": 12752, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4127:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "4103:25:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 12746, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4096:6:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_int256_$", "typeString": "type(int256)"}, "typeName": {"id": 12745, "name": "int256", "nodeType": "ElementaryTypeName", "src": "4096:6:13", "typeDescriptions": {}}}, "id": 12754, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4096:33:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "4066:92:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 12763, "nodeType": "ExpressionStatement", "src": "4066:92:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_bound", "nameLocation": "3029:6:13", "parameters": {"id": 12650, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12645, "mutability": "mutable", "name": "x", "nameLocation": "3043:1:13", "nodeType": "VariableDeclaration", "scope": 12765, "src": "3036:8:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 12644, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3036:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}, {"constant": false, "id": 12647, "mutability": "mutable", "name": "min", "nameLocation": "3053:3:13", "nodeType": "VariableDeclaration", "scope": 12765, "src": "3046:10:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 12646, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3046:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}, {"constant": false, "id": 12649, "mutability": "mutable", "name": "max", "nameLocation": "3065:3:13", "nodeType": "VariableDeclaration", "scope": 12765, "src": "3058:10:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 12648, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3058:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "3035:34:13"}, "returnParameters": {"id": 12653, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12652, "mutability": "mutable", "name": "result", "nameLocation": "3108:6:13", "nodeType": "VariableDeclaration", "scope": 12765, "src": "3101:13:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 12651, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3101:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "3100:15:13"}, "scope": 13170, "stateMutability": "pure", "virtual": true, "visibility": "internal"}, {"id": 12793, "nodeType": "FunctionDefinition", "src": "4171:208:13", "nodes": [], "body": {"id": 12792, "nodeType": "Block", "src": "4266:113:13", "nodes": [], "statements": [{"expression": {"id": 12782, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 12776, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12774, "src": "4276:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 12778, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12767, "src": "4292:1:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, {"id": 12779, "name": "min", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12769, "src": "4295:3:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, {"id": 12780, "name": "max", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12771, "src": "4300:3:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}, {"typeIdentifier": "t_int256", "typeString": "int256"}, {"typeIdentifier": "t_int256", "typeString": "int256"}], "id": 12777, "name": "_bound", "nodeType": "Identifier", "overloadedDeclarations": [12618, 12765], "referencedDeclaration": 12765, "src": "4285:6:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_int256_$_t_int256_$_t_int256_$returns$_t_int256_$", "typeString": "function (int256,int256,int256) pure returns (int256)"}}, "id": 12781, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4285:19:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "src": "4276:28:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "id": 12783, "nodeType": "ExpressionStatement", "src": "4276:28:13"}, {"expression": {"arguments": [{"hexValue": "426f756e6420726573756c74", "id": 12785, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4336:14:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_81387530263afdcc351da6c89e6a10d49583b5beb1fecaddd0371443f1cd026f", "typeString": "literal_string \"Bound result\""}, "value": "Bound result"}, {"arguments": [{"id": 12788, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12774, "src": "4364:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 12786, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12473, "src": "4352:2:13", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12787, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4355:8:13", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "4352:11:13", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 12789, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4352:19:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_81387530263afdcc351da6c89e6a10d49583b5beb1fecaddd0371443f1cd026f", "typeString": "literal_string \"Bound result\""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 12784, "name": "console2_log_StdUtils", "nodeType": "Identifier", "overloadedDeclarations": [13135, 13152, 13169], "referencedDeclaration": 13169, "src": "4314:21:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory,string memory) pure"}}, "id": 12790, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4314:58:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12791, "nodeType": "ExpressionStatement", "src": "4314:58:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "bound", "nameLocation": "4180:5:13", "parameters": {"id": 12772, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12767, "mutability": "mutable", "name": "x", "nameLocation": "4193:1:13", "nodeType": "VariableDeclaration", "scope": 12793, "src": "4186:8:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 12766, "name": "int256", "nodeType": "ElementaryTypeName", "src": "4186:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}, {"constant": false, "id": 12769, "mutability": "mutable", "name": "min", "nameLocation": "4203:3:13", "nodeType": "VariableDeclaration", "scope": 12793, "src": "4196:10:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 12768, "name": "int256", "nodeType": "ElementaryTypeName", "src": "4196:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}, {"constant": false, "id": 12771, "mutability": "mutable", "name": "max", "nameLocation": "4215:3:13", "nodeType": "VariableDeclaration", "scope": 12793, "src": "4208:10:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 12770, "name": "int256", "nodeType": "ElementaryTypeName", "src": "4208:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "4185:34:13"}, "returnParameters": {"id": 12775, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12774, "mutability": "mutable", "name": "result", "nameLocation": "4258:6:13", "nodeType": "VariableDeclaration", "scope": 12793, "src": "4251:13:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 12773, "name": "int256", "nodeType": "ElementaryTypeName", "src": "4251:6:13", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "4250:15:13"}, "scope": 13170, "stateMutability": "pure", "virtual": true, "visibility": "internal"}, {"id": 12811, "nodeType": "FunctionDefinition", "src": "4385:160:13", "nodes": [], "body": {"id": 12810, "nodeType": "Block", "src": "4477:68:13", "nodes": [], "statements": [{"expression": {"id": 12808, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 12800, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12798, "src": "4487:6:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 12802, "name": "privateKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12795, "src": "4503:10:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "31", "id": 12803, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4515:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12806, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"id": 12804, "name": "SECP256K1_ORDER", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12482, "src": "4518:15:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "31", "id": 12805, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4536:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "4518:19:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 12801, "name": "_bound", "nodeType": "Identifier", "overloadedDeclarations": [12618, 12765], "referencedDeclaration": 12618, "src": "4496:6:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$_t_uint256_$returns$_t_uint256_$", "typeString": "function (uint256,uint256,uint256) pure returns (uint256)"}}, "id": 12807, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4496:42:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4487:51:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 12809, "nodeType": "ExpressionStatement", "src": "4487:51:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "boundPrivateKey", "nameLocation": "4394:15:13", "parameters": {"id": 12796, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12795, "mutability": "mutable", "name": "privateKey", "nameLocation": "4418:10:13", "nodeType": "VariableDeclaration", "scope": 12811, "src": "4410:18:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12794, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4410:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4409:20:13"}, "returnParameters": {"id": 12799, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12798, "mutability": "mutable", "name": "result", "nameLocation": "4469:6:13", "nodeType": "VariableDeclaration", "scope": 12811, "src": "4461:14:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12797, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4461:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4460:16:13"}, "scope": 13170, "stateMutability": "pure", "virtual": true, "visibility": "internal"}, {"id": 12845, "nodeType": "FunctionDefinition", "src": "4551:259:13", "nodes": [], "body": {"id": 12844, "nodeType": "Block", "src": "4628:182:13", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12822, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 12819, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12813, "src": "4646:1:13", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 12820, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4648:6:13", "memberName": "length", "nodeType": "MemberAccess", "src": "4646:8:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "3332", "id": 12821, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4658:2:13", "typeDescriptions": {"typeIdentifier": "t_rational_32_by_1", "typeString": "int_const 32"}, "value": "32"}, "src": "4646:14:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5374645574696c73206279746573546f55696e74286279746573293a204279746573206c656e67746820657863656564732033322e", "id": 12823, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4662:55:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_15bc16f8ce72c26d4fbf91f28e31f7cbe900e6386b04cf90f353bff0f5b2da88", "typeString": "literal_string \"StdUtils bytesToUint(bytes): Bytes length exceeds 32.\""}, "value": "StdUtils bytesToUint(bytes): Bytes length exceeds 32."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_15bc16f8ce72c26d4fbf91f28e31f7cbe900e6386b04cf90f353bff0f5b2da88", "typeString": "literal_string \"StdUtils bytesToUint(bytes): Bytes length exceeds 32.\""}], "id": 12818, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "4638:7:13", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 12824, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4638:80:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12825, "nodeType": "ExpressionStatement", "src": "4638:80:13"}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12835, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"hexValue": "3332", "id": 12832, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4773:2:13", "typeDescriptions": {"typeIdentifier": "t_rational_32_by_1", "typeString": "int_const 32"}, "value": "32"}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"expression": {"id": 12833, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12813, "src": "4778:1:13", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 12834, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4780:6:13", "memberName": "length", "nodeType": "MemberAccess", "src": "4778:8:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4773:13:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 12831, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "4763:9:13", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_bytes_memory_ptr_$", "typeString": "function (uint256) pure returns (bytes memory)"}, "typeName": {"id": 12830, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4767:5:13", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}}, "id": 12836, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4763:24:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"id": 12837, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12813, "src": "4789:1:13", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 12828, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "4746:3:13", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 12829, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4750:12:13", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "4746:16:13", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 12838, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4746:45:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 12840, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4794:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 12839, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4794:7:13", "typeDescriptions": {}}}], "id": 12841, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4793:9:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}], "expression": {"id": 12826, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "4735:3:13", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 12827, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4739:6:13", "memberName": "decode", "nodeType": "MemberAccess", "src": "4735:10:13", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 12842, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4735:68:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 12817, "id": 12843, "nodeType": "Return", "src": "4728:75:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "bytesToUint", "nameLocation": "4560:11:13", "parameters": {"id": 12814, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12813, "mutability": "mutable", "name": "b", "nameLocation": "4585:1:13", "nodeType": "VariableDeclaration", "scope": 12845, "src": "4572:14:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 12812, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4572:5:13", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "4571:16:13"}, "returnParameters": {"id": 12817, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12816, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12845, "src": "4619:7:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12815, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4619:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4618:9:13"}, "scope": 13170, "stateMutability": "pure", "virtual": true, "visibility": "internal"}, {"id": 12866, "nodeType": "FunctionDefinition", "src": "4919:281:13", "nodes": [], "body": {"id": 12865, "nodeType": "Block", "src": "5022:178:13", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "636f6d707574654372656174654164647265737320697320646570726563617465642e20506c656173652075736520766d2e636f6d707574654372656174654164647265737320696e73746561642e", "id": 12856, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5054:81:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_7269602979e7efe0cf2435fd830893923e4ac6d12c1b6834ce0c3cdb39769052", "typeString": "literal_string \"computeCreateAddress is deprecated. Please use vm.computeCreateAddress instead.\""}, "value": "computeCreateAddress is deprecated. Please use vm.computeCreateAddress instead."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_7269602979e7efe0cf2435fd830893923e4ac6d12c1b6834ce0c3cdb39769052", "typeString": "literal_string \"computeCreateAddress is deprecated. Please use vm.computeCreateAddress instead.\""}], "id": 12855, "name": "console2_log_StdUtils", "nodeType": "Identifier", "overloadedDeclarations": [13135, 13152, 13169], "referencedDeclaration": 13135, "src": "5032:21:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory) pure"}}, "id": 12857, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5032:104:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12858, "nodeType": "ExpressionStatement", "src": "5032:104:13"}, {"expression": {"arguments": [{"id": 12861, "name": "deployer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12848, "src": "5177:8:13", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 12862, "name": "nonce", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12850, "src": "5187:5:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 12859, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12473, "src": "5153:2:13", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12860, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5156:20:13", "memberName": "computeCreate<PERSON>ddress", "nodeType": "MemberAccess", "referencedDeclaration": 17050, "src": "5153:23:13", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$_t_uint256_$returns$_t_address_$", "typeString": "function (address,uint256) pure external returns (address)"}}, "id": 12863, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5153:40:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 12854, "id": 12864, "nodeType": "Return", "src": "5146:47:13"}]}, "documentation": {"id": 12846, "nodeType": "StructuredDocumentation", "src": "4816:98:13", "text": "@dev Compute the address a contract will be deployed at for a given deployer address and nonce"}, "implemented": true, "kind": "function", "modifiers": [], "name": "computeCreate<PERSON>ddress", "nameLocation": "4928:20:13", "parameters": {"id": 12851, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12848, "mutability": "mutable", "name": "deployer", "nameLocation": "4957:8:13", "nodeType": "VariableDeclaration", "scope": 12866, "src": "4949:16:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12847, "name": "address", "nodeType": "ElementaryTypeName", "src": "4949:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 12850, "mutability": "mutable", "name": "nonce", "nameLocation": "4975:5:13", "nodeType": "VariableDeclaration", "scope": 12866, "src": "4967:13:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12849, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4967:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4948:33:13"}, "returnParameters": {"id": 12854, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12853, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12866, "src": "5013:7:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12852, "name": "address", "nodeType": "ElementaryTypeName", "src": "5013:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5012:9:13"}, "scope": 13170, "stateMutability": "pure", "virtual": true, "visibility": "internal"}, {"id": 12889, "nodeType": "FunctionDefinition", "src": "5206:355:13", "nodes": [], "body": {"id": 12888, "nodeType": "Block", "src": "5367:194:13", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "636f6d70757465437265617465324164647265737320697320646570726563617465642e20506c656173652075736520766d2e636f6d70757465437265617465324164647265737320696e73746561642e", "id": 12878, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5399:83:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_5abd736618531808b1ea1a17b1144019e81db11351698dec9b35fe8aba205691", "typeString": "literal_string \"computeCreate2Address is deprecated. Please use vm.computeCreate2Address instead.\""}, "value": "computeCreate2Address is deprecated. Please use vm.computeCreate2Address instead."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_5abd736618531808b1ea1a17b1144019e81db11351698dec9b35fe8aba205691", "typeString": "literal_string \"computeCreate2Address is deprecated. Please use vm.computeCreate2Address instead.\""}], "id": 12877, "name": "console2_log_StdUtils", "nodeType": "Identifier", "overloadedDeclarations": [13135, 13152, 13169], "referencedDeclaration": 13135, "src": "5377:21:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory) pure"}}, "id": 12879, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5377:106:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12880, "nodeType": "ExpressionStatement", "src": "5377:106:13"}, {"expression": {"arguments": [{"id": 12883, "name": "salt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12868, "src": "5525:4:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 12884, "name": "initcodeHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12870, "src": "5531:12:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 12885, "name": "deployer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12872, "src": "5545:8:13", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 12881, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12473, "src": "5500:2:13", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12882, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5503:21:13", "memberName": "computeCreate2Address", "nodeType": "MemberAccess", "referencedDeclaration": 17030, "src": "5500:24:13", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$_t_bytes32_$_t_address_$returns$_t_address_$", "typeString": "function (bytes32,bytes32,address) pure external returns (address)"}}, "id": 12886, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5500:54:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 12876, "id": 12887, "nodeType": "Return", "src": "5493:61:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "computeCreate2Address", "nameLocation": "5215:21:13", "parameters": {"id": 12873, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12868, "mutability": "mutable", "name": "salt", "nameLocation": "5245:4:13", "nodeType": "VariableDeclaration", "scope": 12889, "src": "5237:12:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 12867, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5237:7:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 12870, "mutability": "mutable", "name": "initcodeHash", "nameLocation": "5259:12:13", "nodeType": "VariableDeclaration", "scope": 12889, "src": "5251:20:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 12869, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5251:7:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 12872, "mutability": "mutable", "name": "deployer", "nameLocation": "5281:8:13", "nodeType": "VariableDeclaration", "scope": 12889, "src": "5273:16:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12871, "name": "address", "nodeType": "ElementaryTypeName", "src": "5273:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5236:54:13"}, "returnParameters": {"id": 12876, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12875, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12889, "src": "5354:7:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12874, "name": "address", "nodeType": "ElementaryTypeName", "src": "5354:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5353:9:13"}, "scope": 13170, "stateMutability": "pure", "virtual": true, "visibility": "internal"}, {"id": 12910, "nodeType": "FunctionDefinition", "src": "5670:283:13", "nodes": [], "body": {"id": 12909, "nodeType": "Block", "src": "5769:184:13", "nodes": [], "statements": [{"expression": {"arguments": [{"hexValue": "636f6d70757465437265617465324164647265737320697320646570726563617465642e20506c656173652075736520766d2e636f6d70757465437265617465324164647265737320696e73746561642e", "id": 12900, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5801:83:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_5abd736618531808b1ea1a17b1144019e81db11351698dec9b35fe8aba205691", "typeString": "literal_string \"computeCreate2Address is deprecated. Please use vm.computeCreate2Address instead.\""}, "value": "computeCreate2Address is deprecated. Please use vm.computeCreate2Address instead."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_5abd736618531808b1ea1a17b1144019e81db11351698dec9b35fe8aba205691", "typeString": "literal_string \"computeCreate2Address is deprecated. Please use vm.computeCreate2Address instead.\""}], "id": 12899, "name": "console2_log_StdUtils", "nodeType": "Identifier", "overloadedDeclarations": [13135, 13152, 13169], "referencedDeclaration": 13135, "src": "5779:21:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory) pure"}}, "id": 12901, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5779:106:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12902, "nodeType": "ExpressionStatement", "src": "5779:106:13"}, {"expression": {"arguments": [{"id": 12905, "name": "salt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12892, "src": "5927:4:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 12906, "name": "initCodeHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12894, "src": "5933:12:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 12903, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12473, "src": "5902:2:13", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 12904, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5905:21:13", "memberName": "computeCreate2Address", "nodeType": "MemberAccess", "referencedDeclaration": 17040, "src": "5902:24:13", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$_t_bytes32_$returns$_t_address_$", "typeString": "function (bytes32,bytes32) pure external returns (address)"}}, "id": 12907, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5902:44:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 12898, "id": 12908, "nodeType": "Return", "src": "5895:51:13"}]}, "documentation": {"id": 12890, "nodeType": "StructuredDocumentation", "src": "5567:98:13", "text": "@dev returns the address of a contract created with CREATE2 using the default CREATE2 deployer"}, "implemented": true, "kind": "function", "modifiers": [], "name": "computeCreate2Address", "nameLocation": "5679:21:13", "parameters": {"id": 12895, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12892, "mutability": "mutable", "name": "salt", "nameLocation": "5709:4:13", "nodeType": "VariableDeclaration", "scope": 12910, "src": "5701:12:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 12891, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5701:7:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 12894, "mutability": "mutable", "name": "initCodeHash", "nameLocation": "5723:12:13", "nodeType": "VariableDeclaration", "scope": 12910, "src": "5715:20:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 12893, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5715:7:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "5700:36:13"}, "returnParameters": {"id": 12898, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12897, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12910, "src": "5760:7:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12896, "name": "address", "nodeType": "ElementaryTypeName", "src": "5760:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5759:9:13"}, "scope": 13170, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 12924, "nodeType": "FunctionDefinition", "src": "6177:135:13", "nodes": [], "body": {"id": 12923, "nodeType": "Block", "src": "6258:54:13", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 12919, "name": "creationCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12913, "src": "6288:12:13", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"hexValue": "", "id": 12920, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6302:2:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}, "value": ""}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}], "id": 12918, "name": "hashInitCode", "nodeType": "Identifier", "overloadedDeclarations": [12924, 12943], "referencedDeclaration": 12943, "src": "6275:12:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory,bytes memory) pure returns (bytes32)"}}, "id": 12921, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6275:30:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 12917, "id": 12922, "nodeType": "Return", "src": "6268:37:13"}]}, "documentation": {"id": 12911, "nodeType": "StructuredDocumentation", "src": "5959:213:13", "text": "@dev returns the hash of the init code (creation code + no args) used in CREATE2 with no constructor arguments\n @param creationCode the creation code of a contract C, as returned by type(C).creationCode"}, "implemented": true, "kind": "function", "modifiers": [], "name": "hashInitCode", "nameLocation": "6186:12:13", "parameters": {"id": 12914, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12913, "mutability": "mutable", "name": "creationCode", "nameLocation": "6212:12:13", "nodeType": "VariableDeclaration", "scope": 12924, "src": "6199:25:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 12912, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6199:5:13", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "6198:27:13"}, "returnParameters": {"id": 12917, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12916, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12924, "src": "6249:7:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 12915, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6249:7:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "6248:9:13"}, "scope": 13170, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 12943, "nodeType": "FunctionDefinition", "src": "6585:171:13", "nodes": [], "body": {"id": 12942, "nodeType": "Block", "src": "6685:71:13", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 12937, "name": "creationCode", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12927, "src": "6729:12:13", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"id": 12938, "name": "args", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12929, "src": "6743:4:13", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 12935, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "6712:3:13", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 12936, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "6716:12:13", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "6712:16:13", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 12939, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6712:36:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 12934, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "6702:9:13", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 12940, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6702:47:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 12933, "id": 12941, "nodeType": "Return", "src": "6695:54:13"}]}, "documentation": {"id": 12925, "nodeType": "StructuredDocumentation", "src": "6318:262:13", "text": "@dev returns the hash of the init code (creation code + ABI-encoded args) used in CREATE2\n @param creationCode the creation code of a contract C, as returned by type(C).creationCode\n @param args the ABI-encoded arguments to the constructor of C"}, "implemented": true, "kind": "function", "modifiers": [], "name": "hashInitCode", "nameLocation": "6594:12:13", "parameters": {"id": 12930, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12927, "mutability": "mutable", "name": "creationCode", "nameLocation": "6620:12:13", "nodeType": "VariableDeclaration", "scope": 12943, "src": "6607:25:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 12926, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6607:5:13", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}, {"constant": false, "id": 12929, "mutability": "mutable", "name": "args", "nameLocation": "6647:4:13", "nodeType": "VariableDeclaration", "scope": 12943, "src": "6634:17:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 12928, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6634:5:13", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "6606:46:13"}, "returnParameters": {"id": 12933, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12932, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 12943, "src": "6676:7:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 12931, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6676:7:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "6675:9:13"}, "scope": 13170, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 13057, "nodeType": "FunctionDefinition", "src": "6867:1124:13", "nodes": [], "body": {"id": 13056, "nodeType": "Block", "src": "7017:974:13", "nodes": [], "statements": [{"assignments": [12955], "declarations": [{"constant": false, "id": 12955, "mutability": "mutable", "name": "tokenCodeSize", "nameLocation": "7035:13:13", "nodeType": "VariableDeclaration", "scope": 13056, "src": "7027:21:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12954, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7027:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12956, "nodeType": "VariableDeclarationStatement", "src": "7027:21:13"}, {"AST": {"nativeSrc": "7067:59:13", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7067:59:13", "statements": [{"nativeSrc": "7081:35:13", "nodeType": "YulAssignment", "src": "7081:35:13", "value": {"arguments": [{"name": "token", "nativeSrc": "7110:5:13", "nodeType": "YulIdentifier", "src": "7110:5:13"}], "functionName": {"name": "extcodesize", "nativeSrc": "7098:11:13", "nodeType": "YulIdentifier", "src": "7098:11:13"}, "nativeSrc": "7098:18:13", "nodeType": "YulFunctionCall", "src": "7098:18:13"}, "variableNames": [{"name": "tokenCodeSize", "nativeSrc": "7081:13:13", "nodeType": "YulIdentifier", "src": "7081:13:13"}]}]}, "evmVersion": "prague", "externalReferences": [{"declaration": 12945, "isOffset": false, "isSlot": false, "src": "7110:5:13", "valueSize": 1}, {"declaration": 12955, "isOffset": false, "isSlot": false, "src": "7081:13:13", "valueSize": 1}], "id": 12957, "nodeType": "InlineAssembly", "src": "7058:68:13"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12961, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12959, "name": "tokenCodeSize", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12955, "src": "7143:13:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 12960, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7159:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "7143:17:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5374645574696c7320676574546f6b656e42616c616e63657328616464726573732c616464726573735b5d293a20546f6b656e2061646472657373206973206e6f74206120636f6e74726163742e", "id": 12962, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "7162:80:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e1cfd8db054d28c838f90dd4aca17e279a1b93ad4e1fab977a6ceb92cad655fe", "typeString": "literal_string \"StdUtils getTokenBalances(address,address[]): Token address is not a contract.\""}, "value": "StdUtils getTokenBalances(address,address[]): Token address is not a contract."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_e1cfd8db054d28c838f90dd4aca17e279a1b93ad4e1fab977a6ceb92cad655fe", "typeString": "literal_string \"StdUtils getTokenBalances(address,address[]): Token address is not a contract.\""}], "id": 12958, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "7135:7:13", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 12963, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7135:108:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 12964, "nodeType": "ExpressionStatement", "src": "7135:108:13"}, {"assignments": [12966], "declarations": [{"constant": false, "id": 12966, "mutability": "mutable", "name": "length", "nameLocation": "7318:6:13", "nodeType": "VariableDeclaration", "scope": 13056, "src": "7310:14:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12965, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7310:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12969, "initialValue": {"expression": {"id": 12967, "name": "addresses", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12948, "src": "7327:9:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 12968, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7337:6:13", "memberName": "length", "nodeType": "MemberAccess", "src": "7327:16:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "7310:33:13"}, {"assignments": [12975], "declarations": [{"constant": false, "id": 12975, "mutability": "mutable", "name": "calls", "nameLocation": "7379:5:13", "nodeType": "VariableDeclaration", "scope": 13056, "src": "7353:31:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_memory_ptr_$dyn_memory_ptr", "typeString": "struct IMulticall3.Call[]"}, "typeName": {"baseType": {"id": 12973, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 12972, "name": "IMulticall3.Call", "nameLocations": ["7353:11:13", "7365:4:13"], "nodeType": "IdentifierPath", "referencedDeclaration": 26480, "src": "7353:16:13"}, "referencedDeclaration": 26480, "src": "7353:16:13", "typeDescriptions": {"typeIdentifier": "t_struct$_Call_$26480_storage_ptr", "typeString": "struct IMulticall3.Call"}}, "id": 12974, "nodeType": "ArrayTypeName", "src": "7353:18:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Call[]"}}, "visibility": "internal"}], "id": 12982, "initialValue": {"arguments": [{"id": 12980, "name": "length", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12966, "src": "7410:6:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 12979, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "7387:22:13", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_struct$_Call_$26480_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (struct IMulticall3.Call memory[] memory)"}, "typeName": {"baseType": {"id": 12977, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 12976, "name": "IMulticall3.Call", "nameLocations": ["7391:11:13", "7403:4:13"], "nodeType": "IdentifierPath", "referencedDeclaration": 26480, "src": "7391:16:13"}, "referencedDeclaration": 26480, "src": "7391:16:13", "typeDescriptions": {"typeIdentifier": "t_struct$_Call_$26480_storage_ptr", "typeString": "struct IMulticall3.Call"}}, "id": 12978, "nodeType": "ArrayTypeName", "src": "7391:18:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_storage_$dyn_storage_ptr", "typeString": "struct IMulticall3.Call[]"}}}, "id": 12981, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7387:30:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_memory_ptr_$dyn_memory_ptr", "typeString": "struct IMulticall3.Call memory[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "7353:64:13"}, {"body": {"id": 13010, "nodeType": "Block", "src": "7464:189:13", "statements": [{"expression": {"id": 13008, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 12993, "name": "calls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12975, "src": "7536:5:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_memory_ptr_$dyn_memory_ptr", "typeString": "struct IMulticall3.Call memory[] memory"}}, "id": 12995, "indexExpression": {"id": 12994, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12984, "src": "7542:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "7536:8:13", "typeDescriptions": {"typeIdentifier": "t_struct$_Call_$26480_memory_ptr", "typeString": "struct IMulticall3.Call memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 12998, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12945, "src": "7573:5:13", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"arguments": [{"hexValue": "30783730613038323331", "id": 13001, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7613:10:13", "typeDescriptions": {"typeIdentifier": "t_rational_1889567281_by_1", "typeString": "int_const 1889567281"}, "value": "0x70a08231"}, {"components": [{"baseExpression": {"id": 13002, "name": "addresses", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12948, "src": "7626:9:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 13004, "indexExpression": {"id": 13003, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12984, "src": "7636:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7626:12:13", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 13005, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "7625:14:13", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_1889567281_by_1", "typeString": "int_const 1889567281"}, {"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 12999, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "7590:3:13", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 13000, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "7594:18:13", "memberName": "encodeWithSelector", "nodeType": "MemberAccess", "src": "7590:22:13", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithselector_pure$_t_bytes4_$returns$_t_bytes_memory_ptr_$", "typeString": "function (bytes4) pure returns (bytes memory)"}}, "id": 13006, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7590:50:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 12996, "name": "IMulticall3", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26635, "src": "7547:11:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IMulticall3_$26635_$", "typeString": "type(contract IMulticall3)"}}, "id": 12997, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7559:4:13", "memberName": "Call", "nodeType": "MemberAccess", "referencedDeclaration": 26480, "src": "7547:16:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Call_$26480_storage_ptr_$", "typeString": "type(struct IMulticall3.Call storage pointer)"}}, "id": 13007, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["7565:6:13", "7580:8:13"], "names": ["target", "callData"], "nodeType": "FunctionCall", "src": "7547:95:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Call_$26480_memory_ptr", "typeString": "struct IMulticall3.Call memory"}}, "src": "7536:106:13", "typeDescriptions": {"typeIdentifier": "t_struct$_Call_$26480_memory_ptr", "typeString": "struct IMulticall3.Call memory"}}, "id": 13009, "nodeType": "ExpressionStatement", "src": "7536:106:13"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 12989, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 12987, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12984, "src": "7447:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 12988, "name": "length", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12966, "src": "7451:6:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7447:10:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 13011, "initializationExpression": {"assignments": [12984], "declarations": [{"constant": false, "id": 12984, "mutability": "mutable", "name": "i", "nameLocation": "7440:1:13", "nodeType": "VariableDeclaration", "scope": 13011, "src": "7432:9:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 12983, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7432:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 12986, "initialValue": {"hexValue": "30", "id": 12985, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7444:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "7432:13:13"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 12991, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": true, "src": "7459:3:13", "subExpression": {"id": 12990, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12984, "src": "7461:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 12992, "nodeType": "ExpressionStatement", "src": "7459:3:13"}, "nodeType": "ForStatement", "src": "7427:226:13"}, {"assignments": [null, 13016], "declarations": [null, {"constant": false, "id": 13016, "mutability": "mutable", "name": "returnData", "nameLocation": "7717:10:13", "nodeType": "VariableDeclaration", "scope": 13056, "src": "7702:25:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 13014, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "7702:5:13", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 13015, "nodeType": "ArrayTypeName", "src": "7702:7:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "id": 13021, "initialValue": {"arguments": [{"id": 13019, "name": "calls", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12975, "src": "7751:5:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Call_$26480_memory_ptr_$dyn_memory_ptr", "typeString": "struct IMulticall3.Call memory[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_array$_t_struct$_Call_$26480_memory_ptr_$dyn_memory_ptr", "typeString": "struct IMulticall3.Call memory[] memory"}], "expression": {"id": 13017, "name": "multicall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12456, "src": "7731:9:13", "typeDescriptions": {"typeIdentifier": "t_contract$_IMulticall3_$26635", "typeString": "contract IMulticall3"}}, "id": 13018, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7741:9:13", "memberName": "aggregate", "nodeType": "MemberAccess", "referencedDeclaration": 26513, "src": "7731:19:13", "typeDescriptions": {"typeIdentifier": "t_function_external_payable$_t_array$_t_struct$_Call_$26480_memory_ptr_$dyn_memory_ptr_$returns$_t_uint256_$_t_array$_t_bytes_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (struct IMulticall3.Call memory[] memory) payable external returns (uint256,bytes memory[] memory)"}}, "id": 13020, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7731:26:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint256_$_t_array$_t_bytes_memory_ptr_$dyn_memory_ptr_$", "typeString": "tuple(uint256,bytes memory[] memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "7699:58:13"}, {"expression": {"id": 13028, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 13022, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12952, "src": "7831:8:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 13026, "name": "length", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12966, "src": "7856:6:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 13025, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "7842:13:13", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (uint256[] memory)"}, "typeName": {"baseType": {"id": 13023, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7846:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 13024, "nodeType": "ArrayTypeName", "src": "7846:9:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}}, "id": 13027, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7842:21:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "src": "7831:32:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "id": 13029, "nodeType": "ExpressionStatement", "src": "7831:32:13"}, {"body": {"id": 13054, "nodeType": "Block", "src": "7910:75:13", "statements": [{"expression": {"id": 13052, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 13040, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12952, "src": "7924:8:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "id": 13042, "indexExpression": {"id": 13041, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13031, "src": "7933:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "7924:11:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"baseExpression": {"id": 13045, "name": "returnData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13016, "src": "7949:10:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_memory_ptr_$dyn_memory_ptr", "typeString": "bytes memory[] memory"}}, "id": 13047, "indexExpression": {"id": 13046, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13031, "src": "7960:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7949:13:13", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 13049, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7965:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 13048, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7965:7:13", "typeDescriptions": {}}}], "id": 13050, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "7964:9:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}], "expression": {"id": 13043, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "7938:3:13", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 13044, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "7942:6:13", "memberName": "decode", "nodeType": "MemberAccess", "src": "7938:10:13", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 13051, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7938:36:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7924:50:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 13053, "nodeType": "ExpressionStatement", "src": "7924:50:13"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 13036, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 13034, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13031, "src": "7893:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 13035, "name": "length", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12966, "src": "7897:6:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7893:10:13", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 13055, "initializationExpression": {"assignments": [13031], "declarations": [{"constant": false, "id": 13031, "mutability": "mutable", "name": "i", "nameLocation": "7886:1:13", "nodeType": "VariableDeclaration", "scope": 13055, "src": "7878:9:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 13030, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7878:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 13033, "initialValue": {"hexValue": "30", "id": 13032, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7890:1:13", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "7878:13:13"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 13038, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": true, "src": "7905:3:13", "subExpression": {"id": 13037, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13031, "src": "7907:1:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 13039, "nodeType": "ExpressionStatement", "src": "7905:3:13"}, "nodeType": "ForStatement", "src": "7873:112:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "getTokenBalances", "nameLocation": "6876:16:13", "parameters": {"id": 12949, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12945, "mutability": "mutable", "name": "token", "nameLocation": "6901:5:13", "nodeType": "VariableDeclaration", "scope": 13057, "src": "6893:13:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12944, "name": "address", "nodeType": "ElementaryTypeName", "src": "6893:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 12948, "mutability": "mutable", "name": "addresses", "nameLocation": "6925:9:13", "nodeType": "VariableDeclaration", "scope": 13057, "src": "6908:26:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 12946, "name": "address", "nodeType": "ElementaryTypeName", "src": "6908:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 12947, "nodeType": "ArrayTypeName", "src": "6908:9:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "6892:43:13"}, "returnParameters": {"id": 12953, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12952, "mutability": "mutable", "name": "balances", "nameLocation": "7003:8:13", "nodeType": "VariableDeclaration", "scope": 13057, "src": "6986:25:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 12950, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6986:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 12951, "nodeType": "ArrayTypeName", "src": "6986:9:13", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "6985:27:13"}, "scope": 13170, "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"id": 13076, "nodeType": "FunctionDefinition", "src": "8211:144:13", "nodes": [], "body": {"id": 13075, "nodeType": "Block", "src": "8294:61:13", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"arguments": [{"id": 13070, "name": "bytesValue", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13059, "src": "8335:10:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 13069, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8327:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 13068, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8327:7:13", "typeDescriptions": {}}}, "id": 13071, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8327:19:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 13067, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8319:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 13066, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "8319:7:13", "typeDescriptions": {}}}, "id": 13072, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8319:28:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 13065, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8311:7:13", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 13064, "name": "address", "nodeType": "ElementaryTypeName", "src": "8311:7:13", "typeDescriptions": {}}}, "id": 13073, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8311:37:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 13063, "id": 13074, "nodeType": "Return", "src": "8304:44:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "addressFromLast20Bytes", "nameLocation": "8220:22:13", "parameters": {"id": 13060, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13059, "mutability": "mutable", "name": "bytesValue", "nameLocation": "8251:10:13", "nodeType": "VariableDeclaration", "scope": 13076, "src": "8243:18:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 13058, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8243:7:13", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "8242:20:13"}, "returnParameters": {"id": 13063, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13062, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 13076, "src": "8285:7:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 13061, "name": "address", "nodeType": "ElementaryTypeName", "src": "8285:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "8284:9:13"}, "scope": 13170, "stateMutability": "pure", "virtual": false, "visibility": "private"}, {"id": 13093, "nodeType": "FunctionDefinition", "src": "8650:235:13", "nodes": [], "body": {"id": 13092, "nodeType": "Block", "src": "8823:62:13", "nodes": [], "statements": [{"AST": {"nativeSrc": "8842:37:13", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8842:37:13", "statements": [{"nativeSrc": "8856:13:13", "nodeType": "YulAssignment", "src": "8856:13:13", "value": {"name": "fnIn", "nativeSrc": "8865:4:13", "nodeType": "YulIdentifier", "src": "8865:4:13"}, "variableNames": [{"name": "fnOut", "nativeSrc": "8856:5:13", "nodeType": "YulIdentifier", "src": "8856:5:13"}]}]}, "evmVersion": "prague", "externalReferences": [{"declaration": 13082, "isOffset": false, "isSlot": false, "src": "8865:4:13", "valueSize": 1}, {"declaration": 13089, "isOffset": false, "isSlot": false, "src": "8856:5:13", "valueSize": 1}], "id": 13091, "nodeType": "InlineAssembly", "src": "8833:46:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_castLogPayloadViewToPure", "nameLocation": "8659:25:13", "parameters": {"id": 13083, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13082, "mutability": "mutable", "name": "fnIn", "nameLocation": "8722:4:13", "nodeType": "VariableDeclaration", "scope": 13093, "src": "8685:41:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes) view"}, "typeName": {"id": 13081, "nodeType": "FunctionTypeName", "parameterTypes": {"id": 13079, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13078, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 13081, "src": "8694:12:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 13077, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "8694:5:13", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "8693:14:13"}, "returnParameterTypes": {"id": 13080, "nodeType": "ParameterList", "parameters": [], "src": "8722:0:13"}, "src": "8685:41:13", "stateMutability": "view", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes) view"}, "visibility": "internal"}, "visibility": "internal"}], "src": "8684:43:13"}, "returnParameters": {"id": 13090, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13089, "mutability": "mutable", "name": "fnOut", "nameLocation": "8812:5:13", "nodeType": "VariableDeclaration", "scope": 13093, "src": "8775:42:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes) pure"}, "typeName": {"id": 13088, "nodeType": "FunctionTypeName", "parameterTypes": {"id": 13086, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13085, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 13088, "src": "8784:12:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 13084, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "8784:5:13", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "8783:14:13"}, "returnParameterTypes": {"id": 13087, "nodeType": "ParameterList", "parameters": [], "src": "8812:0:13"}, "src": "8775:42:13", "stateMutability": "pure", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes) pure"}, "visibility": "internal"}, "visibility": "internal"}], "src": "8774:44:13"}, "scope": 13170, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 13105, "nodeType": "FunctionDefinition", "src": "8891:133:13", "nodes": [], "body": {"id": 13104, "nodeType": "Block", "src": "8952:72:13", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 13101, "name": "payload", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13095, "src": "9009:7:13", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "arguments": [{"id": 13099, "name": "_sendLogPayloadView", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13121, "src": "8988:19:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) view"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_function_internal_view$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) view"}], "id": 13098, "name": "_castLogPayloadViewToPure", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13093, "src": "8962:25:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_function_internal_view$_t_bytes_memory_ptr_$returns$__$_$returns$_t_function_internal_pure$_t_bytes_memory_ptr_$returns$__$_$", "typeString": "function (function (bytes memory) view) pure returns (function (bytes memory) pure)"}}, "id": 13100, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8962:46:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) pure"}}, "id": 13102, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8962:55:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 13103, "nodeType": "ExpressionStatement", "src": "8962:55:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_sendLogPayload", "nameLocation": "8900:15:13", "parameters": {"id": 13096, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13095, "mutability": "mutable", "name": "payload", "nameLocation": "8929:7:13", "nodeType": "VariableDeclaration", "scope": 13105, "src": "8916:20:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 13094, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "8916:5:13", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "8915:22:13"}, "returnParameters": {"id": 13097, "nodeType": "ParameterList", "parameters": [], "src": "8952:0:13"}, "scope": 13170, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 13121, "nodeType": "FunctionDefinition", "src": "9030:381:13", "nodes": [], "body": {"id": 13120, "nodeType": "Block", "src": "9094:317:13", "nodes": [], "statements": [{"assignments": [13111], "declarations": [{"constant": false, "id": 13111, "mutability": "mutable", "name": "payloadLength", "nameLocation": "9112:13:13", "nodeType": "VariableDeclaration", "scope": 13120, "src": "9104:21:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 13110, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "9104:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 13114, "initialValue": {"expression": {"id": 13112, "name": "payload", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13107, "src": "9128:7:13", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 13113, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9136:6:13", "memberName": "length", "nodeType": "MemberAccess", "src": "9128:14:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "9104:38:13"}, {"assignments": [13116], "declarations": [{"constant": false, "id": 13116, "mutability": "mutable", "name": "console<PERSON>dd<PERSON>", "nameLocation": "9160:14:13", "nodeType": "VariableDeclaration", "scope": 13120, "src": "9152:22:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 13115, "name": "address", "nodeType": "ElementaryTypeName", "src": "9152:7:13", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 13118, "initialValue": {"id": 13117, "name": "CONSOLE2_ADDRESS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 12476, "src": "9177:16:13", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "9152:41:13"}, {"AST": {"nativeSrc": "9255:150:13", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9255:150:13", "statements": [{"nativeSrc": "9269:36:13", "nodeType": "YulVariableDeclaration", "src": "9269:36:13", "value": {"arguments": [{"name": "payload", "nativeSrc": "9293:7:13", "nodeType": "YulIdentifier", "src": "9293:7:13"}, {"kind": "number", "nativeSrc": "9302:2:13", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9302:2:13", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "9289:3:13", "nodeType": "YulIdentifier", "src": "9289:3:13"}, "nativeSrc": "9289:16:13", "nodeType": "YulFunctionCall", "src": "9289:16:13"}, "variables": [{"name": "payloadStart", "nativeSrc": "9273:12:13", "nodeType": "YulTypedName", "src": "9273:12:13", "type": ""}]}, {"nativeSrc": "9318:77:13", "nodeType": "YulVariableDeclaration", "src": "9318:77:13", "value": {"arguments": [{"arguments": [], "functionName": {"name": "gas", "nativeSrc": "9338:3:13", "nodeType": "YulIdentifier", "src": "9338:3:13"}, "nativeSrc": "9338:5:13", "nodeType": "YulFunctionCall", "src": "9338:5:13"}, {"name": "console<PERSON>dd<PERSON>", "nativeSrc": "9345:14:13", "nodeType": "YulIdentifier", "src": "9345:14:13"}, {"name": "payloadStart", "nativeSrc": "9361:12:13", "nodeType": "YulIdentifier", "src": "9361:12:13"}, {"name": "payloadLength", "nativeSrc": "9375:13:13", "nodeType": "YulIdentifier", "src": "9375:13:13"}, {"kind": "number", "nativeSrc": "9390:1:13", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9390:1:13", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "9393:1:13", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9393:1:13", "type": "", "value": "0"}], "functionName": {"name": "staticcall", "nativeSrc": "9327:10:13", "nodeType": "YulIdentifier", "src": "9327:10:13"}, "nativeSrc": "9327:68:13", "nodeType": "YulFunctionCall", "src": "9327:68:13"}, "variables": [{"name": "r", "nativeSrc": "9322:1:13", "nodeType": "YulTypedName", "src": "9322:1:13", "type": ""}]}]}, "documentation": "@solidity memory-safe-assembly", "evmVersion": "prague", "externalReferences": [{"declaration": 13116, "isOffset": false, "isSlot": false, "src": "9345:14:13", "valueSize": 1}, {"declaration": 13107, "isOffset": false, "isSlot": false, "src": "9293:7:13", "valueSize": 1}, {"declaration": 13111, "isOffset": false, "isSlot": false, "src": "9375:13:13", "valueSize": 1}], "id": 13119, "nodeType": "InlineAssembly", "src": "9246:159:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "_sendLogPayloadView", "nameLocation": "9039:19:13", "parameters": {"id": 13108, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13107, "mutability": "mutable", "name": "payload", "nameLocation": "9072:7:13", "nodeType": "VariableDeclaration", "scope": 13121, "src": "9059:20:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 13106, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "9059:5:13", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "9058:22:13"}, "returnParameters": {"id": 13109, "nodeType": "ParameterList", "parameters": [], "src": "9094:0:13"}, "scope": 13170, "stateMutability": "view", "virtual": false, "visibility": "private"}, {"id": 13135, "nodeType": "FunctionDefinition", "src": "9417:138:13", "nodes": [], "body": {"id": 13134, "nodeType": "Block", "src": "9479:76:13", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"hexValue": "6c6f6728737472696e6729", "id": 13129, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9529:13:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_41304facd9323d75b11bcdd609cb38effffdb05710f7caf0e9b16c6d9d709f50", "typeString": "literal_string \"log(string)\""}, "value": "log(string)"}, {"id": 13130, "name": "p0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13123, "src": "9544:2:13", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_41304facd9323d75b11bcdd609cb38effffdb05710f7caf0e9b16c6d9d709f50", "typeString": "literal_string \"log(string)\""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 13127, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "9505:3:13", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 13128, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "9509:19:13", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "9505:23:13", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 13131, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9505:42:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 13126, "name": "_sendLogPayload", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13105, "src": "9489:15:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) pure"}}, "id": 13132, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9489:59:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 13133, "nodeType": "ExpressionStatement", "src": "9489:59:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "console2_log_StdUtils", "nameLocation": "9426:21:13", "parameters": {"id": 13124, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13123, "mutability": "mutable", "name": "p0", "nameLocation": "9462:2:13", "nodeType": "VariableDeclaration", "scope": 13135, "src": "9448:16:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 13122, "name": "string", "nodeType": "ElementaryTypeName", "src": "9448:6:13", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9447:18:13"}, "returnParameters": {"id": 13125, "nodeType": "ParameterList", "parameters": [], "src": "9479:0:13"}, "scope": 13170, "stateMutability": "pure", "virtual": false, "visibility": "private"}, {"id": 13152, "nodeType": "FunctionDefinition", "src": "9561:162:13", "nodes": [], "body": {"id": 13151, "nodeType": "Block", "src": "9635:88:13", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"hexValue": "6c6f6728737472696e672c75696e7432353629", "id": 13145, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9685:21:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_b60e72ccf6d57ab53eb84d7e94a9545806ed7f93c4d5673f11a64f03471e584e", "typeString": "literal_string \"log(string,uint256)\""}, "value": "log(string,uint256)"}, {"id": 13146, "name": "p0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13137, "src": "9708:2:13", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 13147, "name": "p1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13139, "src": "9712:2:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_b60e72ccf6d57ab53eb84d7e94a9545806ed7f93c4d5673f11a64f03471e584e", "typeString": "literal_string \"log(string,uint256)\""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 13143, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "9661:3:13", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 13144, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "9665:19:13", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "9661:23:13", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 13148, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9661:54:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 13142, "name": "_sendLogPayload", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13105, "src": "9645:15:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) pure"}}, "id": 13149, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9645:71:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 13150, "nodeType": "ExpressionStatement", "src": "9645:71:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "console2_log_StdUtils", "nameLocation": "9570:21:13", "parameters": {"id": 13140, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13137, "mutability": "mutable", "name": "p0", "nameLocation": "9606:2:13", "nodeType": "VariableDeclaration", "scope": 13152, "src": "9592:16:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 13136, "name": "string", "nodeType": "ElementaryTypeName", "src": "9592:6:13", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 13139, "mutability": "mutable", "name": "p1", "nameLocation": "9618:2:13", "nodeType": "VariableDeclaration", "scope": 13152, "src": "9610:10:13", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 13138, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "9610:7:13", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "9591:30:13"}, "returnParameters": {"id": 13141, "nodeType": "ParameterList", "parameters": [], "src": "9635:0:13"}, "scope": 13170, "stateMutability": "pure", "virtual": false, "visibility": "private"}, {"id": 13169, "nodeType": "FunctionDefinition", "src": "9729:167:13", "nodes": [], "body": {"id": 13168, "nodeType": "Block", "src": "9809:87:13", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"hexValue": "6c6f6728737472696e672c737472696e6729", "id": 13162, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9859:20:13", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4b5c4277d556d03fbf5ee534fba41dc13982b44f2fa82f1d48fdd8b5b5b692ac", "typeString": "literal_string \"log(string,string)\""}, "value": "log(string,string)"}, {"id": 13163, "name": "p0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13154, "src": "9881:2:13", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 13164, "name": "p1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13156, "src": "9885:2:13", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_4b5c4277d556d03fbf5ee534fba41dc13982b44f2fa82f1d48fdd8b5b5b692ac", "typeString": "literal_string \"log(string,string)\""}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 13160, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "9835:3:13", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 13161, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "9839:19:13", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "9835:23:13", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 13165, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9835:53:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 13159, "name": "_sendLogPayload", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13105, "src": "9819:15:13", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (bytes memory) pure"}}, "id": 13166, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9819:70:13", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 13167, "nodeType": "ExpressionStatement", "src": "9819:70:13"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "console2_log_StdUtils", "nameLocation": "9738:21:13", "parameters": {"id": 13157, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 13154, "mutability": "mutable", "name": "p0", "nameLocation": "9774:2:13", "nodeType": "VariableDeclaration", "scope": 13169, "src": "9760:16:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 13153, "name": "string", "nodeType": "ElementaryTypeName", "src": "9760:6:13", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 13156, "mutability": "mutable", "name": "p1", "nameLocation": "9792:2:13", "nodeType": "VariableDeclaration", "scope": 13169, "src": "9778:16:13", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 13155, "name": "string", "nodeType": "ElementaryTypeName", "src": "9778:6:13", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9759:36:13"}, "returnParameters": {"id": 13158, "nodeType": "ParameterList", "parameters": [], "src": "9809:0:13"}, "scope": 13170, "stateMutability": "pure", "virtual": false, "visibility": "private"}], "abstract": true, "baseContracts": [], "canonicalName": "StdUtils", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [13170], "name": "StdUtils", "nameLocation": "210:8:13", "scope": 13171, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 13}