{"abi": [], "bytecode": {"object": "0x6055604b600b8282823980515f1a607314603f577f4e487b71000000000000000000000000000000000000000000000000000000005f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220ac76aa841dcf9101bb7395a69840979bc7520ddd384063185fc6e27b8bcf7b0364736f6c634300081d0033", "sourceMap": "100:10361:11:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220ac76aa841dcf9101bb7395a69840979bc7520ddd384063185fc6e27b8bcf7b0364736f6c634300081d0033", "sourceMap": "100:10361:11:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdStyle.sol\":\"StdStyle\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdStyle.sol": "StdStyle"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/forge-std/src/StdStyle.sol", "id": 11500, "exportedSymbols": {"StdStyle": [11499], "VmSafe": [17281]}, "nodeType": "SourceUnit", "src": "32:10430:11", "nodes": [{"id": 10290, "nodeType": "PragmaDirective", "src": "32:32:11", "nodes": [], "literals": ["solidity", ">=", "0.4", ".22", "<", "0.9", ".0"]}, {"id": 10292, "nodeType": "ImportDirective", "src": "66:32:11", "nodes": [], "absolutePath": "lib/forge-std/src/Vm.sol", "file": "./Vm.sol", "nameLocation": "-1:-1:-1", "scope": 11500, "sourceUnit": 18353, "symbolAliases": [{"foreign": {"id": 10291, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "74:6:11", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 11499, "nodeType": "ContractDefinition", "src": "100:10361:11", "nodes": [{"id": 10309, "nodeType": "VariableDeclaration", "src": "123:92:11", "nodes": [], "constant": true, "mutability": "constant", "name": "vm", "nameLocation": "147:2:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}, "typeName": {"id": 10294, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 10293, "name": "VmSafe", "nameLocations": ["123:6:11"], "nodeType": "IdentifierPath", "referencedDeclaration": 17281, "src": "123:6:11"}, "referencedDeclaration": 17281, "src": "123:6:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "value": {"arguments": [{"arguments": [{"arguments": [{"arguments": [{"arguments": [{"hexValue": "6865766d20636865617420636f6465", "id": 10303, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "193:17:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_885cb69240a935d632d79c317109709ecfa91a80626ff3989d68f67f5b1dd12d", "typeString": "literal_string \"hevm cheat code\""}, "value": "hevm cheat code"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_885cb69240a935d632d79c317109709ecfa91a80626ff3989d68f67f5b1dd12d", "typeString": "literal_string \"hevm cheat code\""}], "id": 10302, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "183:9:11", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 10304, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "183:28:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 10301, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "175:7:11", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 10300, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "175:7:11", "typeDescriptions": {}}}, "id": 10305, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "175:37:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 10299, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "167:7:11", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 10298, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "167:7:11", "typeDescriptions": {}}}, "id": 10306, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "167:46:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 10297, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "159:7:11", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 10296, "name": "address", "nodeType": "ElementaryTypeName", "src": "159:7:11", "typeDescriptions": {}}}, "id": 10307, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "159:55:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 10295, "name": "VmSafe", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17281, "src": "152:6:11", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_VmSafe_$17281_$", "typeString": "type(contract VmSafe)"}}, "id": 10308, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "152:63:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "visibility": "private"}, {"id": 10312, "nodeType": "VariableDeclaration", "src": "222:34:11", "nodes": [], "constant": true, "mutability": "constant", "name": "RED", "nameLocation": "238:3:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10310, "name": "string", "nodeType": "ElementaryTypeName", "src": "222:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b39316d", "id": 10311, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "244:12:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e865f62b1188865fdbe08fdbe8546369f5c78a8f677a27514aadc154b4263c18", "typeString": "literal_string hex\"1b5b39316d\""}, "value": "\u001b[91m"}, "visibility": "internal"}, {"id": 10315, "nodeType": "VariableDeclaration", "src": "262:36:11", "nodes": [], "constant": true, "mutability": "constant", "name": "GREEN", "nameLocation": "278:5:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10313, "name": "string", "nodeType": "ElementaryTypeName", "src": "262:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b39326d", "id": 10314, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "286:12:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_250c6c79af2fd59b948ba31b977e669524bbf27faba009961b135f1635e1e32b", "typeString": "literal_string hex\"1b5b39326d\""}, "value": "\u001b[92m"}, "visibility": "internal"}, {"id": 10318, "nodeType": "VariableDeclaration", "src": "304:37:11", "nodes": [], "constant": true, "mutability": "constant", "name": "YELLOW", "nameLocation": "320:6:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10316, "name": "string", "nodeType": "ElementaryTypeName", "src": "304:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b39336d", "id": 10317, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "329:12:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_801b445b8c4f71d86cf740b8fd9f85e172d35421144725dd58fed362de2e6cf5", "typeString": "literal_string hex\"1b5b39336d\""}, "value": "\u001b[93m"}, "visibility": "internal"}, {"id": 10321, "nodeType": "VariableDeclaration", "src": "347:35:11", "nodes": [], "constant": true, "mutability": "constant", "name": "BLUE", "nameLocation": "363:4:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10319, "name": "string", "nodeType": "ElementaryTypeName", "src": "347:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b39346d", "id": 10320, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "370:12:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_66ecf2e89553c52e360a74737e5e4e3d15e4d08217c17497ca50efb90c95d593", "typeString": "literal_string hex\"1b5b39346d\""}, "value": "\u001b[94m"}, "visibility": "internal"}, {"id": 10324, "nodeType": "VariableDeclaration", "src": "388:38:11", "nodes": [], "constant": true, "mutability": "constant", "name": "MAGENTA", "nameLocation": "404:7:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10322, "name": "string", "nodeType": "ElementaryTypeName", "src": "388:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b39356d", "id": 10323, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "414:12:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_b81cf1fd9bcd2b49f14457c6168490b5ff507c85cc3778934da8235d270d6b5b", "typeString": "literal_string hex\"1b5b39356d\""}, "value": "\u001b[95m"}, "visibility": "internal"}, {"id": 10327, "nodeType": "VariableDeclaration", "src": "432:35:11", "nodes": [], "constant": true, "mutability": "constant", "name": "CYAN", "nameLocation": "448:4:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10325, "name": "string", "nodeType": "ElementaryTypeName", "src": "432:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b39366d", "id": 10326, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "455:12:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f73c74e3aa04446480bd18c1b857a46321f6d66d2bfb703d52333566c779447b", "typeString": "literal_string hex\"1b5b39366d\""}, "value": "\u001b[96m"}, "visibility": "internal"}, {"id": 10330, "nodeType": "VariableDeclaration", "src": "473:34:11", "nodes": [], "constant": true, "mutability": "constant", "name": "BOLD", "nameLocation": "489:4:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10328, "name": "string", "nodeType": "ElementaryTypeName", "src": "473:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b316d", "id": 10329, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "496:11:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_b25b1471c5d449346ad6b37b501b2d5911d6e2bad13ad71d09cdfa3d3b140a17", "typeString": "literal_string hex\"1b5b316d\""}, "value": "\u001b[1m"}, "visibility": "internal"}, {"id": 10333, "nodeType": "VariableDeclaration", "src": "513:33:11", "nodes": [], "constant": true, "mutability": "constant", "name": "DIM", "nameLocation": "529:3:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10331, "name": "string", "nodeType": "ElementaryTypeName", "src": "513:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b326d", "id": 10332, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "535:11:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2f556fa434add49eadfa043e74ff00496b89a16068544c1118ec19f5d8603d51", "typeString": "literal_string hex\"1b5b326d\""}, "value": "\u001b[2m"}, "visibility": "internal"}, {"id": 10336, "nodeType": "VariableDeclaration", "src": "552:36:11", "nodes": [], "constant": true, "mutability": "constant", "name": "ITALIC", "nameLocation": "568:6:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10334, "name": "string", "nodeType": "ElementaryTypeName", "src": "552:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b336d", "id": 10335, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "577:11:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_3889f2814cfbcc60c7a881028023c05aed4a6dae60be0df554f690b1f4e7411f", "typeString": "literal_string hex\"1b5b336d\""}, "value": "\u001b[3m"}, "visibility": "internal"}, {"id": 10339, "nodeType": "VariableDeclaration", "src": "594:39:11", "nodes": [], "constant": true, "mutability": "constant", "name": "UNDERLINE", "nameLocation": "610:9:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10337, "name": "string", "nodeType": "ElementaryTypeName", "src": "594:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b346d", "id": 10338, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "622:11:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_48cbbbbdbcd789b35edf67deaad6f96f406603d9181318ca90ef32f90fedb5bb", "typeString": "literal_string hex\"1b5b346d\""}, "value": "\u001b[4m"}, "visibility": "internal"}, {"id": 10342, "nodeType": "VariableDeclaration", "src": "639:37:11", "nodes": [], "constant": true, "mutability": "constant", "name": "INVERSE", "nameLocation": "655:7:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10340, "name": "string", "nodeType": "ElementaryTypeName", "src": "639:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b376d", "id": 10341, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "665:11:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_963e08c830a620b3640a99ac46ac6850f28c8f20be064518b3acc7016c3e286e", "typeString": "literal_string hex\"1b5b376d\""}, "value": "\u001b[7m"}, "visibility": "internal"}, {"id": 10345, "nodeType": "VariableDeclaration", "src": "682:35:11", "nodes": [], "constant": true, "mutability": "constant", "name": "RESET", "nameLocation": "698:5:11", "scope": 11499, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10343, "name": "string", "nodeType": "ElementaryTypeName", "src": "682:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "1b5b306d", "id": 10344, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "706:11:11", "typeDescriptions": {"typeIdentifier": "t_stringliteral_289c700ce2c600d61adfc66f83b41c26150052f3ea6c772e582ea6afd03d1949", "typeString": "literal_string hex\"1b5b306d\""}, "value": "\u001b[0m"}, "visibility": "internal"}, {"id": 10365, "nodeType": "FunctionDefinition", "src": "724:167:11", "nodes": [], "body": {"id": 10364, "nodeType": "Block", "src": "823:68:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10358, "name": "style", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10347, "src": "864:5:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 10359, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10349, "src": "871:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 10360, "name": "RESET", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10345, "src": "877:5:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "expression": {"id": 10356, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "847:3:11", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 10357, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "851:12:11", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "847:16:11", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 10361, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "847:36:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 10355, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "840:6:11", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 10354, "name": "string", "nodeType": "ElementaryTypeName", "src": "840:6:11", "typeDescriptions": {}}}, "id": 10362, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "840:44:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10353, "id": 10363, "nodeType": "Return", "src": "833:51:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "styleConcat", "nameLocation": "733:11:11", "parameters": {"id": 10350, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10347, "mutability": "mutable", "name": "style", "nameLocation": "759:5:11", "nodeType": "VariableDeclaration", "scope": 10365, "src": "745:19:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10346, "name": "string", "nodeType": "ElementaryTypeName", "src": "745:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 10349, "mutability": "mutable", "name": "self", "nameLocation": "780:4:11", "nodeType": "VariableDeclaration", "scope": 10365, "src": "766:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10348, "name": "string", "nodeType": "ElementaryTypeName", "src": "766:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "744:41:11"}, "returnParameters": {"id": 10353, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10352, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10365, "src": "808:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10351, "name": "string", "nodeType": "ElementaryTypeName", "src": "808:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "807:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "private"}, {"id": 10378, "nodeType": "FunctionDefinition", "src": "897:117:11", "nodes": [], "body": {"id": 10377, "nodeType": "Block", "src": "968:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 10373, "name": "RED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10312, "src": "997:3:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 10374, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10367, "src": "1002:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10372, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "985:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 10375, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "985:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10371, "id": 10376, "nodeType": "Return", "src": "978:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "red", "nameLocation": "906:3:11", "parameters": {"id": 10368, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10367, "mutability": "mutable", "name": "self", "nameLocation": "924:4:11", "nodeType": "VariableDeclaration", "scope": 10378, "src": "910:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10366, "name": "string", "nodeType": "ElementaryTypeName", "src": "910:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "909:20:11"}, "returnParameters": {"id": 10371, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10370, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10378, "src": "953:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10369, "name": "string", "nodeType": "ElementaryTypeName", "src": "953:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "952:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10393, "nodeType": "FunctionDefinition", "src": "1020:111:11", "nodes": [], "body": {"id": 10392, "nodeType": "Block", "src": "1085:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10388, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10380, "src": "1118:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 10386, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "1106:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10387, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1109:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "1106:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 10389, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1106:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10385, "name": "red", "nodeType": "Identifier", "overloadedDeclarations": [10378, 10393, 10408, 10423, 10438], "referencedDeclaration": 10378, "src": "1102:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10390, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1102:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10384, "id": 10391, "nodeType": "Return", "src": "1095:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "red", "nameLocation": "1029:3:11", "parameters": {"id": 10381, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10380, "mutability": "mutable", "name": "self", "nameLocation": "1041:4:11", "nodeType": "VariableDeclaration", "scope": 10393, "src": "1033:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 10379, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1033:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1032:14:11"}, "returnParameters": {"id": 10384, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10383, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10393, "src": "1070:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10382, "name": "string", "nodeType": "ElementaryTypeName", "src": "1070:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1069:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10408, "nodeType": "FunctionDefinition", "src": "1137:110:11", "nodes": [], "body": {"id": 10407, "nodeType": "Block", "src": "1201:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10403, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10395, "src": "1234:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 10401, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "1222:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10402, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1225:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "1222:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 10404, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1222:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10400, "name": "red", "nodeType": "Identifier", "overloadedDeclarations": [10378, 10393, 10408, 10423, 10438], "referencedDeclaration": 10378, "src": "1218:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10405, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1218:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10399, "id": 10406, "nodeType": "Return", "src": "1211:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "red", "nameLocation": "1146:3:11", "parameters": {"id": 10396, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10395, "mutability": "mutable", "name": "self", "nameLocation": "1157:4:11", "nodeType": "VariableDeclaration", "scope": 10408, "src": "1150:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 10394, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1150:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "1149:13:11"}, "returnParameters": {"id": 10399, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10398, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10408, "src": "1186:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10397, "name": "string", "nodeType": "ElementaryTypeName", "src": "1186:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1185:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10423, "nodeType": "FunctionDefinition", "src": "1253:111:11", "nodes": [], "body": {"id": 10422, "nodeType": "Block", "src": "1318:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10418, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10410, "src": "1351:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 10416, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "1339:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10417, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1342:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "1339:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 10419, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1339:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10415, "name": "red", "nodeType": "Identifier", "overloadedDeclarations": [10378, 10393, 10408, 10423, 10438], "referencedDeclaration": 10378, "src": "1335:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10420, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1335:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10414, "id": 10421, "nodeType": "Return", "src": "1328:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "red", "nameLocation": "1262:3:11", "parameters": {"id": 10411, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10410, "mutability": "mutable", "name": "self", "nameLocation": "1274:4:11", "nodeType": "VariableDeclaration", "scope": 10423, "src": "1266:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 10409, "name": "address", "nodeType": "ElementaryTypeName", "src": "1266:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1265:14:11"}, "returnParameters": {"id": 10414, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10413, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10423, "src": "1303:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10412, "name": "string", "nodeType": "ElementaryTypeName", "src": "1303:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1302:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10438, "nodeType": "FunctionDefinition", "src": "1370:108:11", "nodes": [], "body": {"id": 10437, "nodeType": "Block", "src": "1432:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10433, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10425, "src": "1465:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 10431, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "1453:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10432, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1456:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "1453:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 10434, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1453:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10430, "name": "red", "nodeType": "Identifier", "overloadedDeclarations": [10378, 10393, 10408, 10423, 10438], "referencedDeclaration": 10378, "src": "1449:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10435, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1449:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10429, "id": 10436, "nodeType": "Return", "src": "1442:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "red", "nameLocation": "1379:3:11", "parameters": {"id": 10426, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10425, "mutability": "mutable", "name": "self", "nameLocation": "1388:4:11", "nodeType": "VariableDeclaration", "scope": 10438, "src": "1383:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 10424, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1383:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1382:11:11"}, "returnParameters": {"id": 10429, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10428, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10438, "src": "1417:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10427, "name": "string", "nodeType": "ElementaryTypeName", "src": "1417:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1416:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10453, "nodeType": "FunctionDefinition", "src": "1484:121:11", "nodes": [], "body": {"id": 10452, "nodeType": "Block", "src": "1559:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10448, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10440, "src": "1592:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 10446, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "1580:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10447, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1583:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "1580:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 10449, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1580:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10445, "name": "red", "nodeType": "Identifier", "overloadedDeclarations": [10378, 10393, 10408, 10423, 10438], "referencedDeclaration": 10378, "src": "1576:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10450, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1576:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10444, "id": 10451, "nodeType": "Return", "src": "1569:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "redBytes", "nameLocation": "1493:8:11", "parameters": {"id": 10441, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10440, "mutability": "mutable", "name": "self", "nameLocation": "1515:4:11", "nodeType": "VariableDeclaration", "scope": 10453, "src": "1502:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 10439, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1502:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "1501:19:11"}, "returnParameters": {"id": 10444, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10443, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10453, "src": "1544:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10442, "name": "string", "nodeType": "ElementaryTypeName", "src": "1544:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1543:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10468, "nodeType": "FunctionDefinition", "src": "1611:118:11", "nodes": [], "body": {"id": 10467, "nodeType": "Block", "src": "1683:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10463, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10455, "src": "1716:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 10461, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "1704:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10462, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1707:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "1704:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 10464, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1704:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10460, "name": "red", "nodeType": "Identifier", "overloadedDeclarations": [10378, 10393, 10408, 10423, 10438], "referencedDeclaration": 10378, "src": "1700:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10465, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1700:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10459, "id": 10466, "nodeType": "Return", "src": "1693:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "redBytes32", "nameLocation": "1620:10:11", "parameters": {"id": 10456, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10455, "mutability": "mutable", "name": "self", "nameLocation": "1639:4:11", "nodeType": "VariableDeclaration", "scope": 10468, "src": "1631:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 10454, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1631:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "1630:14:11"}, "returnParameters": {"id": 10459, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10458, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10468, "src": "1668:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10457, "name": "string", "nodeType": "ElementaryTypeName", "src": "1668:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1667:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10481, "nodeType": "FunctionDefinition", "src": "1735:121:11", "nodes": [], "body": {"id": 10480, "nodeType": "Block", "src": "1808:48:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 10476, "name": "GREEN", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10315, "src": "1837:5:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 10477, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10470, "src": "1844:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10475, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "1825:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 10478, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1825:24:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10474, "id": 10479, "nodeType": "Return", "src": "1818:31:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "green", "nameLocation": "1744:5:11", "parameters": {"id": 10471, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10470, "mutability": "mutable", "name": "self", "nameLocation": "1764:4:11", "nodeType": "VariableDeclaration", "scope": 10481, "src": "1750:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10469, "name": "string", "nodeType": "ElementaryTypeName", "src": "1750:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1749:20:11"}, "returnParameters": {"id": 10474, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10473, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10481, "src": "1793:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10472, "name": "string", "nodeType": "ElementaryTypeName", "src": "1793:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1792:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10496, "nodeType": "FunctionDefinition", "src": "1862:115:11", "nodes": [], "body": {"id": 10495, "nodeType": "Block", "src": "1929:48:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10491, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10483, "src": "1964:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 10489, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "1952:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10490, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1955:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "1952:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 10492, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1952:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10488, "name": "green", "nodeType": "Identifier", "overloadedDeclarations": [10481, 10496, 10511, 10526, 10541], "referencedDeclaration": 10481, "src": "1946:5:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10493, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1946:24:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10487, "id": 10494, "nodeType": "Return", "src": "1939:31:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "green", "nameLocation": "1871:5:11", "parameters": {"id": 10484, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10483, "mutability": "mutable", "name": "self", "nameLocation": "1885:4:11", "nodeType": "VariableDeclaration", "scope": 10496, "src": "1877:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 10482, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1877:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1876:14:11"}, "returnParameters": {"id": 10487, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10486, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10496, "src": "1914:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10485, "name": "string", "nodeType": "ElementaryTypeName", "src": "1914:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1913:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10511, "nodeType": "FunctionDefinition", "src": "1983:114:11", "nodes": [], "body": {"id": 10510, "nodeType": "Block", "src": "2049:48:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10506, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10498, "src": "2084:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 10504, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "2072:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10505, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2075:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "2072:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 10507, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2072:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10503, "name": "green", "nodeType": "Identifier", "overloadedDeclarations": [10481, 10496, 10511, 10526, 10541], "referencedDeclaration": 10481, "src": "2066:5:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10508, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2066:24:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10502, "id": 10509, "nodeType": "Return", "src": "2059:31:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "green", "nameLocation": "1992:5:11", "parameters": {"id": 10499, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10498, "mutability": "mutable", "name": "self", "nameLocation": "2005:4:11", "nodeType": "VariableDeclaration", "scope": 10511, "src": "1998:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 10497, "name": "int256", "nodeType": "ElementaryTypeName", "src": "1998:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "1997:13:11"}, "returnParameters": {"id": 10502, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10501, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10511, "src": "2034:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10500, "name": "string", "nodeType": "ElementaryTypeName", "src": "2034:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2033:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10526, "nodeType": "FunctionDefinition", "src": "2103:115:11", "nodes": [], "body": {"id": 10525, "nodeType": "Block", "src": "2170:48:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10521, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10513, "src": "2205:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 10519, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "2193:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10520, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2196:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "2193:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 10522, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2193:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10518, "name": "green", "nodeType": "Identifier", "overloadedDeclarations": [10481, 10496, 10511, 10526, 10541], "referencedDeclaration": 10481, "src": "2187:5:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10523, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2187:24:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10517, "id": 10524, "nodeType": "Return", "src": "2180:31:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "green", "nameLocation": "2112:5:11", "parameters": {"id": 10514, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10513, "mutability": "mutable", "name": "self", "nameLocation": "2126:4:11", "nodeType": "VariableDeclaration", "scope": 10526, "src": "2118:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 10512, "name": "address", "nodeType": "ElementaryTypeName", "src": "2118:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2117:14:11"}, "returnParameters": {"id": 10517, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10516, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10526, "src": "2155:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10515, "name": "string", "nodeType": "ElementaryTypeName", "src": "2155:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2154:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10541, "nodeType": "FunctionDefinition", "src": "2224:112:11", "nodes": [], "body": {"id": 10540, "nodeType": "Block", "src": "2288:48:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10536, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10528, "src": "2323:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 10534, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "2311:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10535, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2314:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "2311:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 10537, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2311:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10533, "name": "green", "nodeType": "Identifier", "overloadedDeclarations": [10481, 10496, 10511, 10526, 10541], "referencedDeclaration": 10481, "src": "2305:5:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10538, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2305:24:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10532, "id": 10539, "nodeType": "Return", "src": "2298:31:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "green", "nameLocation": "2233:5:11", "parameters": {"id": 10529, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10528, "mutability": "mutable", "name": "self", "nameLocation": "2244:4:11", "nodeType": "VariableDeclaration", "scope": 10541, "src": "2239:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 10527, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2239:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2238:11:11"}, "returnParameters": {"id": 10532, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10531, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10541, "src": "2273:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10530, "name": "string", "nodeType": "ElementaryTypeName", "src": "2273:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2272:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10556, "nodeType": "FunctionDefinition", "src": "2342:125:11", "nodes": [], "body": {"id": 10555, "nodeType": "Block", "src": "2419:48:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10551, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10543, "src": "2454:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 10549, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "2442:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10550, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2445:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "2442:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 10552, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2442:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10548, "name": "green", "nodeType": "Identifier", "overloadedDeclarations": [10481, 10496, 10511, 10526, 10541], "referencedDeclaration": 10481, "src": "2436:5:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10553, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2436:24:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10547, "id": 10554, "nodeType": "Return", "src": "2429:31:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "greenBytes", "nameLocation": "2351:10:11", "parameters": {"id": 10544, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10543, "mutability": "mutable", "name": "self", "nameLocation": "2375:4:11", "nodeType": "VariableDeclaration", "scope": 10556, "src": "2362:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 10542, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "2362:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "2361:19:11"}, "returnParameters": {"id": 10547, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10546, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10556, "src": "2404:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10545, "name": "string", "nodeType": "ElementaryTypeName", "src": "2404:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2403:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10571, "nodeType": "FunctionDefinition", "src": "2473:122:11", "nodes": [], "body": {"id": 10570, "nodeType": "Block", "src": "2547:48:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10566, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10558, "src": "2582:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 10564, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "2570:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10565, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2573:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "2570:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 10567, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2570:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10563, "name": "green", "nodeType": "Identifier", "overloadedDeclarations": [10481, 10496, 10511, 10526, 10541], "referencedDeclaration": 10481, "src": "2564:5:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10568, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2564:24:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10562, "id": 10569, "nodeType": "Return", "src": "2557:31:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "greenBytes32", "nameLocation": "2482:12:11", "parameters": {"id": 10559, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10558, "mutability": "mutable", "name": "self", "nameLocation": "2503:4:11", "nodeType": "VariableDeclaration", "scope": 10571, "src": "2495:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 10557, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2495:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "2494:14:11"}, "returnParameters": {"id": 10562, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10561, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10571, "src": "2532:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10560, "name": "string", "nodeType": "ElementaryTypeName", "src": "2532:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2531:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10584, "nodeType": "FunctionDefinition", "src": "2601:123:11", "nodes": [], "body": {"id": 10583, "nodeType": "Block", "src": "2675:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 10579, "name": "YELLOW", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10318, "src": "2704:6:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 10580, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10573, "src": "2712:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10578, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "2692:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 10581, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2692:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10577, "id": 10582, "nodeType": "Return", "src": "2685:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "yellow", "nameLocation": "2610:6:11", "parameters": {"id": 10574, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10573, "mutability": "mutable", "name": "self", "nameLocation": "2631:4:11", "nodeType": "VariableDeclaration", "scope": 10584, "src": "2617:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10572, "name": "string", "nodeType": "ElementaryTypeName", "src": "2617:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2616:20:11"}, "returnParameters": {"id": 10577, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10576, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10584, "src": "2660:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10575, "name": "string", "nodeType": "ElementaryTypeName", "src": "2660:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2659:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10599, "nodeType": "FunctionDefinition", "src": "2730:117:11", "nodes": [], "body": {"id": 10598, "nodeType": "Block", "src": "2798:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10594, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10586, "src": "2834:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 10592, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "2822:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10593, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2825:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "2822:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 10595, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2822:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10591, "name": "yellow", "nodeType": "Identifier", "overloadedDeclarations": [10584, 10599, 10614, 10629, 10644], "referencedDeclaration": 10584, "src": "2815:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10596, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2815:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10590, "id": 10597, "nodeType": "Return", "src": "2808:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "yellow", "nameLocation": "2739:6:11", "parameters": {"id": 10587, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10586, "mutability": "mutable", "name": "self", "nameLocation": "2754:4:11", "nodeType": "VariableDeclaration", "scope": 10599, "src": "2746:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 10585, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2746:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2745:14:11"}, "returnParameters": {"id": 10590, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10589, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10599, "src": "2783:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10588, "name": "string", "nodeType": "ElementaryTypeName", "src": "2783:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2782:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10614, "nodeType": "FunctionDefinition", "src": "2853:116:11", "nodes": [], "body": {"id": 10613, "nodeType": "Block", "src": "2920:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10609, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10601, "src": "2956:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 10607, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "2944:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10608, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2947:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "2944:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 10610, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2944:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10606, "name": "yellow", "nodeType": "Identifier", "overloadedDeclarations": [10584, 10599, 10614, 10629, 10644], "referencedDeclaration": 10584, "src": "2937:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10611, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2937:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10605, "id": 10612, "nodeType": "Return", "src": "2930:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "yellow", "nameLocation": "2862:6:11", "parameters": {"id": 10602, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10601, "mutability": "mutable", "name": "self", "nameLocation": "2876:4:11", "nodeType": "VariableDeclaration", "scope": 10614, "src": "2869:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 10600, "name": "int256", "nodeType": "ElementaryTypeName", "src": "2869:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "2868:13:11"}, "returnParameters": {"id": 10605, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10604, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10614, "src": "2905:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10603, "name": "string", "nodeType": "ElementaryTypeName", "src": "2905:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2904:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10629, "nodeType": "FunctionDefinition", "src": "2975:117:11", "nodes": [], "body": {"id": 10628, "nodeType": "Block", "src": "3043:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10624, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10616, "src": "3079:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 10622, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "3067:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10623, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3070:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "3067:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 10625, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3067:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10621, "name": "yellow", "nodeType": "Identifier", "overloadedDeclarations": [10584, 10599, 10614, 10629, 10644], "referencedDeclaration": 10584, "src": "3060:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10626, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3060:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10620, "id": 10627, "nodeType": "Return", "src": "3053:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "yellow", "nameLocation": "2984:6:11", "parameters": {"id": 10617, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10616, "mutability": "mutable", "name": "self", "nameLocation": "2999:4:11", "nodeType": "VariableDeclaration", "scope": 10629, "src": "2991:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 10615, "name": "address", "nodeType": "ElementaryTypeName", "src": "2991:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2990:14:11"}, "returnParameters": {"id": 10620, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10619, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10629, "src": "3028:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10618, "name": "string", "nodeType": "ElementaryTypeName", "src": "3028:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3027:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10644, "nodeType": "FunctionDefinition", "src": "3098:114:11", "nodes": [], "body": {"id": 10643, "nodeType": "Block", "src": "3163:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10639, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10631, "src": "3199:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 10637, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "3187:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10638, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3190:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "3187:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 10640, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3187:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10636, "name": "yellow", "nodeType": "Identifier", "overloadedDeclarations": [10584, 10599, 10614, 10629, 10644], "referencedDeclaration": 10584, "src": "3180:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10641, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3180:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10635, "id": 10642, "nodeType": "Return", "src": "3173:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "yellow", "nameLocation": "3107:6:11", "parameters": {"id": 10632, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10631, "mutability": "mutable", "name": "self", "nameLocation": "3119:4:11", "nodeType": "VariableDeclaration", "scope": 10644, "src": "3114:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 10630, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3114:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3113:11:11"}, "returnParameters": {"id": 10635, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10634, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10644, "src": "3148:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10633, "name": "string", "nodeType": "ElementaryTypeName", "src": "3148:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3147:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10659, "nodeType": "FunctionDefinition", "src": "3218:127:11", "nodes": [], "body": {"id": 10658, "nodeType": "Block", "src": "3296:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10654, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10646, "src": "3332:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 10652, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "3320:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10653, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3323:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "3320:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 10655, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3320:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10651, "name": "yellow", "nodeType": "Identifier", "overloadedDeclarations": [10584, 10599, 10614, 10629, 10644], "referencedDeclaration": 10584, "src": "3313:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10656, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3313:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10650, "id": 10657, "nodeType": "Return", "src": "3306:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "yellowBytes", "nameLocation": "3227:11:11", "parameters": {"id": 10647, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10646, "mutability": "mutable", "name": "self", "nameLocation": "3252:4:11", "nodeType": "VariableDeclaration", "scope": 10659, "src": "3239:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 10645, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "3239:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "3238:19:11"}, "returnParameters": {"id": 10650, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10649, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10659, "src": "3281:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10648, "name": "string", "nodeType": "ElementaryTypeName", "src": "3281:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3280:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10674, "nodeType": "FunctionDefinition", "src": "3351:124:11", "nodes": [], "body": {"id": 10673, "nodeType": "Block", "src": "3426:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10669, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10661, "src": "3462:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 10667, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "3450:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10668, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3453:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "3450:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 10670, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3450:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10666, "name": "yellow", "nodeType": "Identifier", "overloadedDeclarations": [10584, 10599, 10614, 10629, 10644], "referencedDeclaration": 10584, "src": "3443:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10671, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3443:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10665, "id": 10672, "nodeType": "Return", "src": "3436:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "yellowBytes32", "nameLocation": "3360:13:11", "parameters": {"id": 10662, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10661, "mutability": "mutable", "name": "self", "nameLocation": "3382:4:11", "nodeType": "VariableDeclaration", "scope": 10674, "src": "3374:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 10660, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "3374:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "3373:14:11"}, "returnParameters": {"id": 10665, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10664, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10674, "src": "3411:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10663, "name": "string", "nodeType": "ElementaryTypeName", "src": "3411:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3410:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10687, "nodeType": "FunctionDefinition", "src": "3481:119:11", "nodes": [], "body": {"id": 10686, "nodeType": "Block", "src": "3553:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 10682, "name": "BLUE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10321, "src": "3582:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 10683, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10676, "src": "3588:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10681, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "3570:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 10684, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3570:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10680, "id": 10685, "nodeType": "Return", "src": "3563:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "blue", "nameLocation": "3490:4:11", "parameters": {"id": 10677, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10676, "mutability": "mutable", "name": "self", "nameLocation": "3509:4:11", "nodeType": "VariableDeclaration", "scope": 10687, "src": "3495:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10675, "name": "string", "nodeType": "ElementaryTypeName", "src": "3495:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3494:20:11"}, "returnParameters": {"id": 10680, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10679, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10687, "src": "3538:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10678, "name": "string", "nodeType": "ElementaryTypeName", "src": "3538:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3537:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10702, "nodeType": "FunctionDefinition", "src": "3606:113:11", "nodes": [], "body": {"id": 10701, "nodeType": "Block", "src": "3672:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10697, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10689, "src": "3706:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 10695, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "3694:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10696, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3697:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "3694:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 10698, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3694:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10694, "name": "blue", "nodeType": "Identifier", "overloadedDeclarations": [10687, 10702, 10717, 10732, 10747], "referencedDeclaration": 10687, "src": "3689:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10699, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3689:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10693, "id": 10700, "nodeType": "Return", "src": "3682:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "blue", "nameLocation": "3615:4:11", "parameters": {"id": 10690, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10689, "mutability": "mutable", "name": "self", "nameLocation": "3628:4:11", "nodeType": "VariableDeclaration", "scope": 10702, "src": "3620:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 10688, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3620:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3619:14:11"}, "returnParameters": {"id": 10693, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10692, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10702, "src": "3657:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10691, "name": "string", "nodeType": "ElementaryTypeName", "src": "3657:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3656:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10717, "nodeType": "FunctionDefinition", "src": "3725:112:11", "nodes": [], "body": {"id": 10716, "nodeType": "Block", "src": "3790:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10712, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10704, "src": "3824:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 10710, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "3812:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10711, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3815:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "3812:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 10713, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3812:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10709, "name": "blue", "nodeType": "Identifier", "overloadedDeclarations": [10687, 10702, 10717, 10732, 10747], "referencedDeclaration": 10687, "src": "3807:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10714, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3807:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10708, "id": 10715, "nodeType": "Return", "src": "3800:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "blue", "nameLocation": "3734:4:11", "parameters": {"id": 10705, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10704, "mutability": "mutable", "name": "self", "nameLocation": "3746:4:11", "nodeType": "VariableDeclaration", "scope": 10717, "src": "3739:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 10703, "name": "int256", "nodeType": "ElementaryTypeName", "src": "3739:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "3738:13:11"}, "returnParameters": {"id": 10708, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10707, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10717, "src": "3775:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10706, "name": "string", "nodeType": "ElementaryTypeName", "src": "3775:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3774:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10732, "nodeType": "FunctionDefinition", "src": "3843:113:11", "nodes": [], "body": {"id": 10731, "nodeType": "Block", "src": "3909:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10727, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10719, "src": "3943:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 10725, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "3931:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10726, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3934:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "3931:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 10728, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3931:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10724, "name": "blue", "nodeType": "Identifier", "overloadedDeclarations": [10687, 10702, 10717, 10732, 10747], "referencedDeclaration": 10687, "src": "3926:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10729, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3926:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10723, "id": 10730, "nodeType": "Return", "src": "3919:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "blue", "nameLocation": "3852:4:11", "parameters": {"id": 10720, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10719, "mutability": "mutable", "name": "self", "nameLocation": "3865:4:11", "nodeType": "VariableDeclaration", "scope": 10732, "src": "3857:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 10718, "name": "address", "nodeType": "ElementaryTypeName", "src": "3857:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3856:14:11"}, "returnParameters": {"id": 10723, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10722, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10732, "src": "3894:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10721, "name": "string", "nodeType": "ElementaryTypeName", "src": "3894:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3893:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10747, "nodeType": "FunctionDefinition", "src": "3962:110:11", "nodes": [], "body": {"id": 10746, "nodeType": "Block", "src": "4025:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10742, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10734, "src": "4059:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 10740, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "4047:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10741, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4050:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "4047:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 10743, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4047:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10739, "name": "blue", "nodeType": "Identifier", "overloadedDeclarations": [10687, 10702, 10717, 10732, 10747], "referencedDeclaration": 10687, "src": "4042:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10744, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4042:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10738, "id": 10745, "nodeType": "Return", "src": "4035:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "blue", "nameLocation": "3971:4:11", "parameters": {"id": 10735, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10734, "mutability": "mutable", "name": "self", "nameLocation": "3981:4:11", "nodeType": "VariableDeclaration", "scope": 10747, "src": "3976:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 10733, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3976:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3975:11:11"}, "returnParameters": {"id": 10738, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10737, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10747, "src": "4010:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10736, "name": "string", "nodeType": "ElementaryTypeName", "src": "4010:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4009:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10762, "nodeType": "FunctionDefinition", "src": "4078:123:11", "nodes": [], "body": {"id": 10761, "nodeType": "Block", "src": "4154:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10757, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10749, "src": "4188:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 10755, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "4176:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10756, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4179:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "4176:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 10758, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4176:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10754, "name": "blue", "nodeType": "Identifier", "overloadedDeclarations": [10687, 10702, 10717, 10732, 10747], "referencedDeclaration": 10687, "src": "4171:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10759, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4171:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10753, "id": 10760, "nodeType": "Return", "src": "4164:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "blueBytes", "nameLocation": "4087:9:11", "parameters": {"id": 10750, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10749, "mutability": "mutable", "name": "self", "nameLocation": "4110:4:11", "nodeType": "VariableDeclaration", "scope": 10762, "src": "4097:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 10748, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4097:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "4096:19:11"}, "returnParameters": {"id": 10753, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10752, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10762, "src": "4139:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10751, "name": "string", "nodeType": "ElementaryTypeName", "src": "4139:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4138:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10777, "nodeType": "FunctionDefinition", "src": "4207:120:11", "nodes": [], "body": {"id": 10776, "nodeType": "Block", "src": "4280:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10772, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10764, "src": "4314:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 10770, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "4302:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10771, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4305:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "4302:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 10773, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4302:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10769, "name": "blue", "nodeType": "Identifier", "overloadedDeclarations": [10687, 10702, 10717, 10732, 10747], "referencedDeclaration": 10687, "src": "4297:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10774, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4297:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10768, "id": 10775, "nodeType": "Return", "src": "4290:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "blueBytes32", "nameLocation": "4216:11:11", "parameters": {"id": 10765, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10764, "mutability": "mutable", "name": "self", "nameLocation": "4236:4:11", "nodeType": "VariableDeclaration", "scope": 10777, "src": "4228:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 10763, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4228:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "4227:14:11"}, "returnParameters": {"id": 10768, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10767, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10777, "src": "4265:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10766, "name": "string", "nodeType": "ElementaryTypeName", "src": "4265:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4264:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10790, "nodeType": "FunctionDefinition", "src": "4333:125:11", "nodes": [], "body": {"id": 10789, "nodeType": "Block", "src": "4408:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 10785, "name": "MAGENTA", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10324, "src": "4437:7:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 10786, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10779, "src": "4446:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10784, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "4425:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 10787, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4425:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10783, "id": 10788, "nodeType": "Return", "src": "4418:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "magenta", "nameLocation": "4342:7:11", "parameters": {"id": 10780, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10779, "mutability": "mutable", "name": "self", "nameLocation": "4364:4:11", "nodeType": "VariableDeclaration", "scope": 10790, "src": "4350:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10778, "name": "string", "nodeType": "ElementaryTypeName", "src": "4350:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4349:20:11"}, "returnParameters": {"id": 10783, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10782, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10790, "src": "4393:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10781, "name": "string", "nodeType": "ElementaryTypeName", "src": "4393:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4392:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10805, "nodeType": "FunctionDefinition", "src": "4464:119:11", "nodes": [], "body": {"id": 10804, "nodeType": "Block", "src": "4533:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10800, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10792, "src": "4570:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 10798, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "4558:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10799, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4561:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "4558:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 10801, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4558:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10797, "name": "magenta", "nodeType": "Identifier", "overloadedDeclarations": [10790, 10805, 10820, 10835, 10850], "referencedDeclaration": 10790, "src": "4550:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10802, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4550:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10796, "id": 10803, "nodeType": "Return", "src": "4543:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "magenta", "nameLocation": "4473:7:11", "parameters": {"id": 10793, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10792, "mutability": "mutable", "name": "self", "nameLocation": "4489:4:11", "nodeType": "VariableDeclaration", "scope": 10805, "src": "4481:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 10791, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4481:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4480:14:11"}, "returnParameters": {"id": 10796, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10795, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10805, "src": "4518:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10794, "name": "string", "nodeType": "ElementaryTypeName", "src": "4518:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4517:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10820, "nodeType": "FunctionDefinition", "src": "4589:118:11", "nodes": [], "body": {"id": 10819, "nodeType": "Block", "src": "4657:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10815, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10807, "src": "4694:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 10813, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "4682:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10814, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4685:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "4682:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 10816, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4682:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10812, "name": "magenta", "nodeType": "Identifier", "overloadedDeclarations": [10790, 10805, 10820, 10835, 10850], "referencedDeclaration": 10790, "src": "4674:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10817, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4674:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10811, "id": 10818, "nodeType": "Return", "src": "4667:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "magenta", "nameLocation": "4598:7:11", "parameters": {"id": 10808, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10807, "mutability": "mutable", "name": "self", "nameLocation": "4613:4:11", "nodeType": "VariableDeclaration", "scope": 10820, "src": "4606:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 10806, "name": "int256", "nodeType": "ElementaryTypeName", "src": "4606:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "4605:13:11"}, "returnParameters": {"id": 10811, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10810, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10820, "src": "4642:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10809, "name": "string", "nodeType": "ElementaryTypeName", "src": "4642:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4641:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10835, "nodeType": "FunctionDefinition", "src": "4713:119:11", "nodes": [], "body": {"id": 10834, "nodeType": "Block", "src": "4782:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10830, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10822, "src": "4819:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 10828, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "4807:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10829, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4810:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "4807:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 10831, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4807:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10827, "name": "magenta", "nodeType": "Identifier", "overloadedDeclarations": [10790, 10805, 10820, 10835, 10850], "referencedDeclaration": 10790, "src": "4799:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10832, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4799:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10826, "id": 10833, "nodeType": "Return", "src": "4792:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "magenta", "nameLocation": "4722:7:11", "parameters": {"id": 10823, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10822, "mutability": "mutable", "name": "self", "nameLocation": "4738:4:11", "nodeType": "VariableDeclaration", "scope": 10835, "src": "4730:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 10821, "name": "address", "nodeType": "ElementaryTypeName", "src": "4730:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "4729:14:11"}, "returnParameters": {"id": 10826, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10825, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10835, "src": "4767:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10824, "name": "string", "nodeType": "ElementaryTypeName", "src": "4767:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4766:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10850, "nodeType": "FunctionDefinition", "src": "4838:116:11", "nodes": [], "body": {"id": 10849, "nodeType": "Block", "src": "4904:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10845, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10837, "src": "4941:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 10843, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "4929:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10844, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4932:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "4929:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 10846, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4929:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10842, "name": "magenta", "nodeType": "Identifier", "overloadedDeclarations": [10790, 10805, 10820, 10835, 10850], "referencedDeclaration": 10790, "src": "4921:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10847, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4921:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10841, "id": 10848, "nodeType": "Return", "src": "4914:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "magenta", "nameLocation": "4847:7:11", "parameters": {"id": 10838, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10837, "mutability": "mutable", "name": "self", "nameLocation": "4860:4:11", "nodeType": "VariableDeclaration", "scope": 10850, "src": "4855:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 10836, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4855:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "4854:11:11"}, "returnParameters": {"id": 10841, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10840, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10850, "src": "4889:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10839, "name": "string", "nodeType": "ElementaryTypeName", "src": "4889:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "4888:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10865, "nodeType": "FunctionDefinition", "src": "4960:129:11", "nodes": [], "body": {"id": 10864, "nodeType": "Block", "src": "5039:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10860, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10852, "src": "5076:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 10858, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "5064:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10859, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5067:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "5064:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 10861, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5064:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10857, "name": "magenta", "nodeType": "Identifier", "overloadedDeclarations": [10790, 10805, 10820, 10835, 10850], "referencedDeclaration": 10790, "src": "5056:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10862, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5056:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10856, "id": 10863, "nodeType": "Return", "src": "5049:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "magentaBytes", "nameLocation": "4969:12:11", "parameters": {"id": 10853, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10852, "mutability": "mutable", "name": "self", "nameLocation": "4995:4:11", "nodeType": "VariableDeclaration", "scope": 10865, "src": "4982:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 10851, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4982:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "4981:19:11"}, "returnParameters": {"id": 10856, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10855, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10865, "src": "5024:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10854, "name": "string", "nodeType": "ElementaryTypeName", "src": "5024:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5023:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10880, "nodeType": "FunctionDefinition", "src": "5095:126:11", "nodes": [], "body": {"id": 10879, "nodeType": "Block", "src": "5171:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10875, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10867, "src": "5208:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 10873, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "5196:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10874, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5199:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "5196:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 10876, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5196:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10872, "name": "magenta", "nodeType": "Identifier", "overloadedDeclarations": [10790, 10805, 10820, 10835, 10850], "referencedDeclaration": 10790, "src": "5188:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10877, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5188:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10871, "id": 10878, "nodeType": "Return", "src": "5181:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "magentaBytes32", "nameLocation": "5104:14:11", "parameters": {"id": 10868, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10867, "mutability": "mutable", "name": "self", "nameLocation": "5127:4:11", "nodeType": "VariableDeclaration", "scope": 10880, "src": "5119:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 10866, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5119:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "5118:14:11"}, "returnParameters": {"id": 10871, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10870, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10880, "src": "5156:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10869, "name": "string", "nodeType": "ElementaryTypeName", "src": "5156:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5155:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10893, "nodeType": "FunctionDefinition", "src": "5227:119:11", "nodes": [], "body": {"id": 10892, "nodeType": "Block", "src": "5299:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 10888, "name": "CYAN", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10327, "src": "5328:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 10889, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10882, "src": "5334:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10887, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "5316:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 10890, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5316:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10886, "id": 10891, "nodeType": "Return", "src": "5309:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "cyan", "nameLocation": "5236:4:11", "parameters": {"id": 10883, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10882, "mutability": "mutable", "name": "self", "nameLocation": "5255:4:11", "nodeType": "VariableDeclaration", "scope": 10893, "src": "5241:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10881, "name": "string", "nodeType": "ElementaryTypeName", "src": "5241:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5240:20:11"}, "returnParameters": {"id": 10886, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10885, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10893, "src": "5284:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10884, "name": "string", "nodeType": "ElementaryTypeName", "src": "5284:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5283:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10908, "nodeType": "FunctionDefinition", "src": "5352:113:11", "nodes": [], "body": {"id": 10907, "nodeType": "Block", "src": "5418:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10903, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10895, "src": "5452:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 10901, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "5440:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10902, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5443:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "5440:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 10904, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5440:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10900, "name": "cyan", "nodeType": "Identifier", "overloadedDeclarations": [10893, 10908, 10923, 10938, 10953], "referencedDeclaration": 10893, "src": "5435:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10905, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5435:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10899, "id": 10906, "nodeType": "Return", "src": "5428:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "cyan", "nameLocation": "5361:4:11", "parameters": {"id": 10896, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10895, "mutability": "mutable", "name": "self", "nameLocation": "5374:4:11", "nodeType": "VariableDeclaration", "scope": 10908, "src": "5366:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 10894, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5366:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5365:14:11"}, "returnParameters": {"id": 10899, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10898, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10908, "src": "5403:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10897, "name": "string", "nodeType": "ElementaryTypeName", "src": "5403:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5402:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10923, "nodeType": "FunctionDefinition", "src": "5471:112:11", "nodes": [], "body": {"id": 10922, "nodeType": "Block", "src": "5536:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10918, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10910, "src": "5570:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 10916, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "5558:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10917, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5561:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "5558:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 10919, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5558:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10915, "name": "cyan", "nodeType": "Identifier", "overloadedDeclarations": [10893, 10908, 10923, 10938, 10953], "referencedDeclaration": 10893, "src": "5553:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10920, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5553:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10914, "id": 10921, "nodeType": "Return", "src": "5546:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "cyan", "nameLocation": "5480:4:11", "parameters": {"id": 10911, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10910, "mutability": "mutable", "name": "self", "nameLocation": "5492:4:11", "nodeType": "VariableDeclaration", "scope": 10923, "src": "5485:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 10909, "name": "int256", "nodeType": "ElementaryTypeName", "src": "5485:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "5484:13:11"}, "returnParameters": {"id": 10914, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10913, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10923, "src": "5521:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10912, "name": "string", "nodeType": "ElementaryTypeName", "src": "5521:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5520:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10938, "nodeType": "FunctionDefinition", "src": "5589:113:11", "nodes": [], "body": {"id": 10937, "nodeType": "Block", "src": "5655:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10933, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10925, "src": "5689:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 10931, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "5677:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10932, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5680:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "5677:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 10934, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5677:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10930, "name": "cyan", "nodeType": "Identifier", "overloadedDeclarations": [10893, 10908, 10923, 10938, 10953], "referencedDeclaration": 10893, "src": "5672:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10935, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5672:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10929, "id": 10936, "nodeType": "Return", "src": "5665:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "cyan", "nameLocation": "5598:4:11", "parameters": {"id": 10926, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10925, "mutability": "mutable", "name": "self", "nameLocation": "5611:4:11", "nodeType": "VariableDeclaration", "scope": 10938, "src": "5603:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 10924, "name": "address", "nodeType": "ElementaryTypeName", "src": "5603:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5602:14:11"}, "returnParameters": {"id": 10929, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10928, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10938, "src": "5640:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10927, "name": "string", "nodeType": "ElementaryTypeName", "src": "5640:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5639:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10953, "nodeType": "FunctionDefinition", "src": "5708:110:11", "nodes": [], "body": {"id": 10952, "nodeType": "Block", "src": "5771:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10948, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10940, "src": "5805:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 10946, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "5793:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10947, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5796:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "5793:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 10949, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5793:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10945, "name": "cyan", "nodeType": "Identifier", "overloadedDeclarations": [10893, 10908, 10923, 10938, 10953], "referencedDeclaration": 10893, "src": "5788:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10950, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5788:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10944, "id": 10951, "nodeType": "Return", "src": "5781:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "cyan", "nameLocation": "5717:4:11", "parameters": {"id": 10941, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10940, "mutability": "mutable", "name": "self", "nameLocation": "5727:4:11", "nodeType": "VariableDeclaration", "scope": 10953, "src": "5722:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 10939, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5722:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "5721:11:11"}, "returnParameters": {"id": 10944, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10943, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10953, "src": "5756:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10942, "name": "string", "nodeType": "ElementaryTypeName", "src": "5756:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5755:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10968, "nodeType": "FunctionDefinition", "src": "5824:123:11", "nodes": [], "body": {"id": 10967, "nodeType": "Block", "src": "5900:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10963, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10955, "src": "5934:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 10961, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "5922:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10962, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5925:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "5922:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 10964, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5922:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10960, "name": "cyan", "nodeType": "Identifier", "overloadedDeclarations": [10893, 10908, 10923, 10938, 10953], "referencedDeclaration": 10893, "src": "5917:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10965, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5917:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10959, "id": 10966, "nodeType": "Return", "src": "5910:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "cyanBytes", "nameLocation": "5833:9:11", "parameters": {"id": 10956, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10955, "mutability": "mutable", "name": "self", "nameLocation": "5856:4:11", "nodeType": "VariableDeclaration", "scope": 10968, "src": "5843:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 10954, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "5843:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "5842:19:11"}, "returnParameters": {"id": 10959, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10958, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10968, "src": "5885:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10957, "name": "string", "nodeType": "ElementaryTypeName", "src": "5885:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5884:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10983, "nodeType": "FunctionDefinition", "src": "5953:120:11", "nodes": [], "body": {"id": 10982, "nodeType": "Block", "src": "6026:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 10978, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10970, "src": "6060:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 10976, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "6048:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 10977, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6051:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "6048:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 10979, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6048:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10975, "name": "cyan", "nodeType": "Identifier", "overloadedDeclarations": [10893, 10908, 10923, 10938, 10953], "referencedDeclaration": 10893, "src": "6043:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 10980, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6043:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10974, "id": 10981, "nodeType": "Return", "src": "6036:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "cyanBytes32", "nameLocation": "5962:11:11", "parameters": {"id": 10971, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10970, "mutability": "mutable", "name": "self", "nameLocation": "5982:4:11", "nodeType": "VariableDeclaration", "scope": 10983, "src": "5974:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 10969, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5974:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "5973:14:11"}, "returnParameters": {"id": 10974, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10973, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10983, "src": "6011:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10972, "name": "string", "nodeType": "ElementaryTypeName", "src": "6011:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6010:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 10996, "nodeType": "FunctionDefinition", "src": "6079:119:11", "nodes": [], "body": {"id": 10995, "nodeType": "Block", "src": "6151:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 10991, "name": "BOLD", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10330, "src": "6180:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 10992, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10985, "src": "6186:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 10990, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "6168:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 10993, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6168:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 10989, "id": 10994, "nodeType": "Return", "src": "6161:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "bold", "nameLocation": "6088:4:11", "parameters": {"id": 10986, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10985, "mutability": "mutable", "name": "self", "nameLocation": "6107:4:11", "nodeType": "VariableDeclaration", "scope": 10996, "src": "6093:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10984, "name": "string", "nodeType": "ElementaryTypeName", "src": "6093:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6092:20:11"}, "returnParameters": {"id": 10989, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10988, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 10996, "src": "6136:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 10987, "name": "string", "nodeType": "ElementaryTypeName", "src": "6136:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6135:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11011, "nodeType": "FunctionDefinition", "src": "6204:113:11", "nodes": [], "body": {"id": 11010, "nodeType": "Block", "src": "6270:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11006, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10998, "src": "6304:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 11004, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "6292:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11005, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6295:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "6292:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 11007, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6292:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11003, "name": "bold", "nodeType": "Identifier", "overloadedDeclarations": [10996, 11011, 11026, 11041, 11056], "referencedDeclaration": 10996, "src": "6287:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11008, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6287:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11002, "id": 11009, "nodeType": "Return", "src": "6280:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "bold", "nameLocation": "6213:4:11", "parameters": {"id": 10999, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 10998, "mutability": "mutable", "name": "self", "nameLocation": "6226:4:11", "nodeType": "VariableDeclaration", "scope": 11011, "src": "6218:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 10997, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6218:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "6217:14:11"}, "returnParameters": {"id": 11002, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11001, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11011, "src": "6255:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11000, "name": "string", "nodeType": "ElementaryTypeName", "src": "6255:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6254:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11026, "nodeType": "FunctionDefinition", "src": "6323:112:11", "nodes": [], "body": {"id": 11025, "nodeType": "Block", "src": "6388:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11021, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11013, "src": "6422:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 11019, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "6410:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11020, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6413:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "6410:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 11022, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6410:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11018, "name": "bold", "nodeType": "Identifier", "overloadedDeclarations": [10996, 11011, 11026, 11041, 11056], "referencedDeclaration": 10996, "src": "6405:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11023, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6405:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11017, "id": 11024, "nodeType": "Return", "src": "6398:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "bold", "nameLocation": "6332:4:11", "parameters": {"id": 11014, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11013, "mutability": "mutable", "name": "self", "nameLocation": "6344:4:11", "nodeType": "VariableDeclaration", "scope": 11026, "src": "6337:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 11012, "name": "int256", "nodeType": "ElementaryTypeName", "src": "6337:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "6336:13:11"}, "returnParameters": {"id": 11017, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11016, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11026, "src": "6373:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11015, "name": "string", "nodeType": "ElementaryTypeName", "src": "6373:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6372:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11041, "nodeType": "FunctionDefinition", "src": "6441:113:11", "nodes": [], "body": {"id": 11040, "nodeType": "Block", "src": "6507:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11036, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11028, "src": "6541:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 11034, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "6529:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11035, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6532:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "6529:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 11037, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6529:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11033, "name": "bold", "nodeType": "Identifier", "overloadedDeclarations": [10996, 11011, 11026, 11041, 11056], "referencedDeclaration": 10996, "src": "6524:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11038, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6524:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11032, "id": 11039, "nodeType": "Return", "src": "6517:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "bold", "nameLocation": "6450:4:11", "parameters": {"id": 11029, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11028, "mutability": "mutable", "name": "self", "nameLocation": "6463:4:11", "nodeType": "VariableDeclaration", "scope": 11041, "src": "6455:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 11027, "name": "address", "nodeType": "ElementaryTypeName", "src": "6455:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "6454:14:11"}, "returnParameters": {"id": 11032, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11031, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11041, "src": "6492:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11030, "name": "string", "nodeType": "ElementaryTypeName", "src": "6492:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6491:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11056, "nodeType": "FunctionDefinition", "src": "6560:110:11", "nodes": [], "body": {"id": 11055, "nodeType": "Block", "src": "6623:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11051, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11043, "src": "6657:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 11049, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "6645:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11050, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6648:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "6645:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 11052, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6645:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11048, "name": "bold", "nodeType": "Identifier", "overloadedDeclarations": [10996, 11011, 11026, 11041, 11056], "referencedDeclaration": 10996, "src": "6640:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11053, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6640:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11047, "id": 11054, "nodeType": "Return", "src": "6633:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "bold", "nameLocation": "6569:4:11", "parameters": {"id": 11044, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11043, "mutability": "mutable", "name": "self", "nameLocation": "6579:4:11", "nodeType": "VariableDeclaration", "scope": 11056, "src": "6574:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 11042, "name": "bool", "nodeType": "ElementaryTypeName", "src": "6574:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "6573:11:11"}, "returnParameters": {"id": 11047, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11046, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11056, "src": "6608:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11045, "name": "string", "nodeType": "ElementaryTypeName", "src": "6608:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6607:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11071, "nodeType": "FunctionDefinition", "src": "6676:123:11", "nodes": [], "body": {"id": 11070, "nodeType": "Block", "src": "6752:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11066, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11058, "src": "6786:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 11064, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "6774:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11065, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6777:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "6774:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 11067, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6774:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11063, "name": "bold", "nodeType": "Identifier", "overloadedDeclarations": [10996, 11011, 11026, 11041, 11056], "referencedDeclaration": 10996, "src": "6769:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11068, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6769:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11062, "id": 11069, "nodeType": "Return", "src": "6762:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "boldBytes", "nameLocation": "6685:9:11", "parameters": {"id": 11059, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11058, "mutability": "mutable", "name": "self", "nameLocation": "6708:4:11", "nodeType": "VariableDeclaration", "scope": 11071, "src": "6695:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 11057, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "6695:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "6694:19:11"}, "returnParameters": {"id": 11062, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11061, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11071, "src": "6737:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11060, "name": "string", "nodeType": "ElementaryTypeName", "src": "6737:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6736:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11086, "nodeType": "FunctionDefinition", "src": "6805:120:11", "nodes": [], "body": {"id": 11085, "nodeType": "Block", "src": "6878:47:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11081, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11073, "src": "6912:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 11079, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "6900:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11080, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6903:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "6900:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 11082, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6900:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11078, "name": "bold", "nodeType": "Identifier", "overloadedDeclarations": [10996, 11011, 11026, 11041, 11056], "referencedDeclaration": 10996, "src": "6895:4:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11083, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6895:23:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11077, "id": 11084, "nodeType": "Return", "src": "6888:30:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "boldBytes32", "nameLocation": "6814:11:11", "parameters": {"id": 11074, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11073, "mutability": "mutable", "name": "self", "nameLocation": "6834:4:11", "nodeType": "VariableDeclaration", "scope": 11086, "src": "6826:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 11072, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6826:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "6825:14:11"}, "returnParameters": {"id": 11077, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11076, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11086, "src": "6863:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11075, "name": "string", "nodeType": "ElementaryTypeName", "src": "6863:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6862:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11099, "nodeType": "FunctionDefinition", "src": "6931:117:11", "nodes": [], "body": {"id": 11098, "nodeType": "Block", "src": "7002:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11094, "name": "DIM", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10333, "src": "7031:3:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11095, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11088, "src": "7036:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11093, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "7019:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 11096, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7019:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11092, "id": 11097, "nodeType": "Return", "src": "7012:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "dim", "nameLocation": "6940:3:11", "parameters": {"id": 11089, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11088, "mutability": "mutable", "name": "self", "nameLocation": "6958:4:11", "nodeType": "VariableDeclaration", "scope": 11099, "src": "6944:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11087, "name": "string", "nodeType": "ElementaryTypeName", "src": "6944:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6943:20:11"}, "returnParameters": {"id": 11092, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11091, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11099, "src": "6987:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11090, "name": "string", "nodeType": "ElementaryTypeName", "src": "6987:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "6986:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11114, "nodeType": "FunctionDefinition", "src": "7054:111:11", "nodes": [], "body": {"id": 11113, "nodeType": "Block", "src": "7119:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11109, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11101, "src": "7152:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 11107, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "7140:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11108, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7143:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "7140:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 11110, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7140:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11106, "name": "dim", "nodeType": "Identifier", "overloadedDeclarations": [11099, 11114, 11129, 11144, 11159], "referencedDeclaration": 11099, "src": "7136:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11111, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7136:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11105, "id": 11112, "nodeType": "Return", "src": "7129:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "dim", "nameLocation": "7063:3:11", "parameters": {"id": 11102, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11101, "mutability": "mutable", "name": "self", "nameLocation": "7075:4:11", "nodeType": "VariableDeclaration", "scope": 11114, "src": "7067:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 11100, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7067:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7066:14:11"}, "returnParameters": {"id": 11105, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11104, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11114, "src": "7104:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11103, "name": "string", "nodeType": "ElementaryTypeName", "src": "7104:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7103:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11129, "nodeType": "FunctionDefinition", "src": "7171:110:11", "nodes": [], "body": {"id": 11128, "nodeType": "Block", "src": "7235:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11124, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11116, "src": "7268:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 11122, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "7256:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11123, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7259:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "7256:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 11125, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7256:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11121, "name": "dim", "nodeType": "Identifier", "overloadedDeclarations": [11099, 11114, 11129, 11144, 11159], "referencedDeclaration": 11099, "src": "7252:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11126, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7252:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11120, "id": 11127, "nodeType": "Return", "src": "7245:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "dim", "nameLocation": "7180:3:11", "parameters": {"id": 11117, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11116, "mutability": "mutable", "name": "self", "nameLocation": "7191:4:11", "nodeType": "VariableDeclaration", "scope": 11129, "src": "7184:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 11115, "name": "int256", "nodeType": "ElementaryTypeName", "src": "7184:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "7183:13:11"}, "returnParameters": {"id": 11120, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11119, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11129, "src": "7220:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11118, "name": "string", "nodeType": "ElementaryTypeName", "src": "7220:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7219:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11144, "nodeType": "FunctionDefinition", "src": "7287:111:11", "nodes": [], "body": {"id": 11143, "nodeType": "Block", "src": "7352:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11139, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11131, "src": "7385:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 11137, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "7373:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11138, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7376:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "7373:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 11140, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7373:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11136, "name": "dim", "nodeType": "Identifier", "overloadedDeclarations": [11099, 11114, 11129, 11144, 11159], "referencedDeclaration": 11099, "src": "7369:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11141, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7369:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11135, "id": 11142, "nodeType": "Return", "src": "7362:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "dim", "nameLocation": "7296:3:11", "parameters": {"id": 11132, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11131, "mutability": "mutable", "name": "self", "nameLocation": "7308:4:11", "nodeType": "VariableDeclaration", "scope": 11144, "src": "7300:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 11130, "name": "address", "nodeType": "ElementaryTypeName", "src": "7300:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "7299:14:11"}, "returnParameters": {"id": 11135, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11134, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11144, "src": "7337:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11133, "name": "string", "nodeType": "ElementaryTypeName", "src": "7337:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7336:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11159, "nodeType": "FunctionDefinition", "src": "7404:108:11", "nodes": [], "body": {"id": 11158, "nodeType": "Block", "src": "7466:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11154, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11146, "src": "7499:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 11152, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "7487:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11153, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7490:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "7487:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 11155, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7487:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11151, "name": "dim", "nodeType": "Identifier", "overloadedDeclarations": [11099, 11114, 11129, 11144, 11159], "referencedDeclaration": 11099, "src": "7483:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11156, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7483:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11150, "id": 11157, "nodeType": "Return", "src": "7476:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "dim", "nameLocation": "7413:3:11", "parameters": {"id": 11147, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11146, "mutability": "mutable", "name": "self", "nameLocation": "7422:4:11", "nodeType": "VariableDeclaration", "scope": 11159, "src": "7417:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 11145, "name": "bool", "nodeType": "ElementaryTypeName", "src": "7417:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "7416:11:11"}, "returnParameters": {"id": 11150, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11149, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11159, "src": "7451:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11148, "name": "string", "nodeType": "ElementaryTypeName", "src": "7451:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7450:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11174, "nodeType": "FunctionDefinition", "src": "7518:121:11", "nodes": [], "body": {"id": 11173, "nodeType": "Block", "src": "7593:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11169, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11161, "src": "7626:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 11167, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "7614:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11168, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7617:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "7614:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 11170, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7614:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11166, "name": "dim", "nodeType": "Identifier", "overloadedDeclarations": [11099, 11114, 11129, 11144, 11159], "referencedDeclaration": 11099, "src": "7610:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11171, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7610:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11165, "id": 11172, "nodeType": "Return", "src": "7603:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "dimBytes", "nameLocation": "7527:8:11", "parameters": {"id": 11162, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11161, "mutability": "mutable", "name": "self", "nameLocation": "7549:4:11", "nodeType": "VariableDeclaration", "scope": 11174, "src": "7536:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 11160, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "7536:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "7535:19:11"}, "returnParameters": {"id": 11165, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11164, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11174, "src": "7578:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11163, "name": "string", "nodeType": "ElementaryTypeName", "src": "7578:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7577:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11189, "nodeType": "FunctionDefinition", "src": "7645:118:11", "nodes": [], "body": {"id": 11188, "nodeType": "Block", "src": "7717:46:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11184, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11176, "src": "7750:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 11182, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "7738:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11183, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7741:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "7738:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 11185, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7738:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11181, "name": "dim", "nodeType": "Identifier", "overloadedDeclarations": [11099, 11114, 11129, 11144, 11159], "referencedDeclaration": 11099, "src": "7734:3:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11186, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7734:22:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11180, "id": 11187, "nodeType": "Return", "src": "7727:29:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "dimBytes32", "nameLocation": "7654:10:11", "parameters": {"id": 11177, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11176, "mutability": "mutable", "name": "self", "nameLocation": "7673:4:11", "nodeType": "VariableDeclaration", "scope": 11189, "src": "7665:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 11175, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "7665:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "7664:14:11"}, "returnParameters": {"id": 11180, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11179, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11189, "src": "7702:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11178, "name": "string", "nodeType": "ElementaryTypeName", "src": "7702:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7701:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11202, "nodeType": "FunctionDefinition", "src": "7769:123:11", "nodes": [], "body": {"id": 11201, "nodeType": "Block", "src": "7843:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11197, "name": "ITALIC", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10336, "src": "7872:6:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11198, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11191, "src": "7880:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11196, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "7860:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 11199, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7860:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11195, "id": 11200, "nodeType": "Return", "src": "7853:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "italic", "nameLocation": "7778:6:11", "parameters": {"id": 11192, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11191, "mutability": "mutable", "name": "self", "nameLocation": "7799:4:11", "nodeType": "VariableDeclaration", "scope": 11202, "src": "7785:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11190, "name": "string", "nodeType": "ElementaryTypeName", "src": "7785:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7784:20:11"}, "returnParameters": {"id": 11195, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11194, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11202, "src": "7828:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11193, "name": "string", "nodeType": "ElementaryTypeName", "src": "7828:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7827:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11217, "nodeType": "FunctionDefinition", "src": "7898:117:11", "nodes": [], "body": {"id": 11216, "nodeType": "Block", "src": "7966:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11212, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11204, "src": "8002:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 11210, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "7990:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11211, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "7993:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "7990:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 11213, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7990:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11209, "name": "italic", "nodeType": "Identifier", "overloadedDeclarations": [11202, 11217, 11232, 11247, 11262], "referencedDeclaration": 11202, "src": "7983:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11214, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7983:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11208, "id": 11215, "nodeType": "Return", "src": "7976:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "italic", "nameLocation": "7907:6:11", "parameters": {"id": 11205, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11204, "mutability": "mutable", "name": "self", "nameLocation": "7922:4:11", "nodeType": "VariableDeclaration", "scope": 11217, "src": "7914:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 11203, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7914:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7913:14:11"}, "returnParameters": {"id": 11208, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11207, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11217, "src": "7951:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11206, "name": "string", "nodeType": "ElementaryTypeName", "src": "7951:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "7950:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11232, "nodeType": "FunctionDefinition", "src": "8021:116:11", "nodes": [], "body": {"id": 11231, "nodeType": "Block", "src": "8088:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11227, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11219, "src": "8124:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 11225, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "8112:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11226, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8115:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "8112:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 11228, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8112:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11224, "name": "italic", "nodeType": "Identifier", "overloadedDeclarations": [11202, 11217, 11232, 11247, 11262], "referencedDeclaration": 11202, "src": "8105:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11229, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8105:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11223, "id": 11230, "nodeType": "Return", "src": "8098:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "italic", "nameLocation": "8030:6:11", "parameters": {"id": 11220, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11219, "mutability": "mutable", "name": "self", "nameLocation": "8044:4:11", "nodeType": "VariableDeclaration", "scope": 11232, "src": "8037:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 11218, "name": "int256", "nodeType": "ElementaryTypeName", "src": "8037:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "8036:13:11"}, "returnParameters": {"id": 11223, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11222, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11232, "src": "8073:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11221, "name": "string", "nodeType": "ElementaryTypeName", "src": "8073:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8072:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11247, "nodeType": "FunctionDefinition", "src": "8143:117:11", "nodes": [], "body": {"id": 11246, "nodeType": "Block", "src": "8211:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11242, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11234, "src": "8247:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 11240, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "8235:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11241, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8238:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "8235:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 11243, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8235:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11239, "name": "italic", "nodeType": "Identifier", "overloadedDeclarations": [11202, 11217, 11232, 11247, 11262], "referencedDeclaration": 11202, "src": "8228:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11244, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8228:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11238, "id": 11245, "nodeType": "Return", "src": "8221:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "italic", "nameLocation": "8152:6:11", "parameters": {"id": 11235, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11234, "mutability": "mutable", "name": "self", "nameLocation": "8167:4:11", "nodeType": "VariableDeclaration", "scope": 11247, "src": "8159:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 11233, "name": "address", "nodeType": "ElementaryTypeName", "src": "8159:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "8158:14:11"}, "returnParameters": {"id": 11238, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11237, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11247, "src": "8196:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11236, "name": "string", "nodeType": "ElementaryTypeName", "src": "8196:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8195:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11262, "nodeType": "FunctionDefinition", "src": "8266:114:11", "nodes": [], "body": {"id": 11261, "nodeType": "Block", "src": "8331:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11257, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11249, "src": "8367:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 11255, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "8355:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11256, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8358:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "8355:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 11258, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8355:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11254, "name": "italic", "nodeType": "Identifier", "overloadedDeclarations": [11202, 11217, 11232, 11247, 11262], "referencedDeclaration": 11202, "src": "8348:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11259, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8348:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11253, "id": 11260, "nodeType": "Return", "src": "8341:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "italic", "nameLocation": "8275:6:11", "parameters": {"id": 11250, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11249, "mutability": "mutable", "name": "self", "nameLocation": "8287:4:11", "nodeType": "VariableDeclaration", "scope": 11262, "src": "8282:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 11248, "name": "bool", "nodeType": "ElementaryTypeName", "src": "8282:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "8281:11:11"}, "returnParameters": {"id": 11253, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11252, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11262, "src": "8316:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11251, "name": "string", "nodeType": "ElementaryTypeName", "src": "8316:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8315:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11277, "nodeType": "FunctionDefinition", "src": "8386:127:11", "nodes": [], "body": {"id": 11276, "nodeType": "Block", "src": "8464:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11272, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11264, "src": "8500:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 11270, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "8488:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11271, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8491:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "8488:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 11273, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8488:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11269, "name": "italic", "nodeType": "Identifier", "overloadedDeclarations": [11202, 11217, 11232, 11247, 11262], "referencedDeclaration": 11202, "src": "8481:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11274, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8481:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11268, "id": 11275, "nodeType": "Return", "src": "8474:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "italicBytes", "nameLocation": "8395:11:11", "parameters": {"id": 11265, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11264, "mutability": "mutable", "name": "self", "nameLocation": "8420:4:11", "nodeType": "VariableDeclaration", "scope": 11277, "src": "8407:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 11263, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "8407:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "8406:19:11"}, "returnParameters": {"id": 11268, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11267, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11277, "src": "8449:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11266, "name": "string", "nodeType": "ElementaryTypeName", "src": "8449:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8448:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11292, "nodeType": "FunctionDefinition", "src": "8519:124:11", "nodes": [], "body": {"id": 11291, "nodeType": "Block", "src": "8594:49:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11287, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11279, "src": "8630:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 11285, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "8618:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11286, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8621:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "8618:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 11288, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8618:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11284, "name": "italic", "nodeType": "Identifier", "overloadedDeclarations": [11202, 11217, 11232, 11247, 11262], "referencedDeclaration": 11202, "src": "8611:6:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11289, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8611:25:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11283, "id": 11290, "nodeType": "Return", "src": "8604:32:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "italicBytes32", "nameLocation": "8528:13:11", "parameters": {"id": 11280, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11279, "mutability": "mutable", "name": "self", "nameLocation": "8550:4:11", "nodeType": "VariableDeclaration", "scope": 11292, "src": "8542:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 11278, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8542:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "8541:14:11"}, "returnParameters": {"id": 11283, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11282, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11292, "src": "8579:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11281, "name": "string", "nodeType": "ElementaryTypeName", "src": "8579:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8578:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11305, "nodeType": "FunctionDefinition", "src": "8649:129:11", "nodes": [], "body": {"id": 11304, "nodeType": "Block", "src": "8726:52:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11300, "name": "UNDERLINE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10339, "src": "8755:9:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11301, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11294, "src": "8766:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11299, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "8743:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 11302, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8743:28:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11298, "id": 11303, "nodeType": "Return", "src": "8736:35:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "underline", "nameLocation": "8658:9:11", "parameters": {"id": 11295, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11294, "mutability": "mutable", "name": "self", "nameLocation": "8682:4:11", "nodeType": "VariableDeclaration", "scope": 11305, "src": "8668:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11293, "name": "string", "nodeType": "ElementaryTypeName", "src": "8668:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8667:20:11"}, "returnParameters": {"id": 11298, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11297, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11305, "src": "8711:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11296, "name": "string", "nodeType": "ElementaryTypeName", "src": "8711:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8710:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11320, "nodeType": "FunctionDefinition", "src": "8784:123:11", "nodes": [], "body": {"id": 11319, "nodeType": "Block", "src": "8855:52:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11315, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11307, "src": "8894:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 11313, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "8882:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11314, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "8885:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "8882:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 11316, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8882:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11312, "name": "underline", "nodeType": "Identifier", "overloadedDeclarations": [11305, 11320, 11335, 11350, 11365], "referencedDeclaration": 11305, "src": "8872:9:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11317, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8872:28:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11311, "id": 11318, "nodeType": "Return", "src": "8865:35:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "underline", "nameLocation": "8793:9:11", "parameters": {"id": 11308, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11307, "mutability": "mutable", "name": "self", "nameLocation": "8811:4:11", "nodeType": "VariableDeclaration", "scope": 11320, "src": "8803:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 11306, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8803:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8802:14:11"}, "returnParameters": {"id": 11311, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11310, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11320, "src": "8840:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11309, "name": "string", "nodeType": "ElementaryTypeName", "src": "8840:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8839:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11335, "nodeType": "FunctionDefinition", "src": "8913:122:11", "nodes": [], "body": {"id": 11334, "nodeType": "Block", "src": "8983:52:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11330, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11322, "src": "9022:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 11328, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "9010:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11329, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9013:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "9010:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 11331, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9010:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11327, "name": "underline", "nodeType": "Identifier", "overloadedDeclarations": [11305, 11320, 11335, 11350, 11365], "referencedDeclaration": 11305, "src": "9000:9:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11332, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9000:28:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11326, "id": 11333, "nodeType": "Return", "src": "8993:35:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "underline", "nameLocation": "8922:9:11", "parameters": {"id": 11323, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11322, "mutability": "mutable", "name": "self", "nameLocation": "8939:4:11", "nodeType": "VariableDeclaration", "scope": 11335, "src": "8932:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 11321, "name": "int256", "nodeType": "ElementaryTypeName", "src": "8932:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "8931:13:11"}, "returnParameters": {"id": 11326, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11325, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11335, "src": "8968:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11324, "name": "string", "nodeType": "ElementaryTypeName", "src": "8968:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "8967:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11350, "nodeType": "FunctionDefinition", "src": "9041:123:11", "nodes": [], "body": {"id": 11349, "nodeType": "Block", "src": "9112:52:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11345, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11337, "src": "9151:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 11343, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "9139:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11344, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9142:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "9139:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 11346, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9139:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11342, "name": "underline", "nodeType": "Identifier", "overloadedDeclarations": [11305, 11320, 11335, 11350, 11365], "referencedDeclaration": 11305, "src": "9129:9:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11347, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9129:28:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11341, "id": 11348, "nodeType": "Return", "src": "9122:35:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "underline", "nameLocation": "9050:9:11", "parameters": {"id": 11338, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11337, "mutability": "mutable", "name": "self", "nameLocation": "9068:4:11", "nodeType": "VariableDeclaration", "scope": 11350, "src": "9060:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 11336, "name": "address", "nodeType": "ElementaryTypeName", "src": "9060:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "9059:14:11"}, "returnParameters": {"id": 11341, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11340, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11350, "src": "9097:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11339, "name": "string", "nodeType": "ElementaryTypeName", "src": "9097:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9096:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11365, "nodeType": "FunctionDefinition", "src": "9170:120:11", "nodes": [], "body": {"id": 11364, "nodeType": "Block", "src": "9238:52:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11360, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11352, "src": "9277:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 11358, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "9265:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11359, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9268:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "9265:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 11361, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9265:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11357, "name": "underline", "nodeType": "Identifier", "overloadedDeclarations": [11305, 11320, 11335, 11350, 11365], "referencedDeclaration": 11305, "src": "9255:9:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11362, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9255:28:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11356, "id": 11363, "nodeType": "Return", "src": "9248:35:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "underline", "nameLocation": "9179:9:11", "parameters": {"id": 11353, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11352, "mutability": "mutable", "name": "self", "nameLocation": "9194:4:11", "nodeType": "VariableDeclaration", "scope": 11365, "src": "9189:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 11351, "name": "bool", "nodeType": "ElementaryTypeName", "src": "9189:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "9188:11:11"}, "returnParameters": {"id": 11356, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11355, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11365, "src": "9223:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11354, "name": "string", "nodeType": "ElementaryTypeName", "src": "9223:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9222:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11380, "nodeType": "FunctionDefinition", "src": "9296:133:11", "nodes": [], "body": {"id": 11379, "nodeType": "Block", "src": "9377:52:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11375, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11367, "src": "9416:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 11373, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "9404:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11374, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9407:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "9404:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 11376, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9404:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11372, "name": "underline", "nodeType": "Identifier", "overloadedDeclarations": [11305, 11320, 11335, 11350, 11365], "referencedDeclaration": 11305, "src": "9394:9:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11377, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9394:28:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11371, "id": 11378, "nodeType": "Return", "src": "9387:35:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "underlineBytes", "nameLocation": "9305:14:11", "parameters": {"id": 11368, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11367, "mutability": "mutable", "name": "self", "nameLocation": "9333:4:11", "nodeType": "VariableDeclaration", "scope": 11380, "src": "9320:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 11366, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "9320:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "9319:19:11"}, "returnParameters": {"id": 11371, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11370, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11380, "src": "9362:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11369, "name": "string", "nodeType": "ElementaryTypeName", "src": "9362:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9361:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11395, "nodeType": "FunctionDefinition", "src": "9435:130:11", "nodes": [], "body": {"id": 11394, "nodeType": "Block", "src": "9513:52:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11390, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11382, "src": "9552:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 11388, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "9540:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11389, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9543:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "9540:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 11391, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9540:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11387, "name": "underline", "nodeType": "Identifier", "overloadedDeclarations": [11305, 11320, 11335, 11350, 11365], "referencedDeclaration": 11305, "src": "9530:9:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11392, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9530:28:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11386, "id": 11393, "nodeType": "Return", "src": "9523:35:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "underlineBytes32", "nameLocation": "9444:16:11", "parameters": {"id": 11383, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11382, "mutability": "mutable", "name": "self", "nameLocation": "9469:4:11", "nodeType": "VariableDeclaration", "scope": 11395, "src": "9461:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 11381, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "9461:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "9460:14:11"}, "returnParameters": {"id": 11386, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11385, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11395, "src": "9498:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11384, "name": "string", "nodeType": "ElementaryTypeName", "src": "9498:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9497:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11408, "nodeType": "FunctionDefinition", "src": "9571:125:11", "nodes": [], "body": {"id": 11407, "nodeType": "Block", "src": "9646:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 11403, "name": "INVERSE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10342, "src": "9675:7:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 11404, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11397, "src": "9684:4:11", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11402, "name": "styleConcat", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10365, "src": "9663:11:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory,string memory) pure returns (string memory)"}}, "id": 11405, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9663:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11401, "id": 11406, "nodeType": "Return", "src": "9656:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "inverse", "nameLocation": "9580:7:11", "parameters": {"id": 11398, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11397, "mutability": "mutable", "name": "self", "nameLocation": "9602:4:11", "nodeType": "VariableDeclaration", "scope": 11408, "src": "9588:18:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11396, "name": "string", "nodeType": "ElementaryTypeName", "src": "9588:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9587:20:11"}, "returnParameters": {"id": 11401, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11400, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11408, "src": "9631:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11399, "name": "string", "nodeType": "ElementaryTypeName", "src": "9631:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9630:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11423, "nodeType": "FunctionDefinition", "src": "9702:119:11", "nodes": [], "body": {"id": 11422, "nodeType": "Block", "src": "9771:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11418, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11410, "src": "9808:4:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 11416, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "9796:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11417, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9799:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15458, "src": "9796:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_uint256_$returns$_t_string_memory_ptr_$", "typeString": "function (uint256) pure external returns (string memory)"}}, "id": 11419, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9796:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11415, "name": "inverse", "nodeType": "Identifier", "overloadedDeclarations": [11408, 11423, 11438, 11453, 11468], "referencedDeclaration": 11408, "src": "9788:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11420, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9788:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11414, "id": 11421, "nodeType": "Return", "src": "9781:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "inverse", "nameLocation": "9711:7:11", "parameters": {"id": 11411, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11410, "mutability": "mutable", "name": "self", "nameLocation": "9727:4:11", "nodeType": "VariableDeclaration", "scope": 11423, "src": "9719:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 11409, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "9719:7:11", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "9718:14:11"}, "returnParameters": {"id": 11414, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11413, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11423, "src": "9756:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11412, "name": "string", "nodeType": "ElementaryTypeName", "src": "9756:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9755:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11438, "nodeType": "FunctionDefinition", "src": "9827:118:11", "nodes": [], "body": {"id": 11437, "nodeType": "Block", "src": "9895:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11433, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11425, "src": "9932:4:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_int256", "typeString": "int256"}], "expression": {"id": 11431, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "9920:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11432, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "9923:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15466, "src": "9920:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_int256_$returns$_t_string_memory_ptr_$", "typeString": "function (int256) pure external returns (string memory)"}}, "id": 11434, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9920:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11430, "name": "inverse", "nodeType": "Identifier", "overloadedDeclarations": [11408, 11423, 11438, 11453, 11468], "referencedDeclaration": 11408, "src": "9912:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11435, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9912:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11429, "id": 11436, "nodeType": "Return", "src": "9905:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "inverse", "nameLocation": "9836:7:11", "parameters": {"id": 11426, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11425, "mutability": "mutable", "name": "self", "nameLocation": "9851:4:11", "nodeType": "VariableDeclaration", "scope": 11438, "src": "9844:11:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}, "typeName": {"id": 11424, "name": "int256", "nodeType": "ElementaryTypeName", "src": "9844:6:11", "typeDescriptions": {"typeIdentifier": "t_int256", "typeString": "int256"}}, "visibility": "internal"}], "src": "9843:13:11"}, "returnParameters": {"id": 11429, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11428, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11438, "src": "9880:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11427, "name": "string", "nodeType": "ElementaryTypeName", "src": "9880:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "9879:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11453, "nodeType": "FunctionDefinition", "src": "9951:119:11", "nodes": [], "body": {"id": 11452, "nodeType": "Block", "src": "10020:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11448, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11440, "src": "10057:4:11", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 11446, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "10045:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11447, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "10048:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15426, "src": "10045:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_address_$returns$_t_string_memory_ptr_$", "typeString": "function (address) pure external returns (string memory)"}}, "id": 11449, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10045:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11445, "name": "inverse", "nodeType": "Identifier", "overloadedDeclarations": [11408, 11423, 11438, 11453, 11468], "referencedDeclaration": 11408, "src": "10037:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11450, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10037:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11444, "id": 11451, "nodeType": "Return", "src": "10030:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "inverse", "nameLocation": "9960:7:11", "parameters": {"id": 11441, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11440, "mutability": "mutable", "name": "self", "nameLocation": "9976:4:11", "nodeType": "VariableDeclaration", "scope": 11453, "src": "9968:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 11439, "name": "address", "nodeType": "ElementaryTypeName", "src": "9968:7:11", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "9967:14:11"}, "returnParameters": {"id": 11444, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11443, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11453, "src": "10005:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11442, "name": "string", "nodeType": "ElementaryTypeName", "src": "10005:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "10004:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11468, "nodeType": "FunctionDefinition", "src": "10076:116:11", "nodes": [], "body": {"id": 11467, "nodeType": "Block", "src": "10142:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11463, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11455, "src": "10179:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "expression": {"id": 11461, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "10167:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11462, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "10170:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15450, "src": "10167:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bool_$returns$_t_string_memory_ptr_$", "typeString": "function (bool) pure external returns (string memory)"}}, "id": 11464, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10167:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11460, "name": "inverse", "nodeType": "Identifier", "overloadedDeclarations": [11408, 11423, 11438, 11453, 11468], "referencedDeclaration": 11408, "src": "10159:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11465, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10159:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11459, "id": 11466, "nodeType": "Return", "src": "10152:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "inverse", "nameLocation": "10085:7:11", "parameters": {"id": 11456, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11455, "mutability": "mutable", "name": "self", "nameLocation": "10098:4:11", "nodeType": "VariableDeclaration", "scope": 11468, "src": "10093:9:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 11454, "name": "bool", "nodeType": "ElementaryTypeName", "src": "10093:4:11", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "10092:11:11"}, "returnParameters": {"id": 11459, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11458, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11468, "src": "10127:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11457, "name": "string", "nodeType": "ElementaryTypeName", "src": "10127:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "10126:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11483, "nodeType": "FunctionDefinition", "src": "10198:129:11", "nodes": [], "body": {"id": 11482, "nodeType": "Block", "src": "10277:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11478, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11470, "src": "10314:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 11476, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "10302:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11477, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "10305:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15434, "src": "10302:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure external returns (string memory)"}}, "id": 11479, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10302:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11475, "name": "inverse", "nodeType": "Identifier", "overloadedDeclarations": [11408, 11423, 11438, 11453, 11468], "referencedDeclaration": 11408, "src": "10294:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11480, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10294:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11474, "id": 11481, "nodeType": "Return", "src": "10287:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "inverseBytes", "nameLocation": "10207:12:11", "parameters": {"id": 11471, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11470, "mutability": "mutable", "name": "self", "nameLocation": "10233:4:11", "nodeType": "VariableDeclaration", "scope": 11483, "src": "10220:17:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 11469, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "10220:5:11", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "10219:19:11"}, "returnParameters": {"id": 11474, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11473, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11483, "src": "10262:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11472, "name": "string", "nodeType": "ElementaryTypeName", "src": "10262:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "10261:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"id": 11498, "nodeType": "FunctionDefinition", "src": "10333:126:11", "nodes": [], "body": {"id": 11497, "nodeType": "Block", "src": "10409:50:11", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 11493, "name": "self", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 11485, "src": "10446:4:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 11491, "name": "vm", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10309, "src": "10434:2:11", "typeDescriptions": {"typeIdentifier": "t_contract$_VmSafe_$17281", "typeString": "contract VmSafe"}}, "id": 11492, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "10437:8:11", "memberName": "toString", "nodeType": "MemberAccess", "referencedDeclaration": 15442, "src": "10434:11:11", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$_t_bytes32_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes32) pure external returns (string memory)"}}, "id": 11494, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10434:17:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 11490, "name": "inverse", "nodeType": "Identifier", "overloadedDeclarations": [11408, 11423, 11438, 11453, 11468], "referencedDeclaration": 11408, "src": "10426:7:11", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_string_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (string memory) pure returns (string memory)"}}, "id": 11495, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "10426:26:11", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 11489, "id": 11496, "nodeType": "Return", "src": "10419:33:11"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "inverseBytes32", "nameLocation": "10342:14:11", "parameters": {"id": 11486, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11485, "mutability": "mutable", "name": "self", "nameLocation": "10365:4:11", "nodeType": "VariableDeclaration", "scope": 11498, "src": "10357:12:11", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 11484, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "10357:7:11", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "10356:14:11"}, "returnParameters": {"id": 11489, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11488, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 11498, "src": "10394:13:11", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11487, "name": "string", "nodeType": "ElementaryTypeName", "src": "10394:6:11", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "10393:15:11"}, "scope": 11499, "stateMutability": "pure", "virtual": false, "visibility": "internal"}], "abstract": false, "baseContracts": [], "canonicalName": "StdStyle", "contractDependencies": [], "contractKind": "library", "fullyImplemented": true, "linearizedBaseContracts": [11499], "name": "StdStyle", "nameLocation": "108:8:11", "scope": 11500, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 11}