{"abi": [], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/chimera/src/BaseTargetFunctions.sol\":\"BaseTargetFunctions\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@chimera/=lib/chimera/src/\",\":@recon/=lib/setup-helpers/src/\",\":chimera/=lib/chimera/src/\",\":ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":setup-helpers/=lib/setup-helpers/src/\"]},\"sources\":{\"lib/chimera/src/Asserts.sol\":{\"keccak256\":\"0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626\",\"dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK\"]},\"lib/chimera/src/BaseProperties.sol\":{\"keccak256\":\"0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848\",\"dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w\"]},\"lib/chimera/src/BaseSetup.sol\":{\"keccak256\":\"0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe\",\"dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1\"]},\"lib/chimera/src/BaseTargetFunctions.sol\":{\"keccak256\":\"0xe3b3de6200ab7039a14bb0a2a7e090402a36bd2c0c31c6d677d766b0f335bd60\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5326abd254a25c5bf8c2219e36641bc1288114271678dac8611d8365fc432851\",\"dweb:/ipfs/QmR4BF3JrTU3XhPVY4vPxGCQFXbUv94Bi48FjvgphroPxJ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chimera/=lib/chimera/src/", "@recon/=lib/setup-helpers/src/", "chimera/=lib/chimera/src/", "ds-test/=lib/chimera/lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "setup-helpers/=lib/setup-helpers/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/chimera/src/BaseTargetFunctions.sol": "BaseTargetFunctions"}, "evmVersion": "prague", "libraries": {}}, "sources": {"lib/chimera/src/Asserts.sol": {"keccak256": "0xd6596903dda8bf4dcc7faca76b2942052b26ba7ab7741c94d9d215a18a351fb9", "urls": ["bzz-raw://4cf4fb4fa26ae08bf5cbae0d5d5c1b12d192597fd55136d687d1b3c1e191f626", "dweb:/ipfs/QmYRofUUxoqAGB4q9cb789FvkSPtgjnumnfFfMqdfjKJNK"], "license": "MIT"}, "lib/chimera/src/BaseProperties.sol": {"keccak256": "0x3a3e1b9d13c963588bf6531995842380b379fb6283bd0e7826ce6e909a4c443a", "urls": ["bzz-raw://07fe12fd8c12c454d27dab61042b280d4bd69f21dc2164b4693ea9a4ec6ad848", "dweb:/ipfs/QmSfhiHM5m4GFEwT1AopojoNQBse2AYe42efBby1Yrnb3w"], "license": "MIT"}, "lib/chimera/src/BaseSetup.sol": {"keccak256": "0x8ba1382b7135fff00ead02a4807e7c683612221a78153a26d722e0c1ed979107", "urls": ["bzz-raw://de0bd9035d46061850431e2e52123100ae41aa6558a5ee1943698eaff404fbbe", "dweb:/ipfs/QmdGkpe77yzvMKKDeos4BotUSpXycd3ZNn2ELhbuv6SiW1"], "license": "MIT"}, "lib/chimera/src/BaseTargetFunctions.sol": {"keccak256": "0xe3b3de6200ab7039a14bb0a2a7e090402a36bd2c0c31c6d677d766b0f335bd60", "urls": ["bzz-raw://5326abd254a25c5bf8c2219e36641bc1288114271678dac8611d8365fc432851", "dweb:/ipfs/QmR4BF3JrTU3XhPVY4vPxGCQFXbUv94Bi48FjvgphroPxJ"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "lib/chimera/src/BaseTargetFunctions.sol", "id": 106, "exportedSymbols": {"Asserts": [81], "BaseProperties": [88], "BaseTargetFunctions": [105]}, "nodeType": "SourceUnit", "src": "32:186:3", "nodes": [{"id": 96, "nodeType": "PragmaDirective", "src": "32:23:3", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 98, "nodeType": "ImportDirective", "src": "57:52:3", "nodes": [], "absolutePath": "lib/chimera/src/BaseProperties.sol", "file": "./BaseProperties.sol", "nameLocation": "-1:-1:-1", "scope": 106, "sourceUnit": 89, "symbolAliases": [{"foreign": {"id": 97, "name": "BaseProperties", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 88, "src": "65:14:3", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 100, "nodeType": "ImportDirective", "src": "110:38:3", "nodes": [], "absolutePath": "lib/chimera/src/Asserts.sol", "file": "./Asserts.sol", "nameLocation": "-1:-1:-1", "scope": 106, "sourceUnit": 82, "symbolAliases": [{"foreign": {"id": 99, "name": "Asserts", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 81, "src": "118:7:3", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 105, "nodeType": "ContractDefinition", "src": "150:67:3", "nodes": [], "abstract": true, "baseContracts": [{"baseName": {"id": 101, "name": "BaseProperties", "nameLocations": ["191:14:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 88, "src": "191:14:3"}, "id": 102, "nodeType": "InheritanceSpecifier", "src": "191:14:3"}, {"baseName": {"id": 103, "name": "Asserts", "nameLocations": ["207:7:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 81, "src": "207:7:3"}, "id": 104, "nodeType": "InheritanceSpecifier", "src": "207:7:3"}], "canonicalName": "BaseTargetFunctions", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": false, "linearizedBaseContracts": [105, 81, 88, 94], "name": "BaseTargetFunctions", "nameLocation": "168:19:3", "scope": 106, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 3}