// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

contract Counter {
    uint256 public number;
    uint256 public constant MAX_VALUE = 1000;

    event NumberChanged(uint256 oldValue, uint256 newValue);
    event CounterReset();

    function setNumber(uint256 newNumber) public {
        require(newNumber <= MAX_VALUE, "Number exceeds maximum value");
        uint256 oldValue = number;
        number = newNumber;
        emit NumberChanged(oldValue, newNumber);
    }

    function increment() public {
        require(number < MAX_VALUE, "Cannot increment beyond maximum value");
        uint256 oldValue = number;
        number++;
        emit NumberChanged(oldValue, number);
    }

    function decrement() public {
        require(number > 0, "Cannot decrement below zero");
        uint256 oldValue = number;
        number--;
        emit NumberChanged(oldValue, number);
    }

    function reset() public {
        uint256 oldValue = number;
        number = 0;
        emit NumberChanged(oldValue, 0);
        emit CounterReset();
    }

    function add(uint256 value) public {
        require(number + value <= MAX_VALUE, "Addition would exceed maximum value");
        uint256 oldValue = number;
        number += value;
        emit NumberChanged(oldValue, number);
    }

    function subtract(uint256 value) public {
        require(number >= value, "Subtraction would underflow");
        uint256 oldValue = number;
        number -= value;
        emit NumberChanged(oldValue, number);
    }
}
