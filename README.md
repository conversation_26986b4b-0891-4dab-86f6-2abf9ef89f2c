# Solidity Counter Project with Forge and Halmos

This project demonstrates a simple Solidity counter contract with comprehensive testing using both **Forge** (traditional fuzzing) and **Halmos** (symbolic execution).

## Project Structure

```
├── src/
│   └── Counter.sol          # Enhanced counter contract with safety checks
├── test/
│   ├── Counter.t.sol        # Forge unit and fuzz tests
│   └── CounterHalmos.t.sol  # Halmos symbolic execution tests
├── script/
│   └── Counter.s.sol        # Deployment script
└── foundry.toml             # Foundry configuration
```

## Counter Contract Features

The `Counter.sol` contract includes:

- **Basic Operations**: increment, decrement, reset
- **Advanced Operations**: add, subtract, setNumber
- **Safety Bounds**: Maximum value of 1000, prevents overflow/underflow
- **Events**: NumberChanged and CounterReset events
- **Input Validation**: Comprehensive require statements

## Testing Approaches

### 1. Forge Testing (Traditional Fuzzing)

Located in `test/Counter.t.sol`, includes:

- Unit tests for all functions
- Boundary condition testing
- Fuzz tests with random inputs
- Error condition testing with `vm.expectRevert`

**Run Forge tests:**

```shell
forge test
```

**Run with verbose output:**

```shell
forge test -vv
```

### 2. Halmos Testing (Symbolic Execution)

Located in `test/CounterHalmos.t.sol`, includes:

- Symbolic execution tests that explore all possible execution paths
- Invariant checking (counter never exceeds MAX_VALUE)
- Property-based testing for mathematical operations
- Sequence operation safety verification

**Run all Halmos tests:**

```shell
halmos --contract CounterHalmosTest
```

**Run specific Halmos test:**

```shell
halmos --contract CounterHalmosTest --function check_increment_valid_cases
```

## Key Differences: Forge vs Halmos

| Aspect             | Forge                                  | Halmos                                       |
| ------------------ | -------------------------------------- | -------------------------------------------- |
| **Testing Method** | Random input fuzzing                   | Symbolic execution                           |
| **Coverage**       | Statistical coverage                   | Mathematical proof of correctness            |
| **Input Space**    | Samples random inputs                  | Explores all possible inputs symbolically    |
| **Execution**      | Concrete execution with random values  | Abstract execution with symbolic values      |
| **Proof Strength** | High confidence through many test runs | Mathematical certainty for tested properties |
| **Performance**    | Fast execution                         | Slower but more thorough                     |

## Installation Requirements

### Foundry (Forge)

```shell
curl -L https://foundry.paradigm.xyz | bash
foundryup
```

### Halmos

```shell
pipx install halmos
```

## Usage Examples

### Build the project

```shell
forge build
```

### Run all tests

```shell
# Forge tests
forge test

# Halmos symbolic execution
halmos --contract CounterHalmosTest
```

### Test specific functions

```shell
# Test increment functionality with Forge
forge test --match-test test_Increment

# Test increment safety with Halmos
halmos --contract CounterHalmosTest --function check_increment_valid_cases
```

## Test Results Summary

- **Forge Tests**: 16 tests passed (unit tests + fuzz tests)
- **Halmos Tests**: 9 symbolic execution tests passed
- **Coverage**: Both approaches verify contract safety and correctness

## Key Insights

1. **Forge** is excellent for rapid development and catching common bugs through random testing
2. **Halmos** provides mathematical guarantees about contract behavior through symbolic execution
3. **Combined approach** offers both speed (Forge) and thoroughness (Halmos)
4. **Symbolic execution** can find edge cases that random fuzzing might miss

This project demonstrates how to effectively combine traditional fuzzing with symbolic execution for comprehensive smart contract testing.
