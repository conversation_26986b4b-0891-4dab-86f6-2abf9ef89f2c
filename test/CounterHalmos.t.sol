// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Test} from "forge-std/Test.sol";
import {Counter} from "../src/Counter.sol";

contract CounterHalmosTest is Test {
    Counter public counter;

    function setUp() public {
        counter = new Counter();
    }

    // Halmos symbolic execution tests
    // These tests use symbolic inputs to explore all possible execution paths
    // Note: Halmos doesn't support vm.expectRevert, so we test only valid operations

    /// @custom:halmos --function check_increment_valid_cases
    function check_increment_valid_cases(uint256 initial) public {
        // Only test valid increment cases
        vm.assume(initial < counter.MAX_VALUE());

        counter.setNumber(initial);
        counter.increment();

        assert(counter.number() == initial + 1);
        assert(counter.number() <= counter.MAX_VALUE());
    }

    /// @custom:halmos --function check_decrement_valid_cases
    function check_decrement_valid_cases(uint256 initial) public {
        // Only test valid decrement cases
        vm.assume(initial > 0);
        vm.assume(initial <= counter.MAX_VALUE());

        counter.setNumber(initial);
        counter.decrement();

        assert(counter.number() == initial - 1);
    }

    /// @custom:halmos --function check_add_operation_valid_cases
    function check_add_operation_valid_cases(uint256 initial, uint256 value) public {
        // Only test valid addition cases
        vm.assume(initial <= counter.MAX_VALUE());
        vm.assume(value > 0);
        vm.assume(initial + value <= counter.MAX_VALUE());

        counter.setNumber(initial);
        counter.add(value);

        assert(counter.number() == initial + value);
        assert(counter.number() <= counter.MAX_VALUE());
    }

    /// @custom:halmos --function check_subtract_operation_valid_cases
    function check_subtract_operation_valid_cases(uint256 initial, uint256 value) public {
        // Only test valid subtraction cases
        vm.assume(initial <= counter.MAX_VALUE());
        vm.assume(value > 0);
        vm.assume(initial >= value);

        counter.setNumber(initial);
        counter.subtract(value);

        assert(counter.number() == initial - value);
    }

    /// @custom:halmos --function check_set_number_valid_cases
    function check_set_number_valid_cases(uint256 value) public {
        // Only test valid set number cases
        vm.assume(value <= counter.MAX_VALUE());

        counter.setNumber(value);
        assert(counter.number() == value);
    }

    /// @custom:halmos --function check_reset_always_works
    function check_reset_always_works(uint256 initial) public {
        vm.assume(initial <= counter.MAX_VALUE());

        counter.setNumber(initial);
        counter.reset();
        assert(counter.number() == 0);
    }

    /// @custom:halmos --function check_invariant_never_exceeds_max
    function check_invariant_never_exceeds_max(uint256 initial, uint8 operation, uint256 value) public {
        vm.assume(initial <= counter.MAX_VALUE());
        vm.assume(value <= counter.MAX_VALUE());
        vm.assume(value > 0);

        counter.setNumber(initial);

        // Test different valid operations only
        if (operation == 0 && initial < counter.MAX_VALUE()) {
            // Increment (only if safe)
            counter.increment();
        } else if (operation == 1 && initial > 0) {
            // Decrement (only if safe)
            counter.decrement();
        } else if (operation == 2 && initial + value <= counter.MAX_VALUE()) {
            // Add (only if safe)
            counter.add(value);
        } else if (operation == 3 && initial >= value) {
            // Subtract (only if safe)
            counter.subtract(value);
        } else if (operation == 4) {
            // Reset (always safe)
            counter.reset();
        } else if (operation == 5 && value <= counter.MAX_VALUE()) {
            // Set number (only if safe)
            counter.setNumber(value);
        }

        // Invariant: counter should never exceed MAX_VALUE
        assert(counter.number() <= counter.MAX_VALUE());
    }

    /// @custom:halmos --function check_sequence_operations_safety
    function check_sequence_operations_safety(uint256 initial, uint256 addValue, uint256 subValue) public {
        vm.assume(initial <= counter.MAX_VALUE());
        vm.assume(addValue > 0 && addValue <= 100);
        vm.assume(subValue > 0 && subValue <= 100);
        vm.assume(initial + addValue <= counter.MAX_VALUE());
        vm.assume(initial + addValue >= subValue);

        counter.setNumber(initial);

        // Perform sequence: add then subtract
        counter.add(addValue);
        uint256 afterAdd = counter.number();
        assert(afterAdd == initial + addValue);

        counter.subtract(subValue);
        uint256 afterSubtract = counter.number();
        assert(afterSubtract == initial + addValue - subValue);

        // Invariants should hold throughout
        assert(afterAdd <= counter.MAX_VALUE());
        assert(afterSubtract <= counter.MAX_VALUE());
    }

    /// @custom:halmos --function check_multiple_increments
    function check_multiple_increments(uint256 initial, uint8 times) public {
        vm.assume(initial <= counter.MAX_VALUE());
        vm.assume(times > 0 && times <= 10);
        vm.assume(initial + times <= counter.MAX_VALUE());

        counter.setNumber(initial);

        for (uint256 i = 0; i < times; i++) {
            counter.increment();
        }

        assert(counter.number() == initial + times);
        assert(counter.number() <= counter.MAX_VALUE());
    }
}
