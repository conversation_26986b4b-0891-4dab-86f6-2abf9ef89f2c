// SPDX-License-Identifier: GPL-2.0
pragma solidity ^0.8.0;

import {BaseTargetFunctions} from "@chimera/BaseTargetFunctions.sol";
import {BeforeAfter} from "../BeforeAfter.sol";
import {Properties} from "../Properties.sol";
// Chimera deps
import {vm} from "@chimera/Hevm.sol";

// Helpers
import {Panic} from "@recon/Panic.sol";

import "src/Counter.sol";

abstract contract CounterTargets is
    BaseTargetFunctions,
    Properties
{
    /// CUSTOM TARGET FUNCTIONS - Add your own target functions here ///


    /// AUTO GENERATED TARGET FUNCTIONS - WARNING: DO NOT DELETE OR MODIFY THIS LINE ///

    function counter_add(uint256 value) public asActor {
        counter.add(value);
    }

    function counter_decrement() public asActor {
        counter.decrement();
    }

    function counter_increment() public asActor {
        counter.increment();
    }

    function counter_reset() public asActor {
        counter.reset();
    }

    function counter_setNumber(uint256 newNumber) public asActor {
        counter.setNumber(newNumber);
    }

    function counter_subtract(uint256 value) public asActor {
        counter.subtract(value);
    }
}