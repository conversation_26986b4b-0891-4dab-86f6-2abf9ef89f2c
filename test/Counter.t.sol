// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Test} from "forge-std/Test.sol";
import {Counter} from "../src/Counter.sol";

contract CounterTest is Test {
    Counter public counter;

    function setUp() public {
        counter = new Counter();
    }

    function test_InitialValue() public {
        assertEq(counter.number(), 0);
    }

    function test_Increment() public {
        counter.increment();
        assertEq(counter.number(), 1);

        counter.increment();
        assertEq(counter.number(), 2);
    }

    function test_Decrement() public {
        counter.setNumber(5);
        counter.decrement();
        assertEq(counter.number(), 4);

        counter.decrement();
        assertEq(counter.number(), 3);
    }

    function test_Reset() public {
        counter.setNumber(100);
        counter.reset();
        assertEq(counter.number(), 0);
    }

    function test_Add() public {
        counter.setNumber(10);
        counter.add(5);
        assertEq(counter.number(), 15);
    }

    function test_Subtract() public {
        counter.setNumber(10);
        counter.subtract(3);
        assertEq(counter.number(), 7);
    }

    function test_SetNumber() public {
        counter.setNumber(42);
        assertEq(counter.number(), 42);
    }

    // Test boundary conditions
    function test_IncrementAtMaxValue() public {
        counter.setNumber(1000);
        vm.expectRevert("Cannot increment beyond maximum value");
        counter.increment();
    }

    function test_DecrementAtZero() public {
        vm.expectRevert("Cannot decrement below zero");
        counter.decrement();
    }

    function test_SetNumberAboveMax() public {
        vm.expectRevert("Number exceeds maximum value");
        counter.setNumber(1001);
    }

    function test_AddOverflow() public {
        counter.setNumber(999);
        vm.expectRevert("Addition would exceed maximum value");
        counter.add(2);
    }

    function test_SubtractUnderflow() public {
        counter.setNumber(5);
        vm.expectRevert("Subtraction would underflow");
        counter.subtract(6);
    }

    // Fuzz tests
    function testFuzz_SetNumber(uint256 x) public {
        vm.assume(x <= 1000);
        counter.setNumber(x);
        assertEq(counter.number(), x);
    }

    function testFuzz_Add(uint256 initial, uint256 value) public {
        vm.assume(initial <= 1000);
        vm.assume(value <= 1000);
        vm.assume(initial + value <= 1000);

        counter.setNumber(initial);
        counter.add(value);
        assertEq(counter.number(), initial + value);
    }

    function testFuzz_Subtract(uint256 initial, uint256 value) public {
        vm.assume(initial <= 1000);
        vm.assume(value <= initial);

        counter.setNumber(initial);
        counter.subtract(value);
        assertEq(counter.number(), initial - value);
    }

    function testFuzz_IncrementSequence(uint8 times) public {
        vm.assume(times <= 100);

        for (uint256 i = 0; i < times; i++) {
            counter.increment();
        }
        assertEq(counter.number(), times);
    }
}
