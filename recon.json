{"out/Counter.sol/Counter.json": {"functions": [{"signature": "add(uint256)", "actor": "actor", "mode": "normal"}, {"signature": "decrement()", "actor": "actor", "mode": "normal"}, {"signature": "increment()", "actor": "actor", "mode": "normal"}, {"signature": "reset()", "actor": "actor", "mode": "normal"}, {"signature": "setNumber(uint256)", "actor": "actor", "mode": "normal"}, {"signature": "subtract(uint256)", "actor": "actor", "mode": "normal"}], "separated": true}}