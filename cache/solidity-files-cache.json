{"_format": "", "paths": {"artifacts": "out", "build_infos": "out/build-info", "sources": "src", "tests": "test", "scripts": "script", "libraries": ["lib"]}, "files": {"lib/chimera/src/Asserts.sol": {"lastModificationDate": 1757009664834, "contentHash": "d368b2df55779c60", "interfaceReprHash": null, "sourceName": "lib/chimera/src/Asserts.sol", "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Asserts": {"0.8.29": {"default": {"path": "Asserts.sol/Asserts.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/chimera/src/BaseProperties.sol": {"lastModificationDate": 1757009664834, "contentHash": "97fcd949e690a9c9", "interfaceReprHash": null, "sourceName": "lib/chimera/src/BaseProperties.sol", "imports": ["lib/chimera/src/BaseSetup.sol"], "versionRequirement": "^0.8.0", "artifacts": {"BaseProperties": {"0.8.29": {"default": {"path": "BaseProperties.sol/BaseProperties.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/chimera/src/BaseSetup.sol": {"lastModificationDate": 1757009664835, "contentHash": "d9a0ffdaae69b53a", "interfaceReprHash": null, "sourceName": "lib/chimera/src/BaseSetup.sol", "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"BaseSetup": {"0.8.29": {"default": {"path": "BaseSetup.sol/BaseSetup.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/chimera/src/BaseTargetFunctions.sol": {"lastModificationDate": 1757009664835, "contentHash": "85e4abf77ac9833b", "interfaceReprHash": null, "sourceName": "lib/chimera/src/BaseTargetFunctions.sol", "imports": ["lib/chimera/src/Asserts.sol", "lib/chimera/src/BaseProperties.sol", "lib/chimera/src/BaseSetup.sol"], "versionRequirement": "^0.8.0", "artifacts": {"BaseTargetFunctions": {"0.8.29": {"default": {"path": "BaseTargetFunctions.sol/BaseTargetFunctions.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/chimera/src/CryticAsserts.sol": {"lastModificationDate": 1757009664835, "contentHash": "dff3af921604b57f", "interfaceReprHash": null, "sourceName": "lib/chimera/src/CryticAsserts.sol", "imports": ["lib/chimera/src/Asserts.sol"], "versionRequirement": "^0.8.0", "artifacts": {"CryticAsserts": {"0.8.29": {"default": {"path": "CryticAsserts.sol/CryticAsserts.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/chimera/src/FoundryAsserts.sol": {"lastModificationDate": 1757009664835, "contentHash": "10a6e7a75cffd8a5", "interfaceReprHash": null, "sourceName": "lib/chimera/src/FoundryAsserts.sol", "imports": ["lib/chimera/src/Asserts.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": "^0.8.0", "artifacts": {"FoundryAsserts": {"0.8.29": {"default": {"path": "FoundryAsserts.sol/FoundryAsserts.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/chimera/src/Hevm.sol": {"lastModificationDate": 1757009664835, "contentHash": "e1610c8240e70dcd", "interfaceReprHash": null, "sourceName": "lib/chimera/src/Hevm.sol", "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"IHevm": {"0.8.29": {"default": {"path": "Hevm.sol/IHevm.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Base.sol": {"lastModificationDate": 1757009238566, "contentHash": "b30affbf365427e2", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Base.sol", "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"CommonBase": {"0.8.29": {"default": {"path": "Base.sol/CommonBase.json", "build_id": "e3f7bd7a4e03bd89"}}}, "ScriptBase": {"0.8.29": {"default": {"path": "Base.sol/ScriptBase.json", "build_id": "e3f7bd7a4e03bd89"}}}, "TestBase": {"0.8.29": {"default": {"path": "Base.sol/TestBase.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Script.sol": {"lastModificationDate": 1757009238566, "contentHash": "654eb74437773a2d", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Script.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Script": {"0.8.29": {"default": {"path": "Script.sol/Script.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdAssertions.sol": {"lastModificationDate": 1757009238566, "contentHash": "02aafa55c6c27fcf", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdAssertions.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdAssertions": {"0.8.29": {"default": {"path": "StdAssertions.sol/StdAssertions.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdChains.sol": {"lastModificationDate": 1757009238566, "contentHash": "a40952ce0d242817", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdChains.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdChains": {"0.8.29": {"default": {"path": "StdChains.sol/StdChains.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdCheats.sol": {"lastModificationDate": 1757009238566, "contentHash": "30325e8cda32c7ae", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdCheats.sol", "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdCheats": {"0.8.29": {"default": {"path": "StdCheats.sol/StdCheats.json", "build_id": "e3f7bd7a4e03bd89"}}}, "StdCheatsSafe": {"0.8.29": {"default": {"path": "StdCheats.sol/StdCheatsSafe.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdConstants.sol": {"lastModificationDate": 1757009238566, "contentHash": "23303eb7e922efe4", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdConstants.sol", "imports": ["lib/forge-std/src/Vm.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdConstants": {"0.8.29": {"default": {"path": "StdConstants.sol/StdConstants.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdError.sol": {"lastModificationDate": 1757009238566, "contentHash": "a1a86c7115e2cdf3", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdError.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdError": {"0.8.29": {"default": {"path": "StdError.sol/stdError.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdInvariant.sol": {"lastModificationDate": 1757009238566, "contentHash": "0111ef959dff6f54", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdInvariant.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdInvariant": {"0.8.29": {"default": {"path": "StdInvariant.sol/StdInvariant.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdJson.sol": {"lastModificationDate": 1757009238566, "contentHash": "5fb1b35c8fb281fd", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdJson.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdJson": {"0.8.29": {"default": {"path": "StdJson.sol/stdJson.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdMath.sol": {"lastModificationDate": 1757009238566, "contentHash": "72584abebada1e7a", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdMath.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdMath": {"0.8.29": {"default": {"path": "StdMath.sol/stdMath.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStorage.sol": {"lastModificationDate": 1757009238566, "contentHash": "9a44dcb9bda3bfa9", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdStorage.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdStorage": {"0.8.29": {"default": {"path": "StdStorage.sol/stdStorage.json", "build_id": "e3f7bd7a4e03bd89"}}}, "stdStorageSafe": {"0.8.29": {"default": {"path": "StdStorage.sol/stdStorageSafe.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStyle.sol": {"lastModificationDate": 1757009238567, "contentHash": "ee166ef95092736e", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdStyle.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"StdStyle": {"0.8.29": {"default": {"path": "StdStyle.sol/StdStyle.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdToml.sol": {"lastModificationDate": 1757009238567, "contentHash": "fc667e4ecb7fa86c", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdToml.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdToml": {"0.8.29": {"default": {"path": "StdToml.sol/stdToml.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdUtils.sol": {"lastModificationDate": 1757009238567, "contentHash": "b7cdeb66252de708", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdUtils.sol", "imports": ["lib/forge-std/src/Vm.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdUtils": {"0.8.29": {"default": {"path": "StdUtils.sol/StdUtils.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Test.sol": {"lastModificationDate": 1757009238567, "contentHash": "f56119a09f81c62c", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Test.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Test": {"0.8.29": {"default": {"path": "Test.sol/Test.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Vm.sol": {"lastModificationDate": 1757009238567, "contentHash": "c9680a64b964ef8d", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Vm.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Vm": {"0.8.29": {"default": {"path": "Vm.sol/Vm.json", "build_id": "e3f7bd7a4e03bd89"}}}, "VmSafe": {"0.8.29": {"default": {"path": "Vm.sol/VmSafe.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/console.sol": {"lastModificationDate": 1757009238567, "contentHash": "bae85493a76fb054", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/console.sol", "imports": [], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"console": {"0.8.29": {"default": {"path": "console.sol/console.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/console2.sol": {"lastModificationDate": 1757009238567, "contentHash": "49a7da3dfc404603", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/console2.sol", "imports": ["lib/forge-std/src/console.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"console2": {"0.8.29": {"default": {"path": "console2.sol/console2.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"lastModificationDate": 1757009238568, "contentHash": "b680a332ebf10901", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/interfaces/IMulticall3.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"IMulticall3": {"0.8.29": {"default": {"path": "IMulticall3.sol/IMulticall3.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/forge-std/src/safeconsole.sol": {"lastModificationDate": 1757009238568, "contentHash": "621653b34a6691ea", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/safeconsole.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"safeconsole": {"0.8.29": {"default": {"path": "safeconsole.sol/safeconsole.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "lib/setup-helpers/src/ActorManager.sol": {"lastModificationDate": 1757009675031, "contentHash": "1172074d3c73c35d", "interfaceReprHash": null, "sourceName": "lib/setup-helpers/src/ActorManager.sol", "imports": ["lib/chimera/src/BaseSetup.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/EnumerableSet.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ActorManager": {"0.8.29": {"default": {"path": "ActorManager.sol/ActorManager.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/setup-helpers/src/AssetManager.sol": {"lastModificationDate": 1757009675031, "contentHash": "5c733bdd2849a03b", "interfaceReprHash": null, "sourceName": "lib/setup-helpers/src/AssetManager.sol", "imports": ["lib/chimera/src/BaseSetup.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol"], "versionRequirement": "^0.8.0", "artifacts": {"AssetManager": {"0.8.29": {"default": {"path": "AssetManager.sol/AssetManager.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/setup-helpers/src/EnumerableSet.sol": {"lastModificationDate": 1757009675031, "contentHash": "0380c81466f451c5", "interfaceReprHash": null, "sourceName": "lib/setup-helpers/src/EnumerableSet.sol", "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"EnumerableSet": {"0.8.29": {"default": {"path": "EnumerableSet.sol/EnumerableSet.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/setup-helpers/src/MockERC20.sol": {"lastModificationDate": 1757009675031, "contentHash": "894f05876d32361e", "interfaceReprHash": null, "sourceName": "lib/setup-helpers/src/MockERC20.sol", "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"ERC20": {"0.8.29": {"default": {"path": "MockERC20.sol/ERC20.json", "build_id": "a74cedb45c32d659"}}}, "MockERC20": {"0.8.29": {"default": {"path": "MockERC20.sol/MockERC20.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/setup-helpers/src/Panic.sol": {"lastModificationDate": 1757009675031, "contentHash": "65d1c7f51bb7c53f", "interfaceReprHash": null, "sourceName": "lib/setup-helpers/src/Panic.sol", "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Panic": {"0.8.29": {"default": {"path": "Panic.sol/Panic.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "lib/setup-helpers/src/Utils.sol": {"lastModificationDate": 1757009675031, "contentHash": "9ce33a429519eab3", "interfaceReprHash": null, "sourceName": "lib/setup-helpers/src/Utils.sol", "imports": ["lib/setup-helpers/src/Panic.sol"], "versionRequirement": "^0.8.0", "artifacts": {"Utils": {"0.8.29": {"default": {"path": "Utils.sol/Utils.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "script/Counter.s.sol": {"lastModificationDate": 1757009236546, "contentHash": "2f28174698c31b47", "interfaceReprHash": null, "sourceName": "script/Counter.s.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol", "src/Counter.sol"], "versionRequirement": "^0.8.13", "artifacts": {"CounterScript": {"0.8.29": {"default": {"path": "Counter.s.sol/CounterScript.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "src/Counter.sol": {"lastModificationDate": 1757009262636, "contentHash": "18ce396731ec01be", "interfaceReprHash": null, "sourceName": "src/Counter.sol", "imports": [], "versionRequirement": "^0.8.13", "artifacts": {"Counter": {"0.8.29": {"default": {"path": "Counter.sol/Counter.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "test/Counter.t.sol": {"lastModificationDate": 1757009293034, "contentHash": "1d29b290a23eae99", "interfaceReprHash": null, "sourceName": "test/Counter.t.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol", "src/Counter.sol"], "versionRequirement": "^0.8.13", "artifacts": {"CounterTest": {"0.8.29": {"default": {"path": "Counter.t.sol/CounterTest.json", "build_id": "e3f7bd7a4e03bd89"}}}}, "seenByCompiler": true}, "test/CounterHalmos.t.sol": {"lastModificationDate": 1757009423078, "contentHash": "e18e4fc7a1233c89", "interfaceReprHash": null, "sourceName": "test/CounterHalmos.t.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol", "src/Counter.sol"], "versionRequirement": "^0.8.13", "artifacts": {"CounterHalmosTest": {"0.8.29": {"default": {"path": "CounterHalmos.t.sol/CounterHalmosTest.json", "build_id": "03dedf8f401cfbc7"}}}}, "seenByCompiler": true}, "test/recon/BeforeAfter.sol": {"lastModificationDate": 1757009695189, "contentHash": "d29558e37dc4f264", "interfaceReprHash": null, "sourceName": "test/recon/BeforeAfter.sol", "imports": ["lib/chimera/src/BaseSetup.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/ActorManager.sol", "lib/setup-helpers/src/AssetManager.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol", "lib/setup-helpers/src/Panic.sol", "lib/setup-helpers/src/Utils.sol", "src/Counter.sol", "test/recon/Setup.sol"], "versionRequirement": "^0.8.0", "artifacts": {"BeforeAfter": {"0.8.29": {"default": {"path": "BeforeAfter.sol/BeforeAfter.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "test/recon/CryticTester.sol": {"lastModificationDate": 1757009695190, "contentHash": "77c570813495ca65", "interfaceReprHash": null, "sourceName": "test/recon/CryticTester.sol", "imports": ["lib/chimera/src/Asserts.sol", "lib/chimera/src/BaseProperties.sol", "lib/chimera/src/BaseSetup.sol", "lib/chimera/src/BaseTargetFunctions.sol", "lib/chimera/src/CryticAsserts.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/ActorManager.sol", "lib/setup-helpers/src/AssetManager.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol", "lib/setup-helpers/src/Panic.sol", "lib/setup-helpers/src/Utils.sol", "src/Counter.sol", "test/recon/BeforeAfter.sol", "test/recon/Properties.sol", "test/recon/Setup.sol", "test/recon/TargetFunctions.sol", "test/recon/targets/AdminTargets.sol", "test/recon/targets/CounterTargets.sol", "test/recon/targets/DoomsdayTargets.sol", "test/recon/targets/ManagersTargets.sol"], "versionRequirement": "^0.8.0", "artifacts": {"CryticTester": {"0.8.29": {"default": {"path": "CryticTester.sol/CryticTester.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "test/recon/CryticToFoundry.sol": {"lastModificationDate": 1757009695191, "contentHash": "85c00d724d610087", "interfaceReprHash": null, "sourceName": "test/recon/CryticToFoundry.sol", "imports": ["lib/chimera/src/Asserts.sol", "lib/chimera/src/BaseProperties.sol", "lib/chimera/src/BaseSetup.sol", "lib/chimera/src/BaseTargetFunctions.sol", "lib/chimera/src/FoundryAsserts.sol", "lib/chimera/src/Hevm.sol", "lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol", "lib/setup-helpers/src/ActorManager.sol", "lib/setup-helpers/src/AssetManager.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol", "lib/setup-helpers/src/Panic.sol", "lib/setup-helpers/src/Utils.sol", "src/Counter.sol", "test/recon/BeforeAfter.sol", "test/recon/Properties.sol", "test/recon/Setup.sol", "test/recon/TargetFunctions.sol", "test/recon/targets/AdminTargets.sol", "test/recon/targets/CounterTargets.sol", "test/recon/targets/DoomsdayTargets.sol", "test/recon/targets/ManagersTargets.sol"], "versionRequirement": "^0.8.0", "artifacts": {"CryticToFoundry": {"0.8.29": {"default": {"path": "CryticToFoundry.sol/CryticToFoundry.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "test/recon/Properties.sol": {"lastModificationDate": 1757009695193, "contentHash": "b14fa81446beb750", "interfaceReprHash": null, "sourceName": "test/recon/Properties.sol", "imports": ["lib/chimera/src/Asserts.sol", "lib/chimera/src/BaseSetup.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/ActorManager.sol", "lib/setup-helpers/src/AssetManager.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol", "lib/setup-helpers/src/Panic.sol", "lib/setup-helpers/src/Utils.sol", "src/Counter.sol", "test/recon/BeforeAfter.sol", "test/recon/Setup.sol"], "versionRequirement": "^0.8.0", "artifacts": {"Properties": {"0.8.29": {"default": {"path": "Properties.sol/Properties.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "test/recon/Setup.sol": {"lastModificationDate": 1757009695195, "contentHash": "0736e3346ff5019e", "interfaceReprHash": null, "sourceName": "test/recon/Setup.sol", "imports": ["lib/chimera/src/BaseSetup.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/ActorManager.sol", "lib/setup-helpers/src/AssetManager.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol", "lib/setup-helpers/src/Panic.sol", "lib/setup-helpers/src/Utils.sol", "src/Counter.sol"], "versionRequirement": "^0.8.0", "artifacts": {"Setup": {"0.8.29": {"default": {"path": "Setup.sol/Setup.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "test/recon/TargetFunctions.sol": {"lastModificationDate": 1757009695197, "contentHash": "6d034acacb86b3af", "interfaceReprHash": null, "sourceName": "test/recon/TargetFunctions.sol", "imports": ["lib/chimera/src/Asserts.sol", "lib/chimera/src/BaseProperties.sol", "lib/chimera/src/BaseSetup.sol", "lib/chimera/src/BaseTargetFunctions.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/ActorManager.sol", "lib/setup-helpers/src/AssetManager.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol", "lib/setup-helpers/src/Panic.sol", "lib/setup-helpers/src/Utils.sol", "src/Counter.sol", "test/recon/BeforeAfter.sol", "test/recon/Properties.sol", "test/recon/Setup.sol", "test/recon/targets/AdminTargets.sol", "test/recon/targets/CounterTargets.sol", "test/recon/targets/DoomsdayTargets.sol", "test/recon/targets/ManagersTargets.sol"], "versionRequirement": "^0.8.0", "artifacts": {"TargetFunctions": {"0.8.29": {"default": {"path": "TargetFunctions.sol/TargetFunctions.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "test/recon/targets/AdminTargets.sol": {"lastModificationDate": 1757009695198, "contentHash": "68bf2a2fdbdbdad1", "interfaceReprHash": null, "sourceName": "test/recon/targets/AdminTargets.sol", "imports": ["lib/chimera/src/Asserts.sol", "lib/chimera/src/BaseProperties.sol", "lib/chimera/src/BaseSetup.sol", "lib/chimera/src/BaseTargetFunctions.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/ActorManager.sol", "lib/setup-helpers/src/AssetManager.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol", "lib/setup-helpers/src/Panic.sol", "lib/setup-helpers/src/Utils.sol", "src/Counter.sol", "test/recon/BeforeAfter.sol", "test/recon/Properties.sol", "test/recon/Setup.sol"], "versionRequirement": "^0.8.0", "artifacts": {"AdminTargets": {"0.8.29": {"default": {"path": "AdminTargets.sol/AdminTargets.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "test/recon/targets/CounterTargets.sol": {"lastModificationDate": 1757009695201, "contentHash": "18a708c11eae8bdd", "interfaceReprHash": null, "sourceName": "test/recon/targets/CounterTargets.sol", "imports": ["lib/chimera/src/Asserts.sol", "lib/chimera/src/BaseProperties.sol", "lib/chimera/src/BaseSetup.sol", "lib/chimera/src/BaseTargetFunctions.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/ActorManager.sol", "lib/setup-helpers/src/AssetManager.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol", "lib/setup-helpers/src/Panic.sol", "lib/setup-helpers/src/Utils.sol", "src/Counter.sol", "test/recon/BeforeAfter.sol", "test/recon/Properties.sol", "test/recon/Setup.sol"], "versionRequirement": "^0.8.0", "artifacts": {"CounterTargets": {"0.8.29": {"default": {"path": "CounterTargets.sol/CounterTargets.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "test/recon/targets/DoomsdayTargets.sol": {"lastModificationDate": 1757009695199, "contentHash": "df1aa7f7fb348b09", "interfaceReprHash": null, "sourceName": "test/recon/targets/DoomsdayTargets.sol", "imports": ["lib/chimera/src/Asserts.sol", "lib/chimera/src/BaseProperties.sol", "lib/chimera/src/BaseSetup.sol", "lib/chimera/src/BaseTargetFunctions.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/ActorManager.sol", "lib/setup-helpers/src/AssetManager.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol", "lib/setup-helpers/src/Panic.sol", "lib/setup-helpers/src/Utils.sol", "src/Counter.sol", "test/recon/BeforeAfter.sol", "test/recon/Properties.sol", "test/recon/Setup.sol"], "versionRequirement": "^0.8.0", "artifacts": {"DoomsdayTargets": {"0.8.29": {"default": {"path": "DoomsdayTargets.sol/DoomsdayTargets.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}, "test/recon/targets/ManagersTargets.sol": {"lastModificationDate": 1757009695200, "contentHash": "772b7ca44142d2b1", "interfaceReprHash": null, "sourceName": "test/recon/targets/ManagersTargets.sol", "imports": ["lib/chimera/src/Asserts.sol", "lib/chimera/src/BaseProperties.sol", "lib/chimera/src/BaseSetup.sol", "lib/chimera/src/BaseTargetFunctions.sol", "lib/chimera/src/Hevm.sol", "lib/setup-helpers/src/ActorManager.sol", "lib/setup-helpers/src/AssetManager.sol", "lib/setup-helpers/src/EnumerableSet.sol", "lib/setup-helpers/src/MockERC20.sol", "lib/setup-helpers/src/Panic.sol", "lib/setup-helpers/src/Utils.sol", "src/Counter.sol", "test/recon/BeforeAfter.sol", "test/recon/Properties.sol", "test/recon/Setup.sol"], "versionRequirement": "^0.8.0", "artifacts": {"ManagersTargets": {"0.8.29": {"default": {"path": "ManagersTargets.sol/ManagersTargets.json", "build_id": "a74cedb45c32d659"}}}}, "seenByCompiler": true}}, "builds": ["03dedf8f401cfbc7", "a74cedb45c32d659", "e3f7bd7a4e03bd89"], "profiles": {"default": {"solc": {"optimizer": {"enabled": false, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"": ["ast"], "*": ["abi", "evm.bytecode.object", "evm.bytecode.sourceMap", "evm.bytecode.linkReferences", "evm.deployedBytecode.object", "evm.deployedBytecode.sourceMap", "evm.deployedBytecode.linkReferences", "evm.deployedBytecode.immutableReferences", "evm.methodIdentifiers", "metadata", "storageLayout"]}}, "evmVersion": "prague", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "prague", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}}, "preprocessed": false, "mocks": []}